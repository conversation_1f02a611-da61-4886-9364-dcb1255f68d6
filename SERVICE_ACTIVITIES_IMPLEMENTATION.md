# Service Activities Component Implementation

## Overview

A shared service activities component has been created to provide consistent functionality across expert overview, end user overview, and user details components. This component handles displaying service activities with like and comment functionality for logged-in users.

## Components Updated

### 1. Shared Service Activities Component

- **Location**: `src/app/shared/components/service-activities/`
- **Files**:
  - `service-activities.component.ts`
  - `service-activities.component.html`
  - `service-activities.component.scss`

### 2. Updated Components

- **Expert Overview**: `src/app/modules/user/components/expert-overview/`
- **End User Overview**: `src/app/modules/user/components/end-user-overview/`
- **User Details**: `src/app/modules/user/components/user-details/`

## Features

### Service Activities Component

- **Loading State**: Shows spinner while loading activities
- **Empty State**: Displays message when no activities are available
- **Activity Display**: Shows user profile, content, links, and images
- **Like Functionality**: Users can like/unlike activities (only for logged-in users)
- **Comment System**: Users can post comments on activities (only for logged-in users)
- **Comment Display**: Shows existing comments with user info and timestamps

### Input Properties

- `userId`: The user ID whose activities to display
- `userProfilePhoto`: Profile photo URL for the activity owner
- `userName`: Name of the activity owner
- `userRole`: Role or expertise of the activity owner
- `isLoggedIn`: Boolean indicating if current user is logged in
- `currentUserId`: Current logged-in user's ID

### Methods

- `getServiceActivities()`: Fetches service activities from API
- `toggleLike(activity)`: Handles like/unlike functionality
- `postComment(activity)`: Posts new comment on activity
- `onCommentKeyDown(event, activity)`: Handles Enter key for comment submission

## API Integration

The component integrates with the following API endpoints:

- `getServiceActivities(userId)`: Fetches activities for a user
- `addOrRemovePostLike(payload)`: Handles like/unlike actions
- `addActivityComment(payload)`: Posts new comments

## Usage Examples

### Expert Overview Component

```html
<app-service-activities [userId]="vm.userId" [userProfilePhoto]="vm.profilePhoto" [userName]="vm.firstName + ' ' + vm.lastName" [userRole]="vm.expertDetail?.roleName" [isLoggedIn]="!!userState$.userId" [currentUserId]="userState$.userId"> </app-service-activities>
```

### User Details Component

```html
<app-service-activities [userId]="vm.userId" [userProfilePhoto]="vm.profilePhoto" [userName]="isCompany ? vm.companyName : (vm?.admin?.firstName + ' ' + vm?.admin?.lastName)" [userRole]="getExpertiseString()" [isLoggedIn]="!!userState$.userId" [currentUserId]="userState$.userId"> </app-service-activities>
```

### End User Overview Component

```html
<app-service-activities [userId]="vm.userId" [userProfilePhoto]="vm.profilePhoto" [userName]="vm.firstName + ' ' + vm.lastName" [userRole]="vm.expertDetail?.roleName || 'End User'" [isLoggedIn]="!!userState$.userId" [currentUserId]="userState$.userId"> </app-service-activities>
```

## Styling

The component uses consistent styling with:

- Card-based layout for activities
- User avatar and information display
- Like and comment buttons with icons
- Comment input field for logged-in users
- Responsive design for mobile devices

## Security

- Like and comment functionality is only available for logged-in users
- User authentication is checked before allowing interactions
- Input validation prevents empty comments

## Future Enhancements

1. **Real-time Updates**: Implement WebSocket for real-time activity updates
2. **Pagination**: Add pagination for large numbers of activities
3. **Activity Types**: Support different types of activities (posts, videos, etc.)
4. **Media Upload**: Allow users to upload images/videos with activities
5. **Activity Sharing**: Add social sharing functionality
6. **Activity Analytics**: Track engagement metrics

## Testing

The component should be tested for:

- Loading states
- Empty states
- Like functionality
- Comment posting
- User authentication checks
- Responsive design
- Error handling
