{"name": "focile", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "CI=false ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^15.2.9", "@angular/common": "^15.1.0", "@angular/compiler": "^15.1.0", "@angular/core": "^15.1.0", "@angular/forms": "^15.1.0", "@angular/platform-browser": "^15.1.0", "@angular/platform-browser-dynamic": "^15.1.0", "@angular/router": "^15.1.0", "@ctrl/ngx-emoji-mart": "^9.2.0", "@fortawesome/fontawesome-free": "^5.15.3", "@microsoft/signalr": "^7.0.11", "@ng-select/ng-select": "^10.0.4", "@stripe/stripe-js": "^3.5.0", "@tinymce/tinymce-angular": "^7.0.0", "@types/leaflet": "^1.9.3", "angular-cropperjs": "^14.0.1", "aws-amplify": "^3.4.3", "aws-amplify-angular": "^6.0.60", "bootstrap": "^5.2.3", "bootstrap-icons": "^1.10.5", "cropperjs": "^1.5.13", "leaflet": "^1.9.4", "ngx-bootstrap": "^10.2.0", "ngx-owl-carousel-o": "^15.0.2", "ngx-toastr": "16.0.0", "rxjs": "~7.8.0", "sass": "^1.62.1", "swiper": "^10.3.1", "tslib": "^2.3.0", "zone.js": "~0.12.0"}, "devDependencies": {"@angular-devkit/build-angular": "^15.1.2", "@angular/cli": "~15.1.2", "@angular/compiler-cli": "^15.1.0", "@types/bootstrap": "^5.2.10", "@types/jasmine": "~4.3.0", "jasmine-core": "~4.5.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "typescript": "~4.9.4"}}