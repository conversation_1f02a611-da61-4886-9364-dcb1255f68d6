import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from './guards/auth.guard';

const routes: Routes = [
  {
    path: 'account',
    loadChildren: () =>
      import('./modules/account/account.module').then((x) => x.AccountModule),
  },
  {
    path: 'admin/ZQWCFQW5374vkwLVimVVMFm5rXgQTR',
    loadChildren: () =>
      import('./modules/admin/admin.module').then((x) => x.AdminModule),
  },
  {
    path: '',
    loadChildren: () =>
      import('./modules/user/end-user/end-user.module').then(
        (x) => x.EndUserModule
      ),
  },
  {
    path: 'adf',
    loadChildren: () =>
      import('./layout/layout.module').then((x) => x.LayoutModule),
  },
  {
    path: 'my-account',
    loadChildren: () =>
      import('./modules/user-details/user-detail.module').then(
        (x) => x.UserDetailModule
      ),
    canActivate: [AuthGuard],
  },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule {}
