.blur-dot {
  position: absolute;
  width: 500px;
  height: 500px;
  left: -356px;
  top: -223px;
  background: #014681;
  opacity: 0.5;
  filter: blur(250px);
  z-index: -1;
}

::ng-deep {
  // Modal centering and smooth animations
  .modal {
    // Default centering for all modals
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 1rem;

    &.custom-modal-animation {
      // Additional styling for enhanced animations
    }

    // Center the modal dialog
    .modal-dialog {
      &.modal-dialog-centered {
        display: flex;
        align-items: center;
        min-height: calc(100% - 2rem);
        margin: 1rem auto;
      }

      // Responsive sizing
      max-width: 90vw;
      width: auto;
      min-width: 300px;

      &.modal-sm {
        max-width: 400px;
      }

      &.modal-lg {
        max-width: 800px;
      }

      &.modal-xl {
        max-width: 1200px;
      }

      // Smooth scale animation for all modals
      // &.modal-animated {
      //   animation: modalSlideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      // }
    }

    // Modal content styling
    .modal-content {
      border: none;
      border-radius: 12px;
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
      overflow: hidden;
      width: 100%;

      // Smooth content animation
      animation: modalContentFadeIn 0.5s ease-out 0.1s both;
    }
  }

  // Modal backdrop styling
  .modal-backdrop {
    background-color: rgba(0, 0, 0, 0.6) !important;
    backdrop-filter: blur(3px);

    &.show {
      opacity: 1 !important;
    }
  }

  // Ensure proper stacking
  .modal {
    z-index: 1050;
  }

  .modal-backdrop {
    z-index: 1040;
  }

  // Keyframe animations for smooth modal appearance
  @keyframes modalSlideIn {
    0% {
      opacity: 0;
      transform: scale(0.7) translateY(-50px);
    }
    50% {
      opacity: 0.8;
      transform: scale(0.95) translateY(-10px);
    }
    100% {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  @keyframes modalContentFadeIn {
    0% {
      opacity: 0;
      transform: translateY(20px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes modalSlideOut {
    0% {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
    100% {
      opacity: 0;
      transform: scale(0.8) translateY(-30px);
    }
  }

  // Smooth closing animation
  .modal.fade-out {
    .modal-dialog {
      animation: modalSlideOut 0.3s ease-in forwards !important;
    }
  }

  // Bounce effect for attention
  @keyframes modalBounceIn {
    0% {
      opacity: 0;
      transform: scale(0.3);
    }
    50% {
      opacity: 1;
      transform: scale(1.05);
    }
    70% {
      transform: scale(0.9);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  // Alternative bounce animation
  .modal.bounce-in {
    .modal-dialog {
      animation: modalBounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55) !important;
    }
  }
}

.cookies-btn-row {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-block: 1rem;
  gap: 10px;
}
.cookies-policy-text {
  font-size: 14px;
  margin: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}
