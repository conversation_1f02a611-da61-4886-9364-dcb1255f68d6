import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { NavigationEnd, NavigationStart, Router } from '@angular/router';
import { delay, filter, tap } from 'rxjs';
import { UtilsService } from './shared/services/utils.service';
import { AccountService } from './modules/account/services/account.service';
import { ChatService } from './shared/services/chat.service';
import { ModalService } from './shared/services/modal.service';
import { companyType } from './shared/constant';
import { BsModalService } from 'ngx-bootstrap/modal';
import { TestimonialsService } from './shared/services/testimonials.service';
import { EcardFiltersService } from './shared/services/ecard-filter.service';
import { SolutionService } from './shared/services/solution.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent implements OnInit, OnDestroy {
  title = 'focile';
  user: any;
  modalRef: any;
  @ViewChild('cookies') cookiesNgTemplte!: any;
  constructor(
    private router: Router,
    private utils: UtilsService,
    private account: AccountService,
    private chatService: ChatService,
    private modalService: ModalService,
    private readonly bsModalService: BsModalService,
    private readonly testimonial: TestimonialsService,
    private readonly ecardFilters: EcardFiltersService,
    private readonly solution: SolutionService
  ) {
    this.getLocation();

    router.events
      .pipe(filter((e) => e instanceof NavigationStart))
      .subscribe(() => {
        this.utils.loading = true;
      });
    router.events
      .pipe(
        tap(() => {
          window.scrollTo({
            top: 0,
            left: 0,
          });
        }),
        filter((e) => e instanceof NavigationEnd),
        delay(500)
      )
      .subscribe((router: any) => {
        if (
          !router.url.startsWith('/account') &&
          !router.url.startsWith('/my-account') &&
          !router.url.startsWith('/admin')
        ) {
          let user: any = localStorage.getItem('user');
          user = JSON.parse(user);
          this.user = user;
          if (
            this.user &&
            this.user?.isCompleted == false &&
            this.user?.userType == companyType.Admin
          ) {
            // display only for admins only
            this.modalService.openModal('profile-not-complete', {});
          }
        }

        this.utils.loading = false;
      });

    let user: any = localStorage.getItem('user');
    user = JSON.parse(user);
    this.user = user;

    this.account.profileImage$.next(user?.profilePhoto);
    this.account.user$.next(user);
    if (user) {
      this.account.isLoggedIn$.next(true);
    }
    this.account.isLoggedIn$.subscribe((response) => {
      if (response) {
        this.chatService.startConnection();
      }
    });
  }

  ngOnDestroy(): void {
    this.user && this.chatService.hubConnection?.stop();
  }

  ngOnInit(): any {
    setTimeout(() => {
      const cookiesAccepts = localStorage.getItem('cookies');
      if (!cookiesAccepts) {
        this.modalRef = this.bsModalService.show(this.cookiesNgTemplte, {
          class: 'modal-xl',
        });
      }
    });
    this.account.isLoggedIn$.subscribe((response) => {
      if (response) {
        this.ecardFilters.getEcardFilters();
        this.solution.getSolutions();
      }
    });
    this.testimonial.getTestimonials();
  }

  acceptCooikes(cookieTyoe: string) {
    localStorage.setItem('cookies', cookieTyoe);
    this.account.cookiesPrivacy$.next(cookieTyoe);
    this.modalRef?.hide();
  }

  getLocation() {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position: any) => {
          if (position) {
            this.utils.location$.next({
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
            });
          }
        },
        (error: any) => {}
      );
    } else {
      alert('Geolocation is not supported by this browser.');
    }
  }
}
