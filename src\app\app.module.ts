import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { LandingPageComponent } from './modules/user/components/landing-page/landing-page.component';
import { FocileSearchComponent } from './modules/user/components/focile-search/focile-search.component';
import { AccountModule } from './modules/account/account.module';
import { RouterModule } from '@angular/router';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { HttpInterceptorInterceptor } from './utils/http-interceptor.interceptor';
import { SharedModule } from './shared/shared.module';

import { ToastrModule } from 'ngx-toastr';

import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { PopoverModule } from 'ngx-bootstrap/popover';
import { EndUserModule } from './modules/user/end-user/end-user.module';
import { AboutUsComponent } from './modules/user/components/about-us/about-us.component';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import {
  CommonModule,
  HashLocationStrategy,
  LocationStrategy,
} from '@angular/common';
import { ExpertLocatorComponent } from './modules/user/components/expert-locator/expert-locator.component';
import { ModalModule } from 'ngx-bootstrap/modal';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ProfileOverviewComponent } from './modules/user/components/profile-overview/profile-overview.component';
import { EndUserOverviewComponent } from './modules/user/components/end-user-overview/end-user-overview.component';
import { OurCompanyComponent } from './modules/user/components/our-company/our-company.component';
import { PartnershipComponent } from './modules/user/components/partnership/partnership.component';
import { ExpertOverviewComponent } from './modules/user/components/expert-overview/expert-overview.component';

// import { CarouselModule } from 'ngx-bootstrap/carousel';
import { ConnectionsComponent } from './modules/user/components/connections/connections.component';
import { TrademarkPolicyComponent } from './modules/user/components/trademark-policy/trademark-policy.component';
import { MediaUsageComponent } from './modules/user/components/media-usage/media-usage.component';
import { CookiePolicyComponent } from './modules/user/components/cookie-policy/cookie-policy.component';
import { RatingModule } from 'ngx-bootstrap/rating';
import { register } from 'swiper/element/bundle';
import { FocileRecentPartnerComponent } from './modules/user/components/focile-recent-partner/focile-recent-partner.component';
import { TopCategoriesComponent } from './modules/user/components/top-categories/top-categories.component';
import { EasyConnectionComponent } from './modules/user/components/easy-connection/easy-connection.component';
import { PeopleJoinedComponent } from './modules/user/components/people-joined/people-joined.component';
import { KeyFeaturesComponent } from './modules/user/components/key-features/key-features.component';
import { OurTestimonailComponent } from './modules/user/components/our-testimonail/our-testimonail.component';
import { CarouselModule as BsCarouselModule } from 'ngx-bootstrap/carousel';
import { BlogListComponent } from './modules/user/components/blog-list/blog-list.component';
import { BlogDetailComponent } from './modules/user/components/blog-detail/blog-detail.component';
import { AboutFocileComponent } from './modules/user/components/about-us/about-focile/about-focile.component';
import { OurValuesComponent } from './modules/user/components/about-us/our-values/our-values.component';
import { OurCultureComponent } from './modules/user/components/about-us/our-culture/our-culture.component';
import { OurCommitmentComponent } from './modules/user/components/about-us/our-commitment/our-commitment.component';
import { BulkInviteComponent } from './shared/components/bulk-invite/bulk-invite.component';
import { RecrutingChannelComponent } from './modules/user/components/top-categories/recruting-channel/recruting-channel.component';
import { LeverageFocileComponent } from './modules/user/components/top-categories/leverage-focile/leverage-focile.component';
import { CreatedProfileComponent } from './modules/user/components/top-categories/created-profile/created-profile.component';
import { DirectConnectionComponent } from './modules/user/components/top-categories/direct-connection/direct-connection.component';
import { OurValuePropositionComponent } from './modules/user/components/top-categories/our-value-proposition/our-value-proposition.component';
import { InviteConnectionsComponent } from './modules/user/components/top-categories/invite-connections/invite-connections.component';
import { ProcessComponent } from './modules/user/components/key-features/process/process.component';
import { ECardMarketingComponent } from './modules/user/components/key-features/e-card-marketing/e-card-marketing.component';
import { WinWithConfidenceComponent } from './modules/user/components/key-features/win-with-confidence/win-with-confidence.component';
import { TargetAudienceComponent } from './modules/user/components/key-features/target-audience/target-audience.component';
import { PlatformComponent } from './modules/user/components/platform/platform.component';
import { FrequentlyAskedQuestionsComponent } from './modules/user/components/frequently-asked-questions/frequently-asked-questions.component';
import { PromoteYourBrandComponent } from './modules/user/components/top-categories/promote-your-brand/promote-your-brand.component';
import { ValuePropositionComponent } from './modules/user/components/top-categories/value-proposition/value-proposition.component';
import { AdminEcardPortalComponent } from './modules/user/components/admin-ecard-portal/admin-ecard-portal.component';
import { PartnerEngagementComponent } from './modules/user/components/top-categories/partner-engagement/partner-engagement.component';
import { TestimonialsService } from './shared/services/testimonials.service';
import { EcardFiltersService } from './shared/services/ecard-filter.service';
import { SolutionService } from './shared/services/solution.service';

@NgModule({
  declarations: [
    AppComponent,
    LandingPageComponent,
    FocileSearchComponent,
    AboutUsComponent,
    ExpertLocatorComponent,
    ProfileOverviewComponent,
    EndUserOverviewComponent,
    OurCompanyComponent,
    PartnershipComponent,
    ExpertOverviewComponent,
    ConnectionsComponent,
    TrademarkPolicyComponent,
    MediaUsageComponent,
    CookiePolicyComponent,
    FocileRecentPartnerComponent,
    TopCategoriesComponent,
    EasyConnectionComponent,
    PeopleJoinedComponent,
    KeyFeaturesComponent,
    OurTestimonailComponent,
    BlogListComponent,
    BlogDetailComponent,
    AboutFocileComponent,
    OurValuesComponent,
    OurCultureComponent,
    OurCommitmentComponent,
    RecrutingChannelComponent,
    LeverageFocileComponent,
    CreatedProfileComponent,
    DirectConnectionComponent,
    OurValuePropositionComponent,
    InviteConnectionsComponent,
    ProcessComponent,
    ECardMarketingComponent,
    WinWithConfidenceComponent,
    TargetAudienceComponent,
    PlatformComponent,
    FrequentlyAskedQuestionsComponent,
    PromoteYourBrandComponent,
    ValuePropositionComponent,
    AdminEcardPortalComponent,
    PartnerEngagementComponent,
  ],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    AppRoutingModule,
    SharedModule,
    AccountModule,
    RouterModule,
    HttpClientModule,
    ToastrModule.forRoot(),
    PopoverModule.forRoot(),
    EndUserModule,
    TooltipModule.forRoot(),
    ModalModule.forRoot(),
    BsCarouselModule.forRoot(),
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    RatingModule.forRoot(),
  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HttpInterceptorInterceptor,
      multi: true,
    },
    {
      useClass: HashLocationStrategy,
      provide: LocationStrategy,
    },
    TestimonialsService,
    EcardFiltersService,
    SolutionService,
  ],
  bootstrap: [AppComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class AppModule {
  constructor() {
    register();
  }
}
