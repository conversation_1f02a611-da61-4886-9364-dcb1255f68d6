import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot, UrlTree } from '@angular/router';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class CompanyDetailsGuard  {
  constructor(private router: Router) {}
  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ):
    | Observable<boolean | UrlTree>
    | Promise<boolean | UrlTree>
    | boolean
    | UrlTree {
    let user: any = localStorage.getItem('user');
    if (user) {
      user = JSON.parse(user);
      if (user.userType == 1) {
        return this.router.navigate(['/my-account/user-detail']);
      }
    }
    return true;
  }
}
