<div class="relative z-11">
  <header class="fc-header-wrapper" [ngClass]="{ hovered: isMenuHovered }">
    <div class="fc-container">
      <div class="focile-header">
        <div class="focile-logo">
          <a class="navbar-brand" routerLink="/home">
            <div class="position-relative">
              <img src="./assets/images/focile-logo.svg" height="54" width="100" />
              <br />
              <span>The Power of Connection</span>
            </div>
          </a>
        </div>

        <div class="navbar-top d-none d-sm-flex">
          <ul>
            <li>
              <a href="#" routerLink="/home">Home</a>
            </li>
            <li>
              <a href="javascript:void(0);" (mouseenter)="toggleHover(true)"
                (mouseleave)="toggleHover(false)">Company</a>
              <div class="submenu-item" (mouseenter)="toggleHover(true)" (mouseleave)="toggleHover(false)">
                <div class="border--"></div>
                <div class="fc-container">
                  <div class="d-flex gap-4">
                    <div class="left-bar order-2">
                      <div class="submenu-item-menu">
                        <div class="picture-menu">
                          <img src="./assets/images/legal.jpg" alt="avconsultant" />
                        </div>
                        <div class="sublist-item-list">
                          <div class="company-item">
                            <h4>Legal</h4>
                            <ul>
                              <li>
                                <a href="#" routerLink="/privacy-policy">Privacy</a>
                              </li>
                              <li>
                                <a href="#" routerLink="/terms-and-condtions">Terms of use</a>
                              </li>
                              <li>
                                <a href="#" routerLink="/cookies-policy">Cookies Policy</a>
                              </li>
                              <li>
                                <a href="#" routerLink="/agreements">Agreements</a>
                              </li>
                              <li>
                                <a routerLink="/copyrights"> Copyright </a>
                              </li>
                              <li>
                                <a routerLink="/trademark-policy">
                                  Trademark
                                </a>
                              </li>
                              <li>
                                <a href="#" routerLink="/accessibility">Accessibility
                                </a>
                              </li>
                              <li>
                                <a href="#" routerLink="/api">API Terms </a>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="right-bar order-1">
                      <div class="inner-menu">
                        <div class="picture-menu">
                          <img src="./assets/images/company.jpg" t="avconsultant" />
                        </div>
                        <div class="inner-menu-item">
                          <div class="company-item">
                            <h4>Company</h4>
                            <ul>
                              <li>
                                <a href="#" routerLink="/about-us">About Focile</a>
                              </li>
                              <li>
                                <a href="#" routerLink="/partnership">Partnership</a>
                              </li>
                              <li>
                                <a href="#" routerLink="/contact-us">Contact us</a>
                              </li>
                              <li>
                                <a href="#" routerLink="/blog-list">Blog</a>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </li>
            <li><a href="#" [routerLink]="isLoggedIn ? '/home' : '/account/login'">Explore Channel</a></li>
            <li>
              <a href="javascript:void(0);" (mouseenter)="toggleHover(true)" (mouseleave)="toggleHover(false)">Resource</a>
              <div class="submenu-item" (mouseenter)="toggleHover(true)" (mouseleave)="toggleHover(false)">
                <div class="border--"></div>
                <div class="fc-container">
                  <div class="d-flex gap-4">
                    <div class="left-bar order-1">
                      <div class="submenu-item-menu">
                        <div class="picture-menu">
                          <img src="./assets/images/channel-partner.jpg" alt="Channel Partners" />
                        </div>
                        <div class="sublist-item-list">
                          <div class="company-item">
                            <h4>Channel Partners</h4>
                            <ul>
                              <li>
                                <a href="#" routerLink="/partnership">Partnership</a>
                              </li>
                              <li>
                                <a href="#" routerLink="/user-accounts-info">Users account</a>
                              </li>
                              <li>
                                <a href="#" routerLink="/connections">Connections</a>
                              </li>
                              <li>
                                <a href="#" routerLink="/terms-and-condtions">Terms of Use</a>
                              </li>
                              <li>
                                <a href="#" routerLink="/media-usage">Media Usage</a>
                              </li>
                              <li>
                                <a href="#" routerLink="/agreements">Agreement</a>
                              </li>
                              <li><a href="#" routerLink="/become-partner">Business Partner</a></li>
                              <li>
                                <a href="#" routerLink="/faq">FAQ</a>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="right-bar order-2">
                      <div class="inner-menu">
                        <div class="picture-menu">
                          <img src="./assets/images/business-team.jpg" alt="Users & Businesses" />
                        </div>
                        <div class="inner-menu-item">
                          <div class="company-item">
                            <h4>Users & Businesses</h4>
                            <ul>
                              <li>
                                <a href="#" routerLink="/user-accounts-info">Users Account</a>
                              </li>
                              <li>
                                <a href="#" routerLink="/connections">Connections</a>
                              </li>
                              <li>
                                <a href="#" routerLink="/agreements">User Agreement</a>
                              </li>
                              <li>
                                <a href="#" routerLink="/terms-and-condtions">Terms of Use</a>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </li>
            <li *ngIf="isLoggedIn">
              <a href="#" routerLink="/platform">Platform</a>
            </li>

            <li><a href="#" routerLink="/contact-us">Contact</a></li>
          </ul>
        </div>
        <div>
        </div>
        <div class="d-flex d-md-none align-items-center gap-3 me-0 ml-auto">
          <div *ngIf="isLoggedIn" class="d-flex justify-content-center align-items-center gap-3">
            <span role="button" tooltip="My Connections" routerLink="/my-account/favorites">
              <img src="./assets/images/my-connect.png" alt="My Connections" />
            </span>
            <span type="button" routerLink="/my-account/chat" class="position-relative pt-1">
              <!-- <i class="fas fa-comments fs-18"></i> -->
              <img src="./assets/images/chat.png" alt="Chat" />
              <span class="position-absolute notfication-count">
                {{ chatService.unReadMessagesConversationsCount | async }}
              </span>
            </span>
            <span role="button" [popoverTitle]="'Notifications'" [popover]="notificationsPop" [outsideClick]="true"
              class="position-relative pt-1">
              <img src="./assets/images/notifications.png" alt="Notifications" />
          
              <span class="position-absolute notfication-count">
                {{ unReadNotificationCount }}
              </span>
            </span>
          
            <ng-template #notificationsPop>
              <ng-container *ngTemplateOutlet="notifications"> </ng-container>
            </ng-template>
          </div>
        </div>
        <!-- Mobile Toggle -->
        <button class="mobile-toggle" (click)="toggleMobileMenu()">
          <span *ngIf="!isMobileMenuOpen">
            <img src="./assets/svgs/bars-solid.svg" alt="Menu" />  
          </span> <!-- hamburger icon -->
          <span *ngIf="isMobileMenuOpen">
            <img src="./assets/svgs/xmark-solid.svg" alt="Close" />  
          </span> <!-- close icon -->
        </button>
        <div class="fc-login-sign-up d-none d-sm-flex">
          <a routerLink="/account/login" class="login-btn" *ngIf="!isLoggedIn">Log In</a>
          <a routerLink="/account/choose-option" class="sign-up-btn" *ngIf="!isLoggedIn">Sign Up for free</a>
          <div>
            <div *ngIf="isLoggedIn" class="d-flex justify-content-center align-items-center gap-3 me-3">
              <span role="button" tooltip="My Connections" routerLink="/my-account/favorites">
                <img src="./assets/images/my-connect.png" alt="My Connections" />
              </span>
              <span type="button" routerLink="/my-account/chat" class="position-relative pt-1">
                <!-- <i class="fas fa-comments fs-18"></i> -->
                <img src="./assets/images/chat.png" alt="Chat" />
                <span class="position-absolute notfication-count">
                  {{ chatService.unReadMessagesConversationsCount | async }}
                </span>
              </span>
              <span role="button" [popoverTitle]="'Notifications'" [popover]="notificationsPop" [outsideClick]="true"
                class="position-relative pt-1">
                <img src="./assets/images/notifications.png" alt="Notifications" />

                <span class="position-absolute notfication-count">
                  {{ unReadNotificationCount }}
                </span>
              </span>

              <ng-template #notificationsPop>
                <ng-container *ngTemplateOutlet="notifications"> </ng-container>
              </ng-template>
            </div>
          </div>

          <div role="button" class="user-profile-data" *ngIf="isLoggedIn">
            <div class="dropdown" dropdown>
              <button dropdownToggle class="btn dropdown-toggle d-flex justify-content-center align-items-center">
                <img [src]="profileImage" appImg />
                <label class="ms-2 mb-0">{{
                  user?.userName | titlecase
                  }}</label>
              </button>

              <ul id="dropdown-animated" *dropdownMenu class="dropdown-menu dropdown-menu-end">
                <li *ngFor="let link of accountLinks">
                  <a role="button" (click)="gotoUserDetails(link)" class="dropdown-item">
                    {{ link.text }}
                  </a>
                </li>
                <li>
                  <a class="dropdown-item text-danger" role="button" (click)="openModal()">
                    <i class="fa fa-sign-out-alt me-1"></i> Logout
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
</div>

<div class="categories-navbar" *ngIf="secondNav && isHomeRoute">
  <div class="fc-container">
    <div class="" *ngIf="isLoggedIn">
      <ul class="category-navbar-item gap-4">
        <li class="nav-item" role="button" [ngClass]="{ active: viewType$ === 'avconsultant' }">
          <a class="" aria-current="page" (click)="setView('avconsultant')" routerLink="/home">
            <span>
              <img src="./assets/images/consultant.png" alt="avconsultant" />
            </span>
            Consultants</a>
        </li>
        <li class="nav-item" role="button" [ngClass]="{ active: viewType$ === 'reseller' }">
          <a class="" aria-current="page" (click)="setView('reseller')" routerLink="/home"><span>
              <img src="./assets/images/reseller-icon.png" alt="Resellers" /> </span>Resellers</a>
        </li>
        <!-- <li *ngIf="user.userType == companyType.EndUser" class="nav-item" role="button"
          [ngClass]="{ active: viewType$ === 'business' }">
          <a class="" aria-current="page" (click)="setView('business')" routerLink="/home">
            <span>
              <img src="./assets/images/consultant.png" alt="Business" /> </span>Business</a>
        </li> -->
        <li *ngIf="
            user.userType != companyType.EndUser &&
            user.type != memberType.vendor
          " class="nav-item" role="button" [ngClass]="{ active: viewType$ === 'vendor' }">
          <a class="" aria-current="page" (click)="setView('vendor')" routerLink="/home"><span>
              <img src="./assets/images/vendors.png" alt="Vendors" /> </span>Vendors</a>
        </li>
        <li *ngIf="
            user.userType != companyType.EndUser &&
            user.type != memberType.distributer
          " class="nav-item" role="button" [ngClass]="{ active: viewType$ === 'distributor' }">
          <a class="" aria-current="page" (click)="setView('distributor')" routerLink="/home"><span>
              <img src="./assets/images/distributer.png" alt="Distributors" /> </span>Distributors</a>
        </li>
      </ul>
    </div>
  </div>
</div>

<ng-template #notifications>
  <div id="box" class="show-notifications">
    <ul class="list-group list-group-flush">
      <ng-container *ngFor="let notification of notifications$; let i = index">
        <li class="list-group-item" [ngClass]="{
            'fw-semibold bg-primary bg-opacity-10': !notification.isRead
          }" role="button" placement="top" tooltip="Mark as read" container="body"
          (click)="handleNotificationClick(notification, i)">
          <span class="fs-14">{{ notification.text }} </span>
          <br />
          <span class="fs-10 text-muted">{{ notification.createdAt | date : "medium" }}
          </span>
          <div *ngIf="notification.type == 4 && !notification.isRead" class="d-flex justify-content-evenly mt-2">
            <div>
              <button (click)="approvedConnection($event, notification, i)"
                class="btn btn-outline-success rounded-5 h-auto">
                <i class="fa fa-check"></i>
              </button>
            </div>
            <div>
              <button class="btn btn-outline-danger rounded-5 h-auto">
                <i class="fa fa-times"></i>
              </button>
            </div>
          </div>
        </li>
      </ng-container>
    </ul>
  </div>
</ng-template>






<!-- Overlay -->
<div class="mobile-overlay" *ngIf="isMobileMenuOpen" (click)="toggleMobileMenu()"></div>

<!-- Sidebar Menu -->
<div class="mobile-menu" [class.open]="isMobileMenuOpen">

  <a routerLink="/account/login" class="login-btn" *ngIf="!isLoggedIn">Log In</a>
  <a routerLink="/account/choose-option" class="sign-up-btn" *ngIf="!isLoggedIn">Sign Up</a>

  <button class="m-user-name" *ngIf="isLoggedIn">
    <img [src]="profileImage" appImg />
    <label class="ms-2 mb-0">{{
      user?.userName | titlecase
      }}</label>
  </button>

  <ul class="menu m-after-login" *ngIf="isLoggedIn">
    <li class="menu-item" *ngFor="let link of accountLinks" (click)="toggleMobileMenu()">
      <a role="button" (click)="gotoUserDetails(link)" class="dropdown-item">
        {{ link.text }}
      </a>
    </li>
  </ul>

  <ul class="menu">
    <li class="menu-item">
      <a routerLink="/home" (click)="toggleMobileMenu()">Home</a>
    </li>

    <!-- Company -->
    <li class="menu-item has-submenu">
      <a href="#" (click)="toggleSubmenu('company'); $event.preventDefault()">
        Company
        <span class="arrow">
          <span *ngIf="mobileSubmenu === 'company'">
            <img src="./assets/svgs/angle-up.svg" alt="Menu" />  
          </span> <!-- Up Arrow -->
          <span *ngIf="mobileSubmenu !== 'company'"><img src="./assets/svgs/angle-down.svg" alt="Menu" />  </span> <!-- Down Arrow -->
        </span>
      </a>
      <ul class="submenu" *ngIf="mobileSubmenu === 'company'">
        <li><a routerLink="/about-us" (click)="toggleMobileMenu()">About Focile</a></li>
        <li><a routerLink="/partnership" (click)="toggleMobileMenu()">Partnership</a></li>
        <li><a routerLink="/contact-us" (click)="toggleMobileMenu()">Contact Us</a></li>
        <li><a routerLink="/blog-list" (click)="toggleMobileMenu()">Blog</a></li>
        <hr />
        <!-- <li><strong>Legal</strong></li> -->
        <li><a routerLink="/privacy-policy" (click)="toggleMobileMenu()">Privacy</a></li>
        <li><a routerLink="/terms-and-condtions" (click)="toggleMobileMenu()">Terms of Use</a></li>
        <li><a routerLink="/cookies-policy" (click)="toggleMobileMenu()">Cookies Policy</a></li>
        <li><a routerLink="/agreements" (click)="toggleMobileMenu()">Agreements</a></li>
        <li><a routerLink="/copyrights" (click)="toggleMobileMenu()">Copyright</a></li>
        <li><a routerLink="/trademark-policy" (click)="toggleMobileMenu()">Trademark</a></li>
        <li><a routerLink="/accessibility" (click)="toggleMobileMenu()">Accessibility</a></li>
        <li><a routerLink="/api" (click)="toggleMobileMenu()">API Terms</a></li>
      </ul>
    </li>

    <!-- Explore Channel -->
    <li class="menu-item">
      <a [routerLink]="isLoggedIn ? '/home' : '/account/login'" (click)="toggleMobileMenu()">Explore Channel</a>
    </li>

    <!-- Resource -->
    <li class="menu-item has-submenu">
      <a href="#" (click)="toggleSubmenu('resource'); $event.preventDefault()">
        Resource
        <span class="arrow">
          <span *ngIf="mobileSubmenu === 'company'">
            <img src="./assets/svgs/angle-up.svg" alt="Menu" />  
          </span> <!-- Up Arrow -->
          <span *ngIf="mobileSubmenu !== 'company'"><img src="./assets/svgs/angle-down.svg" alt="Menu" />  </span> <!-- Down Arrow -->
        </span>
      </a>
      <ul class="submenu" *ngIf="mobileSubmenu === 'resource'">
        <li><strong>Channel Partners</strong></li>
        <li><a routerLink="/partnership" (click)="toggleMobileMenu()">Partnership</a></li>
        <li><a routerLink="/user-accounts-info" (click)="toggleMobileMenu()">Users Account</a></li>
        <li><a routerLink="/connections" (click)="toggleMobileMenu()">Connections</a></li>
        <li><a routerLink="/terms-and-condtions" (click)="toggleMobileMenu()">Terms of Use</a></li>
        <li><a routerLink="/media-usage" (click)="toggleMobileMenu()">Media Usage</a></li>
        <li><a routerLink="/agreements" (click)="toggleMobileMenu()">Agreement</a></li>
        <li><a routerLink="/become-partner" (click)="toggleMobileMenu()">Business Partner</a></li>
        <li><a routerLink="/faq" (click)="toggleMobileMenu()">FAQ</a></li>
        <hr />
        <li><strong>Users & Businesses</strong></li>
        <li><a routerLink="/user-accounts-info" (click)="toggleMobileMenu()">Users Account</a></li>
        <li><a routerLink="/connections" (click)="toggleMobileMenu()">Connections</a></li>
        <li><a routerLink="/agreements" (click)="toggleMobileMenu()">User Agreement</a></li>
        <li><a routerLink="/terms-and-condtions" (click)="toggleMobileMenu()">Terms of Use</a></li>
      </ul>
    </li>

    <!-- Platform -->
    <li class="menu-item" *ngIf="isLoggedIn">
      <a routerLink="/platform" (click)="toggleMobileMenu()">Platform</a>
    </li>

    <!-- Contact -->
    <li class="menu-item">
      <a routerLink="/contact-us" (click)="toggleMobileMenu()">Contact</a>
    </li>

    <!-- Contact -->
    <li class="menu-item" *ngIf="isLoggedIn">
      <a class="d-flex align-items-center" role="button" (click)="openModal()"><i class="fa fa-sign-out-alt me-2"></i> Logout</a>
    </li>
  </ul>
</div>
<!-- End of Sidebar Menu -->