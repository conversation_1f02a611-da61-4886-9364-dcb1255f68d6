.h-45-p {
  height: 2.8125rem;
}

.w-120-p {
  width: 8.9375rem;
}

.sub-header {
  .link-text {
    font-size: 1rem;
    font-weight: 700;
    line-height: 1.2894rem;
  }
}

.search-icon {
  top: 0.75rem;
  left: 1.5rem;
}

.mr-10 {
  margin-right: 10%;
}

.bs-dropdown-container {
  z-index: 1060;
}

.nav > .dropdown-megamenu {
  position: static;
}

@media (min-width: 768px) {
  .navbar-nav .dropdown-megamenu:hover .dropdown-container {
    display: block;
  }
}

@media (max-width: 767px) {
  .navbar-nav .show .dropdown-container {
    position: static;
    float: none;
    width: auto;
    margin-top: 0;
    border: 0;
    box-shadow: none;
    border-radius: 0;
  }
}

.dropdown-megamenu > .dropdown-container {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  max-width: 100%;
  padding: 15px;
}

.dropdown-megamenu .dropdown-menu {
  display: block;
  width: 45.125rem;
}

.link-image .media-object {
  float: left;
  margin-bottom: 7.5px;
}

.link-image-sm + .link-image-sm .media-object {
  margin-left: 7.5px;
}

.thumbnail .caption {
  min-height: 120px;
}

.thumbnail:hover {
  text-decoration: none;
}

/* Link list */
.list-links {
  list-style: none;
  padding: 0;
}

.list-links li {
  line-height: 1.71428571;
}

.list-links a {
  color: #555;
}

.list-links a:hover,
.list-links a:focus,
.list-links a:active {
  color: #22527b;
}

h3 {
  font-family: "Open Sans", sans-serif;
  font-weight: bold;
  text-align: center;
  line-height: 1.3;
  margin-bottom: 2rem;
  color: #fff;
}

.dropdown-menu {
  left: 37.5rem;
  right: 6.225rem;
}

.icon {
  cursor: pointer;
  margin-right: 50px;
  line-height: 60px;
}

.icon span {
  background: #f00;
  padding: 7px;
  border-radius: 50%;
  color: #fff;
  vertical-align: top;
  margin-left: -25px;
}

.icon img {
  display: inline-block;
  width: 26px;
  margin-top: 4px;
}

.icon:hover {
  opacity: 0.7;
}

.logo {
  flex: 1;
  margin-left: 50px;
  color: #eee;
  font-size: 20px;
  font-family: monospace;
}

.notifications {
  width: 300px;
  position: absolute;
  top: 63px;
  right: 255px;
  opacity: 0;
  height: 0px;
  border-radius: 5px 0px 5px 5px;
  background-color: #fff;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
}

.show-notifications {
  height: 250px;
  opacity: 1;
  overflow-y: scroll;
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
}

.show-notifications::-webkit-scrollbar {
  width: 0;
  /* Hide scrollbar track */
  background: transparent;
  /* Hide scrollbar background */
}

.show-notifications::-webkit-scrollbar-thumb {
  background: transparent;
  /* Hide scrollbar thumb (the draggable part) */
}

.notifications h2 {
  font-size: 14px;
  padding: 10px;
  border-bottom: 1px solid #eee;
}

.notifications h2 span {
  color: #f00;
}

.notifications-item {
  display: flex;
  border-bottom: 1px solid #eee;
  padding: 6px 9px;
  margin-bottom: 0px;
  cursor: pointer;
}

.unread-notification {
  background-color: #014681;
}

.notifications-item .text h4 {
  font-size: 16px;
  margin-top: 3px;
}

.notifications-item .text p {
  font-size: 12px;
}

.notfication-count {
  top: -5%;
  right: -6px;
  height: 18px;
  width: 18px;
  background: var(--bs-primary);
  border-radius: 50%;
  color: white !important;
  display: flex;
  justify-content: center;
  font-size: 10px;
  align-items: center;
}

/* New Header Stylesheet */

.focile-logo {
  span {
    font-size: 14px;
  }
}

.fc-header-wrapper {
  padding: 0px;
  transition: background-color 0.3s ease;
  height: 100px;
  display: flex;
  align-items: center;
}

.navbar-top a:hover .fc-header-wrapper {
  background-color: #f0f0f0;
}

.focile-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 11;
}

.navbar-top {
  ul {
    display: flex;
    flex-direction: row;
    list-style: none;
    margin-bottom: 0px;
    padding-left: 0px;

    li {
      display: flex;

      a {
        padding: 42px 1rem;
        font-style: normal;
        font-weight: 600;
        font-size: 14px;
        line-height: 120%;
        color: rgba(25, 24, 37, 0.5);
        text-decoration: none;
        transition: 0.3ms;
        cursor: pointer;

        &:hover {
          color: rgba(25, 24, 37, 1);
        }
      }

      &:hover .submenu-item {
        transform: translateY(100px);
        transition: transform 200ms;
        display: block;
      }
    }
  }

  .submenu-item {
    position: absolute;
    width: 100%;
    height: auto;
    background-color: #ffffff;
    top: 0px;
    left: 0;
    right: 0;
    z-index: -1;
    -webkit-transform: translateY(calc(-101% - 110px));
    -moz-transform: translateY(calc(-101% - 110px));
    -ms-transform: translateY(calc(-101% - 110px));
    transform: translateY(calc(-101% - 110px));
    -webkit-transition: -webkit-transform 200ms;
    transition: transform 200ms;
    cursor: auto;
    box-shadow: 0px 7px 10px #00000017;
    border-top: 0px;
    display: none;
    ul {
      display: flex;
      flex-direction: column;
      list-style: none;
      padding: 1rem;

      li {
        a {
          padding: 8px 1rem;
          font-style: normal;
          font-weight: 400;
          font-size: 14px;
          line-height: 16px;
          color: rgba(25, 24, 37, 0.75);
          flex: none;
          order: 0;
          flex-grow: 0;
          width: 100%;

          &:hover {
            color: rgb(0 87 147);
          }
        }
      }
    }
  }
}

.fc-login-sign-up {
  display: flex;
  flex-direction: row;
  align-items: center;

  .login-btn {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 119px;
    height: 49px;
    background: white;
    border-radius: 100px;
    flex: none;
    order: 1;
    flex-grow: 0;
    font-size: 14px;
    color: #222831;
    text-decoration: none;
    font-weight: bold;
  }

  .sign-up-btn {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 8px;
    min-width: 119px;
    height: 49px;
    background: #014681;
    border-radius: 100px;
    flex: none;
    order: 1;
    flex-grow: 0;
    font-size: 14px;

    color: white;
    text-decoration: none;
    font-weight: 600;
    padding-inline: 1rem;
    width: auto;
  }

  .user-profile-data {
    img {
      width: 30px;
      height: 30px;
      object-fit: cover;
      border-radius: 15px !important;
    }

    label {
      font-weight: 600;
      font-size: 14px;
      cursor: pointer;
    }

    button {
      &:focus {
        outline: none !important;
        box-shadow: none;
      }
    }
  }

  .dropdown-menu {
    width: 224px;
    height: auto;
    background: #ffffff;
    box-shadow: 0px 1px 20px 3px #0000001a, 0 0 #0000001a;
    border-radius: 16px;
    border: none;
    overflow: hidden;
  }

  .dropdown-item {
    padding: 8px 1rem;
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 16px;
    color: rgba(25, 24, 37, 0.75);
    flex: none;
    order: 0;
    flex-grow: 0;
    width: 100%;

    &.active {
      color: white !important;
    }
    &:active {
      color: white !important;
    }
  }
}

.categories-navbar {
  margin-top: 100px;
}

.category-navbar-item {
  display: flex;
  flex-direction: row;
  border-bottom: 1px solid #d9dbe9;
  align-items: center;
  justify-content: center;
  list-style: none;
  padding-left: 0px;

  li {
    width: 25%;
    display: flex;
    align-items: start;
    justify-content: center;
    min-height: 72px;
    border-bottom: 2px solid transparent;

    a {
      text-decoration: none;
      font-size: 18px;
      font-weight: 500;
      display: flex;
      flex-direction: row;
      gap: 1rem;
      align-items: center;
      color: #a0a3bd;
    }

    span {
      min-width: 46.37px;
      min-height: 48px;
      left: 0px;
      top: 0px;
      background: rgba(1, 69, 129, 0.1);
      border-radius: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &.active {
      border-color: #014681;

      a {
        color: #014681;
      }
    }
  }
}

.fc-header-wrapper.scrolled {
  background-color: white;
  /* Example styling */
  transition: background-color 0.3s ease-in-out;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 0;
}

.fc-header-wrapper.hovered {
  background-color: white;
  /* Example styling */
  transition: background-color 0.1s ease-in-out;
  top: 0;
  width: 100%;
  -webkit-transition: -webkit-transform 400ms;
  transition: transform 400ms;

  .navbar-top li:hover a {
    color: black;
  }
}

.relative {
  position: relative;
}

.z-10 {
  z-index: 10;
}
.z-11 {
  z-index: 111;
}
.border-- {
  width: 100%;
  height: 1px;
  background-color: #d6d6d8;
}
.left-bar {
  width: 60%;
  .inner-menu {
    grid-template-columns: 30% 70%;
  }

  .submenu-item-menu {
    width: 100%;
    display: flex;
    flex-direction: row;
    padding: 2rem 0px;
    gap: 1.5rem;

    .picture-menu {
      width: 200px;
      border-radius: 15px;
      overflow: hidden;
      height: 190px;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .sublist-item-list {
      width: calc(100% - 200px);
      display: flex;

      h4 {
        font-size: 18px;
        border-bottom: 1px solid #dddddd;
        padding-bottom: 1rem;
      }

      ul {
        padding-left: 0px;
        list-style: none;
        -webkit-flex-basis: 100%;
        -ms-flex-preferred-size: 100%;
        flex-basis: 100%;
        -webkit-box-flex: 0;
        -webkit-flex-grow: 0;
        -ms-flex-positive: 0;
        flex-grow: 0;
        max-width: 100%;
        padding: 0px;

        a {
          padding-inline: 0px !important;
        }
      }
    }
    .company-item {
      display: flex;
      flex-direction: column;
      padding-right: 0px;

      ul {
        width: 100%;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;

        li {
          width: 50%;
        }
      }
    }
  }
}
.right-bar {
  width: 40%;
  .inner-menu {
    display: flex;
    flex-direction: row;
    gap: 1.5rem;
  }
  .inner-menu-item {
    flex: 1;
  }
  .company-item {
    display: flex;
    flex-direction: column;
    padding-right: 0rem;

    ul {
      width: 100%;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;

      li {
        width: 100%;
      }
    }
  }
}
.inner-menu {
  display: flex;
  flex-direction: row;
  display: grid;
  grid-template-columns: 30% 70%;
  padding: 2rem 0px;

  .inner-menu-item {
    width: 100%;
    display: flex;
    flex-direction: row;

    h4 {
      font-size: 18px;
      border-bottom: 1px solid #dddddd;
      padding-bottom: 1rem;
    }

    ul {
      padding-left: 0px;
      list-style: none;
      -webkit-flex-basis: 100%;
      -ms-flex-preferred-size: 100%;
      flex-basis: 100%;
      -webkit-box-flex: 0;
      -webkit-flex-grow: 0;
      -ms-flex-positive: 0;
      flex-grow: 0;
      max-width: 100%;
      padding: 0px;
      padding-right: 48px;

      a {
        padding-inline: 0px !important;
      }
    }

    &.two-coulmn {
      width: 100%;
      display: grid;
      grid-template-columns: 75% 35%;
      ul {
        padding-left: 0px;
        list-style: none;
        -webkit-flex-basis: auto;
        -ms-flex-preferred-size: auto;
        flex-basis: auto;
        -webkit-box-flex: 0;
        -webkit-flex-grow: 0;
        -ms-flex-positive: 0;
        flex-grow: 0;
        max-width: 100%;
        padding: 0px;
        padding-right: 48px;

        h4 {
          width: 100%;
        }
      }

      li {
        width: 50%;
      }

      .company-item {
        display: flex;
        flex-direction: column;
        padding-right: 0rem;

        ul {
          width: 100%;
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;

          li {
            width: 50%;
          }
        }
      }
    }
  }
  .picture-menu {
    width: 200px;

    img {
      width: 100%;
      width: 200px;
      border-radius: 15px;
      overflow: hidden;
      height: 190px;
      object-fit: cover;
    }
  }
}

// ::ng-deep {
//   .modal {
//     z-index: 999999 !important;
//   }

//   .modal-backdrop {
//     z-index: 999998 !important;
//   }
// }

.mobile-toggle {
  position: relative;
  top: 0px;
  left: 0;
  z-index: 9999;
  font-size: 28px;
  background: none;
  border: none;
}

.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1111;
}

.mobile-menu {
  position: fixed;
  top: 0;
  left: 0;
  width: 270px;
  height: 100%;
  background: #fff;
  overflow-y: auto;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  z-index: 99999;

  .submenu {
    display: flex;
    gap: 10px;
    flex-direction: column;
    li a {
      text-decoration: none;
    }
  }
}

.mobile-menu.open {
  transform: translateX(0);
}

.menu {
  list-style: none;
  padding: 20px;
  margin: 0;
}

.menu-item {
  margin-bottom: 15px;

  .arrow {
    margin-left: auto;
    img {
      width: 18px;
    }
  }
}

.menu-item > a {
  text-decoration: none;
  font-size: 16px;
  display: flex;
}

.menu-item.has-submenu > a {
  font-weight: normal;
}

.submenu {
  list-style: none;
  padding-left: 15px;
  margin-top: 10px;
}

.mobile-toggle {
  display: none;
}

@media (max-width: 768px) {
  .fc-header-wrapper {
    height: auto;
    padding-block: 1rem;
  }
  .focile-logo {
    img {
      width: 60px;
      height: auto;
    }
  }
  .categories-navbar {
    margin-top: 0px;
  }
  .mobile-toggle {
    display: flex;
    width: 36px;
    height: 36px;
    span {
      width: 100%;
      height: 100%;
      display: flex;

      img {
        width: 100%;
      }
    }
  }
  .category-navbar-item {
    overflow: auto;
    white-space: nowrap;
    justify-content: start;
    scrollbar-width: none;
    margin-top: 1rem;
    z-index: 1111;
    position: relative;
    border-bottom: 1px solid #00579338;

    li {
      width: auto;
      min-height: 38px;
      a {
        font-size: 14px;
        gap: 10px;
      }
      span {
        min-width: 24px;
        min-height: 24px;

        img {
          height: 14px;
          width: auto;
        }
      }
    }
  }
  .m-user-name {
    border: none;
    background: white;
    padding: 20px;
    display: flex;
    width: 100%;
    align-items: center;
    img {
      width: 30px;
      height: 30px;
      border-radius: 15px;
    }
  }
  .menu-item {
    a {
      color: black;
    }
  }

  .m-after-login {
    background-color: #ebebeb;

    li {
      &:last-child {
        margin-bottom: 0px;
      }
    }
  }

  .mobile-menu {
    a.login-btn {
      background: #014681;
      color: white;
      padding: 10px 1rem;
      width: calc(100% - 2rem);
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8px;
      text-decoration: none;
      margin: 1rem auto 0.5rem;
    }
    a.sign-up-btn {
      background: #014681;
      color: white;
      padding: 10px 1rem;
      width: calc(100% - 2rem);
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8px;
      text-decoration: none;
      margin: 0.5rem auto;
    }
  }
}
