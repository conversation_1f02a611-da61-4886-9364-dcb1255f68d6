import { HttpClient } from '@angular/common/http';
import {
  Component,
  ElementRef,
  HostListener,
  Input,
  OnDestroy,
  OnInit,
  Renderer2,
  ViewChild,
} from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import {
  forkJoin,
  map,
  retry,
  Subject,
  Subscription,
  takeUntil,
  tap,
} from 'rxjs';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { ExpertService } from 'src/app/modules/admin/services/expert.service';
import {
  companyType,
  DropMessageType,
  memberType,
} from 'src/app/shared/constant';
import { filterNonNull } from 'src/app/shared/oprators/filter-null-values';
import { ChatService } from 'src/app/shared/services/chat.service';
import { ModalService } from 'src/app/shared/services/modal.service';
import { UtilsService } from 'src/app/shared/services/utils.service';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
})
export class HeaderComponent implements OnInit, OnDestroy {
  @ViewChild('popover') becomeParnerButton!: ElementRef;
  @ViewChild('notificationsPopup') notificationPopup!: ElementRef;
  @Input() secondNav = true;
  accountLinks = [
    {
      text: 'My Profile',
      icon: 'fa-user-circle',
      url: 'profile-overview/',
    },
    {
      text: 'Manage Settings',
      icon: 'fa-cog',
      url: '/my-account/company-details',
    },
    {
      text: 'My Connections',
      icon: 'fa-share-alt',
      url: '/my-account/favorites',
    },
    {
      text: 'Pricing',
      icon: 'fa-share-alt',
      url: '/subscription-plans',
    },
    {
      text: 'FAQ',
      icon: 'fa-question',
      url: '/faq',
    },
  ];

  members = [
    {
      text: 'Resellers/Integrators',
      type: 'reseller',
      id: 1,
    },
    {
      text: 'Vendors',
      type: 'vendor',
      id: 3,
    },
    {
      text: 'Distributors',
      type: 'distributor',
      id: 2,
    },
    {
      text: 'Consultants',
      type: 'avconsultant',
      id: 4,
    },
  ];

  partnerResources = [
    {
      text: 'Partnership',
      url: '/partnership',
    },
    {
      text: 'Users Account',
      url: '/user-accounts-info',
    },
    {
      text: 'Connections',
      url: '/connections',
    },
    {
      text: 'Media Usage',
      url: '/media-usage',
    },
    {
      text: 'Partner Agreement',
      url: '/agreements',
    },
    {
      text: 'Pricing',
      url: '/subscription-plans',
    },
  ];

  isLoggedIn = false;
  secondHeaderOffset: number | undefined = 0;
  viewType$: string = 'reseller';
  user: any = {};
  profileImage = '';
  notifications$: any;
  isAuto = false;
  unReadNotificationCount = 0;
  companyType = companyType;
  memberType = memberType;
  isHomeRoute: boolean = false;
  subscriptions: Subscription[] = [];
  private destroy$ = new Subject<void>();

  constructor(
    public account: AccountService,
    private router: Router,
    private utils: UtilsService,
    public chatService: ChatService,
    private modalService: ModalService,
    private httpClient: HttpClient,
    private readonly toaster: ToastrService,
    private renderer: Renderer2,
    private el: ElementRef,
    private expertService: ExpertService
  ) {
    account.isLoggedIn$.subscribe((response) => {
      this.isLoggedIn = response;
    });
  }

  ngOnDestroy(): void {
    this.secondHeaderOffset = 0;
    this.subscriptions.map((x) => x?.unsubscribe());
    this.destroy$.next();
    this.destroy$.complete();
  }

  ngOnInit(): void {
    this.account.profileImage$.subscribe((profileImage: string) => {
      this.profileImage = profileImage; //this.utils.setProfileImage(profileImage);
    });
    this.account.user$.pipe(filterNonNull()).subscribe((response) => {
      if (!response) return;
      this.user = response;

      // Filter out "Pricing" URL if userType is 3
      if (this.user?.userType === 1) {
        this.accountLinks = this.accountLinks.filter(
          (link) => link.text !== 'Pricing'
        );
      }

      if (this.user?.isCompleted) {
        this.getNotfications();
      }
    });

    // this.chatService.unReadMessagesConversationsCount.subscribe((count) => {
    //   console.log({ count });
    // });

    this.chatService.onNewNotification$.subscribe((response: any) => {
      (this.notifications$ as any[]).unshift(response);
      this.unReadNotificationCount = this.notifications$.filter(
        (x: any) => !x.isRead
      )?.length;
    });

    const headerHeight = document.getElementById('mainNavBar');
    this.secondHeaderOffset = (headerHeight?.clientHeight || 0) + 30;

    this.router.events.subscribe(() => {
      this.isHomeRoute = this.router.url === '/home';
    });

    this.header = this.el.nativeElement.querySelector('.fc-header-wrapper');
  }

  getNotfications() {
    const subscription = this.account
      .getUserNotifications(this.user?.userId)
      .pipe(
        retry(3),
        tap((response: any) => {
          if (response?.data?.length) {
            this.unReadNotificationCount = response.data.filter(
              (x: { isRead: boolean }) => !x.isRead
            ).length;
          }
        }),
        map((response: any) => {
          return response?.data?.filter((x: any) => !x.isRead) || [];
        }),
        takeUntil(this.destroy$)
      )
      .subscribe((notifications) => {
        this.notifications$ = notifications;
      });
    this.subscriptions.push(subscription);
  }

  showNotification() {
    if (this.notifications$.length) {
      this.isAuto = !this.isAuto;
    }
  }

  handleAuth() {
    localStorage.removeItem('user');
    if (this.isLoggedIn) {
      this.chatService.disconnectUser(this.account.user$.getValue().userId);
      this.account.user = {};
      this.account.isLoggedIn = false;
      this.account.isLoggedIn$.next(false);
      this.account.user$.next(null);
    }

    this.router.navigate(['/home']);
    window.scroll({
      left: 0,
      top: 0,
    });
  }

  setView(viewType: string) {
    this.viewType$ = viewType;
    this.account.viewType$.next(viewType);
    this.findMemberType();
  }

  gotoUserDetails(link: any) {
    if (link.url === 'profile-overview/') {
      this.router.navigate([link.url]);
    } else {
      this.router.navigate([link.url]);
    }
  }

  openModal() {
    this.modalService.openModal('confirmation', {
      class: 'modal-sm',
      initialState: {
        message: 'Are you sure you want to Logout',
        firstButtonText: 'Yes, Logout',
        onConfirm: () => {
          this.logout();
          this.modalService.closeModal();
        },
      },
    });
    this.isMobileMenuOpen = false;
  }

  logout(): void {
    this.handleAuth();
  }

  findMemberType() {
    document
      .getElementsByClassName('dropdown-menu')
      .item(0)
      ?.classList.remove('show');
  }

  handleNotificationClick(notification: any, i: number) {
    this.account
      .markAsReadNotification(notification.id)
      .subscribe((response: any) => {
        if (response.data.item1) {
          notification.isRead = true;
          if (this.unReadNotificationCount) {
            this.unReadNotificationCount -= 1;
            if (response.messageType == DropMessageType.Info) {
              this.toaster.info(response.data.message);
            } else if (
              response.messageType == DropMessageType.Success &&
              response.data.message
            ) {
              this.toaster.success(response.data.message);
            }
          }
        } else {
          if (response.data.item2) {
            notification.isRead = true;
          }
          this.toaster.success(response.data.item3);
        }
      });
  }

  approvedConnection($event: any, notification: any, i: number) {
    $event.stopPropagation();
    this.account
      .markAsReadNotification(notification.id)
      .subscribe((response: any) => {
        if (response.data.item1) {
          notification.isRead = true;
          if (this.unReadNotificationCount) {
            this.unReadNotificationCount -= 1;
            if (notification.type === 4) {
              const payload = {
                sender: notification.requestor,
                receiver: this.user.userId,
              };

              const apis = [
                this.httpClient.post('chat/savemessage', payload),
                this.expertService.SetConnectionApproval(
                  payload.receiver,
                  payload.sender
                ),
              ];
              forkJoin(apis).subscribe((response: any) => {
                if (response.messageType == DropMessageType.Info) {
                  this.toaster.info(response.data.message);
                } else if (
                  response.messageType == DropMessageType.Success &&
                  response.data.message
                ) {
                  this.toaster.success(response.data.message);
                }
              });
            }
          }
        } else {
          if (response.data.item2) {
            notification.isRead = true;
          }
          this.toaster.info(response.data.item2);
        }
      });
  }

  private header: HTMLElement | null = null;

  @HostListener('window:scroll', [])
  onWindowScroll() {
    if (!this.header) {
      return; // Exit if the header element is not found
    }

    if (window.scrollY > 100) {
      this.renderer.addClass(this.header, 'scrolled');
    } else {
      this.renderer.removeClass(this.header, 'scrolled');
    }
  }

  isMenuHovered: boolean = false;

  toggleHover(state: boolean) {
    this.isMenuHovered = state;
  }

  isMobileMenuOpen = false;
  mobileSubmenu: string | null = null;

  toggleMobileMenu() {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
    if (!this.isMobileMenuOpen) this.mobileSubmenu = null;
  }

  toggleSubmenu(menu: string) {
    this.mobileSubmenu = this.mobileSubmenu === menu ? null : menu;
  }
}
