.fc-choose-option-wrap{
    display: grid;
    grid-template-columns: 45% 55%;
    height: 100%;        
    min-height: 100vh;
}

.fc-back-arrow{
    color: rgba(0, 0, 0, 0.5);    

    svg path{
        fill: rgba(0, 0, 0, 0.5);
    }

    span{
        display: flex;
      }
}
.join-header-row{
    margin-bottom: 2rem;
    label{
        font-family: 'Poppins';
        font-style: normal;
        font-weight: 600;
        font-size: 30px;
        line-height: 45px;
        display: flex;
        align-items: center;
        color: #000000;
    }

    p{
        font-family: 'Poppins';
        font-style: normal;
        font-weight: 400;
        font-size: 18px;
        line-height: 28px;
        display: flex;
        align-items: center;
        color: #8692A6; 
        max-width: 411px;
    }
}

.fc-card-row{
    width: 490px;
    min-height: 161px;
    background: #FFFFFF;
    box-shadow: 0px 2px 14px 1px rgba(0, 0, 0, 0.06);
    border-radius: 6px;
    display: flex;
    flex-direction: row;
    gap: 1rem;
    justify-content: start;
    align-items: center;
    padding: 25px 1rem;
    border: 1px solid transparent;
    cursor: pointer;

    label{
        font-style: normal;
        font-weight: 500;
        font-size: 16px;
        line-height: 19px;
        display: flex;
        align-items: center;
        color: #000000;
    }

    p{
        width: 100%;
        font-family: 'Poppins';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 21px;
        display: flex;
        align-items: center;
        color: #8692A6;
        margin-bottom: 0px;
    }

    .forward-arrow{
        display: none;        
    }

    &:hover{
        border-color:#014681;
        background-color: #F5F9FF;

        .forward-arrow{
            display: flex;
        }
    }
}

.fc-back-arrow{
    position: absolute;
    left: 1rem;
    top: 1rem;

    span{
        display: flex;
      }
}


.left-bar{
    padding: 2rem;
    padding: 1.5rem 3rem;
    background:url('../../../../assets/images/white-pattern.svg')#014681 no-repeat left 100%;
    background-size:100% 100%;  
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    z-index: 11;
    background-size: 100%;
}

.right-bar{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;

    span{
        display: flex;
    }
}

.text-row{
    width: calc(100% - 100px);
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.fc-testimonial-quote{
    display: flex;
    align-items: center;
    width: 100%;
 
}


@media(max-height: 600px) {
    .fc-choose-option-wrap{        
        height: 100%;
        min-height: calc(100vh + 200px);
    }
}

@media(max-width:768px){
    .fc-choose-option-wrap{
       display: flex;
       flex-direction: column;
       height: auto;
       min-height: auto;

       .left-bar{
        display: none;
       }
       .right-bar{
        align-items: start;
        padding: 1.5rem 1rem;
        padding-top: 0px;
       }
    }
    .fc-choose-user-categories{
        width: 100%;
        display: flex;
        flex-direction: column;
        padding-top: 2.5rem;        
    }
    .fc-card-row{
        width: 100%;
    }
    .fc-back-arrow{
        position: relative;
        top: 0px;
        left: 0px;
        z-index: 111;
    }
    .join-header-row{
        margin-bottom:1rem;

        p{
            font-size: 14px;
            line-height: 1.5;
        }
    }
}