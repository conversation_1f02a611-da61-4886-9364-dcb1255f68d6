import { Component } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-choose-option',
  templateUrl: './choose-option.component.html',
  styleUrls: ['./choose-option.component.scss']
})
export class ChooseOptionComponent {
  easyCards = [
    {
      image: '../../../../../assets/images/quote-icon.svg',
      title: 'Your connection matters!',
      description: `In today's digital era, reputation is everything. Every connection will
have the potential to drive success and a gateway to valuable referrals.
Embracing a positive attitude is crucial to our values.`,
    person:'Focile Inc.',
      closingImg: '../../../../../assets/images/quote-arrow.svg'
    },
    {
      image: '../../../../../assets/images/quote-icon.svg',
      title: 'Keep your connection active!',
      description: `Focile helps you find dedicated Experts in the fields of your choice! A connection platform that simplifies direct and instant communication with your source of expertise.`,
      person:'Focile Inc.',
      closingImg: '../../../../../assets/images/quote-arrow.svg'
    },
    {
      image: '../../../../../assets/images/quote-icon.svg',
      title: 'Forge an impactful relationship!',
      description: `Focile is set to revolutionize connections between individuals and experts by creating meaningful relationships that transcend borders and transform interactions into enriching social experiences.`,
      person:'Focile Inc.',
      closingImg: '../../../../../assets/images/quote-arrow.svg'
    },
  ];

  constructor(private router: Router) {}

  chooseOption(userType: number) {
    this.router.navigate(['/register'], { queryParams: { userType } });
  }

}