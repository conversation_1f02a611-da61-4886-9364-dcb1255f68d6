import {
  Component,
  EventEmitter,
  Input,
  Output,
  SimpleChanges,
} from '@angular/core';

@Component({
  selector: 'app-password-rules',
  templateUrl: './password-rules.component.html',
})
export class PasswordRulesComponent {
  @Input() password = '';
  @Output() isValid = new EventEmitter();
  rules = [
    {
      rule: /\p{Lu}/u,
      ruleText: `Your password should include at least one uppercase letter (A-Z).`,
      valid: false,
      title: 'Uppercase Letter',
    },
    {
      rule: /^.{12,}$/,
      ruleText: 'Your password Length should be 12 Characters minimum (A-Z).',
      valid: false,
      title: 'Minimum Length',
    },
    {
      rule: /[^a-zA-Z0-9_]/,
      ruleText: `Your password should include at least one special character (e.g., @, $, !, %, *, ?, #, ^, etc.). Special characters add complexity to the password.`,
      valid: false,
      title: 'Special Character',
    },
    {
      rule: /\d/,
      ruleText: `Your password should include at least one digit (0-9).`,
      valid: false,
      title: 'Digit',
    },
  ];

  ngOnChanges(changes: SimpleChanges): void {
    this.updateRulesState();
  }

  updateRulesState() {
    const password = this.password;
    this.rules.forEach((rule) => {
      const regex = rule.rule;
      rule.valid = regex.test(password);
    });
    this.isValid.emit(this.rules.find((x) => !x.valid)?.ruleText);
  }
}
