import { Component, OnInit } from '@angular/core';
import { AccountService } from '../services/account.service';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { Regex } from 'src/app/shared/constant';
import { finalize } from 'rxjs';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-forgot-password',
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.scss'],
})
export class ForgotPasswordComponent implements OnInit {
  loading = false;
  isEmailSended = false;
  togglePasswordText = false;
  toggleConfirmPasswordText = false;
  email: string = '';
  regex = Regex;
  token = '';
  password = '';
  resetPasswordForm!: FormGroup;
  recoveryForm!: FormGroup;
  showConfirmPassword!: boolean;
  showPassword!: boolean;
  showStrongPasswordPopup = false;
  constructor(
    private readonly account: AccountService,
    private readonly toaster: ToastrService,
    private readonly activate: ActivatedRoute,
    private readonly router: Router,
    private readonly formBuilder: FormBuilder
  ) {}

  ngOnInit(): void {
    this.initRecoveryForm();
    this.initForm();
    this.activate.queryParams.subscribe((response: any) => {
      if (response.email && response.token) {
        this.email = response.email;
        this.token = response.token;
        this.resetPasswordForm.patchValue({
          email: response.email,
          token: response.token,
        });
        this.isEmailSended = true;
      }
    });
  }

  initForm() {
    this.resetPasswordForm = this.formBuilder.group({
      email: null,
      token: null,
      password: [
        null,
        [Validators.required, Validators.pattern(Regex.strongPassword)],
      ],
      confirmPassword: [null, [Validators.required]],
    });
  }

  initRecoveryForm() {
    this.recoveryForm = this.formBuilder.group({
      email: [null, [Validators.required, Validators.email]],
    });
  }

  handleRecoveryClick($event: any) {
    this.loading = !this.loading;
    this.account
      .getRecoveryMail(this.recoveryForm.value.email)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((response: any) => {
        if (!response.messageType) {
          this.toaster.success(
            '',
            `We've sent an email to ${this.recoveryForm.value.email}, please check your email inbox.`
          );
          this.recoveryForm.reset();
        } else {
          this.toaster.error('', response.message);
        }
      });
  }

  togglePassword(type = 'password') {
    if (type == 'password') {
      this.togglePasswordText = !this.togglePasswordText;
    } else {
      this.toggleConfirmPasswordText = !this.toggleConfirmPasswordText;
    }
  }

  recoverPassword() {
    const payload = this.resetPasswordForm.value;
    this.loading = true;
    this.account
      .setNewPassword(payload)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((response: any) => {
        if (response.messageType) {
          return this.toaster.error('', response.message);
        }
        this.toaster.success('','Password reset successfully.')
        this.router.navigate(['/account']);
        return;
      });
  }

  handleIconClick(type: string) {
    if (type === 'confirmPassword') {
      this.showConfirmPassword = !this.showConfirmPassword;
    } else {
      this.showPassword = !this.showPassword;
    }
  }
}
