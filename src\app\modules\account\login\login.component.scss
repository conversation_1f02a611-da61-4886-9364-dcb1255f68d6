.input-group-icon {
  position: relative;
}

.input-group-icon .input-group-prepend {
  position: absolute;
  right: 0;
  z-index: 1;
}

.input-group-icon .input-group-text {
  background-color: transparent;
  border-color: #ccc;
  color: #777;
}


.sign-in-wrapper{
  background: url('../../../../assets/images/blue-connecting-pattern.svg') #F5F5F5 no-repeat left 118%;
  background-position: 100% calc(100% + 135px);
  display: flex;
  align-items: start;
  background-size: 100% 124%;
}

.sign-box{
  width: 646px;
  height: auto;
  box-sizing: border-box;
  background: #FFFFFF;
  border: 1px solid rgba(102, 102, 102, 0.3);
  border-radius: 24px;
  padding: 56px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin:60px auto 60px;  

  img{
    max-width:120px;
  }

  p{
    color:#333333;
    font-size: 1rem;
    line-height: 30px;
    margin-bottom: 40px;
  }

  form{

    label{
      color: #666666;
      font-size: 1rem;
      font-weight: 400;
    }
    ::ng-deep input:not([type="checkbox"]) {
      box-sizing: border-box;
      width: 100%;        
      border: 1px solid rgba(102, 102, 102, 0.35);
      border-radius: 12px;
      flex: none;
      order: 1;
      align-self: stretch;
      flex-grow: 0;  
      padding-inline: 1rem;
    
      &::placeholder {
        color: rgba(102, 102, 102, 0.60);
      }
    }
    .password-hint{
      color: #666666;
      margin-top: 0px;
      display: flex;
      font-size: 14px;
      max-width: 60%;
    }
    .forgot-text{
      text-decoration: none;
    }
  }

  .recover-label{
    margin-right: auto;
    text-align: right;
    display: flex;
 
    label{
    font-weight: 400;
    color: #333333;
    }
  }
  }

  .fc-dont-account-link{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-top:10px;

    span{
      color: rgba(17, 17, 17, 0.6);
    }
    .create-new{
      text-decoration: underline;
      color: #111111;
      margin-left: 5px;
    }

    ::ng-deep .submit-btn {
      padding: 1rem;
      background: #014681;
      border-radius: 32px;
      font-size: 22px;
      min-width: 140px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight:600 !important;
      min-height: 54px;
    }
  }

  .form-txt{
    position: relative;
  }

  .hide-show-tag{
    color: #333333;
    font-size: 16px;
  }

  @media(max-width:768px){
    .sign-box{
      margin: 0px;
      border-radius: 0px;
      width: 100%;
      padding: 1.5rem;

      p{
        font-size: 14px;
        margin-bottom: 1.5rem;
        line-height: 1.5;
      }
    }
    .fc-dont-account-link{
      flex-direction: column;
      gap: 1rem;
    }

    ::ng-deep{
      .submit-btn{
        width: 100% !important;
      }
    }
    ::ng-deep focile-button{
      width: 100% !important;
    }
  }