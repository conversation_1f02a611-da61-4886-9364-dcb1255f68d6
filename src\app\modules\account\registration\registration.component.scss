input[type=radio]{
  transform:scale(1.5);
}
.required {
  color: var(--bs-red);
}


.text-fw-800 {
  font-weight: 800 !important;
}

.form-control{
  box-sizing: border-box;
  height: 56px;
  border: 1px solid rgba(102, 102, 102, 0.35);
  border-radius: 12px;
  /* Inside auto layout */
  flex: none;
  order: 1;
  align-self: stretch;
  flex-grow: 0;
}

.form-label{
  color: #666666;
  font-weight: 400;
  font-size: 1rem;
}
.text-danger{
  margin-top: 5px;
  font-size: 14px;
}

::ng-deep .sign-up-btn {
  width: 240px;
  height:60px;
  background: #014681;
  border-radius: 32px;
  flex: none;
  order: 1;
  flex-grow: 0;
}

.fc-back-arrow{
  color: rgba(0, 0, 0, 0.5);

  span{
    display: flex;
  }
}

::ng-deep .form-check-input:checked{
  background-color:black;
  border-color: black;
}

.light-blue {
  ::ng-deep {
    .fc-slider-bar {
      background: url('../../../../assets/images/white-pattern.svg')#B6DDFE no-repeat left 100%;
    }
    .fc-easy-card div p, .fc-easy-card div h4, .fc-easy-card div p, .fc-easy-card div h5 {
      color:black !important;
    }
    .full-width .owl-theme .owl-dots .owl-dot span{
      background-color: #014681;    
    }
    .full-width[_ngcontent-cys-c39] .owl-theme .owl-dots .owl-dot:hover span{
      background-color: #014581b9;
    }
    svg{
      fill: #004681;
    }
  }
}

@media(max-width:768px){
  .fc-dont-account-link{
    flex-direction: column;
    gap: 1rem;
  }

  ::ng-deep .sign-up-btn{
    height: 45px;
    width: 100%;
  }
  ::ng-deep focile-button{
    width: 100% !important;
  }
  .fc-back-arrow{
    position: relative;
    top: 0px;
    left: 0px;
    z-index: 111;    
    margin-bottom: 0px;
}

.light-blue{
  p{
    font-size: 14px;
    line-height: 1.5;
  }
}
.dynamic-text{
  span{
    font-size: 1.25rem !important;
  }
}
}


