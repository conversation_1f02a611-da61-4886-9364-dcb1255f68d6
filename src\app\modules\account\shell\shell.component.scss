$original-color: #014681;
$lightened-color: lighten($original-color, 60%);

.login-shell {
  min-height: 100vh !important;
  width: 100%;
  display: grid;
  grid-template-columns: 45% 55%;

   .second-column {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    align-self: start;
    height: 100%;
    overflow-y: auto;
    order: 1;
  }
}

.fc-account-logo{
  z-index: 1;
  position: relative;
}
.fc-slider-bar{
  padding: 1.5rem 3rem;
  background:url('../../../../assets/images/white-pattern.svg')#014681 no-repeat left 100%;
  background-size:100% 100%;  
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 11;
  background-size: 100%;
}

@media (max-width: 900px) {
  .container-bg,
  .login-shell:nth-child(2),
  .second-column {
    display: none !important;
  }

  .login-shell {
    grid-template-columns: 1fr;
  }
}
@media(max-width:768px){
  .fc-slider-bar{
    display: none;
  }
}