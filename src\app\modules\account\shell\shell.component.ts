import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { filter } from 'rxjs';

@Component({
  selector: 'app-account-shell',
  templateUrl: './shell.component.html',
  styleUrls: ['./shell.component.scss'],
})
export class ShellComponent implements OnInit {
  filteredCards: any[] = [];
  easyCards = [
    {
      userType: '2', // Individuals and Business Members
      image: '../../../../../assets/images/quote-icon.svg',
      title: 'Stay ahead of the curve',
      description: `Be the first to join as a channel partner and explore the potential
of your e-Card brand and get an instant connection by end user businesses
looking for sales inquiries and long term relations!.`,
      person:'Focile Inc.',
      closingImg: '../../../../../assets/images/quote-arrow.svg',
    },
    {
      userType: '2', // Individuals and Business Members
      image: '../../../../../assets/images/quote-icon.svg',
      title: 'Maintaining your connections',
      description: `Valuable resources and expert connections are key to successful
businesses and long term relationships. Businesses are thriving
to rely on good and wise connections, great expertise, on-time response, and resulting in great customer experience.`,
      person:'Focile Inc.',
      closingImg: '../../../../../assets/images/quote-arrow.svg',
    },
    {
      userType: '2', // Individuals and Business Members
      image: '../../../../../assets/images/quote-icon.svg',
      title: 'Collaboration is effortless!',
      description: `Harness the power of collaboration with Focile, your ultimate platform for success. We are dedicated to making the impossible possible for every member.`,
      person:'Focile Inc.',
      closingImg: '../../../../../assets/images/quote-arrow.svg',
    },
    {
      userType: '1', // Expert testimonials
      image: '../../../../../assets/images/quote-icon.svg',
      title: 'Keep your connection active!',
      description: `Focile helps you find dedicated Experts in the fields of your choice! A connection platform that simplifies direct and instant communication with your source of expertise.`,
      person:'Focile Inc.',
      closingImg: '../../../../../assets/images/quote-arrow.svg',
    },
    {
      userType: '1', // Expert testimonials
      image: '../../../../../assets/images/quote-icon.svg',
      title: 'Build your circle of connections!',
      description: `Favorite list of circle close connections for easy outreach, and build wise connections with a diversity of network individuals and experts.`,
      person:'Focile Inc.',
      closingImg: '../../../../../assets/images/quote-arrow.svg',
    },
    {
      userType: '1', // Expert testimonials
      image: '../../../../../assets/images/quote-icon.svg',
      title: 'Your connection matters!',
      description: `Discover your dedicated partner or expert with our user-friendly landing webpage. Connect with local and non-local resellers, consultant effortlessly.`,
      person:'Focile Inc.',
      closingImg: '../../../../../assets/images/quote-arrow.svg',
    },
    {
      userType: undefined, // Expert testimonials
      image: '',
      title: '',
      description: `We prioritize the security of your account and connections. Our continuous data monitoring  
 ensures trust and reliability across all devices. Your safety is our top priority!
`,
      person:'',
      closingImg: '',
    }
  ];

  constructor(private route: ActivatedRoute, private router: Router) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      const userType = params['userType'];
      this.filterCards(userType);
    });

    
    // Listen for query parameter changes
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        this.route.queryParams.subscribe((params) => {
          const userType = params['userType'];
          this.filterCards(userType);
        });
      });
  }

  filterCards(userType: string): void {
    console.log('userType:', userType); // Debugging: Log the userType value
    this.filteredCards = this.easyCards.filter((card) => card.userType === userType);
    console.log('filteredCards:', this.filteredCards); // Debugging: Log the filtered cards
  }
}