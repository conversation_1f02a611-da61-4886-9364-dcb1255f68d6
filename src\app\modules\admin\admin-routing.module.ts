import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { HomeComponent } from './home/<USER>';
import { AdminShellComponent } from './admin-shell/admin-shell.component';
import { ServicesComponent } from './services/services.component';
import { TechnologiesComponent } from './technologies/technologies.component';
import { IndustriesComponent } from './industries/industries.component';
import { RolesComponent } from './roles/roles.component';
import { ProductsComponent } from './products/products.component';
import { SolutionsComponent } from './solutions/solutions.component';
import { ExpertsComponent } from './experts/experts.component';
import { TypeOfExpertsComponent } from './type-of-experts/type-of-experts.component';
import { PendingApprovalsComponent } from './pending-approvals/pending-approvals.component';
import { VendorsComponent } from './vendors/vendors.component';
import { DistributorsComponent } from './distributors/distributors.component';
import { ManagePlansComponent } from './manage-plans/manage-plans.component';
import { ElevatorApprovalsComponent } from './elevator-approvals/elevator-approvals.component';
import { ExpertiseComponent } from './expertise/expertise.component';
import { ConsultantsComponent } from './consultants/consultants.component';
import { EndUsersComponent } from './end-users/end-users.component';
import { ManageFaqComponent } from './manage-faq/manage-faq.component';
import { ManageTestimonalComponent } from 'src/app/shared/modals/manage-testimonal/manage-testimonal.component';
import { ManageBlogsComponent } from './manage-blogs/manage-blogs.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'services',
    pathMatch: 'full',
  },
  {
    path: '',
    component: AdminShellComponent,
    children: [
      {
        path: '',
        component: HomeComponent,
      },
      {
        path: 'services',
        component: ServicesComponent,
      },
      {
        path: 'experts',
        component: ExpertsComponent,
      },
      {
        path: 'technologies',
        component: TechnologiesComponent,
      },
      {
        path: 'industries',
        component: IndustriesComponent,
      },
      {
        path: 'roles',
        component: RolesComponent,
      },
      {
        path: 'products',
        component: ProductsComponent,
      },
      {
        path: 'solutions',
        component: SolutionsComponent,
      },
      {
        path: 'expert',
        component: ExpertsComponent,
      },
      {
        path: 'type-of-expert',
        component: TypeOfExpertsComponent,
      },
      {
        path: 'pending-approvals',
        component: PendingApprovalsComponent,
      },
      {
        path: 'elevator-approvals',
        component: ElevatorApprovalsComponent,
      },
      {
        path: 'vendors',
        component: VendorsComponent,
      },
      {
        path: 'distributors',
        component: DistributorsComponent,
      },
      {
        path: 'manage-plans',
        component: ManagePlansComponent,
      },
      {
        path: 'manage-expertise',
        component: ExpertiseComponent,
      },
      {
        path: 'consultants',
        component: ConsultantsComponent,
      },
      {
        path: 'end-users',
        component: EndUsersComponent,
      },
      {
        path: 'manage-faq',
        component: ManageFaqComponent,
      },
      {
        path: 'manage-testimonials',
        component: ManageTestimonalComponent,
      },
      {
        path: 'manage-blogs',
        component: ManageBlogsComponent,
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AdminRoutingModule {}
