<div class="d-flex" id="sidebarMenu">
  <div class="d-flex flex-column flex-shrink-0 p-3 text-white bg-dark">
    <a
      class="d-flex flex-column align-items-center mb-3 mb-md-0 text-white text-decoration-none"
    >
      <img src="./assets/svgs/focile-white.svg" />
      <span class="fs-3 mt-3">Focile Admin</span>
    </a>
    <hr />
    <ul class="nav nav-pills flex-column mb-auto">
      <li *ngFor="let item of navigationItems">
        <a
          routerLinkActive="active"
          routerLink="{{ item.routerLink }}"
          class="nav-link text-white"
          aria-current="page"
          href="#"
        >
          <svg class="bi me-2" width="16" height="16">
            <use xlink:href="#home"></use>
          </svg>
          {{ item.label }}
        </a>
      </li>
    </ul>
    <hr />
    <div class="dropdown">
      <a
        href="#"
        class="d-flex align-items-center text-white text-decoration-none dropdown-toggle"
        id="dropdownUser1"
        data-bs-toggle="dropdown"
        aria-expanded="false"
      >
        <img
          src="./assets/svgs/focile.svg"
          width="32"
          height="32"
          class="rounded-circle me-2"
        />
        <strong>Focile Admin</strong>
      </a>
      <ul class="dropdown-menu dropdown-menu-dark text-small shadow">
        <li>
          <a class="dropdown-item" (click)="adminSignOut()">Sign out</a>
        </li>
      </ul>
    </div>
  </div>
  <main class="px-3 d-flex pt-3 flex-column">
    <router-outlet></router-outlet>
  </main>
</div>
