import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { AccountService } from '../../account/services/account.service';

@Component({
  selector: 'app-admin-shell',
  templateUrl: './admin-shell.component.html',
  styleUrls: ['./admin-shell.component.scss'],
})
export class AdminShellComponent {
  constructor(private router: Router, private account: AccountService) {}

  navigationItems = [
    // { label: 'Type Of Experts', routerLink: 'type-of-expert', icon: '' },
    { label: 'Solutions', routerLink: 'solutions', icon: 'fas fa-toolbox' },
    { label: 'Roles', routerLink: 'roles', icon: 'fas fa-user-tag' },
    { label: 'Products', routerLink: 'products', icon: 'fab fa-product-hunt' },
    { label: 'Services', routerLink: 'services', icon: 'fab fa-servicestack' },
    { label: 'Industries', routerLink: 'industries', icon: 'fas fa-industry' },
    // { label: 'Technologies', routerLink: 'technologies', icon: 'layers' },
    { label: 'Reseller', routerLink: 'experts', icon: 'fas fa-share-alt' },
    { label: 'Vendors', routerLink: 'vendors', icon: 'fas fa-truck-moving' },
    {
      label: 'Distributors',
      routerLink: 'distributors',
      icon: 'fas fa-people-arrows',
    },
    {
      label: 'Consultants',
      routerLink: 'consultants',
      icon: 'fas fa-user-nurse',
    },
    { label: 'End Users', routerLink: 'end-users', icon: 'fas fa-user' },
    {
      label: 'Pending Approvals',
      routerLink: 'pending-approvals',
      icon: 'fas fa-thumbs-up',
    },
    {
      label: 'Elevator Approvals',
      routerLink: 'elevator-approvals',
      icon: 'fas fa-video',
    },
    {
      label: 'Plans',
      routerLink: 'manage-plans',
      icon: 'fas fa-link',
    },
    {
      label: 'Expertise',
      routerLink: 'manage-expertise',
      icon: 'fab fa-buromobelexperte',
    },
    {
      label: 'FAQs',
      routerLink: 'manage-faq',
      icon: 'layers',
    },
    {
      label: 'Testimonials',
      routerLink: 'manage-testimonials',
      icon: 'layers',
    },
    {
      label: 'Blogs',
      routerLink: 'manage-blogs',
      icon: 'layers',
    },
  ];

  adminSignOut() {
    localStorage.clear();
    this.account.user = {};
    this.account.isLoggedIn = false;
    this.account.isLoggedIn$.next(false);
    this.account.user$.next(null);
    this.router.navigate(['/home']);
  }
}
