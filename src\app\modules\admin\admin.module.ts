import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { AdminRoutingModule } from './admin-routing.module';
import { HomeComponent } from './home/<USER>';
import { AdminShellComponent } from './admin-shell/admin-shell.component';
import { RouterModule } from '@angular/router';
import { ServicesComponent } from './services/services.component';
import { SitesComponent } from './sites/sites.component';
import { ExpertsComponent } from './experts/experts.component';
import { SolutionsComponent } from './solutions/solutions.component';
import { RolesComponent } from './roles/roles.component';
import { ProductsComponent } from './products/products.component';
import { IndustriesComponent } from './industries/industries.component';
import { TechnologiesComponent } from './technologies/technologies.component';
import { HttpClientModule } from '@angular/common/http';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { StatusPipe } from './pipes/status.pipe';
import { ToastrModule } from 'ngx-toastr';
import { TypeOfExpertsComponent } from './type-of-experts/type-of-experts.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { BsDropdownModule } from 'ngx-bootstrap/dropdown';
import { VendorsComponent } from './vendors/vendors.component';
import { DistributorsComponent } from './distributors/distributors.component';
import { PendingApprovalsComponent } from './pending-approvals/pending-approvals.component';
import { TableComponent } from 'src/app/shared/components/table/table.component';
import { ManagePlansComponent } from './manage-plans/manage-plans.component';
import { ElevatorApprovalsComponent } from './elevator-approvals/elevator-approvals.component';
import { ExpertiseComponent } from './expertise/expertise.component';
import { ConsultantsComponent } from './consultants/consultants.component';
import { SearchPipe } from 'src/app/shared/pipes/search.pipe';
import { EndUsersComponent } from './end-users/end-users.component';
import { ManageBlogsComponent } from './manage-blogs/manage-blogs.component';
import { AddEditBlogCategoryComponent } from 'src/app/shared/modals/add-edit-blog-category/add-edit-blog-category.component';
import { AddEditBlogComponent } from './add-edit-blog/add-edit-blog.component';
import { TinyMCEditorComponent } from 'src/app/shared/components/editor/editor.component';
import { BlogPreviewComponent } from './blog-preview/blog-preview.component';

@NgModule({
  declarations: [
    HomeComponent,
    AdminShellComponent,
    ServicesComponent,
    SitesComponent,
    ExpertsComponent,
    SolutionsComponent,
    RolesComponent,
    ProductsComponent,
    IndustriesComponent,
    TechnologiesComponent,
    StatusPipe,
    TypeOfExpertsComponent,
    VendorsComponent,
    DistributorsComponent,
    PendingApprovalsComponent,
    TableComponent,
    ManagePlansComponent,
    ElevatorApprovalsComponent,
    ExpertiseComponent,
    ConsultantsComponent,
    SearchPipe,
    EndUsersComponent,
    ManageBlogsComponent,
    AddEditBlogCategoryComponent,
    AddEditBlogComponent,
    BlogPreviewComponent
  ],
  imports: [
    CommonModule,
    RouterModule,
    AdminRoutingModule,
    HttpClientModule,
    ReactiveFormsModule,
    FormsModule,
    SharedModule,
    BsDropdownModule,
    SharedModule,
  ],
  exports: [RouterModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class AdminModule {}
