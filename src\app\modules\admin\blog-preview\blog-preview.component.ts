import { Component, Input, type OnInit } from '@angular/core';
import { ModalService } from 'src/app/shared/services/modal.service';

@Component({
  selector: 'app-blog-preview',
  templateUrl: './blog-preview.component.html',
  styleUrls: ['./blog-preview.component.scss'],
})
export class BlogPreviewComponent implements OnInit {

  @Input() blog: any;

  constructor(private modalService: ModalService) { }
  ngOnInit(): void { }

  handleCancel() {
    // Logic to handle cancel action, e.g., close the modal
    this.modalService.closeModal();
  }

handleOnEdit() {
    this.modalService.closeModal(); 
    this.modalService.openModal('add-edit-blog', {
      class: 'modal-xl',
      initialState: {
        blog: this.blog,
        onEditSuccess: (updatedBlog: any) => {
          this.blog.content = updatedBlog.content;
          this.blog.title = updatedBlog.title;
          this.blog.subTitle = updatedBlog.subTitle;
          this.blog.categoryId = updatedBlog.categoryId;
          this.blog.slug = updatedBlog.slug;
          this.blog.id = updatedBlog.id;
        },
      },
    });
  }
}
