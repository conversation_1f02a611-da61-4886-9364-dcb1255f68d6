<div class="row mb-3">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card">
      <div class="card-body">
        <h4 class="card-title">End Users</h4>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card">
      <div class="card-body">
        <h4 class="card-title">
          <div class="mb-3">
            <input
              type="search"
              class="form-control"
              id="search"
              [(ngModel)]="searchTerm"
              placeholder="Search..."
            />
          </div>
        </h4>
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>#</th>
                <th>Unique Identification</th>
                <th>First Name</th>
                <th>Last Name</th>
                <th>Email</th>
                <th>Organization</th>
                <th>Address</th>
                <th>Contact</th>
                <!-- <th>Created At</th> -->
              </tr>
            </thead>
            <tbody>
              <ng-container *ngIf="!dataLoading">
                <tr
                  *ngFor="
                    let item of endUsers
                      | search : searchTerm : 'FirstName' : 'LastName' : 'email'
                  "
                >
                  <td>
                    {{ item.id }}
                  </td>
                  <td>
                    {{ item.aspNetId }}
                  </td>
                  <td>
                    {{ item.firstName }}
                  </td>
                  <td>
                    {{ item.lastName }}
                  </td>
                  <td>
                    {{ item?.email }}
                  </td>
                  <td>
                    {{ item.organizationName }}
                  </td>
                  <td>
                    {{ item.address || "Not available" }} {{ item.cityName }}
                    {{ item.stateName }} {{ item.countryName }}
                  </td>
                  <td>{{ item.mobileCountryCode }} {{ item.phoneNumber }}</td>
                  <!-- <td>
                    {{ item.phoneNumber | date }}
                  </td> -->
                </tr>
              </ng-container>
              <tr *ngIf="dataLoading">
                <td colspan="7" class="text-center">data loading</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
