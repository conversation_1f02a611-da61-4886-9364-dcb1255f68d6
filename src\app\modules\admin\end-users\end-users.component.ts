import { HttpClient } from '@angular/common/http';
import { Component, type OnInit } from '@angular/core';
import { finalize, map, pipe } from 'rxjs';

@Component({
  selector: 'app-end-users',
  templateUrl: './end-users.component.html',
  styleUrls: ['./end-users.component.scss'],
})
export class EndUsersComponent implements OnInit {
  searchTerm = '';
  dataLoading = false;
  endUsers = [];

  constructor(private http: HttpClient) {}
  ngOnInit(): void {
    this.getUsers();
  }

  getUsers() {
    this.dataLoading = true;
    this.http.get('EndUser/GetAll').pipe(
      finalize(() => (this.dataLoading = false)),
      map((response: any) => response['data'])
    ).subscribe(response => {
      this.endUsers = response;
    });
  }
}
