<div class="card">
  <div class="card-header">
    <span class="fs-2 fw-bolder">Expertise</span>
  </div>
  <div class="card-body">
    <div class="form-group">
      <label for="expertiseName" class="mb-2">Expertise Name</label>
      <input
        type="text"
        class="form-control"
        id="expertiseName"
        name="expertiseName"
        placeholder="Enter the expetise name"
        [(ngModel)]="expertiseName"
        #expertiseNameElem
      />
    </div>
  </div>
  <div class="card-footer">
    <button
      class="btn btn-primary float-end"
      (click)="addExpertise()"
      [disabled]="!expertiseName"
    >
      {{ expertiseId ? "Edit" : "Add" }} Expertise
    </button>
  </div>
</div>

<div class="card mt-3">
  <div class="card-body">
    <div class="form-group mb-3">
      <input
        type="search"
        name="search"
        [(ngModel)]="searchTerm"
        class="form-control"
        id="search"
        placeholder="Search Expertise"
      />
    </div>
    <div class="table-responsive">
      <table class="table table-centered table-nowrap mb-0 rounded">
        <thead class="thead-light">
          <tr>
            <th scope="col">#</th>
            <th scope="col">Expetise</th>
            <th scope="col">Action</th>
          </tr>
        </thead>
        <tbody>
          <tr
            *ngFor="
              let item of expertise | filterBy : searchTerm;
              let i = index
            "
          >
            <td scope="col">{{ i + 1 }}</td>
            <td scope="col">{{ item.name }}</td>
            <td scope="col">
              <button
                tooltip="Edit"
                (click)="editExpertise(item)"
                class="btn btn-sm btn-primary"
              >
                <i class="fa fa-solid fa-pen"></i>
              </button>
              <button
                tooltip="Delete"
                (click)="deleteConfirmation(item, i)"
                class="btn btn-sm btn-danger ms-2"
              >
                <i class="fa fa-solid fa-trash"></i>
              </button>
            </td>
          </tr>
          <tr *ngIf="loading || !(expertise | filterBy : searchTerm).length">
            <td colspan="4" class="text-center">
              {{ loading ? "Loading..." : "No Records Found" }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
