import { HttpClient } from '@angular/common/http';
import { Component, ElementRef, ViewChild, type OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { finalize, map } from 'rxjs';
import { ConfirmationComponent } from 'src/app/shared/modals/confirmation/confirmation.component';
import { ModalService } from 'src/app/shared/services/modal.service';

@Component({
  selector: 'app-expertise',
  templateUrl: './expertise.component.html',
  styleUrls: ['./expertise.component.css'],
})
export class ExpertiseComponent implements OnInit {
  @ViewChild('expertiseNameElem') expertiseNameEl!: ElementRef;
  loading = false;
  expertise: any[] = [];
  expertiseName: any;
  expertiseId: any;
  saving = false;
  searchTerm:any;
  constructor(
    private http: HttpClient,
    private readonly toaster: ToastrService,
    private readonly modalService: ModalService
  ) {}
  ngOnInit(): void {
    this.getExpertise();
  }

  getExpertise() {
    this.loading = true;
    this.http
      .get('Expertise/GetAll')
      .pipe(
        map((response: any) => {
          return response.data;
        }),
        finalize(() => (this.loading = false))
      )
      .subscribe((response) => {
        this.expertise = response;
      });
  }

  addExpertise() {
    const expertisPayload: any = {
      name: this.expertiseName,
      isActive: true,
    };
    if (this.expertiseId) {
      expertisPayload.id = this.expertiseId;
    }
    this.saving = true;
    this.http
      .post('Expertise/AddUpdate', expertisPayload)
      .pipe(finalize(() => (this.saving = false)))
      .subscribe((response: any) => {
        if (response.error) {
          this.toaster.error(response.message);
        } else {
          if (this.expertiseId) {
            this.expertise.find((x) => x.id == this.expertiseId).name =
              this.expertiseName;
          } else {
            this.toaster.success('Expertise added');
            this.getExpertise();
          }
          this.expertiseId = null;
          this.expertiseName = null;
        }
      });
  }

  editExpertise(item: any) {
    this.expertiseName = item.name;
    this.expertiseId = item.id;
    this.expertiseNameEl.nativeElement.focus();
  }

  deleteConfirmation(item: any, index: number) {
    this.modalService.openModal('confirmation', {
      initialState: {
        message: `Are you sure you want to delete ${item.name}?`,
        onConfirm: () => {
          this.deleteExpertise(item, index);
        },
      },
    });
  }

  deleteExpertise(item: any, index: number) {
    this.http
      .delete(`Expertise/Delete?id=${item.id}`)
      .subscribe((response: any) => {
        if (response.error) {
          this.toaster.error(response.error);
        } else {
          this.expertise = this.expertise.filter((x) => x.id !== item.id);
          this.toaster.success(`${item.name} removed successfully`);
        }
        this.modalService.closeModal();
      });
  }
}
