<div class="row">
  <div class="col-md-10">
    <h2>Industries</h2>
  </div>
</div>

<form>
  <div class="card shadow rounded">
    <div class="card-header">
      <h3>Add Industry</h3>
    </div>
    <div class="card-body">
      <div class="form-group">
        <label for="solutionName" class="mb-2">Industry Name</label>
        <input
          type="text"
          class="form-control"
          id="solutionName"
          [(ngModel)]="industryName"
          name="solutionName"
          placeholder="Enter the Industry name"
        />
      </div>
    </div>
    <div class="card-footer d-flex justify-content-end">
      <focile-button
        [btnType]="'primary'"
        [loading]="loading"
        [disabled]="!industryName"
        (onClick)="generateIndustry()"
        type="submit"
        >Add Industry</focile-button
      >
    </div>
  </div>
</form>

<div class="card shadow my-3">
  <div class="card-body">
    <div class="table-responsive">
      <table class="table table-centered table-nowrap mb-0 rounded">
        <thead class="thead-light">
          <tr>
            <th scope="col">#</th>
            <th scope="col">Industry Name</th>
            <th scope="col">Is Active</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of products; let i = index">
            <td>
              {{ i + 1 }}
            </td>
            <td>
              {{ item.name }}
            </td>
            <td>
              <button (click)="edit(item)" class="btn btn-sm btn-primary">
                <i class="fa fa-solid fa-pen"></i>
              </button>
              <button
                (click)="deleteConfirmation(template, item)"
                class="btn btn-sm btn-danger ms-2"
              >
                <i class="fa fa-solid fa-trash"></i>
              </button>
            </td>
          </tr>
          <tr *ngIf="isLoading || !products.length">
            <td colspan="3" class="text-center">
              {{ isLoading ? "Loading..." : "No Records Found" }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

<ng-template #template>
  <div class="modal-header">
    <h4 class="modal-title">Confirmation</h4>
  </div>
  <div class="modal-body">Are you sure you want to perform this action?</div>
  <div class="modal-footer">
    <button class="btn btn-secondary" (click)="modalRef?.hide()">Cancel</button>
    <button class="btn btn-primary" (click)="deleteRole()">Confirm</button>
  </div>
</ng-template>
