<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body  ">
                <div class="d-flex flex-direction-row gap-2">
                    <div tooltip="work in progress">
                        <button class="btn btn-primary" (click)="handleOnAddBlog()">Add Blog</button>
                    </div>
                    <div>
                        <button class="btn btn-primary" tooltip="Add / Edit Blog Categorise" (click)="addCategory()">Add
                            Category</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row py-3">
    <div class="col-md-12 pull-right mb-3">
        <button (click)="handleRefresh()" class="btn btn-outline-primary">Load Blogs Or Refresh</button>
    </div>
    <span *ngIf="loading">Loading...</span>
    <ng-container *ngIf="!loading">
        <div class="col-md-3 mb-3" *ngFor="let item of blogs">
            <div class="card" style="width: 18rem;">
                <div class="card-body">
                    <img *ngIf="item.imageUrl" src="{{ item.imageUrl }}" alt="{{ item.title }}" class="img-fluid "/>
                    <h5 class="card-title">{{ item.title }}</h5>
                    <h3 class="card-title">{{ item.subTitle }}</h3>
                    <span *ngFor="let tag of item.tags" class="badge bg-secondary ms-2">{{ tag }}</span>
                    <p>Created At {{ item.createdAt | date: 'mediumDate' }}</p>
                    <button class="btn btn-primary" (click)="handleonPreview(item)">Preview</button>
                    <button class="btn btn-primary ms-2" (click)="handleonEdit(item)">Edit</button>
                </div>
            </div>
        </div>
    </ng-container>
</div>