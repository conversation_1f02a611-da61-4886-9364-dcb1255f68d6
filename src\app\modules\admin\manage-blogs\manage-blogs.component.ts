import { HttpClient } from '@angular/common/http';
import { Component, type OnInit } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { ModalService } from 'src/app/shared/services/modal.service';

@Component({
  selector: 'app-manage-blogs',
  templateUrl: './manage-blogs.component.html',
  styleUrls: ['./manage-blogs.component.scss'],
})
export class ManageBlogsComponent implements OnInit {
  /**
   *
   */
  blogs: any[] = [];
  loading = false;

  constructor(private modalService: ModalService, private http: HttpClient) {}
  ngOnInit(): void {
    this.handleRefresh();
  }

  addCategory() {
    this.modalService.openModal('add-edit-blog-category', {
      class: 'modal-xl',
    });
  }

  handleOnAddBlog() {
    this.modalService.openModal('add-edit-blog', {
      class: 'modal-xl',
    });
  }

  handleonPreview(item: any) {
    this.modalService.openModal('blog-preview', {
      class: 'modal-xl',
      initialState: {
        blog: item,
      },
    });
  }

  handleRefresh() {
    this.loading = true;
    this.http
      .get('blog/getallblogs')
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((response: any) => {
        response.forEach((blog: any) => {
          if (blog.tags) {
            blog.tags = blog.tags.split(',').map((tag: string) => tag.trim());}
        });
        this.blogs = response;
      });
  }

  handleonEdit(item: any) {
    this.modalService.openModal('add-edit-blog', {
      class: 'modal-xl',
      initialState: {
        blog: item,
        onEditSuccess: (updatedBlog: any) => {
          // Update the blog in the list with the edited values
          const index = this.blogs.findIndex((b) => b.id === updatedBlog.id);
          if (index !== -1) {
            updatedBlog.createdAt = new Date();
            this.blogs[index] = updatedBlog;
          }
        },
      },
    });
  }
}
