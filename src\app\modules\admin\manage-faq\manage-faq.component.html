<div class="row mb-3">
    <div class="col-md-12 grid-margin stretch-card">
        <div class="card">
            <div class="card-body d-flex justify-content-between align-items-center">
                <h4 class="card-title">Manage Frequently Asked Questions</h4>
                <button type="button" (click)="addFaq()" class="btn btn-primary">Add FAQ</button>
            </div>
        </div>
    </div>
</div>


<div class="row">
    <div class="col-md-12 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Question</th>
                                <th>Answer</th>
                                <th>Created At</th>
                                <th>Updated At</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <ng-container *ngIf="!dataLoading">
                                <tr *ngFor="
                      let item of endUsers
                        | search : searchTerm : 'FirstName' : 'LastName' : 'email'
                    ">
                                    <td>
                                        {{ item.id }}
                                    </td>
                                    <td>
                                        {{ item.AspNetId }}
                                    </td>
                                    <td>
                                        {{ item.FirstName }}
                                    </td>
                                    <td>
                                        {{ item.LastName }}
                                    </td>
                                    <td>
                                        {{ item?.email }}
                                    </td>
                                    <td>
                                        {{ item.OrganizationName }}
                                    </td>
                                    <td>
                                        {{ item.Address || "Not available" }} {{ item.Cityname }} {{ item.StateName }}
                                        {{ item.Country }}
                                    </td>
                                    <td>{{ item.countryCode }} {{ item.phoneNumber }}</td>
                                    <td>
                                        {{ item.RegisterdOn }}
                                    </td>
                                </tr>
                            </ng-container>
                            <tr *ngIf="dataLoading">
                                <td colspan="7" class="text-center">data loading</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>