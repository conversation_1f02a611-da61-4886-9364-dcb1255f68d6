import { Component, type OnInit } from '@angular/core';
import { AddEditFaqComponent } from 'src/app/shared/modals/add-edit-faq/add-edit-faq.component';
import { ModalService } from 'src/app/shared/services/modal.service';
import { FAQ } from 'src/app/shared/types/faq.type';

@Component({
  selector: 'app-manage-faq',
  templateUrl: './manage-faq.component.html',
  styleUrls: ['./manage-faq.component.scss'],
})
export class ManageFaqComponent implements OnInit {

  searchTerm = '';
  endUsers = [];
  dataLoading = true;
  faqs: FAQ[] = [];
  constructor(
    private modalService: ModalService
  ) {

  }

  ngOnInit(): void { }

  addFaq() {
    // this.modalService.openModal('add-edit', {
    //   initialState: {
        
    //   }
    // });
  }

}
