<div>
  <tabset>
    <tab
      (selectTab)="getPendingApprovals()"
      heading="Pending Approval"
      id="tab1"
    >
      <ng-container *ngTemplateOutlet="pendingApprovals"></ng-container>
    </tab>
    <tab (selectTab)="getEarlyAdopters()" heading="Early Adopters Approval">
      <ng-container *ngTemplateOutlet="earlyAdopters"></ng-container>
    </tab>
  </tabset>
  <ng-template #pendingApprovals>
    <div
      class="d-flex flex-column justify-content-between w-100 flex-wrap py-3"
    >
      <div class="mb-3 mb-lg-0">
        <h1 class="h4">Pending Approvals</h1>
      </div>
      <div class="card border-0 shadow-sm mb-5">
        <div class="card-body table">
          <div class="table-responsive">
            <table class="table table-centered table-nowrap mb-0 rounded">
              <thead class="thead-light">
                <tr>
                  <th class="border-0 rounded-start">#</th>
                  <th class="border-0 col-2">Person Name</th>
                  <th class="border-0">Company Name &amp; Role</th>
                  <th class="border-0">Address</th>
                  <th class="border-0">Email &amp; Phone number</th>
                  <th class="border-0">Registration Date</th>
                  <th class="border-0">Status</th>
                  <th class="border-0 rounded-end text-center">Action</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngIf="loading">
                  <td colspan="8" class="text-center">
                    <app-spinner></app-spinner>
                  </td>
                </tr>
                <ng-container *ngFor="let expert of data; let i = index">
                  <tr *ngIf="!expert?.companyAdmin">
                    <td class="border-0">
                      {{ i + 1 }}
                    </td>
                    <td class="border-0 fw-bold col-2">
                      {{ expert.personName }}
                    </td>
                    <td class="border-0 text-danger col-2">
                      <div class="d-flex align-items-center">
                        <span class="fw-bold">
                          <a
                            (click)="redirectToExternalURL(expert.website)"
                            target="_blank"
                          >
                            {{ expert.companyName_role }}
                          </a>
                        </span>
                      </div>
                    </td>
                    <td class="border-0 fw-bold col-2">
                      {{ expert.address }}
                    </td>
                    <td class="border-0">
                      {{ expert.email_phoneNumber }}
                    </td>
                    <td class="border-0 col-2">
                      {{ expert.createdAt | date : "short" }}
                    </td>
                    <td class="border-0 col-2">
                      <label
                        *ngIf="expert.isUserApproved == 1"
                        class="badge text-bg-primary"
                      >
                        Approved
                      </label>
                      <button
                        *ngIf="expert.isUserApproved == 2"
                        class="badge text-bg-danger"
                      >
                        Rejected
                      </button>
                      <button
                        *ngIf="expert.isUserApproved == 0"
                        class="badge text-bg-danger"
                      >
                        Pending
                      </button>
                    </td>
                    <td class="border-0 text-success text-center col-2">
                      <div class="dropdown">
                        <a
                          data-bs-toggle="dropdown"
                          class="text-dark btn-outline-default"
                          aria-expanded="true"
                          role="button"
                        >
                          <i class="fas fa-ellipsis-v"></i>
                        </a>
                        <ul
                          class="dropdown-menu dropdown-menu-end"
                          data-popper-placement="bottom-end"
                          role="menu"
                        >
                          <li
                            role="menuitem"
                            class="dropdown-item cursor-pointer"
                            (click)="viewProfile(expert)"
                          >
                            View Details
                          </li>
                          <li
                            role="menu"
                            (click)="setProfileState(expert)"
                            class="dropdown-item cursor-pointer"
                            *ngIf="!expert.isActive"
                          >
                            Send Activate Link
                          </li>
                          <li
                            role="menu"
                            (click)="confirmationDeactivateUser(expert)"
                            class="dropdown-item text-danger cursor-pointer"
                            *ngIf="expert.isActive"
                          >
                            Deactivate
                          </li>
                        </ul>
                      </div>
                    </td>
                  </tr>
                  <tr *ngIf="loading">
                    <td colspan="8" class="text-center">
                      <app-spinner></app-spinner>
                    </td>
                  </tr>
                  <tr *ngIf="!loading && !data.length">
                    <td colspan="8" class="text-center">no records found</td>
                  </tr>
                </ng-container>
              </tbody>
            </table>
            <app-pagination
              *ngIf="!loading"
              [data]="dataCopy"
              (onPageChange)="data = $event.data"
            ></app-pagination>
          </div>
        </div>
      </div>
    </div>
  </ng-template>

  <ng-template #earlyAdopters>
    <div
      class="d-flex flex-column justify-content-between w-100 flex-wrap py-3"
    >
      <div class="mb-3 mb-lg-0">
        <h1 class="h4">Pending Approvals</h1>
      </div>
      <div class="card border-0 shadow-sm mb-5 table">
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-centered mb-0 rounded">
              <thead class="thead-light">
                <tr>
                  <th class="border-0 rounded-start">#</th>
                  <th style="min-width: 12.5rem" class="border-0 col-2">
                    Admin Name
                  </th>
                  <th class="border-0">Company Website</th>
                  <th class="border-0">Company Name</th>
                  <th class="border-0">Email</th>
                  <th class="border-0">Registration Date</th>
                  <th class="border-0">Is Profile Completed</th>
                  <th class="border-0">Is Profile Approved</th>
                  <th class="border-0">Is Active</th>
                  <th class="border-0 rounded-end text-center">Action</th>
                </tr>
              </thead>
              <tbody>
              
                <tr *ngFor="let expert of adminData; let i = index">
                  <td class="border-0 text-truncate" style="max-width: 100px">
                    {{ expert.id }}
                  </td>
                  <td class="border-0 fw-bold col-2">
                    {{ expert.adminName }}
                  </td>
                  <td class="border-0 text-danger col-2">
                    <div class="d-flex align-items-center">
                      <span role="button" class="fw-bold">
                        <a
                          (click)="redirectToExternalURL(expert.companyWebsite)"
                          target="_blank"
                        >
                          {{ expert.companyWebsite }}
                        </a>
                      </span>
                    </div>
                  </td>
                  <td class="border-0 fw-bold col-2">
                    {{ expert.companyName }} - {{ expert.companyType }}
                  </td>
                  <td class="border-0">{{ expert.email }}</td>
                  <td class="border-0 col-2">
                    {{ expert.createdAt | date : "short" }}
                  </td>
                  <td class="border-0 col-2">
                    {{ expert.isProfileComplate ? "Yes" : "No" }}
                  </td>
                  <td class="border-0 col-2">
                    <span
                      *ngIf="expert.isUserApproved == 1"
                      class="badge text-bg-primary"
                    >
                      Approved
                    </span>
                    <span
                      *ngIf="expert.isUserApproved == 2"
                      class="badge text-bg-danger"
                    >
                      Rejected
                    </span>
                    <span
                      *ngIf="expert.isUserApproved == 0"
                      class="badge text-bg-danger"
                    >
                      Pending
                    </span>
                  </td>
                  <td class="border-0 col-2">
                    <div *ngIf="expert.isActive" class="badge text-bg-primary">
                      Active
                    </div>
                    <span *ngIf="!expert.isActive" class="badge text-bg-danger">
                      Deactive
                    </span>
                  </td>
                  <td class="border-0 text-success text-center col-2">
                    <div class="dropdown">
                      <a
                        data-bs-toggle="dropdown"
                        class="text-dark btn-outline-default"
                        aria-expanded="true"
                        role="button"
                      >
                        <i class="fas fa-ellipsis-v"></i>
                      </a>
                      <ul
                        class="dropdown-menu dropdown-menu-end"
                        data-popper-placement="bottom-end"
                        role="menu"
                      >
                        <li
                          role="menuitem"
                          class="dropdown-item cursor-pointer"
                          (click)="viewProfile(expert)"
                        >
                          View Details
                        </li>
                        <li
                          role="menu"
                          (click)="setProfileState(expert)"
                          class="dropdown-item cursor-pointer"
                          *ngIf="!expert.isActive"
                        >
                          Send Activate Link
                        </li>
                        <li
                          role="menu"
                          (click)="confirmationDeactivateUser(expert)"
                          class="dropdown-item text-danger cursor-pointer"
                          *ngIf="expert.isActive"
                        >
                          Deactivate
                        </li>
                      </ul>
                    </div>
                  </td>
                </tr>
                <tr *ngIf="loading">
                  <td colspan="8" class="text-center">
                    <app-spinner></app-spinner>
                  </td>
                </tr>
                <tr *ngIf="!loading && !data.length">
                  <td colspan="8" class="text-center">no records found</td>
                </tr>
              </tbody>
            </table>
            <app-pagination
              *ngIf="!loading"
              [data]="adminDataCopy"
              (onPageChange)="adminData = $event.data"
            ></app-pagination>
          </div>
        </div>
      </div>
    </div>
  </ng-template>
</div>
