import { Component, OnInit } from '@angular/core';
import { ProductService } from '../services/product.service';
import { ToastrService } from 'ngx-toastr';
import { finalize } from 'rxjs';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';

@Component({
  selector: 'app-products',
  templateUrl: './products.component.html',
  styleUrls: ['./products.component.scss'],
})
export class ProductsComponent implements OnInit {
  isLoading = true;
  products: Array<any> = [];
  productName = null;
  loading = false;
  editProductObj: any;
  delteProductId = '';
  modalRef!: BsModalRef;
  constructor(
    private product: ProductService,
    private toaster: ToastrService,
    private bsModalRef: BsModalService
  ) {}

  ngOnInit(): void {
    this.getProducts();
  }

  getProducts() {
    this.product
      .getProducts()
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe((response: any) => {
        this.products = response.data;
      });
  }

  generateProduct() {
    const product: any = {
      name: this.productName,
      isActive: true,
    };
    if (this.editProductObj) {
      product.id = this.editProductObj.id;
    }
    this.loading = true;
    this.product
      .createRecord(product)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((response) => {
        if (response) {
          if (this.editProductObj) {
            this.products = this.products.map((product) => {
              if (product.id === this.editProductObj.id) {
                product.name = this.productName;
              }
              return product;
            });
          } else {
            this.products.push({
              id: this.products.length + 1,
              ...product,
            });
          }
          this.editProductObj = undefined;
          this.productName = null;
          this.toaster.success(response.message);
        }
      });
  }

  deleteConfirmation(template: any, item: any) {
    this.delteProductId = item.id;
    this.modalRef = this.bsModalRef.show(template, {
      class: 'modal-lg',
    });
  }

  edit(item: any) {
    this.editProductObj = item;
    this.productName = item.name;
  }

  deleteItem() {
    this.product.deleteItem(this.delteProductId).subscribe((response: any) => {
      if (response.messageType) {
        return this.toaster.error(response.message);
      }
      this.modalRef.hide();
      this.delteProductId = '';
      return this.toaster.success(response.message);
    });
  }
}
