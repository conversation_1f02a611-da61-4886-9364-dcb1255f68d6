import { Component, OnInit } from '@angular/core';
import { RolesService } from '../services/roles.service';
import { finalize } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';

@Component({
  selector: 'app-roles',
  templateUrl: './roles.component.html',
  styleUrls: ['./roles.component.scss'],
})
// TYPE OF EXPERT
export class RolesComponent implements OnInit {
  rolesList: Array<any> = [];
  roleName: string = '';
  loading = false;
  editRoleObj: any;
  modalRef!: BsModalRef;
  deleteRoleId: any;
  constructor(
    private roles: RolesService,
    private readonly toaster: ToastrService,
    private bsModalService: BsModalService
  ) {}

  ngOnInit(): void {
    this.getRoles();
  }

  getRoles() {
    this.roles.getRoleOfExperts().subscribe((response: any) => {
      this.rolesList = response.data;
    });
  }

  generateRole() {
    const role: any = {
      name: this.roleName,
      isActive: true,
    };
    if (this.editRoleObj) {
      role.id = this.editRoleObj.id;
    }
    this.loading = true;
    this.roles
      .addUpdateRole(role)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((response: any) => {
        if (response) {
          if (this.editRoleObj) {
            this.rolesList = this.rolesList.map((role) => {
              if (role.id == this.editRoleObj.id) {
                role.name = this.roleName;
              }
              return role;
            });
          } else {
            this.rolesList.push({
              id: this.rolesList.length + 1,
              ...role,
            });
          }
          this.roleName = '';
          this.editRoleObj = undefined;
          this.toaster.success(response.message);
        }
      });
  }

  edit(item: any) {
    this.editRoleObj = item;
    this.roleName = item.name;
  }

  deleteConfirmation(template: any, item: any) {
    this.deleteRoleId = item.id;
   this.modalRef =  this.bsModalService.show(template, {
      class: 'modal-lg',
    });
  }

  deleteRole() {
    this.roles.deleteRole(this.deleteRoleId).subscribe((response: any) => {
      if (response.messageType) {
        return this.toaster.error(response.message);
      }
      this.modalRef.hide();
      this.deleteRoleId = '';
      return this.toaster.success(response.message);
    });
  }
}
