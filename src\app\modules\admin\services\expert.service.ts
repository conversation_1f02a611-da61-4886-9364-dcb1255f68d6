import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, forkJoin } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class ExpertService {
  constructor(private http: HttpClient) {}

  createRecord(record: any): Observable<any> {
    return this.http.post('Expertise/AddUpdate', record);
  }

  createRecords(records: any[]): Observable<any[]> {
    const createRequests: Observable<any>[] = [];
    for (const record of records) {
      createRequests.push(this.createRecord(record));
    }
    return forkJoin(createRequests);
  }

  getExperties(companyType = 1) {
    return this.http.get(`User/GetUserList?companyType=${companyType}`);
  }

  getPendingApprovals() {
    return this.http.get(`User/GetPendingApprovalList`);
  }

  setProfileApproval(email: any, state: number) {
    return this.http.post(
      `User/SetUserApproval?userId=${email}&state=${state}`,
      {}
    );
  }

  getEarlyAdopters() {
    return this.http.get(`User/GetEarlyAdaptors`);
  }

  SetConnectionApproval(userId: any, followerUserId: any) {
    return this.http.get(
      `Follower/SetConnectionApproval?userId=${userId}&followerUserId=${followerUserId}`
    );
  }
}
