import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, forkJoin } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class IndustryService {
  private apiUrl = 'https://example.com/api/endpoint'; // Replace with your API endpoint URL

  constructor(private http: HttpClient) { }

  createRecord(record: any): Observable<any> {
    return this.http.post('Industry/AddUpdate', record);
  }

  createRecords(records: any[]): Observable<any[]> {
    const createRequests: Observable<any>[] = [];
    for (const record of records) {
      createRequests.push(this.createRecord(record));
    }
    return forkJoin(createRequests);
  }

  getIndustries() {
    return this.http.get('Industry/GetAll');
  }

  deleteItem(id: any) {
    return this.http.delete(`Industry/delete?id=${id}`);
  }
}
