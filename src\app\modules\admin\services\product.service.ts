import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, forkJoin } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class ProductService {
  constructor(private httpClient: HttpClient) {}

  createRecord(record: any): Observable<any> {
    return this.httpClient.post('Product/AddUpdate', record);
  }

  getProducts() {
    return this.httpClient.get('Product/GetAll');
  }

  createRecords(records: any[]): Observable<any[]> {
    const createRequests: Observable<any>[] = [];
    for (const record of records) {
      createRequests.push(this.createRecord(record));
    }
    return forkJoin(createRequests);
  }

  deleteItem(id: any) {
    return this.httpClient.delete(`product/delete?id=${id}`);
  }
}
