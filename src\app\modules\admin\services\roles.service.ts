import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, forkJoin } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class RolesService {
  private apiUrl = 'Role/GetAll'; // Replace with your API endpoint URL

  constructor(private http: HttpClient) { }

  createRecord(record: any): Observable<any> {
    return this.http.post(this.apiUrl, record);
  }

  createRecords(records: any[]): Observable<any[]> {
    const createRequests: Observable<any>[] = [];
    for (const record of records) {
      createRequests.push(this.createRecord(record));
    }
    return forkJoin(createRequests);
  }

  getRoleOfExperts() {
    return this.http.get(this.apiUrl)
  }

  addUpdateRole(payload:any) {
    return this.http.post('role/addUpdate',payload);
  }

  deleteRole(id:string) {
   return this.http.delete(`Role/Delete?id=${id}`);
  }
}
