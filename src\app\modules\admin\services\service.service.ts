import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, forkJoin } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ServicesService {

  constructor(private http: HttpClient) { }

  createRecord(record: any): Observable<any> {
    return this.http.post('Services/AddUpdate', record);
  }

  createRecords(records: any[]): Observable<any[]> {
    const createRequests: Observable<any>[] = [];
    for (const record of records) {
      createRequests.push(this.createRecord(record));
    }
    return forkJoin(createRequests);
  }

  getServices() {
    return this.http.get(`Services/GetAll`);
  }


  deleteItem(id: any) {
    return this.http.delete(`Services/delete?id=${id}`);
  }
}
