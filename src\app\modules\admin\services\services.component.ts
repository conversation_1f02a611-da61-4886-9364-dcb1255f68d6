import { Component } from '@angular/core';
import { ServicesService } from './service.service';
import { finalize } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { BsModalService } from 'ngx-bootstrap/modal';

@Component({
  selector: 'app-services',
  templateUrl: './services.component.html',
  styleUrls: ['./services.component.scss'],
})
export class ServicesComponent {
  isLoading = true;
  products: Array<any> = [];
  serviceName = null;
  loading = false;
  editObj: any;
  deleteId = '';
  modalRef: any;
  constructor(
    private service: ServicesService,
    private readonly toaster: ToastrService,
    private modalService: BsModalService
  ) {}

  ngOnInit(): void {
    this.getServices();
  }

  getServices() {
    this.service
      .getServices()
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe((response: any) => {
        this.products = response.data;
      });
  }

  generateService() {
    const service: any = {
      name: this.serviceName,
      isActive: true,
    };
    if (this.editObj) {
      service.id = this.editObj.id;
    }
    this.loading = false;
    this.service
      .createRecord(service)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((response) => {
        if (response) {
          if (this.editObj) {
            this.products = this.products.map((product: any) => {
              if (product.id === this.editObj.id) {
                product.name = this.serviceName;
              }
              return product;
            });
          } else {
            this.products.push({
              id: this.products.length + 1,
              ...service,
            });
          }
          this.editObj = null;
          this.serviceName = null;
          this.toaster.success(response.message);
        }
      });
  }

  edit(item: any) {
    this.editObj = item;
    this.serviceName = item.name;
  }

  deleteConfirmation(template: any, item: any) {
    this.deleteId = item.id;
    this.modalRef = this.modalService.show(template, {
      class: 'modal-lg',
    });
  }

  deleteRole() {
    this.service.deleteItem(this.deleteId).subscribe((response: any) => {
      if (response.messageType) {
        return this.toaster.error(response.message);
      }
      this.modalRef.hide();
      this.deleteId = '';
      return this.toaster.success(response.message);
    });
  }
}
