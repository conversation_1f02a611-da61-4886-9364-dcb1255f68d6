import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, forkJoin } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class TechnologiesService {

  constructor(private http: HttpClient) { }

  createRecord(record: any): Observable<any> {
    const formData = new FormData();
    Object.keys(record).forEach(key => {
      formData.append(key,record[key]);
    });
    return this.http.post('Technology/AddUpdate', formData);
  }

  createRecords(records: any[]): Observable<any[]> {
    const createRequests: Observable<any>[] = [];
    for (const record of records) {
      createRequests.push(this.createRecord(record));
    }
    return forkJoin(createRequests);
  }

  getAllTechnologies() {
    return this.http.get('Technology/GetTechnologyLookUp');
  }

  deleteItem(id: any) {
    return this.http.delete(`Technology/delete?id=${id}`);
  }
}
