<form>
  <div class="card shadow rounded">
    <div class="card-header">
      <h3>Add Solution</h3>
    </div>
    <div class="card-body">
      <div class="form-group">
        <label for="solutionName" class="mb-2">Solution Name</label>
        <input
          type="text"
          class="form-control"
          id="solutionName"
          [(ngModel)]="solutionName"
          name="solutionName"
          placeholder="Enter the solution name"
        />
      </div>
      <div class="form-group mt-3">
        <div class="mb-3">
          <label for="solutionName" class="mb-2">Solution Image</label>
          <input
            type="file"
            (change)="imageChange($event.target)"
            [(ngModel)]="solutionImage"
            class="form-control"
            name="solutionImage"
            id="inputGroupFile01"
          />
        </div>
      </div>
    </div>
    <div class="card-footer d-flex justify-content-end">
      <focile-button
        [btnType]="'primary'"
        [loading]="loading"
        [disabled]="!solutionName?.length"
        (onClick)="generateSolution()"
        type="submit"
        >{{ editSolutionObj ? "Update" : "Add" }} Solution</focile-button
      >
    </div>
  </div>
</form>

<div class="card shadow my-3">
  <div class="card-body">
    <div class="table-responsive">
      <table class="table table-centered table-nowrap mb-0 rounded">
        <thead class="thead-light">
          <tr>
            <th scope="col">#</th>
            <th scope="col">Image</th>
            <th scope="col">Solution Name</th>
            <th scope="col">Action</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of solutions; let i = index">
            <td scope="col">{{ i + 1 }}</td>
            <td scope="col">
              <img
                *ngIf="item.description; else noImage"
                class="focile-avatar"
                [src]="item.description"
                alt=""
                srcset=""
              />
              <ng-template #noImage> No Image Available </ng-template>
            </td>
            <td scope="col">{{ item.name }}</td>
            <td scope="col">
              <button
                class="btn btn-sm btn-primary"
                (click)="editSolution(item)"
              >
                <i class="fa fa-solid fa-pen"></i>
              </button>
              <button
                (click)="openConfirmationModal(template, item)"
                class="btn btn-sm btn-danger ms-2"
              >
                <i class="fa fa-solid fa-trash"></i>
              </button>
            </td>
          </tr>
          <tr *ngIf="isLoading || !solutions.length">
            <td colspan="4" class="text-center">
              {{ isLoading ? "Loading..." : "No Records Found" }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

<ng-template #template>
  <div class="modal-header">
    <h4 class="modal-title">Confirmation</h4>
  </div>
  <div class="modal-body">Are you sure you want to perform this action?</div>
  <div class="modal-footer">
    <button class="btn btn-secondary" (click)="modalRef.hide()">Cancel</button>
    <button class="btn btn-primary" (click)="deleteSolution()">Confirm</button>
  </div>
</ng-template>
