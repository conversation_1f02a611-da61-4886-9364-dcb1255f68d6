import { Component, TemplateRef } from '@angular/core';
import { SolutionService } from '../services/solutions.service';
import { finalize } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { UtilsService } from 'src/app/shared/services/utils.service';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';

@Component({
  selector: 'app-solutions',
  templateUrl: './solutions.component.html',
  styleUrls: ['./solutions.component.scss'],
})
export class SolutionsComponent {
  isLoading = true;
  solutions: Array<any> = [];
  solutionName: any = '';
  loading = false;
  solutionImage: any = null;
  editSolutionObj: any = null;
  modalRef!: BsModalRef;
  deleteSolutionId = '';
  constructor(
    private solution: SolutionService,
    private readonly toaster: ToastrService,
    private utils: UtilsService,
    private modalService: BsModalService
  ) {}

  ngOnInit(): void {
    this.getProducts();
  }

  getProducts() {
    this.solution
      .getSolutions()
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe((response: any) => {
        this.solutions = response.data;
      });
  }

  generateSolution() {
    const solution: any = {
      name: this.solutionName,
      isActive: true,
      image: this.solutionImage || null,
    };
    if (this.editSolutionObj) {
      solution.id = this.editSolutionObj.idGuid;
    }
    this.loading = true;
    this.solution
      .createRecord(solution)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((response) => {
        if (response) {
          this.toaster.success(response.message);
          if (this.editSolutionObj) {
            this.solutions = this.solutions.map((s) => {
              if (s.idGuid === this.editSolutionObj.idGuid) {
                s.name = this.solutionName;
                if (this.solutionImage) {
                  s.description = this.solutionImage;
                }
              }
              return s;
            });
          } else {
            this.solutions.push({
              id: this.solutions.length + 1,
              ...solution,
            });
          }
          this.editSolutionObj = null;
          this.solutionName = null;
          this.solutionImage = null;
        }
      });
  }

  imageChange($event: any) {
    this.solutionImage = $event.files[0];
  }

  editSolution(item: any) {
    this.editSolutionObj = item;
    this.solutionName = item.name;
  }

  openConfirmationModal(template: TemplateRef<any>, item: any) {
    this.deleteSolutionId = item.idGuid;
    this.modalRef = this.modalService.show(template, {
      class: 'modal-lg',
    });
  }

  deleteSolution() {
    this.solution.deleteSolution(this.deleteSolutionId).subscribe((response:any) => {
      if (response.messageType) {
        return this.toaster.error(response.message)
      }
      this.solutions = this.solutions.filter(x => x.idGuid !== this.deleteSolutionId);
      this.deleteSolutionId = '';
      this.modalRef.hide();
      return this.toaster.success(response.message);
    });

  }

  updateSolution() {}
}
