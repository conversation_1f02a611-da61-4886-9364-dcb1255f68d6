import { Component, OnInit } from '@angular/core';
import { TechnologiesService } from '../services/technology.service';
import { finalize } from 'rxjs';
import { BsModalService } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-technologies',
  templateUrl: './technologies.component.html',
  styleUrls: ['./technologies.component.scss'],
})
export class TechnologiesComponent implements OnInit {
  technoLogies: Array<any> = [];
  technologyName = null;
  loading = false;
  editObj: any;
  deleteId: any;
  modalRef: any;
  solutionImage: any;
  constructor(
    private readonly technology: TechnologiesService,
    private readonly bsModalRef: BsModalService,
    private readonly toaster: ToastrService
  ) {}

  ngOnInit(): void {
    this.getTechnoLogies();
  }

  getTechnoLogies() {
    this.technology.getAllTechnologies().subscribe((response: any) => {
      this.technoLogies = response.data;
    });
  }

  generateTechnology() {
    const technologyName: any = {
      name: this.technologyName,
      isActive: true,
      image: this.solutionImage,
    };
    if (this.editObj) {
      technologyName.id = this.editObj.idGuid;
    }
    this.loading = true;
    this.technology
      .createRecord(technologyName)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((response) => {
        if (response) {
          if (this.editObj) {
            this.technoLogies = this.technoLogies.map((technology) => {
              if (technology.idGuid === this.editObj.idGuid) {
                technology.name = this.technologyName;
                if (this.solutionImage) {
                  technology.description = this.solutionImage;
                }
              }

              return technology;
            });
          } else {
            this.technoLogies.push({
              id: this.technoLogies.length + 1,
              ...technologyName,
            });
          }
          this.editObj = null;
          this.deleteId = '';
          this.technologyName = null;
          this.solutionImage = '';
        }
      });
  }

  edit(item: any) {
    this.editObj = item;
    this.technologyName = item.name;
  }

  deleteConfirmation(template: any, item: any) {
    this.deleteId = item.idGuid;
    this.modalRef = this.bsModalRef.show(template, {
      class: 'modal-lg',
    });
  }

  deleteTechnology() {
    this.technology.deleteItem(this.deleteId).subscribe((response: any) => {
      if (response.messageType) {
        return this.toaster.error(response.message);
      }
      this.technoLogies = this.technoLogies.filter(
        (x) => x.idGuid != this.deleteId
      );
      this.modalRef.hide();
      this.deleteId = '';
      return this.toaster.success(response.message);
    });
  }

  imageChange($event: any) {
    this.solutionImage = $event.files[0];
  }
}
