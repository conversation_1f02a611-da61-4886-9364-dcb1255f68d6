<div class="row">
  <div class="col-md-10">
    <h2>Type of experts</h2>
  </div>
</div>

<div class="accordion accordion-flush" id="accordionFlushExample">
  <div class="accordion-item">
    <h2 class="accordion-header" id="flush-headingOne">
      <button
        class="accordion-button collapsed"
        type="button"
        data-bs-toggle="collapse"
        data-bs-target="#flush-collapseOne"
        aria-expanded="false"
        aria-controls="flush-collapseOne"
      >
        Add Type of expert
      </button>
    </h2>
    <div
      id="flush-collapseOne"
      class="accordion-collapse collapse"
      aria-labelledby="flush-headingOne"
      data-bs-parent="#accordionFlushExample"
    >
      <div class="accordion-body">
        <div class="row">
          <div class="col-md-6">
            <div class="mb-3">
              <label for="service" class="form-label">Expert</label>
              <select [(ngModel)]="selectedExpert" class="form-control">
                <option *ngFor="let expert of expertes" [value]="expert.id">
                  {{ expert.name }}
                </option>
              </select>
            </div>
          </div>
          <div class="col-md-4">
            <div class="mb-3">
              <label for="typeOfExpert" class="form-label"
                >Type of expert Name</label
              >
              <input
                type="text"
                class="form-control"
                id="typeOfExpert"
                [(ngModel)]="typeOfExpertName"
                placeholder="Add Expert"
              />
            </div>
          </div>
          <div class="col-md-2 pt-4">
            <button
              (click)="createTypeOfExpert()"
              class="btn btn-sm btn-primary"
            >
              Add
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="table-responsive">
  <table class="table table-striped table-sm">
    <thead>
      <tr>
        <th scope="col">#</th>
        <th scope="col">Expert Name</th>
        <th scope="col">Type of expert</th>
        <th scope="col">Is Active</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let typeOfExpert of typeOfExpertes; let i = index">
        <td>
          {{ i + 1 }}
        </td>
        <td>
          {{ typeOfExpert.expert }}
        </td>
        <td>{{ typeOfExpert.name }}</td>
        <td>{{ typeOfExpert.isActive | status }}</td>
      </tr>
      <tr *ngIf="loading">
        <td colspan="4" class="text-center">Loading...</td>
      </tr>
    </tbody>
  </table>
</div>
