import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { finalize } from 'rxjs';

@Component({
  selector: 'app-type-of-experts',
  templateUrl: './type-of-experts.component.html',
  styleUrls: ['./type-of-experts.component.scss'],
})
export class TypeOfExpertsComponent implements OnInit {
  expertes: Array<any> = [];
  selectedExpert = 1;
  typeOfExpertName = null;
  typeOfExpertes: Array<any> = [];
  loading = false;
  constructor(private http: HttpClient, private toastr: ToastrService) {}

  ngOnInit(): void {
    this.getExperts();
  }

  getTypeOfExpertes() {
    this.http
      .get('TypeOfExpert/GetAll')
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((response: any) => {
        if (response.data.length) {
          (response.data as any).forEach((expert: any) => {
            const exp = this.expertes.find((x) => x.id === expert.typeId).name;
            expert.expert = exp;
          });
          this.typeOfExpertes = response.data;
        }
      });
  }

  getExperts() {
    this.loading = true;
    this.http
      .get('TypeOfExpert/GetAddUpdateData')
      .pipe(finalize(() => this.getTypeOfExpertes()))
      .subscribe((response: any) => {
        if (response.data) {
          this.expertes = response.data.typeList;
          this.getTypeOfExpertes();
        }
      });
  }

  createTypeOfExpert() {
    const obj = {
      name: this.typeOfExpertName,
      isActive: true,
      typeId: this.selectedExpert,
    };

    this.http
      .post('/TypeOfExpert/AddUpdate', obj)
      .subscribe((response: any) => {
        if (!response.messageType) {
          this.typeOfExpertName = null;
          this.selectedExpert = 1;
          this.toastr.success('Type of expert added');
          this.getTypeOfExpertes();
        } else {
          this.toastr.success(response.message);
        }
      });
  }
}
