<div class="py-4">
  <div class="d-flex justify-content-between w-100 flex-wrap">
    <div class="mb-3 mb-lg-0">
      <h1 class="h4">Vendors</h1>
      <p class="mb-0">
        you can add vendor from <a (click)="addUser()">here </a>.
      </p>
    </div>
  </div>
</div>

<div class="card border-0 shadow mb-5">
  <div class="card-body">
    <div class="table-responsive">
      <table class="table table-centered table-nowrap mb-0 rounded">
        <thead class="thead-light">
          <tr>
            <th class="border-0 rounded-start">#</th>
            <th class="border-0">Person Name</th>
            <th class="border-0">Company Name &amp; Role</th>
            <th class="border-0">Address</th>
            <th class="border-0">Email &amp; Phone number</th>
            <th class="border-0 rounded-end">Action</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let expert of experts; let i = index">
            <td class="border-0">
              {{ i + 1 }}
            </td>
            <td class="border-0 fw-bold">
              {{ expert.personName }}
            </td>
            <td class="border-0 text-danger">
              <div class="d-flex align-items-center">
                <span class="fw-bold">
                  {{ expert.expertDetail?.companyName }} -
                  {{ expert.expertDetail?.roleName }} -
                  {{ expert.userTypeStr }}
                </span>
              </div>
            </td>
            <td class="border-0 fw-bold">
              {{ expert.address }}
            </td>
            <td class="border-0">
              {{ expert.email }} - {{ expert.phoneNumber }}
            </td>
            <td class="border-0 text-success">
              <button
                (click)="editItem(editUser, expert)"
                class="btn btn-primary"
                [disabled]="expert.loading"
              >
                <span *ngIf="!expert.loading">Edit</span>
                <app-spinner *ngIf="expert.loading"></app-spinner>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

<ng-template #editUser>
  <div class="modal-header">
    <h4 class="modal-title">Edit</h4>
  </div>
  <div class="modal-body" [formGroup]="formGroup">
    <div class="row">
      <div class="col-md-6">
        <div class="mb-3">
          <label for="name" class="form-label"
            >First Name <span class="text-danger">*</span></label
          >
          <app-focile-input
            [type]="'text'"
            [id]="'id'"
            [name]="'name'"
            [disabled]="false"
            formControlName="firstName"
            [elementClass]="
              formGroup.get('firstName')?.touched &&
              (formGroup.get('firstName')?.errors?.required ||
                formGroup.get('firstName')?.errors?.pattern)
                ? 'is-invalid'
                : null
            "
          ></app-focile-input>
          <ng-container *ngIf="formGroup.get('firstName')?.touched">
            <span
              *ngIf="formGroup.get('firstName')?.errors?.required"
              class="text-danger"
            >
              First Name is required.
            </span>
            <span
              *ngIf="formGroup.get('firstName')?.errors?.pattern"
              class="text-danger"
            >
              First Name should contains only letters.
            </span>
          </ng-container>
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="lastName" class="form-label"
            >Last Name <span class="text-danger">*</span></label
          >
          <app-focile-input
            [type]="'text'"
            [id]="'lastName'"
            [name]="'lastName'"
            [disabled]="false"
            formControlName="lastName"
            [elementClass]="
              formGroup.get('lastName')?.touched &&
              (formGroup.get('lastName')?.errors?.required ||
                formGroup.get('lastName')?.errors?.pattern)
                ? 'is-invalid'
                : null
            "
          ></app-focile-input>
          <ng-container *ngIf="formGroup.get('lastName')?.touched">
            <span
              *ngIf="formGroup.get('lastName')?.errors?.required"
              class="text-danger"
            >
              Last Name is required.
            </span>
            <span
              *ngIf="formGroup.get('lastName')?.errors?.pattern"
              class="text-danger"
            >
              Last Name should contains only letters.
            </span>
          </ng-container>
        </div>
      </div>
      <div class="col-md-12">
        <div>
          <label for="email" class="form-label fw-bold"
            >Email <span class="text-danger">*</span></label
          >
          <app-focile-input
            [type]="'email'"
            [id]="'email'"
            [name]="'email'"
            [disabled]="false"
            [iconName]="'envelope'"
            formControlName="email"
            [elementClass]="
              (formGroup.get('email')?.touched &&
                formGroup.get('email')?.errors?.required) ||
              (formGroup.get('email')?.touched &&
                formGroup.get('email')?.errors?.email)
                ? 'is-invalid'
                : null
            "
          ></app-focile-input>
        </div>
        <span
          *ngIf="
            formGroup.get('email')?.touched &&
            formGroup.get('email')?.errors?.required
          "
          class="text-danger"
        >
          Email is required.
        </span>
        <span
          *ngIf="
            formGroup.get('email')?.touched &&
            formGroup.get('email')?.errors?.email
          "
          class="text-danger"
        >
          Email is not valid.
        </span>
      </div>
      <div class="col-md-6">
        <div class="form-group">
          <!-- <app-phone-number
            lable="Phone number"
            countryCodeKey="countryCode"
            numberKey="phoneNumber"
          ></app-phone-number> -->
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-group">
          <!-- <app-phone-number
            lable="Mobile number"
            countryCodeKey="mobileCountryCode"
            numberKey="workMobileNumber"
          ></app-phone-number> -->
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="orgname" class="form-label"
              >Company Website <span class="required">*</span></label
            >
            <input
              type="text"
              name="companyWebsite"
              formControlName="companyWebsite"
              id="companyWebsite"
              class="form-control"
            />
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="orgname" class="form-label"
              >Company name <span class="required">*</span></label
            >
            <input
              type="text"
              class="form-control"
              id="orgname"
              placeholder="Type here"
              formControlName="companyName"
              required
              [ngClass]="{
                'is-invalid':
                  formGroup.get('companyName')?.invalid &&
                  formGroup.get('companyName')?.touched
              }"
            />
            <span class="invalid-feedback"> Company name is required. </span>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="export" class="form-label"
              >Channel Partner <span class="required">*</span></label
            >
            <focile-dropdown
              [items]="editExpert.companyTypeList"
              formControlName="expertId"
              [bindValue]="'id'"
              [disabled]="formGroup.disabled"
              (change)="handleExpertChange($event)"
              [loading]="dataLoading"
              [clearable]="false"
            ></focile-dropdown>
            <span
              class="text-danger"
              *ngIf="
                formGroup.get('expert')?.touched &&
                formGroup.get('expert')?.errors?.required
              "
            >
              Expert is required.
            </span>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="companySystemIds" class="form-label fw-bold"
              >Company Partner Type <span class="required">*</span>
              <i
                *ngIf="!formGroup.get('expert')?.value"
                tooltip="Select Channel Partner first to enable this"
                class="fa fa-info-circle text-primary ms-1"
              ></i>
            </label>
            <focile-dropdown
              [items]="editExpert.companySystemList"
              [placeholder]="'Select'"
              id="companySystemIds"
              formControlName="companySystemIds"
              [loading]="isCompanySystem"
              [multiple]="true"
              [clearable]="false"
            ></focile-dropdown>
            <span
              class="text-danger"
              *ngIf="
                formGroup.get('companySystemIds')?.touched &&
                formGroup.get('companySystemIds')?.errors?.required
              "
            >
              Type of expert is required.
            </span>
          </div>
        </div>
        <div *ngIf="showInstallationType" class="col-md-6">
          <div class="mb-3">
            <label for="typeOfInstallationIds" class="form-label fw-bold"
              >Type of installation <span class="required">*</span></label
            >
            <focile-dropdown
              [items]="editExpert.installationList"
              [placeholder]="'Select'"
              id="typeOfInstallationIds"
              formControlName="typeOfInstallationIds"
              [loading]="isCompanySystem"
              [multiple]="true"
            ></focile-dropdown>
            <span
              class="text-danger"
              *ngIf="
                formGroup.get('typeOfExpert')?.touched &&
                formGroup.get('typeOfExpert')?.errors?.required
              "
            >
              Type of expert is required.
            </span>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="solution" class="form-label fw-bold"
              >Solution <span class="required">*</span></label
            >
            <focile-dropdown
              [items]="editExpert.solutionList"
              [placeholder]="'Select'"
              [multiple]="true"
              id="solution"
              formControlName="solutionIds"
              [disabled]="formGroup.disabled"
              [loading]="dataLoading"
            ></focile-dropdown>
            <span
              class="text-danger"
              *ngIf="
                formGroup.get('solution')?.touched &&
                formGroup.get('solution')?.errors?.required
              "
            >
              Solution is required.
            </span>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="roles" class="form-label fw-bold"
              >Role <span class="required">*</span></label
            >
            <focile-dropdown
              [items]="editExpert.roleList"
              [placeholder]="'Select'"
              id="roles"
              formControlName="roleId"
              [clearable]="false"
              [disabled]="formGroup.disabled"
              [loading]="dataLoading"
            ></focile-dropdown>
            <span
              class="text-danger"
              *ngIf="
                formGroup.get('roles')?.touched &&
                formGroup.get('roles')?.errors?.required
              "
            >
              Role is required.
            </span>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="role" class="form-label fw-bold"
              >Product <span class="required">*</span></label
            >
            <focile-dropdown
              [items]="editExpert.productList"
              [multiple]="true"
              [placeholder]="'Select'"
              id="products"
              [clearable]="false"
              formControlName="productIds"
              [disabled]="formGroup.disabled"
              [loading]="dataLoading"
            ></focile-dropdown>
            <span
              class="text-danger"
              *ngIf="
                formGroup.get('products')?.touched &&
                formGroup.get('products')?.errors?.required
              "
            >
              Product is required.
            </span>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="role" class="form-label fw-bold"
              >Services <span class="required">*</span></label
            >
            <focile-dropdown
              [items]="editExpert.servicesList"
              [multiple]="true"
              [clearable]="false"
              [placeholder]="'Select'"
              id="services"
              formControlName="serviceIds"
              [disabled]="formGroup.disabled"
              [loading]="dataLoading"
            ></focile-dropdown>
            <span
              class="text-danger"
              *ngIf="
                formGroup.get('services')?.touched &&
                formGroup.get('services')?.errors?.required
              "
            >
              Services is required.
            </span>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="role" class="form-label fw-bold"
              >Industry <span class="required">*</span></label
            >
            <focile-dropdown
              [items]="editExpert.industryList"
              [placeholder]="'Select'"
              [multiple]="true"
              [clearable]="false"
              id="industries"
              formControlName="industryIds"
              [disabled]="formGroup.disabled"
              [loading]="dataLoading"
            ></focile-dropdown>
            <span
              class="text-danger"
              *ngIf="
                formGroup.get('industries')?.touched &&
                formGroup.get('industries')?.errors?.required
              "
            >
              Industry is required.
            </span>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="companySize" class="form-label fw-bold"
              >Company Size <span class="required">*</span></label
            >
            <focile-dropdown
              [items]="editExpert.companySizeList"
              [placeholder]="'Select'"
              id="companySize"
              bindValue="id"
              [clearable]="false"
              formControlName="companySize"
              [disabled]="formGroup.disabled"
              [loading]="dataLoading"
            ></focile-dropdown>
            <span
              class="text-danger"
              *ngIf="
                formGroup.get('companySize')?.touched &&
                formGroup.get('companySize')?.errors?.required
              "
            >
              Company Size is required.
            </span>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="technologies" class="form-label fw-bold"
              >Technology <span class="required">*</span></label
            >
            <focile-dropdown
              [items]="editExpert.technologyList"
              [placeholder]="'Select'"
              id="technologies"
              formControlName="technologyIds"
              [disabled]="formGroup.disabled"
              [loading]="dataLoading"
              [multiple]="true"
              [clearable]="false"
            ></focile-dropdown>
            <span
              class="text-danger"
              *ngIf="
                formGroup.get('technologies')?.touched &&
                formGroup.get('technologies')?.errors?.required
              "
            >
              Technology is required.
            </span>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="expertise" class="form-label fw-bold"
              >Expertise <span class="required">*</span></label
            >
            <focile-dropdown
              [items]="editExpert.expertiseList"
              [placeholder]="'Select'"
              id="expertise"
              formControlName="expertiseIds"
              [disabled]="formGroup.disabled"
              [loading]="dataLoading"
              [multiple]="true"
              [clearable]="false"
            ></focile-dropdown>
            <span
              class="text-danger"
              *ngIf="
                formGroup.get('expertise')?.touched &&
                formGroup.get('expertise')?.errors?.required
              "
            >
              Expertise is required.
            </span>
          </div>
        </div>
        <!-- <div class="col-md-6">
          <div class="mb-3">
            <label for="technologies" class="form-label fw-bold">
              Level <span class="required">*</span></label
            >
            <focile-dropdown
              [items]="subscriptionLevels"
              [placeholder]="'Select'"
              id="subsciptionLevelId"
              [bindValue]="'id'"
              formControlName="subsciptionLevelId"
              [disabled]="formGroup.disabled"
              [loading]="dataLoading"
              [clearable]="false"
            ></focile-dropdown>
            <span
              class="text-danger"
              *ngIf="
                formGroup.get('subsciptionLevelId')?.touched &&
                formGroup.get('subsciptionLevelId')?.errors?.required
              "
            >
              Expertise is required.
            </span>
          </div>
        </div> -->
      </div>

      <div class="row">
        <div class="col-md-12">
          <div class="mb-3">
            <label for="address" class="form-label"
              >Address <span class="required">*</span></label
            >
            <input
              type="text"
              class="form-control"
              id="address"
              name="address"
              placeholder="Enter your address"
              formControlName="address"
              required
            />
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="country" class="form-label"
              >Country <span class="required">*</span></label
            >
            <focile-dropdown
              [items]="editExpert.countryList"
              [loading]="dataLoading"
              [placeholder]="'Select Country'"
              formControlName="country"
              id="country"
              [bindValue]="'id'"
              (change)="handleCountryChange()"
              [clearable]="false"
            ></focile-dropdown>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="state" class="form-label"
              >State <span class="required">*</span></label
            >
            <focile-dropdown
              [items]="states"
              [placeholder]="'Select State'"
              formControlName="state"
              [bindValue]="'id'"
              id="state"
              (change)="handleStateChange()"
            ></focile-dropdown>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label
              for="city
            "
              class="form-label"
              >City <span class="required">*</span></label
            >
            <focile-dropdown
              [items]="cities"
              [placeholder]="'Select City'"
              formControlName="city"
              [bindValue]="'id'"
              id="city"
            ></focile-dropdown>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="address" class="form-label"
              >Zipcode <span class="required">*</span></label
            >
            <input
              type="text"
              class="form-control"
              id="zipCode"
              placeholder="Enter your zipCode"
              formControlName="zipCode"
              required
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button class="btn btn-secondary" (click)="modalRef.hide()">Cancel</button>
    <button class="btn btn-primary" (click)="updateExpert()">Update</button>
  </div>
</ng-template>
