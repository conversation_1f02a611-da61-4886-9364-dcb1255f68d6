import { Component } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { UtilsService } from 'src/app/shared/services/utils.service';
import { AccountService } from '../../account/services/account.service';
import { ExpertService } from '../services/expert.service';
import { finalize, forkJoin } from 'rxjs';
import { AddUserComponent } from 'src/app/shared/modals/add-user/add-user.component';
import { ModalService } from 'src/app/shared/services/modal.service';

@Component({
  selector: 'app-vendors',
  templateUrl: './vendors.component.html',
  styleUrls: ['./vendors.component.scss'],
})
export class VendorsComponent {
  experts: any[] = [];
  modalRef!: BsModalRef;
  editExpert: any = {};
  formGroup!: FormGroup;
  companyList: any;
  dataLoading = false;
  companySystems: any[] = [];
  isCompanySystem = false;
  showInstallationType: any;
  installationList: any = [];
  solutionItems = [];
  roleItems = [];
  productItems = [];
  servicesItems = [];
  industryItems = [];
  companySize = [];
  technologyItems = [];
  expertise = [];
  subscriptionLevels = [];
  organizationType: any;
  stateLoading!: boolean;
  states: any;
  citiesLoading!: boolean;
  cities: any;
  constructor(
    private expert: ExpertService,
    private bsModalService: BsModalService,
    private formBuilder: FormBuilder,
    private utils: UtilsService,
    private account: AccountService,
    private readonly toaster: ToastrService,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    this.getProducts();
    this.ngInitForm();
  }

  ngInitForm() {
    this.formGroup = this.formBuilder.group({
      firstName: [null, Validators.required],
      phoneNumber: [null, Validators.required],
      email: [null, Validators.required],
      lastName: [null, Validators.required],
      address: [null, Validators.required],
      country: [null, Validators.required],
      state: [null, Validators.required],
      city: [null, Validators.required],
      zipCode: [null, Validators.required],
      userType: [null, Validators.required],
      countryCode: ['+1', Validators.required],
      mobileCountryCode: ['+1', Validators.required],
      workMobileNumber: [null, Validators.required],
      companyName: [null, Validators.required],
      companyId: [null],
      companyWebsite: [null, [Validators.required]],
      workMobileNumer: [null, Validators.required],
      expertId: [null, Validators.required],
      expert: [null, Validators.required],
      companySystemIds: [null, Validators.required],
      solutionIds: [null, Validators.required],
      productIds: [null, Validators.required],
      expertiseIds: [null, Validators.required],
      serviceIds: [null, Validators.required],
      industryIds: [null, Validators.required],
      companySize: [null, Validators.required],
      technologyIds: [null, Validators.required],
      roleId: [null, Validators.required],
      organizationName: [null, Validators.required],
      organizationType: [null, Validators.required],
      subsciptionLevelId: [null, Validators.required],
      typeOfInstallationIds: [null],
    });
  }

  getProducts() {
    this.expert.getExperties(2).subscribe((response: any) => {
      response.data.forEach((expertItem: any) => {
        const {
          firstName,
          lastName,
          address,
          countryName,
          countryCode,
          stateName,
          cityName,
          zipCode,
          isVerified,
          isCompleted,
          createdAt,
          profilePhoto,
          banner,
          createdBy,
          createdByName,
          updatedAt,
          updatedBy,
          updatedByName,
          isActive,
          userType,
          userTypeStr,
          latLong,
          userId,
          organizationName,
          organizationTypeId,
          youtubeLink,
          facebookLink,
          instagramLink,
          twitterLink,
          about,
          phoneNumber,
          email,
          companyImage,
          comments,
          expertDetail,
        } = expertItem;

        const expert = {
          profilePhoto,
          personName: `${firstName} ${lastName}`,
          address: `${address}, ${cityName}, ${stateName}, ${countryName} - ${zipCode}`,
          isVerified,
          email,
          phoneNumber,
          countryName,
          companyImage,
          organizationName,
          organizationTypeId,
          youtubeLink,
          facebookLink,
          instagramLink,
          twitterLink,
          userTypeStr,
          latLong,
          about,
          isCompleted,
          updatedAt,
          userId,
          createdAt,
          banner,
          expertDetail,
          ...expertItem,
        };
        this.experts.push(expert);
      });
    });
  }

  editItem(template: any, item: any) {
    this.formGroup.controls.phoneNumber.setValue(item.phoneNumber);
    const workConfig = this.utils.decodePhoneNumber(
      item.expertDetail.workMobileNumber
    );
    this.formGroup.controls.workMobileNumber.setValue(
      item.expertDetail.workMobileNumber
    );
    this.account.getUserDetails(item.userId).subscribe((response: any) => {
      this.editExpert = response.data;
      let {
        userId,
        companyName,
        companyId,
        companyWebsite,
        companyBanner,
        workMobileNumber,
        expertId,
        subsciptionLevelId,
        subsciptionLevel,
        isShowWorkMobileNumber,
        companySystemIds,
        solutionIds,
        productIds,
        expertiseIds,
        serviceIds,
        companyType,
        industryIds,
        rating,
        typeOfInstallationIds,
        isCertified,
        companySize,
        technologyIds,
        youtubeLink,
        facebookLink,
        instagramLink,
        twitterLink,
        about,
        roleId,
        roleName,
      } = response.data;

      this.formGroup.patchValue({
        firstName: response.data.firstName,
        lastName: response.data.lastName,
        email: response.data.email,
        userId,
        companyName: response.data.expertDetail.companyName,
        companyId,
        companyWebsite: response.data.expertDetail.companyWebsite,
        companyBanner,
        workMobileNumber: response.data.expertDetail.workMobileNumber,
        expertId: response.data.expertDetail.expertId,
        subsciptionLevelId: response.data.expertDetail.subsciptionLevelId,
        subsciptionLevel,
        isShowWorkMobileNumber,
        companySystemIds:
          response.data.expertDetail.companySystemIds?.split(','),
        solutionIds: response.data.expertDetail.solutionIds?.split(','),
        productIds: response.data.expertDetail.productIds?.split(','),
        expertiseIds: response.data.expertDetail.expertiseIds?.split(','),
        serviceIds: response.data.expertDetail.serviceIds?.split(','),
        companyType: response.data.expertDetail.companyType,
        industryIds: response.data.expertDetail.industryIds?.split(','),
        technologyIds: response.data.expertDetail.technologyIds?.split(','),
        rating,
        typeOfInstallationIds,
        isCertified,
        companySize: response.data.expertDetail.companySize,
        youtubeLink,
        facebookLink,
        instagramLink,
        twitterLink,
        about,
        roleId: response.data.expertDetail.roleId,
        roleName,
        address: response.data.address,
        country: response.data.country,
        state: response.data.state,
        city: response.data.city,
        zipCode: response.data.zipCode,
      });
      this.handleCountryChange(false);
      this.handleExpertChange(response.data.expertDetail.expertId, false);
      this.modalRef = this.bsModalService.show(template, {
        class: 'modal-xl',
      });
    });
  }

  initForm() {
    this.formGroup.get('companyWebsite')?.disable();
  }

  getRegistraionData(userType: string) {
    this.dataLoading = true;
    this.account
      .getRegistrationData(userType)
      .pipe(finalize(() => (this.dataLoading = false)))
      .subscribe(
        (response: any) => {
          this.productItems = response.data.productList;
          this.experts = response.data.expertList;
          this.industryItems = response.data.industryList;
          this.productItems = response.data.productList;
          this.servicesItems = response.data.servicesList;
          this.solutionItems = response.data.solutionList;
          this.companySize = response.data.companySizeList;
          this.technologyItems = response.data.technologyList;
          this.roleItems = response.data.roleList;
          this.expertise = response.data.expertiseList;
          this.subscriptionLevels = response.data.subsciptionLevelList;
          this.organizationType = response.data.organizationTypeList;
        },
        (e) => {
          console.error(e);
        }
      );
  }

  handleSearchChange(searchInputValue: string) {
    this.formGroup.get('companyWebsite')?.setValue(searchInputValue);
    if (searchInputValue) {
      this.account
        .searchCompanyWebsite(searchInputValue)
        .subscribe((response: any) => {
          if (response['data']) {
            this.companyList = response.data;
          }
        });
    }
  }

  handleOptionSelection(searchInputValue: any) {
    this.formGroup
      .get('companyWebsite')
      ?.setValue(searchInputValue.description);

    // this.getCompanyDetails(searchInputValue.idGuid, true);
    this.companyList = [];
  }

  handleExpertChange(companyType: any, clearIds = true) {
    if (clearIds) {
      this.formGroup.get('companySystemIds')?.setValue([]);
      this.formGroup.get('companySystemIds')?.markAllAsTouched();
    }

    if (companyType === 4) {
      this.formGroup
        .get('typeOfInstallationIds')
        ?.setValidators([Validators.required]);
    } else {
      this.formGroup.get('typeOfInstallationIds')?.setValidators([]);
    }

    this.showInstallationType = companyType === 4;

    const promises = [this.account.getTypeOfExpert(companyType)];
    if (this.showInstallationType) {
      promises.push(this.account.getInstallationList());
    }

    forkJoin(promises)
      .pipe(finalize(() => (this.dataLoading = false)))
      .subscribe(([typeofExpertList, installationList]) => {
        if (typeofExpertList) {
          const typeOfExpertList: any = typeofExpertList;
          this.companySystems = typeOfExpertList.data;
        }
        if (installationList) {
          const companyTypeList: any = installationList;
          this.installationList = companyTypeList.data;
        }
      });

    this.isCompanySystem = true;
    this.account
      .getTypeOfExpert(companyType)
      .pipe(finalize(() => (this.isCompanySystem = false)))
      .subscribe((response: any) => {
        this.companySystems = response.data;
        // if (disbleForm) {
        //   this.formGroup.disable();
        //   this.formGroup.get('roles')?.enable();
        // }
      });
  }

  updateExpert() {
    let updatedUser: any = this.formGroup.value;
    const userId = this.editExpert.userId;
    updatedUser.organizationType = this.utils.getEmptyGuid();
    Object.keys(updatedUser).forEach((key: string) => {
      if (Array.isArray(updatedUser[key])) {
        updatedUser[key] = updatedUser[key].toString();
      }
    });

    updatedUser.id = userId;
    updatedUser.companyId = this.editExpert.expertDetail.companyId;
    updatedUser.userType = '2';
    delete updatedUser.expertDetail;

    this.account.updateUserFromAdmin(updatedUser).subscribe((response: any) => {
      this.modalRef.hide();
      this.ngOnInit();
      this.experts = [];
      if (response.messageType) {
        return this.toaster.success(response.message);
      }
      return this.toaster.success(response.message);
    });
  }

  handleCountryChange(clearFields = true) {
    if (clearFields) {
      // this.formGroup.value.state.setValue(null);
      this.formGroup.value.city.setValue(null);
    }
    this.stateLoading = true;
    this.account
      .getStates(this.formGroup.controls.country.value)
      .pipe(finalize(() => (this.stateLoading = false)))
      .subscribe((stateResponse: any) => {
        if (stateResponse.message) {
          return console.error('unable to fetch states');
        }
        this.states = stateResponse.data;
        this.handleStateChange(false);
      });
  }

  handleStateChange(clearcity = true) {
    if (clearcity) {
      this.formGroup.controls.city.setValue(null);
    }
    this.citiesLoading = true;
    this.account
      .getCities(this.formGroup.controls.state.value)
      .pipe(finalize(() => (this.citiesLoading = false)))
      .subscribe((stateResponse: any) => {
        if (stateResponse.message) {
          return console.error('unable to fetch states');
        }
        this.cities = stateResponse.data;
      });
  }

  addUser() {
    this.modalService.openModal('add-user', {
      initialState: {
        memberType: 'vendor'
      },
      class: 'modal-xl',
    });
  }
}
