<div class="fc-account-detail-content">
  <h5 class="fw-bold mb-4 mt-2">General Information</h5>

  <div class="company-profile-log">
    <label>Company Profile Logo</label>
    <div class="position-relative company-logo-banner" style="background: #********; width: 100px; height: 100px">
      <img [src]="profileImage" class="" style="width: 100%; height: 100%" alt="Avatar" tooltip="company logo"
        placement="right" viewType="company" appImg />
      <span class="company-logo" (click)="updateProfile()" role="button" tooltip="Change profile picture">
        <i class="fa fa-camera"></i>
      </span>
    </div>
    <input type="file" accept="image/png, image/gif, image/jpeg" name="profile" class="d-none" id="profile" #profile
      (change)="uploadProfileImage($event)" />
  </div>

  <a class="list-group-item p-0 list-group-item-action" aria-current="true">
    <label class="fc-company-banner-label">Company Banner</label>
    <div class="text-center">
      <div class="position-relative banner-wrapper pb-3" [ngStyle]="{
          'background-image':
            'url(' + selectedBanner || './assets/svgs/bg.svg' + ')'
        }" style="background-size: cover">
        <!-- <div class="position-relative company-logo-banner" style="background: #********;  width: 100px; height: 100px;"> 
          <img
            [src]="profileImage"
            class="mb-3 mt-3"
            style="width: 84px; height: auto"
            alt="Avatar"
            tooltip="company logo"
            placement="right"
            viewType="company"
            appImg
          />
          <span          
            class="company-logo"
            (click)="updateProfile()"
            role="button"
            tooltip="Change profile picture"          
          >
            <i class="fa fa-camera"></i>
          </span>
        </div> -->
        <h5 class="mb-2 text-white">
          <strong>
            {{ userState$.companyName }}
          </strong>
        </h5>
      </div>
      <div class="user-icon position-absolute" style="right: 0.5rem; bottom: 0.5rem" *ngIf="userState$?.userType == 3">
        <button (click)="uploadBanner()" tooltip="Change company banner" placement="left" class="change-btn">
          <i class="fa fa-pen"></i>
        </button>
        <input type="file" class="hide d-none" #bannerFile (change)="uploadBannerImage($event)" />
      </div>
    </div>
  </a>

  <!-- ...existing code... -->
  <label class="fc-ecard-banner-label">eCard Banner</label>
  <div class="text-center position-relative">
    <div class="position-relative banner-wrapper pb-3" [ngStyle]="{
        'background-image':
          'url(' + selectedEcardBanner || './assets/svgs/bg.svg' + ')'
      }" style="background-size: cover">
      <h5 class="mb-2 text-white">
        <strong>
          {{ userState$.companyName }}
        </strong>
      </h5>
    </div>
    <div class="user-icon position-absolute" style="right: 0.5rem; bottom: 0.5rem" *ngIf="userState$?.userType == 3">
      <button (click)="uploadEcardBanner()" tooltip="Change eCard banner" placement="left" class="change-btn">
        <i class="fa fa-pen"></i>
      </button>
      <input type="file" class="hide d-none" #ecardBannerFile (change)="uploadEcardBannerImage($event)" />
    </div>
  </div>
  <!-- ...existing code... -->

  <div class="mt-4" *ngIf="!dataLoading">
    <ul class="category-navbar-item gap-5 gap-sm-4">
      <li class="nav-item" role="button" (click)="setActiveTab('companyDetails'); changeTab(1)"
        [class.active]="activeTab === 'companyDetails'">
        <a class="" aria-current="page">
          <span>
            <svg width="13" height="12" viewBox="0 0 13 12" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M6.62109 0.00976562C3.36797 0.00976562 0.714844 2.66289 0.714844 5.91602C0.714844 9.16914 3.36797 11.8223 6.62109 11.8223C9.87422 11.8223 12.5273 9.16914 12.5273 5.91602C12.5273 2.66289 9.87422 0.00976562 6.62109 0.00976562ZM6.62109 11.0723C3.78047 11.0723 1.46484 8.75664 1.46484 5.91602C1.46484 3.07539 3.78047 0.759766 6.62109 0.759766C9.46172 0.759766 11.7773 3.07539 11.7773 5.91602C11.7773 8.75664 9.46172 11.0723 6.62109 11.0723Z"
                fill="#014681" />
              <path d="M6.99609 5.02539H6.24609V8.77539H6.99609V5.02539Z" fill="#014681" />
              <path d="M6.99609 3.24414H6.24609V3.99414H6.99609V3.24414Z" fill="#014681" />
            </svg>
          </span>
          Company Details
        </a>
      </li>
      <li class="nav-item" role="button" (click)="setActiveTab('following'); changeTab(2)"
        [class.active]="activeTab === 'following'">
        <a class="" aria-current="page">
          <span>
            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g clip-path="url(#clip0_774_6418)">
                <path d="M14 6.125H12.25V4.375H11.375V6.125H9.625V7H11.375V8.75H12.25V7H14V6.125Z" fill="#014681" />
                <path
                  d="M5.25 1.75C6.4575 1.75 7.4375 2.73 7.4375 3.9375C7.4375 5.145 6.4575 6.125 5.25 6.125C4.0425 6.125 3.0625 5.145 3.0625 3.9375C3.0625 2.73 4.0425 1.75 5.25 1.75ZM5.25 0.875C3.55687 0.875 2.1875 2.24437 2.1875 3.9375C2.1875 5.63062 3.55687 7 5.25 7C6.94312 7 8.3125 5.63062 8.3125 3.9375C8.3125 2.24437 6.94312 0.875 5.25 0.875Z"
                  fill="#014681" />
                <path
                  d="M9.625 13.125H8.75V10.9375C8.75 9.73 7.77 8.75 6.5625 8.75H3.9375C2.73 8.75 1.75 9.73 1.75 10.9375V13.125H0.875V10.9375C0.875 9.24875 2.24875 7.875 3.9375 7.875H6.5625C8.25125 7.875 9.625 9.24875 9.625 10.9375V13.125Z"
                  fill="#014681" />
              </g>
              <defs>
                <clipPath id="clip0_774_6418">
                  <rect width="14" height="14" fill="white" />
                </clipPath>
              </defs>
            </svg>
          </span>
          Following
        </a>
      </li>
      <li class="nav-item" role="button" (click)="setActiveTab('followers'); changeTab(3)"
        [class.active]="activeTab === 'followers'">
        <a class="" aria-current="page">
          <span>
            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g clip-path="url(#clip0_774_6433)">
                <path
                  d="M4.25 1.75293C5.4575 1.75293 6.4375 2.73293 6.4375 3.94043C6.4375 5.14793 5.4575 6.12793 4.25 6.12793C3.0425 6.12793 2.0625 5.14793 2.0625 3.94043C2.0625 2.73293 3.0425 1.75293 4.25 1.75293ZM4.25 0.87793C2.55687 0.87793 1.1875 2.2473 1.1875 3.94043C1.1875 5.63355 2.55687 7.00293 4.25 7.00293C5.94312 7.00293 7.3125 5.63355 7.3125 3.94043C7.3125 2.2473 5.94312 0.87793 4.25 0.87793Z"
                  fill="#014681" />
                <path
                  d="M8.625 13.1279H7.75V10.9404C7.75 9.73293 6.77 8.75293 5.5625 8.75293H2.9375C1.73 8.75293 0.75 9.73293 0.75 10.9404V13.1279H-0.125V10.9404C-0.125 9.25168 1.24875 7.87793 2.9375 7.87793H5.5625C7.25125 7.87793 8.625 9.25168 8.625 10.9404V13.1279Z"
                  fill="#014681" />
                <path
                  d="M10.5055 8.43921C10.4549 8.4395 10.4047 8.42986 10.3579 8.41083C10.3111 8.3918 10.2685 8.36376 10.2326 8.32832L8.69507 6.79874C8.62269 6.72673 8.58203 6.62907 8.58203 6.52724C8.58203 6.4254 8.62269 6.32774 8.69507 6.25574C8.76745 6.18373 8.86562 6.14328 8.96798 6.14328C9.07034 6.14328 9.1685 6.18373 9.24088 6.25574L10.5055 7.51764L12.9232 5.10855C12.9956 5.03655 13.0937 4.99609 13.1961 4.99609C13.2984 4.99609 13.3966 5.03655 13.469 5.10855C13.5414 5.18056 13.582 5.27822 13.582 5.38005C13.582 5.48189 13.5414 5.57955 13.469 5.65155L10.7784 8.32832C10.7425 8.36376 10.6999 8.3918 10.653 8.41083C10.6062 8.42986 10.5561 8.4395 10.5055 8.43921Z"
                  fill="#014681" />
              </g>
              <defs>
                <clipPath id="clip0_774_6433">
                  <rect width="14" height="14" fill="white" transform="translate(0 0.00292969)" />
                </clipPath>
              </defs>
            </svg>
          </span>
          Followers
        </a>
      </li>
      <li class="nav-item" role="button" (click)="setActiveTab('experts'); changeTab(4)"
        [class.active]="activeTab === 'experts'">
        <a class="" aria-current="page">
          <span>
            <svg width="13" height="12" viewBox="0 0 13 12" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g clip-path="url(#clip0_774_6448)">
                <path
                  d="M4.37562 4.00146C4.07892 4.00159 3.78885 3.91372 3.5421 3.74897C3.29534 3.58422 3.10299 3.35 2.98936 3.07592C2.87573 2.80184 2.84594 2.50022 2.90375 2.20921C2.96155 1.9182 3.10437 1.65087 3.31412 1.44102C3.52388 1.23118 3.79115 1.08826 4.08214 1.03033C4.37313 0.972405 4.67476 1.00207 4.94888 1.11559C5.22301 1.2291 5.45731 1.42136 5.62216 1.66805C5.78701 1.91473 5.875 2.20477 5.875 2.50147C5.87451 2.89903 5.7164 3.28018 5.43534 3.56136C5.15427 3.84254 4.77319 4.00081 4.37562 4.00146ZM4.37562 1.62218C4.20154 1.62205 4.03133 1.67357 3.88654 1.77021C3.74174 1.86685 3.62886 2.00426 3.56219 2.16507C3.49551 2.32588 3.47804 2.50285 3.51197 2.67359C3.5459 2.84434 3.62972 3.00118 3.75281 3.12427C3.87591 3.24737 4.03275 3.33119 4.20349 3.36512C4.37423 3.39905 4.55121 3.38157 4.71201 3.3149C4.87282 3.24822 5.01024 3.13534 5.10688 2.99055C5.20351 2.84575 5.25503 2.67555 5.25491 2.50147C5.25458 2.26836 5.16184 2.0449 4.99701 1.88008C4.83218 1.71525 4.60872 1.6225 4.37562 1.62218Z"
                  fill="#014681" />
                <path
                  d="M8.87567 5.00146C8.48011 5.0016 8.09339 4.88443 7.76442 4.66478C7.43545 4.44513 7.179 4.13286 7.0275 3.76745C6.876 3.40205 6.83626 2.99993 6.9133 2.61195C6.99034 2.22396 7.1807 1.86753 7.46031 1.58773C7.73992 1.30793 8.09622 1.11733 8.48416 1.04003C8.8721 0.962726 9.27424 1.0022 9.63975 1.15345C10.0052 1.3047 10.3177 1.56094 10.5376 1.88977C10.7574 2.21859 10.8749 2.60523 10.875 3.00079C10.8746 3.53106 10.6639 4.03953 10.2891 4.41461C9.91427 4.7897 9.40594 5.00075 8.87567 5.00146ZM8.87567 1.67531C8.61317 1.67518 8.35652 1.75292 8.13821 1.89869C7.91989 2.04446 7.74973 2.25172 7.64924 2.49423C7.54875 2.73674 7.52246 3.00361 7.57369 3.26107C7.62492 3.51853 7.75136 3.75501 7.93703 3.94058C8.1227 4.12616 8.35924 4.25249 8.61673 4.30358C8.87421 4.35468 9.14107 4.32825 9.38353 4.22764C9.62599 4.12703 9.83316 3.95676 9.97882 3.73837C10.1245 3.51999 10.2021 3.2633 10.2018 3.00079C10.2011 2.64936 10.0611 2.31253 9.81258 2.06409C9.56401 1.81564 9.22711 1.67585 8.87567 1.67531Z"
                  fill="#014681" />
                <path
                  d="M2.17312 9.00146C2.09406 9.00146 2.01823 8.96388 1.96232 8.89697C1.90641 8.83007 1.875 8.73932 1.875 8.6447V6.72676C1.875 5.75922 2.87491 5.00146 4.15088 5.00146C4.74713 5.00146 5.30761 5.16914 5.73035 5.47239C5.76394 5.49656 5.79322 5.52842 5.81652 5.56614C5.83983 5.60385 5.85669 5.6467 5.86616 5.69221C5.87563 5.73773 5.87751 5.78504 5.8717 5.83143C5.8659 5.87781 5.85251 5.92238 5.83231 5.96258C5.8121 6.00278 5.78548 6.03782 5.75397 6.0657C5.72245 6.09359 5.68665 6.11378 5.64861 6.12511C5.61057 6.13644 5.57104 6.13869 5.53228 6.13174C5.49351 6.12479 5.45627 6.10877 5.42268 6.08459C5.09653 5.84984 4.63325 5.71499 4.15088 5.71499C3.16111 5.71499 2.47125 6.24799 2.47125 6.72676V8.6447C2.47125 8.73932 2.43984 8.83007 2.38393 8.89697C2.32802 8.96388 2.25219 9.00146 2.17312 9.00146Z"
                  fill="#014681" />
                <path
                  d="M11.5606 10.0015C11.4772 10.0015 11.3972 9.96347 11.3383 9.89583C11.2793 9.8282 11.2462 9.73647 11.2462 9.64082V7.1437C11.2462 6.45775 10.2936 5.72276 8.875 5.72276C7.45643 5.72276 6.5038 6.45775 6.5038 7.1437V9.64082C6.5038 9.73647 6.47068 9.8282 6.41171 9.89583C6.35275 9.96347 6.27278 10.0015 6.1894 10.0015C6.10602 10.0015 6.02605 9.96347 5.96709 9.89583C5.90812 9.8282 5.875 9.73647 5.875 9.64082V7.1437C5.875 5.94275 7.19548 5.00146 8.875 5.00146C10.5545 5.00146 11.875 5.94275 11.875 7.1437V9.64082C11.875 9.68818 11.8669 9.73508 11.8511 9.77883C11.8353 9.82259 11.8121 9.86235 11.7829 9.89583C11.7537 9.92932 11.7191 9.95589 11.6809 9.97401C11.6428 9.99214 11.6019 10.0015 11.5606 10.0015Z"
                  fill="#014681" />
                <path
                  d="M11.5447 12.0015H2.20529C2.1177 12.0015 2.03368 11.9488 1.97174 11.855C1.9098 11.7612 1.875 11.6341 1.875 11.5015C1.875 11.3689 1.9098 11.2417 1.97174 11.1479C2.03368 11.0541 2.1177 11.0015 2.20529 11.0015H11.5447C11.6323 11.0015 11.7163 11.0541 11.7783 11.1479C11.8402 11.2417 11.875 11.3689 11.875 11.5015C11.875 11.6341 11.8402 11.7612 11.7783 11.855C11.7163 11.9488 11.6323 12.0015 11.5447 12.0015Z"
                  fill="#014681" />
              </g>
              <defs>
                <clipPath id="clip0_774_6448">
                  <rect width="12" height="12" fill="white" transform="translate(0.875 0.00146484)" />
                </clipPath>
              </defs>
            </svg>
          </span>
          Add Experts
        </a>
      </li>
    </ul>

    <div class="fc-tab-content">
      <div *ngIf="activeTab === 'companyDetails'">
        <div>
          <div [formGroup]="userForm" class="tab-content p-0" id="nav-tabContent">
            <div class="tab-pane fade show active" id="nav-home" role="tabpanel" aria-labelledby="nav-home-tab">
              <h5 class="fw-bold my-4">Company Details</h5>
              <div [formGroup]="userForm" class="">
                <div class="row">
                  <div class="col-md-6">
                    <div class="mb-4">
                      <label for="companyName" class="form-label">Company name <span class="required">*</span></label>
                      <input type="text" class="form-control" id="companyName" formControlName="companyName"
                        placeholder="Type here" [ngClass]="{
                          'is-invalid':
                            userForm.get('companyName')?.hasError('required') &&
                            userForm.get('companyName')?.touched
                        }" />
                      <ng-container [ngTemplateOutlet]="errorTemplate" [ngTemplateOutletContext]="{
                          field: 'Company Name',
                          control: userForm.get('companyName')
                        }"></ng-container>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="mb-4">
                      <label for="exampleFormControlInput1" class="form-label">Company Website</label>
                      <focile-dropdown [bindValue]="'description'" [bindLabel]="'description'"
                        formControlName="companyWebsite" [clearable]="false" [readonly]="true"
                        [items]="companyList"></focile-dropdown>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="mb-4">
                      <label for="export" class="form-label">Channel Group <span class="required">*</span></label>
                      <focile-dropdown formControlName="expert" [bindValue]="'id'" [items]="companyTypeList"
                        formControlName="expertId" (change)="handleCompanyTypeChange($event)"
                        [readonly]="false"></focile-dropdown>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="mb-4">
                      <label for="mobile" class="form-label">Group Type <span class="required">*</span></label>
                      <focile-dropdown [placeholder]="'Select'" id="focileDropdown" formControlName="companySystemIds"
                        [items]="typeOfExpertList" [multiple]="true" [showOptions]="3"
                        [loading]="dataLoading"></focile-dropdown>
                      <ng-container [ngTemplateOutlet]="errorTemplate" [ngTemplateOutletContext]="{
                          field: 'Group Type',
                          control: userForm.get('companySystemIds')
                        }"></ng-container>
                    </div>
                  </div>
                  <!-- <div *ngIf="showInstallationType" class="col-md-6">
                  <div class="mb-4">
                    <label for="mobile" class="form-label fw-bold">Type of installation <span
                        class="required">*</span></label>
                    <focile-dropdown [placeholder]="'Select'" id="focileDropdown" formControlName="typeOfInstallationIds"
                      [items]="typeOfInstallationList" [multiple]="true" [showOptions]="3"
                      [loading]="dataLoading"></focile-dropdown>
                    <ng-container [ngTemplateOutlet]="errorTemplate" [ngTemplateOutletContext]="{
                        field: 'Installation',
                        control: userForm.get('typeOfInstallationIds')
                      }"></ng-container>
                  </div>
                </div> -->
                  <div class="col-md-6">
                    <div class="mb-4">
                      <label for="experties" class="form-label">Expertise <span class="required">*</span></label>
                      <focile-dropdown [placeholder]="'Select'" id="focileDropdown" formControlName="expertiseIds"
                        [items]="expertiseList" [multiple]="true" [showOptions]="3"></focile-dropdown>
                      <ng-container [ngTemplateOutlet]="errorTemplate" [ngTemplateOutletContext]="{
                          field: 'Expertise',
                          control: userForm.get('expertiseIds')
                        }"></ng-container>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="mb-4">
                      <label for="solutions" class="form-label">Solution <span class="required">*</span></label>
                      <focile-dropdown [placeholder]="'Select'" id="solutions" [items]="solutionList"
                        formControlName="solutionIds" [multiple]="true"></focile-dropdown>
                      <ng-container [ngTemplateOutlet]="errorTemplate" [ngTemplateOutletContext]="{
                          field: 'Solution',
                          control: userForm.get('solutionIds')
                        }"></ng-container>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="mb-4">
                      <label for="products" class="form-label">Products <span class="required">*</span></label>
                      <focile-dropdown [placeholder]="'Select'" id="products" [items]="productList"
                        formControlName="productIds" [multiple]="true"></focile-dropdown>
                      <ng-container [ngTemplateOutlet]="errorTemplate" [ngTemplateOutletContext]="{
                          field: 'Products',
                          control: userForm.get('productIds')
                        }"></ng-container>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="mb-4">
                      <label for="services" class="form-label">Services <span class="required">*</span></label>
                      <focile-dropdown [placeholder]="'Select'" id="services" [multiple]="true" [items]="servicesList"
                        formControlName="serviceIds"></focile-dropdown>
                      <ng-container [ngTemplateOutlet]="errorTemplate" [ngTemplateOutletContext]="{
                          field: 'Services',
                          control: userForm.get('serviceIds')
                        }"></ng-container>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="mb-4">
                      <label for="industry" class="form-label">Industry <span class="required">*</span></label>
                      <focile-dropdown [placeholder]="'Select'" id="industry" [multiple]="true"
                        formControlName="industryIds" [items]="industryList"></focile-dropdown>
                      <ng-container [ngTemplateOutlet]="errorTemplate" [ngTemplateOutletContext]="{
                          field: 'Industry',
                          control: userForm.get('industryIds')
                        }"></ng-container>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="mb-4">
                      <label for="companySize" class="form-label">Company Size <span class="required">*</span></label>
                      <focile-dropdown [placeholder]="'Select'" id="companySize" [bindValue]="'id'"
                        formControlName="companySize" placeholder="Select" [items]="companySizeList"></focile-dropdown>
                    </div>
                  </div>
                </div>
              </div>
              <div class="tab-pane fade show mt-3" id="nav-home" role="tabpanel" aria-labelledby="nav-home-tab">
                <ng-container formArrayName="usercontacts">
                  <ng-container *ngFor="
                      let linkControl of userContacts.controls;
                      let i = index
                    " [formGroupName]="i">
                    <h5 class="fw-bold mt-4 mb-4">Address {{ i + 1 }}</h5>
                    <div class="">
                      <div class="row">
                        <div class="col-md-12">
                          <div class="mb-3">
                            <label for="address" class="form-label">Address <span class="required">*</span></label>
                            <input type="text" class="form-control" formControlName="address" id="address"
                              placeholder="Type here" [ngClass]="{
                                'is-invalid':
                                  linkControl.get('address')?.touched &&
                                  linkControl
                                    .get('address')
                                    ?.hasError('required')
                              }" />
                            <ng-container [ngTemplateOutlet]="errorTemplate" [ngTemplateOutletContext]="{
                                field: 'Address',
                                control: linkControl.get('address')
                              }"></ng-container>
                          </div>
                        </div>
                        <ng-container *ngIf="editAddressIndex === i">
                          <div class="col-md-6">
                            <div class="mb-4">
                              <label for="country" class="form-label">Country <span class="required">*</span></label>
                              <focile-dropdown [bindValue]="'id'" [items]="countryList" formControlName="country"
                                placeholder="Select a country" [clearable]="false" [container]="'null'"
                                [loading]="statesLoading" (change)="handleCountryChange($event, i)"></focile-dropdown>
                              <ng-container [ngTemplateOutlet]="errorTemplate" [ngTemplateOutletContext]="{
                                  field: 'Country',
                                  control: linkControl.get('country')
                                }"></ng-container>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="mb-4">
                              <label for="state" class="form-label">State <span class="required">*</span></label>
                              <focile-dropdown id="state" formControlName="state" placeholder="Select a state"
                                [bindValue]="'id'" [clearable]="false" [loading]="statesLoading"
                                (change)="handleStateChange($event)" [items]="states"></focile-dropdown>
                              <ng-container [ngTemplateOutlet]="errorTemplate" [ngTemplateOutletContext]="{
                                  field: 'State',
                                  control: linkControl.get('state')
                                }"></ng-container>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="mb-4">
                              <label for="city" class="form-label">City <span class="required">*</span></label>
                              <focile-dropdown id="city" formControlName="city" placeholder="Select a city"
                                [clearable]="false" [loading]="citiesLoading" [bindValue]="'id'"
                                [items]="cityList"></focile-dropdown>
                              <ng-container [ngTemplateOutlet]="errorTemplate" [ngTemplateOutletContext]="{
                                  field: 'City',
                                  control: linkControl.get('city')
                                }"></ng-container>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="mb-4">
                              <label for="zipcode" class="form-label">Zipcode <span class="required">*</span>
                              </label>
                              <input type="text" class="form-control" id="zipCode" formControlName="zipCode"
                                placeholder="Type here" [ngClass]="{
                                  'is-invalid':
                                    linkControl
                                      .get('zipCode')
                                      ?.hasError('required') &&
                                    linkControl.get('zipCode')?.touched
                                }" />
                              <ng-container [ngTemplateOutlet]="errorTemplate" [ngTemplateOutletContext]="{
                                  field: 'Zipcode',
                                  control: linkControl.get('zipCode')
                                }"></ng-container>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="mb-4">
                              <label for="orgname" class="form-label">Work Phone No.<span
                                  class="required">*</span></label>
                              <span class="d-flex gap-3">
                                <span class="w-150">
                                  <focile-dropdown [items]="countryList" formControlName="workMobileNumberCountryCode"
                                    [bindValue]="'description'" [bindLabel]="'description'" [loading]="dataLoading"
                                    [clearable]="false"></focile-dropdown>
                                </span>
                                <div class="w-100">
                                  <input type="text" class="form-control" id="orgname"
                                    formControlName="workMobileNumber" placeholder="(xxx) xxx.xxxx" [ngClass]="{
                                      'is-invalid':
                                        linkControl
                                          .get('workMobileNumber')
                                          ?.hasError('workMobileNumber') &&
                                        linkControl.get('workMobileNumber')
                                          ?.touched
                                    }" />
                                  <ng-container [ngTemplateOutlet]="errorTemplate" [ngTemplateOutletContext]="{
                                      field: 'Mobile Number',
                                      control:
                                        linkControl.get('workMobileNumber')
                                    }"></ng-container>
                                </div>
                              </span>
                            </div>
                          </div>
                        </ng-container>
                        <div class="col-md-12 d-flex gap-2" *ngIf="isAdmin">
                          <button *ngIf="userContacts.controls.length == i + 1" class="add-btn round-btn"
                            (click)="addAddress()" tooltip="Add New Address" [disabled]="linkControl.invalid">
                            <i class="fa fa-plus"></i>
                          </button>
                          <button *ngIf="
                              userContacts.controls.length > 1 &&
                              !linkControl.value.isPrimary
                            " class="remove-btn round-btn" (click)="removeAddress(i)" tooltip="Remove this address">
                            <i class="fa fa-minus"></i>
                          </button>
                          <button *ngIf="editAddressIndex !== i" class="edit-btn round-btn" (click)="editAddress(i)"
                            tooltip="Edit address">
                            <i class="fa fa-pen"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                  </ng-container>
                </ng-container>
                <div class="mt-4">
                  <label for="company Description" class="form-label">Company Description</label>
                  <div class="">
                    <div class="row">
                      <div class="col-md-12">
                        <textarea class="form-control" placeholder="Type here" id="about" formControlName="about"
                          style="height: 100px"></textarea>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="mt-4">
                  <label for="company Value Proposition" class="form-label">Company Value Proposition</label>
                  <div class="">
                    <div class="row">
                      <div class="col-md-12">
                        <textarea class="form-control" placeholder="Type here" id="valueProposition"
                          formControlName="companyValueProposition" style="height: 100px"></textarea>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="mt-4">
                  <label for="message To Audience" class="form-label">Message To Audience</label>
                  <div class="">
                    <div class="row">
                      <div class="col-md-12">
                        <textarea class="form-control" placeholder="Type here" id="messageToAudience"
                          style="height: 100px" formControlName="companyMessageToAudience"></textarea>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="mt-4 elvator-pitch-card">
                  <h5 class="fw-medium mb-4">
                    Company Landing Page Elevator Pitch
                  </h5>
                  <div class="mt-3">
                    <div class="alert alert-warning alert-dismissible mb-4 fade show" role="alert">
                      <strong>Note!</strong> The approved elevator pitch is not
                      subject to modification.
                      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <div class="row">
                      <div class="col-sm-12">
                        <label for="elevatorPitch"
                          class="mb-2 d-flex align-items-center gap-2 justify-content-start">Elevator Pitch
                          <span tooltip="Video length should be less then or equal to 30 seconds" class="text-primary">
                            <img src="../../../../assets/svgs/info.svg" />
                          </span>
                        </label>
                      </div>
                      <div class="col-sm-12 d-flex align-items-center justify-content-start gap-3">
                        <div class="col-md-10">
                          <div class="form-group">
                            <input class="form-control h-auto" placeholder="Paste your link here"
                              formControlName="ElevatorPitchVideo" id="elevatorPitchVideo" type="text" />
                            <div class="text-danger" *ngIf="
                                userForm.get('ElevatorPitchVideo')?.errors
                                  ?.pattern
                              ">
                              Not valid link.
                            </div>
                          </div>
                        </div>
                        <div class="col-md-2 d-flex align-items-start gap-2">
                          <ng-container *ngIf="
                              elevatorPitchConfig.status ==
                                approvePostStatusEnum.Pendding &&
                              userForm.get('ElevatorPitchVideo')?.value
                            ">
                            <focile-button btnClass="btn-outline-warning" [btnType]="null" tooltip="Approval Pending">
                              <i class="fa fa-clock"></i>
                            </focile-button>
                          </ng-container>
                          <ng-container *ngIf="
                              elevatorPitchConfig.status ==
                              approvePostStatusEnum.Approved
                            ">
                            <button *ngIf="
                                userForm.get('ElevatorPitchVideo')?.disabled
                              " class="right-btn round-btn" tooltip="This is a approved elevator pitch video">
                              <i class="fa fa-check"></i>
                            </button>
                            <button *ngIf="
                                userForm.get('ElevatorPitchVideo')?.disabled
                              " class="edit-btn round-btn"
                              tooltip="New Elevator Pitch needs to be approved by focile platform"
                              (click)="enableElevatorPitch()">
                              <i class="fa fa-pen"></i>
                            </button>

                            <focile-button btnClass="btn-outline-danger" [btnType]="null" *ngIf="
                                userForm.get('ElevatorPitchVideo')?.enabled
                              " tooltip="Cancel" (onClick)="disableElevatorPitch()">
                              <i class="fa fa-times"></i>
                            </focile-button>
                          </ng-container>
                          <div class="ms-2">
                            <focile-button type="submit" [loading]="savingElevatorPitch" [disabled]="
                                userForm.get('ElevatorPitchVideo')?.errors
                                  ?.pattern ||
                                savingElevatorPitch ||
                                !userForm.get('ElevatorPitchVideo')?.value
                              " (onClick)="saveElevatorPitch()" *ngIf="
                                userForm.get('ElevatorPitchVideo')?.enabled
                              ">
                              <i class="fa fa-check"></i>
                            </focile-button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="mt-4 fc-video-presentation">
                  <h5 class="fw-medium mb-4">
                    Company Profile Page Video Presentation
                  </h5>
                  <div class="">
                    <div class="row">
                      <ng-container formArrayName="userPosts">
                        <ng-container *ngFor="
                            let linkControl of userPosts.controls;
                            let i = index
                          " [formGroupName]="i">
                          <div class="col-sm-12">
                            <label for="youtube" *ngIf="i == 0">Main Video Presentaion</label>
                          </div>
                          <div class="col-sm-12 d-flex flex-row align-items-center justify-content-start gap-3">
                            <div class="col-md-10 mt-2">
                              <div class="form-group">
                                <input class="form-control" placeholder="Type here" formControlName="path"
                                  type="text" />
                                <div class="text-danger" *ngIf="
                                    userPosts.controls[i].get('path')?.errors
                                      ?.pattern
                                  ">
                                  Not valid link.
                                </div>
                              </div>
                            </div>
                            <div class="col-md-2 d-flex align-items-end gap-2 mt-3">
                              <button *ngIf="userPosts.controls.length > 1" class="remove-btn round-btn"
                                tooltip="Remove link" (click)="removeLink(i)">
                                <i class="fa fa-minus"></i>
                              </button>
                              <button *ngIf="userPosts.controls.length == i + 1" class="add-btn round-btn"
                                (click)="addLink()" tooltip="Add link" [disabled]="
                                  userPosts.controls[i].get('path')?.errors
                                    ?.pattern
                                ">
                                <i class="fa fa-plus"></i>
                              </button>
                            </div>
                          </div>
                        </ng-container>
                      </ng-container>
                    </div>
                  </div>
                </div>
                <div class="mt-5">
                  <h5 class="fw-bold mb-3">Social Network</h5>
                  <div class="">
                    <div class="row">
                      <div class="alert alert-warning alert-dismissible mb-4 fade show" role="alert">
                      <strong>Note!</strong> Your basic  plan is limited to 1 social network links. If you want to add more social network links, please upgrade your plan.
                      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                      <div class="col-md-12 social-network-wrap">
                        <div class="form-group">
                          <label for="linkedInLink" class="mb-2">LinkedIn</label>
                          <input class="form-control" placeholder="Type here" formControlName="linkedInLink"
                            type="text" />
                          <div class="text-danger" *ngIf="userForm.get('linkedInLink')?.errors?.pattern">
                            Not valid LinkedIn link.
                          </div>
                        </div>

                        <div class="form-group">
                          <label for="facebookLink" class="mb-2">Facebook</label>
                          <input class="form-control" placeholder="Type here" formControlName="facebookLink"
                            type="text" />
                          <div class="text-danger" *ngIf="userForm.get('facebookLink')?.errors?.pattern">
                            Not valid Facebook link.
                          </div>
                        </div>

                        <div class="form-group">
                          <label for="twitterLink" class="mb-2">Twitter</label>
                          <input class="form-control" placeholder="Type here" formControlName="twitterLink"
                            type="text" />
                          <div class="text-danger" *ngIf="userForm.get('twitterLink')?.errors?.pattern">
                            Not valid Twitter link.
                          </div>
                        </div>

                        <div class="form-group">
                          <label for="instagramLink" class="mb-2">Instagram</label>
                          <input class="form-control" placeholder="Type here" formControlName="instagramLink"
                            type="text" />
                          <div class="text-danger" *ngIf="userForm.get('instagramLink')?.errors?.pattern">
                            Not valid Instagram link.
                          </div>
                        </div>

                        <div class="form-group">
                          <label for="pinterestLink" class="mb-2">Pinterest</label>
                          <input class="form-control" placeholder="Type here" formControlName="pinterestLink"
                            type="text" />
                          <div class="text-danger" *ngIf="userForm.get('pinterestLink')?.errors?.pattern">
                            Not valid Pinterest link.
                          </div>
                        </div>

                        <div class="form-group">
                          <label for="youtubeLink" class="mb-2">YouTube Channel Link</label>
                          <input class="form-control" placeholder="Type here" formControlName="youtubeLink"
                            type="text" />
                          <div class="text-danger" *ngIf="userForm.get('youtubeLink')?.errors?.pattern">
                            Not valid YouTube link.
                          </div>
                        </div>
                      </div>

                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="tab-pane fade" id="nav-profile" role="tabpanel" aria-labelledby="nav-profile-tab" Profile></div>
            <div class="tab-pane fade" id="nav-contact" role="tabpanel" aria-labelledby="nav-contact-tab">
              Contact
            </div>
          </div>
        </div>
        <div class="text-center" *ngIf="isAdmin">
          <div class="m-auto my-4 mb-4">
            <focile-button [loading]="saving" [disabled]="userForm.invalid" (onClick)="updateAccountDetail()"
              class="custom-btn my-4">Update Setting</focile-button>
          </div>
        </div>
      </div>
      <div *ngIf="activeTab === 'following'">
        <div class="mt-3">
          <div>
            <h5 class="fw-bold my-4">Following</h5>
          </div>
          <div class="" heading="Follower">
            <div *ngIf="!followerFollowingLoading">
              <ng-container *ngIf="followingList?.length; else noFollowingTemplate">
                <div class="grid-container">
                  <div class="grid-item" (click)="navigate(item.userId)" *ngFor="let item of followingList">
                    <div class="follow-card">
                      <div class="d-flex rounded-5 overflow-hidden align-items-center justify-content-center"
                        style="height: 56px; width: 56px; background-color: #f0f0f0">
                        <img [src]="item.profilePhoto || './assets/svgs/focile.svg'"
                          style="height: 56px; width: 56px; border-radius:100%" alt="" srcset="" appImg />
                      </div>
                      <span class="follow-name">
                        {{ item.userName | titlecase }}
                      </span>
                      <p class="follower-role">
                        <span *ngFor="let e of item.expertise | slice : 0 : 3; let i = index">
                          <div>
                            <span *ngIf="i !== 0"> || </span>{{ $any(e).name }}
                          </div>
                        </span>
                      </p>
                      <div class="d-flex justify-content-end" style="flex: 1">
                        <button class="follow-btn" (click)="unfollow($event, item)" *ngIf="item.isFollowing">
                          Following
                        </button>
                        <button class="follow-btn" (click)="followUser($event, item)" *ngIf="!item.isFollowing">
                          follow
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </ng-container>
              <ng-template #noFollowingTemplate>
                <div class="alert alert-info">
                  No following yet for your profile.
                </div>
              </ng-template>
            </div>
            <div class="row" *ngIf="dataLoading">
              <div class="col-md-12 text-center">
                <app-spinner></app-spinner>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div *ngIf="activeTab === 'followers'">
        <div class="mt-3">
          <div>
            <h5 class="fw-bold my-4">Followers</h5>
          </div>
          <div class="">
            <div *ngIf="!followerFollowingLoading">
              <div class="grid-container">
                <div class="grid-item" *ngFor="let item of followersList" (click)="navigate(item.userId)">
                  <div class="follow-card">
                    <div>
                      <img [src]="item.profilePhoto || './assets/svgs/focile.svg'" class="focile-avatar" />
                    </div>
                    <span class="follow-name">{{
                      item["userName"] | titlecase
                      }}</span>
                    <p class="follow-company-name">
                      {{ item["roleName"] }} at {{ item["companyName"] }}
                    </p>
                    <p class="follower-role">
                      <span *ngFor="let e of item.expertise | slice : 0 : 3; let i = index">
                        <span *ngIf="i !== 0"> || </span> {{ $any(e).name }}
                      </span>
                    </p>
                  </div>
                </div>
              </div>
              <ng-container *ngIf="!followersList?.length">
                <div class="alert alert-info">
                  There are no followers in your list.
                </div>
              </ng-container>
            </div>
            <div class="row" *ngIf="dataLoading">
              <div class="col-md-12 text-center">
                <app-spinner></app-spinner>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div *ngIf="activeTab === 'experts'">
        <div>
          <div class="d-flex flex-row justify-content-between align-items-center my-4">
            <h5 class="fw-bold mb-0">Experts</h5>
            <div *ngIf="userState$?.userType === 3" class="col-md-6 d-flex justify-content-end">
              <button class="add-expert-btn" (click)="openAddExpertModal()">
                Add Expert
              </button>
            </div>
          </div>
          <div class="">
            <div *ngIf="!dataLoading" class="row">
              
                <div class="grid-container">
                  <div class="grid-item" *ngFor="let item of companyExperts; let i = index">
                    <div class="follow-card text-start">
                      <div (click)="navigate(item.userId)" class="w-100">
                        <img [src]="
                            item['profilePhoto'] || './assets/svgs/focile.svg'
                          " class="focile-avatar" />
                      </div>
                      <div (click)="navigate(item.userId)" class="w-100 d-flex flex-column gap-1">
                        <span class="fs-14 fw-bold">{{
                          item["firstName"] | titlecase
                          }} {{
                          item["lastName"] | titlecase
                          }}</span>
                        <p class="m-0 fs-14">
                          {{ item["roleName"] }}
                        </p>

                        <div class="fc-call-btn">
                          <span class="call-icon">
                            <svg _ngcontent-rao-c17="" width="13" height="13" viewBox="0 0 24 24" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path _ngcontent-rao-c17=""
                                d="M4 20C3.45 20 2.97917 19.8042 2.5875 19.4125C2.19583 19.0208 2 18.55 2 18V6C2 5.45 2.19583 4.97917 2.5875 4.5875C2.97917 4.19583 3.45 4 4 4H20C20.55 4 21.0208 4.19583 21.4125 4.5875C21.8042 4.97917 22 5.45 22 6V18C22 18.55 21.8042 19.0208 21.4125 19.4125C21.0208 19.8042 20.55 20 20 20H4ZM12 13L4 8V18H20V8L12 13ZM12 11L20 6H4L12 11ZM4 8V6V18V8Z"
                                fill="white"></path>
                            </svg>
                          </span>
                          <a class="fs-14" href="mailto:{{ item['email'] }}">{{
                            item["email"]
                            }}</a>
                        </div>
                      </div>
                      <div class="d-flex justify-content-start" style="flex: 1">
                        <div *ngIf="!item.isAdmin" class="d-flex align-items-start">
                          <button class="follow-btn" style="
                              height: 24px;
                              width: 84px;
                              border-radius: 100px;
                            " (click)="toggleExpert(i)">
                            Remove
                          </button>
                        </div>
                      </div>
                  </div>
                </div>
                </div>              
            </div>
            <ng-container *ngIf="!dataLoading && !companyExperts.length">
              <app-helper-text [message]="'No Experts Available.'" />
            </ng-container>
            <div class="row" *ngIf="dataLoading">
              <div class="col-md-12 text-center">
                <app-spinner></app-spinner>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <ng-template #experToggleConfirmation>
    <div class="modal-header">
      <strong> Confirmation </strong>
    </div>
    <div class="modal-body">Are you you want to remove this expert ?</div>

    <div class="modal-footer">
      <button (click)="removeExpert()" class="btn btn-danger text-white btn-sm">
        Yes Remove
      </button>
      <button (click)="modalRef?.hide()" class="btn btn-primary btn-sm">
        No, Not Now
      </button>
    </div>
  </ng-template>

  <ng-template #errorTemplate let-field="field" let-control="control">
    <span *ngIf="control?.touched && control?.errors?.required">
      <span class="text-danger"> {{ field }} is required. </span>
    </span>
  </ng-template>

  <div *ngIf="dataLoading" class="row text-center col-md-12">
    <app-spinner></app-spinner>
  </div>
</div>