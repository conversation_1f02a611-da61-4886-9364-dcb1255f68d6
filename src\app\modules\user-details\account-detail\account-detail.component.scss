.required {
  color: var(--bs-danger);
}

.banner-wrapper {
  position: relative;
  background: url("../../../../assets/svgs/bg.svg") #f6f6f6 center center
    no-repeat;
  background-size: cover;
  height: 100px;
  border-radius: 15px;
  border: 1px solid #f2f2f2;
}
//
.company-logo-banner {
  border-radius: 12px;
}

.h-48 {
  height: 3rem;
}

.fc-account-detail-content {
  box-sizing: border-box;
  width: 100%;
  background: #ffffff;
  border: 1px solid rgba(140, 140, 140, 0.2);
  border-radius: 18px;
  padding: 1.5rem;
  z-index: 1;
  position: relative;
}

.form-control {
  box-sizing: border-box;
  margin: 0 auto;
  height: 56px;
  background: #f6f6f6;
  border: 1px solid rgba(246, 246, 245, 0.4);
  border-radius: 12px;
  flex: none;
  order: 1;
  align-self: stretch;
  flex-grow: 0;
  padding: 1rem;

  &::placeholder {
    color: #666666;
  }
}
.form-label {
  font-weight: 400;
  font-size: 1rem;
  color: black;
}

::ng-deep .ng-select.ng-select-opened > .ng-select-container {
  box-sizing: border-box;
  margin: 0 auto;
  height: 56px;
  background: #f6f6f6;
  border: 1px solid rgba(246, 246, 245, 0.4);
  border-radius: 12px;
  flex: none;
  order: 1;
  align-self: stretch;
  flex-grow: 0;
}

.category-navbar-item {
  display: flex;
  flex-direction: row;
  border-bottom: 1px solid #d9dbe9;
  align-items: center;
  justify-content: center;
  list-style: none;
  padding-left: 0px;

  li {
    width: 25%;
    display: flex;
    align-items: start;
    justify-content: center;
    min-height: 40px;
    border-bottom: 2px solid transparent;

    a {
      text-decoration: none;
      font-size: 16px;
      font-weight: 500;
      display: flex;
      flex-direction: row;
      gap: 1rem;
      align-items: center;
      color: #a0a3bd;
    }

    span {
      min-width: 26px;
      min-height: 26px;
      left: 0px;
      top: 0px;
      background: rgba(1, 69, 129, 0.1);
      border-radius: 14px;
      display: flex;
      align-items: center;
      justify-content: center;

      svg {
        width: 14px;
      }
    }

    &.active {
      border-color: #014681;

      a {
        color: #014681;
      }
    }
  }
}

.follow-card {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: start;
  padding: 1rem;
  gap: 1rem;
  width: 240px;
  height: 100%;
  background: #ffffff;
  border: 1px solid rgba(140, 140, 140, 0.1);
  box-shadow: 0px -1px 10px rgba(0, 0, 0, 0.05);
  border-radius: 20px;
  text-align: center;
  cursor: pointer;
  overflow: hidden;

  &:hover {
    background: rgba(171, 173, 196, 0.3);
    border: 1px solid rgba(140, 140, 140, 0.1);
    box-shadow: 0px -1px 10px rgba(0, 0, 0, 0.05);
    border-radius: 20px;
  }

  img {
    object-fit: cover;
  }

  .follower-role {
    font-size: 14px;
    color: #666666;
    text-align: left;
    height: 66px;
  }
  .follow-name {
    font-size: 18px;
    color: black;
    font-weight: 500;
  }

  p {
    margin-bottom: 0px;
  }
  .follow-company-name {
    color: black;
    font-size: 14px;
    text-align: left;
  }

  .focile-avatar {
    width: 56px;
    height: 56px;
  }

  .fc-call-btn {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
    color: black;
    font-size: 13px;
    font-weight: 400;
    margin-top: auto;

    a {
      width: 100%;
      display: flex;
      flex-direction: row;
      text-decoration: none;
      flex: 1;
    }
    .call-icon {
      width: 20px;
      height: 20px;
      background-color: #035891;
      border-radius: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.follow-btn {
  gap: 10px;
  width: 97px;
  height: 30px;
  background: #000000;
  border-radius: 200px;
  flex: none;
  order: 1;
  flex-grow: 0;
  font-size: 14px;
  color: white;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;

  a {
    text-decoration: none;
    color: white;
  }
}

::ng-deep .ng-select.ng-select-disabled > .ng-select-container {
  border: none;
}

::ng-deep .ng-select .ng-select-container {
  box-sizing: border-box;
  margin: 0 auto;
  height: 56px !important;
  background: #f6f6f6;
  border: 1px solid rgba(246, 246, 245, 0.4);
  border-radius: 12px;
  flex: none;
  order: 1;
  align-self: stretch;

  input[type="text"] {
    padding-inline: 0px;
    background: transparent !important;
    border: none !important;
  }
}

::ng-deep
  .ng-select.ng-select-multiple
  .ng-select-container
  .ng-value-container
  .ng-value {
  background-color: #e7e7e7;
  border-radius: 5px;
}

::ng-deep .ng-dropdown-panel.ng-select-bottom {
  background: #ffffff;
  box-shadow: 0px 41px 89px rgba(0, 0, 0, 0.1), 0px 0px 0px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  border: none;
}

::ng-deep .ng-select .ng-arrow-wrapper {
  width: 35px;
}

.round-btn {
  width: 26px;
  height: 26px;
  background: transparent;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #014681;
  font-size: 10px;
  font-weight: 600;
  border: 1px solid #014681;
}

.add-btn {
  border-color: black;
  color: black;
}
.remove-btn {
  border-color: #f44336;
  color: #f44336;
}

.edit-btn {
  color: #4caf50;
  border-color: #4caf50;
}

textarea.form-control,
textarea.form-control:not([type="file"]) {
  height: auto;
  min-height: 200px;
  resize: none;
  padding: 1rem;
}

.elvator-pitch-card {
  background-color: #f6f6f6;
  padding: 1.5rem;
  gap: 1.5rem;
  border-radius: 12px;

  input {
    background-color: white;
    min-height: 56px;

    &::placeholder {
      color: rgba(102, 102, 102, 0.6);
    }
  }

  .edit-btn {
    color: #4caf50;
    border-color: #4caf50;
  }
  .right-btn {
    color: #ec971f;
    border-color: #ec971f;
  }

  h5 {
    font-size: 20px;
    font-weight: 600;
  }
  .alert-warning {
    background-color: #ffeeba;
    color: black;
    font-size: 1rem;
    strong {
      color: black;
    }
  }
  .btn-close {
    font-size: 12px;
    top: 2px;
  }
}

.fc-video-presentation {
  background-color: #f6f6f6;
  padding: 1.5rem;
  gap: 1.5rem;
  border-radius: 12px;
  input {
    background-color: white;
  }
  h5 {
    font-size: 20px;
    font-weight: 600;
  }
}

.social-network-wrap {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;

  input {
    font-size: 14px;
  }
}

.add-expert-btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  min-width: 125px;
  height: 46px;
  background: #014681;
  box-shadow: 0px 548px 219px rgba(0, 0, 0, 0.01),
    0px 308px 185px rgba(0, 0, 0, 0.04), 0px 137px 137px rgba(0, 0, 0, 0.06),
    0px 34px 75px rgba(0, 0, 0, 0.07), 0px 0px 0px rgba(0, 0, 0, 0.07);
  border-radius: 100px;
  border: none;
  text-align: center;
  color: white;
  font-size: 16px;
  font-weight: 500;
}

::ng-deep .add-expert-dailog {
  label {
    color: #666666;
    font-size: 16px;
    font-weight: 400;
  }
  input {
    box-sizing: border-box;
    width: 270px;
    height: 56px;
    background-color: #f6f6f6 !important;
    border: 1px solid rgba(246, 246, 245, 0.4) !important;
    border-radius: 12px !important;
    flex: none;
    order: 1;
    align-self: stretch;
    flex-grow: 0;
    padding-inline: 24px;
    font-size: 16px;

    &::placeholder {
      color: rgba(102, 102, 102, 0.6);
    }
  }
}

.fc-save-btn {
  min-width: 175px;
  height: 50px;
  background: #014681;
  box-shadow: 0px 548px 219px rgba(0, 0, 0, 0.01),
    0px 308px 185px rgba(0, 0, 0, 0.04), 0px 137px 137px rgba(0, 0, 0, 0.06),
    0px 34px 75px rgba(0, 0, 0, 0.07), 0px 0px 0px rgba(0, 0, 0, 0.07);
  border-radius: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  border: none;
  font-size: 18px;
  gap: 0.5rem;
}

.add-expert-header {
  font-size: 18px;
}

.list-group-item-action {
  height: auto;
  border-radius: 10px;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10px;
  }
}

.change-btn {
  height: auto;
  display: flex;
  padding: 10px;
  border-radius: 30px !important;
  background: rgb(0 0 0 / 25%);
  border: none;
  color: white;
  font-size: 14px;
}
.company-logo {
  bottom: 60px;
  width: 25px;
  height: 25px;
  top: 64%;
  left: 64%;
  background: white;
  position: absolute;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.custom-btn {
  ::ng-deep button {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    min-width: 175px;
    height: 50px;
    background: #014681;
    box-shadow: 0px 548px 219px rgba(0, 0, 0, 0.01),
      0px 308px 185px rgba(0, 0, 0, 0.04), 0px 137px 137px rgba(0, 0, 0, 0.06),
      0px 34px 75px rgba(0, 0, 0, 0.07), 0px 0px 0px rgba(0, 0, 0, 0.07);
    border-radius: 100px;
    border: none;
    text-align: center;
    color: white;
    font-size: 18px;
    font-weight: 500 !important;
  }
}

.w-150 {
  width: 150px;
}

::ng-deep .ng-select .ng-select-container .ng-value-container {
  padding-left: 1rem;
}
::ng-deep
  .ng-select.ng-select-multiple
  .ng-select-container
  .ng-value-container {
  padding-left: 1rem;
}

::ng-deep .modal {
  z-index: 1111;
}

.company-profile-log {
  margin-bottom: 1.5rem;
  margin-top: 1rem;

  label {
    margin-bottom: 10px;
  }
}

.fc-account-detail-content .fc-company-banner-label {
  margin-bottom: 10px;
}
.list-group-item {
  margin-bottom: 2rem;
}
.grid-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
}

.grid-item {
  display: flex;
  justify-content: center;
  height: 100%;
}
@media (max-width: 768px) {
  .fc-account-detail-content {
    padding: 1rem;
  }

  .category-navbar-item {
    justify-content: start;
    overflow: auto;
    white-space: nowrap;
    margin-top: 3rem;
    overflow-x: scroll; /* Enable horizontal scroll */
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
    scrollbar-width: none; /* Firefox */

    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, and Edge */
    }

    li {
      width: auto;
      a {
        justify-content: start;
        flex-direction: column;
        gap: 10px;
        padding-bottom: 10px;
      }
    }
  }
  .social-network-wrap {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .follow-card {
    width: 100%;
    margin-bottom: 1rem;
  }

  .elvator-pitch-card {
    padding: 1rem;
  }
  .fc-video-presentation {
    padding: 1rem;
  }
}
