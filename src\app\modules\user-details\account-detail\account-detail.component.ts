import {
  Compo<PERSON>,
  <PERSON>ement<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  On<PERSON>nit,
  Query<PERSON>ist,
  ViewChild,
  ViewChildren,
} from '@angular/core';
import { AccountService } from '../../account/services/account.service';
import { Form<PERSON>rray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { finalize, forkJoin, Subscription, tap } from 'rxjs';
import { FDropdownComponent } from 'src/app/shared/components/f-dropdown/f-dropdown.component';
import { ToastrService } from 'ngx-toastr';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Router } from '@angular/router';
import {
  ApprovePostStatusEnum,
  ApprovePostTypeEnum,
  companyType,
  EMPTY_GUID,
  Regex,
} from 'src/app/shared/constant';
import { HttpClient } from '@angular/common/http';
import { ChatService } from 'src/app/shared/services/chat.service';
import { ModalService } from 'src/app/shared/services/modal.service';
import { ModalTypes } from 'src/app/shared/modal.type';
import { UtilsService } from 'src/app/shared/services/utils.service';
import { filterNonNull } from 'src/app/shared/oprators/filter-null-values';
@Component({
  selector: 'app-account-detail',
  templateUrl: './account-detail.component.html',
  styleUrls: ['./account-detail.component.scss'],
})
export class AccountDetailComponent implements OnInit, OnDestroy {
  @ViewChild('experToggleConfirmation') experToggleConfirmationPopup: any;
  @ViewChild('bannerFile') bannerInput: any;
  @ViewChild('ecardBannerFile') ecardBannerFile!: ElementRef;
  @ViewChild('profile') profile!: ElementRef;
  @ViewChildren(FDropdownComponent)
  dropdowns!: QueryList<FDropdownComponent>;
  modalRef?: BsModalRef;
  user: any = {};
  userForm!: FormGroup;
  companyList = [];
  companySizeList = [];
  countryList = [];
  companyTypeList = [];
  expertiseList = [];
  industryList = [];
  productList = [];
  roleList = [];
  servicesList = [];
  solutionList = [];
  technologyList = [];
  typeOfExpertList = [];
  states: Array<any> = [];
  dataLoading = false;
  cityList: any;
  userState$: any = {};
  showInstallationType = false;
  typeOfInstallationList = [];
  saving = false;
  followingList: Array<any> = [];
  followersList: Array<any> = [];
  allDetails: any = {};
  companyExperts: Array<any> = [];
  expert: any = {};
  selectedImage: string | ArrayBuffer | null | undefined;
  companyId!: string;
  savingExpert = false;
  showWorkMobileNumber = true;
  selectedVideo!: any;
  isInvalidFileType!: boolean;
  isInvalidDuration!: boolean;
  editAddressIndex = 0;
  statesLoading = false;
  citiesLoading = false;
  isAdmin: any;
  countryCodes: any = [];
  approvePostStatusEnum = ApprovePostStatusEnum;
  elevatorPitchConfig: any = {};
  savingElevatorPitch = false;
  followerFollowingLoading = false;
  selectedBanner: any;
  selectedEcardBanner: any = null;
  profileImage = '';
  userType: number = 0;

  subscriptionArr: Subscription[] = [];
  constructor(
    private account: AccountService,
    private formBuilder: FormBuilder,
    private toastrService: ToastrService,
    private modalService: BsModalService,
    private router: Router,
    private httpClient: HttpClient,
    private chatService: ChatService,
    private customModalService: ModalService,
    private utils: UtilsService
  ) {}
  ngOnDestroy(): void {
    this.subscriptionArr.map((s) => s?.unsubscribe());
  }

  ngOnInit(): void {
    this.account.user$.pipe(filterNonNull()).subscribe((response) => {
      if (!response) return;
      this.userState$ = response;
      this.isAdmin = response.userType === companyType.Admin;
      this.profileImage =
        this.userState$?.userType == 3
          ? this.userState$?.companyPhoto
          : this.userState$?.profilePhoto;
      this.selectedBanner = this.userState$?.companyBanner;
      this.selectedEcardBanner = this.userState$?.ecardBanner;
      this.initUserForm();
      if (response.userType === companyType.ExpertUser) {
        this.getAccountDetails(response.adminId);
      } else {
        this.getAccountDetails(response.userId);
      }
      this.getElevatorPitch();
      this.getCompanyEcardBanner();
    });
  }

  getCompanyEcardBanner() {
    const subscription = this.account
      .getCompanyEcardBanner(this.userState$.companyId)
      .subscribe((ecardBanner: any) => {
        this.selectedEcardBanner = ecardBanner.data;
      });
    this.subscriptionArr.push(subscription);
  }

  updateProfile() {
    this.profile.nativeElement.click();
  }

  async uploadProfileImage($event: any) {
    const valid = await this.displaySelectedImage($event.target.files[0]);
    if (!valid) return;
    const formData = new FormData();
    formData.append('UserId', this.userState$.userId);
    formData.append('photo', $event.target.files[0]);
    if (this.userState$?.userType === 3) {
      this.account
        .uploadCompanyProfileImage(formData)
        .subscribe((response: any) => {
          if (response.data) {
            this.toastrService.success('Company logo updated successfully');
          }
        });
    } else {
      this.account.uploadProfileImage(formData).subscribe((response) => { });
    }
  }

  uploadBanner() {
    this.bannerInput.nativeElement.click();
  }

  async uploadBannerImage($event: any) {
    const valid = await this.displaySelectedImage(
      $event.target.files[0],
      'banner'
    );
    if (!valid) return;
    const formData = new FormData();
    formData.append('UserId', this.userState$.userId);
    formData.append(
      'file',
      $event.target.files[0],
      this.utils.generateRandomFileName(10)
    );
    this.account
      .uploadBanner(formData, this.userState$.companyId)
      .subscribe((response: any) => {
        if (response.data) {
          this.toastrService.success('Company banner updated successfully');
        }
      });
  }

  uploadEcardBanner() {
    this.ecardBannerFile.nativeElement.click();
  }

  async uploadEcardBannerImage($event: any) {
    const valid = await this.displaySelectedImage(
      $event.target.files[0],
      'ebanner'
    );
    if (!valid) return;
    const formData = new FormData();
    formData.append('UserId', this.userState$.userId);
    formData.append(
      'file',
      $event.target.files[0],
      this.utils.generateRandomFileName(10)
    );
    this.account
      .uploadeCardBanner(formData, this.userState$.companyId)
      .subscribe((response: any) => {
        if (response.data) {
          this.toastrService.success('eCard banner updated successfully');
        }
      });
  }

  displayBannerImage(file: File): void {
    const reader = new FileReader();
    reader.onload = (e) => {
      this.selectedBanner = e.target?.result;
      let user: any = localStorage.getItem('user');
      user = JSON.parse(user);
      user.companyBanner = this.selectedBanner;
      localStorage.setItem('user', JSON.stringify(user));
      // this.account.companyBanner.next(this.selectedImage);
      user.ecardBanner = this.selectedEcardBanner;
    };
    reader.readAsDataURL(file);
  }

  displaySelectedImage(file: File, fileType = 'profile') {
    return new Promise((resolve, reject) => {
      if (!file.type.startsWith('image/')) {
        this.toastrService.error('Selected file is not an image.');
        return resolve(false);
      }
      const isProfile = fileType == 'profile';

      // Check the file size (in bytes)
      if (isProfile && file.size > this.utils.getProfileImageSize()) {
        this.toastrService.error('Image size should be under 2MB.');
        return resolve(false);
      } else if (file.size > this.utils.getBannerImageSize()) {
        this.toastrService.error('Image size should be under 5MB.');
        return resolve(false);
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const file = e.target?.result;
        let user: any = localStorage.getItem('user');
        if (user) {
          user = JSON.parse(user);
        }
        if (fileType === 'profile') {
          user.companyPhoto = file;
          this.profileImage = user.companyPhoto;
        } else if (fileType === 'ebanner') {
          user.ecardBanner = file;
          this.selectedEcardBanner = file;
        } else {
          user.companyBanner = file;
          this.selectedBanner = file;
        }

        localStorage.setItem('user', JSON.stringify(user));
      };
      reader.readAsDataURL(file);
      resolve(true);
    });
  }

  activeTab: string = 'companyDetails';

  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }

  initUserForm() {
    this.userForm = this.formBuilder.group({
      WorkMobileNumer: null,
      companySize: null,
      expertId: null,
      companyName: [null, Validators.required],
      expertiseIds: [null, Validators.required],
      industryIds: [null, Validators.required],
      productIds: [null, Validators.required],
      roleId: [null, Validators.required],
      serviceIds: [null, Validators.required],
      solutionIds: [null, Validators.required],
      companySystemIds: [null, Validators.required],
      companyWebsite: [null, Validators.required],
      address: null,
      zipCode: null,
      country: null,
      state: null,
      city: null,
      about: null,
      userPosts: this.formBuilder.array([]),
      facebookLink: [null, Validators.pattern(Regex.facebookLink)],
      instagramLink: [null, Validators.pattern(Regex.instagramLink)],
      twitterLink: [null, Validators.pattern(Regex.twitterLink)],
      linkedInLink: [null, Validators.pattern(Regex.linkedInLink)],
      pinterestLink: [null, Validators.pattern(Regex.pinteresetLink)],
      youtubeLink: [null, Validators.pattern(Regex.youtubeChannelLink)],
      typeOfInstallationIds: null,
      ElevatorPitchVideo: [null, Validators.pattern(Regex.youtubeLink)],
      usercontacts: this.formBuilder.array([]),
      companyValueProposition: null,
      companyMessageToAudience: null,
    });

    const socialFields = [
      'linkedInLink',
      'facebookLink',
      'twitterLink',
      'instagramLink',
      'pinterestLink',
      'youtubeLink',
    ];

    socialFields.forEach((field) => {
      this.userForm.get(field)?.valueChanges.subscribe((value) => {
        if (value) {
          socialFields
            .filter((other) => other !== field)
            .forEach((otherField) => {
              this.userForm.get(otherField)?.setValue('', { emitEvent: false }); // avoid infinite loop
            });
        }
      });
    });
  }

  getAccountDetails(userId: string, isAdmin = false) {
    this.dataLoading = true;
    this.account
      .getAccountDetails(userId, true)
      .pipe(
        finalize(() => (this.dataLoading = false)),
        tap((response: any) => {
          if (response.data?.expertDetail?.userContacts?.length) {
            const userAddresses = response.data.expertDetail.userContacts.find(
              (x: any) => x.isPrimary
            );
            userAddresses && this.getStateCities(userAddresses);
          }
          if (response.data.expertDetail.expertId) {
            this.showInstallationType =
              response.data.expertDetail.expertId === 4;
            this.handleCompanyTypeChange(response.data.expertDetail.expertId);
          }
        })
      )
      .subscribe((response: any) => {
        this.allDetails = response.data;
        this.companyList = response.data.companyList;
        this.companySizeList = response.data.companySizeList;
        this.countryList = response.data.countryList;
        this.companyTypeList = response.data.companyTypeList;
        this.expertiseList = response.data.expertiseList;
        this.industryList = response.data.industryList;
        this.productList = response.data.productList;
        this.roleList = response.data.roleList;
        this.servicesList = response.data.servicesList;
        this.solutionList = response.data.solutionList;
        this.technologyList = response.data.technologyList;
        this.typeOfExpertList = response.data.typeOfExpertList;
        this.typeOfExpertList = response.data.typeOfExpertList;
        this.typeOfInstallationList = response.data.typeOfInstallationList;

        const admin = response.data.expertDetail;
        this.selectedImage = admin.companyBanner;

        this.userForm.patchValue({
          companySize: admin.companySize,
          expertId: admin.expertId,
          expertiseIds: admin.expertiseIds?.split(','),
          industryIds: admin.industryIds?.split(','),
          productIds: admin.productIds?.split(','),
          roleId: admin.roleId?.split(','),
          serviceIds: admin.serviceIds?.split(','),
          solutionIds: admin.solutionIds?.split(','),
          technologyIds: admin.technologyIds?.split(','),
          companyName: admin.companyName,
          companyWebsite: admin.companyWebsite,
          address: response.data.address,
          zipCode: response.data.zipCode,
          country: response.data.country,
          state: response.data.state,
          city: response.data.city,
          about: response.data.expertDetail.about,
          experties: response.data.about,
          facebookLink: response.data.expertDetail.facebookLink,
          instagramLink: response.data.expertDetail.instagramLink,
          twitterLink: response.data.expertDetail.twitterLink,
          linkedInLink: response.data.expertDetail.linkedInLink,
          pinterestLink: response.data.expertDetail.pinterestLink,
          youtubeLink: response.data.expertDetail.youtubeLink,
          companySystemIds:
            response.data.expertDetail?.companySystemIds?.split(','),
          companyMessageToAudience: response.data.companyMessageToAudience,
          companyValueProposition: response.data.companyValueProposition,
        });

        // if (response.data?.expertDetail?.userContacts?.length) {
        const userContacts = this.userForm.get('usercontacts') as FormArray;
        if (!response.data.expertDetail?.userContacts?.length) {
          userContacts.push(this.createAddressFormGroup());
        }
        const userPosts = this.userForm.get('userPosts') as FormArray;
        if (!response.data?.expertDetail?.userPosts?.length) {
          userPosts.push(this.createLink());
        }
        // Iterate through the array of objects
        response.data.expertDetail.userContacts.forEach(
          (obj: any, index: number) => {
            userContacts.push(this.createAddressFormGroup());
            if (index < userContacts.length) {
              userContacts.at(index).patchValue({
                id: obj.id || EMPTY_GUID,
                address: obj.address,
                country: obj.country,
                state: obj.state,
                city: obj.city,
                zipCode: obj.zipCode,
                workMobileNumber: obj.workMobileNumber,
                workMobileNumberCountryCode: obj.workMobileNumberCountryCode,
                isShowWorkMobileNumber: obj.isShowWorkMobileNumber,
                isPrimary: obj.isPrimary,
              });
            }
          }
        );
        response.data.expertDetail.userPosts.forEach(
          (obj: any, index: number) => {
            userPosts.push(this.createLink());
            if (index < userPosts.length) {
              userPosts.at(index).patchValue({
                id: obj.id || EMPTY_GUID,
                path: obj.path,
                postType: obj.postType,
                userId: obj.userId,
              });
            }
          }
        );
        // }

        if (this.userState$?.userType == companyType.ExpertUser) {
          // if not admin
          this.userForm.disable();
        }
        this.getExperts();
      });
  }

  /**
   * set up state and cities bases on user selected primary adddres
   * @param userPrimaryAddress userPrimaryAddress
   */
  getStateCities(userPrimaryAddress: any) {
    this.statesLoading = true;
    this.citiesLoading = true;
    forkJoin([
      this.account.getCountries(),
      this.account.getStates(userPrimaryAddress.country),
      this.account.getCities(userPrimaryAddress.state),
    ])
      .pipe(
        finalize(() => {
          this.statesLoading = false;
          this.citiesLoading = false;
        })
      )
      .subscribe(([countries, states, cities]) => {
        const _states = (states as any)['data'];
        const _cities = (cities as any)['data'];
        if ((countries as any).data?.length) {
          this.countryCodes = (countries as any)['data'];
          this.countryCodes = this.countryCodes.map((x: any) => {
            x.description = x.description;
            return x;
          });
        }
        if (_states.length) {
          this.states = _states;
        }
        if (_cities.length) {
          this.cityList = _cities;
        }
      });
  }

  saveElevatorPitch() {
    if (this.userForm.get('ElevatorPitchVideo')?.disabled) return;
    this.toastrService.clear();

    const id = this.userState$.userId;
    const url: string = this.userForm?.value?.ElevatorPitchVideo || '';
    if (this.userState$?.userType != 1) {
      const payload: Record<string, any> = {
        UserId: id,
        ApprovePostType: ApprovePostTypeEnum.CompanyElevatorPitchVideo,
        url,
        id: this.elevatorPitchConfig.id,
        isRemoved: url.trim().length === 0,
      };
      const formData = new FormData();
      Object.keys(payload).forEach((key) => {
        if (payload[key]?.toString()) {
          formData.append(key, payload[key].toString());
        }
      });
      this.savingElevatorPitch = true;
      this.account
        .addUserElevatorPitch(formData)
        .pipe(finalize(() => (this.savingElevatorPitch = false)))
        .subscribe((response: any) => {
          if (response?.error) {
            this.toastrService.error(
              'We are unable to save your video',
              'Something went wrong'
            );
          } else {
            if (response.data.statusMessage) {
              this.elevatorPitchConfig.id = response.data.id;
              this.elevatorPitchConfig.status =
                this.approvePostStatusEnum.Pendding;
              this.toastrService.success(response.data.statusMessage);
            }
          }
        });
    }
  }

  getElevatorPitch() {
    this.httpClient
      .get(
        'approvepost/GetCompanyElevatorPost?adminid=' + this.userState$.userId
      )
      .subscribe((response: any) => {
        this.elevatorPitchConfig = response.data;
        this.userForm.get('ElevatorPitchVideo')?.setValue(response?.data?.url);
        if (response?.data?.status === this.approvePostStatusEnum.Approved) {
          this.disableElevatorPitch();
        } else {
          this.enableElevatorPitch(false);
        }
      });
  }

  enableElevatorPitch(focus = true) {
    this.userForm.get('ElevatorPitchVideo')?.enable();
    if (focus) document.getElementById('elevatorPitchVideo')?.focus();
  }
  disableElevatorPitch() {
    this.userForm.get('ElevatorPitchVideo')?.disable();
  }

  getExperts() {
    this.account
      .getCompanyExpertList(this.allDetails.expertDetail.companyId)
      .subscribe((response: any) => {
        if (response.data.length) {
          this.companyExperts = response.data;

          // this.companyExperts.map((x) => (x.isActive = true));
          this.companyExperts.map(
            (x) =>
              (x.isAdmin =
                `${this.userState$.userId}`.toLowerCase() ==
                `${x.userId}`.toLowerCase())
          );
        } else {
        }
      });
  }

  toggleExpert(index: any) {
    this.expert = this.companyExperts[index];
    this.expert.index = index;
    this.modalRef = this.modalService.show(this.experToggleConfirmationPopup, {
      class: 'modal-dialog-centered modal-lg',
    });
  }

  removeExpert() {
    this.companyExperts.splice(this.expert.index, 1);
    this.modalRef?.hide();
    this.toastrService.success('', 'Expert removed successfully.');
  }

  handleCountryChange(countryId: any, index: any = null) {
    this.statesLoading = true;
    this.account
      .getStates(countryId)
      .pipe(finalize(() => (this.statesLoading = false)))
      .subscribe((stateResponse: any) => {
        this.states = stateResponse.data as any;
        this.cityList = [];
        if (index != null) {
          this.userContacts.at(index).get('state')?.setValue(null);
          this.userContacts.at(index).get('state')?.markAsUntouched();
          this.userContacts.at(index).get('city')?.setValue(null);
          this.userContacts.at(index).get('city')?.markAsUntouched();
        }
      });
  }

  handleStateChange(stateId: any, index = null) {
    this.citiesLoading = true;
    this.account
      .getCities(stateId)
      .pipe(finalize(() => (this.citiesLoading = false)))
      .subscribe((cityResponse: any) => {
        this.cityList = cityResponse.data as any;
        if (index != null) {
          this.userContacts.at(index).get('city')?.setValue(null);
          this.userContacts.at(index).get('city')?.markAsUntouched();
        }
      });
  }

  handleCompanyTypeChange($event: any) {
    this.typeOfExpertList = [];
    this.userForm.get('companySystemIds')?.setValue(null);
    this.dataLoading = true;
    const promises = [this.account.getTypeOfExpert($event)];
    forkJoin(promises)
      .pipe(finalize(() => (this.dataLoading = false)))
      .subscribe(([typeofExpertList, installationList]) => {
        if (typeofExpertList) {
          const typeOfExpertList: any = typeofExpertList;
          this.typeOfExpertList = typeOfExpertList.data;
        }
        if (installationList) {
          const companyTypeList: any = installationList;
          this.typeOfInstallationList = companyTypeList.data;
        }
      });
  }

  updateAccountDetail() {
    const user = this.userForm.value;
    Object.keys(user).forEach((k: string) => {
      if (Array.isArray(user[k]) && k != 'usercontacts' && k != 'userPosts') {
        user[k] = user[k].toString();
      }
    });
    user.aspNetId = this.userState$.aspNetUserId;
    user.id = this.userState$.userId;
    user.email = this.userState$.email;
    user.userType = this.userState$?.userType;
    user.sideMenuId = 1;
    user.WorkMobileNumer = this.showWorkMobileNumber;
    user.userType = this.userState$?.userType;
    user.UserPosts = [];
    this.saving = !this.saving;
    const promises = [this.account.updateAccountDetail(user)];
    if (this.selectedVideo) {
      const elevatorForm = this.getFormData();
      promises.push(this.account.saveDocuments(elevatorForm));
    }
    user.userPosts = user.userPosts.filter((x: any) => x.path);
    this.account
      .updateAccountDetail(user)
      .pipe(finalize(() => (this.saving = !this.saving)))
      .subscribe((response: any) => {
        if (response.data.messageType) {
          return this.toastrService.error(response.message);
        }
        if (response.data.isCompleted) {
          let user: any = localStorage.getItem('user');
          user = JSON.parse(user);
          user.isCompleted = response.data.isCompleted;
          localStorage.setItem('user', JSON.stringify(user));
        }
        const itemsArray = this.userForm.get('usercontacts') as FormArray;
        if (!response.data?.userContacts?.length) {
          itemsArray.push(this.createAddressFormGroup());
        }
        // Iterate through the array of objects
        response.data.userContacts.forEach((obj: any, index: number) => {
          itemsArray.at(index).patchValue({
            id: obj.id || EMPTY_GUID,
          });
        });
        let message = 'Company Details updated';
        return this.toastrService.success(message);
      });
  }

  getFormData() {
    const formData = new FormData();
    formData.set('ElevatorPitchVideo', this.selectedVideo);
    formData.set('userId', this.userState$.userId);
    return formData;
  }

  changeTab($event: any) {
    if ($event === 2) {
      this.followerFollowingLoading = true;
      this.account
        .followingList(this.userState$.userId)
        .pipe(finalize(() => (this.followerFollowingLoading = false)))
        .subscribe((response: any) => {
          this.followingList = response.data;
        });
    } else if ($event === 3) {
      this.followerFollowingLoading = true;
      this.account
        .followersList(this.userState$.userId)
        .pipe(finalize(() => (this.followerFollowingLoading = false)))
        .subscribe((response: any) => {
          this.followersList = response.data;
        });
    }
  }

  unfollow($event: MouseEvent, item: any) {
    $event.stopPropagation();
    const loginUserId = this.userState$.userId;
    const companyAdminId = item.userId;
    const payload = {
      userId: companyAdminId,
      followerUserId: loginUserId,
      loginUserId: loginUserId,
    };
    item.loading = true;
    this.httpClient
      .post('follower/unfollow', {
        userId: item.userId,
        followerUserId: this.userState$.userId,
      })
      .pipe(finalize(() => (item.followingLoading = false)))
      .subscribe((response: any) => {
        if (response.data) {
          item.isFollowing = false;
        }
      });
  }

  followUser($event: MouseEvent, item: any) {
    $event.stopPropagation();
    item.followingLoading = true;
    this.httpClient
      .post('follower/follow', {
        userId: item.userId,
        followerUserId: this.userState$.userId,
      })
      .pipe(finalize(() => (item.followingLoading = false)))
      .subscribe((response: any) => {
        if (response.data) {
          item.isFollowing = true;
          this.chatService.sendFollowNotification(
            item.userId,
            response.data.text,
            this.userState$.userId
          );
        }
      });
  }

  openAddExpertModal() {
    this.customModalService.openModal('add-edit-company-expert', {
      class: 'modal-lg',
      initialState: {
        companyId: this.allDetails.expertDetail.companyId,
        onAdd: (expert: any) => {
          expert.isInvited = true;
          this.companyExperts.push(expert);
          this.customModalService.closeModal();
        },
      },
    });
  }

  generateRandomString(length: number) {
    const characters =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let randomString = '';

    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * characters.length);
      randomString += characters.charAt(randomIndex);
    }

    return randomString;
  }

  navigate(userId: string) {
    this.router.navigate(['/details/' + userId + '/expert']);
  }

  createAddressFormGroup(): FormGroup {
    return this.formBuilder.group({
      id: EMPTY_GUID,
      contectType: 0,
      userId: this.userState$.userId,
      address: [null, Validators.required],
      country: [null, Validators.required],
      state: [null, Validators.required],
      city: [null, Validators.required],
      zipCode: [null, Validators.required],
      workMobileNumber: [null, Validators.required],
      isShowWorkMobileNumber: true,
      workMobileNumberCountryCode: null,
      isPrimary: this.userContacts.length ? false : true,
      phoneNumber: '',
    });
  }

  addAddress(): void {
    this.userContacts.push(this.createAddressFormGroup());
    this.editAddressIndex = this.userContacts.length - 1;
  }

  removeAddress(index: number): void {
    this.userContacts.removeAt(index);
  }

  get userContacts(): FormArray {
    return this.userForm.get('usercontacts') as FormArray;
  }

  createLink(): FormGroup {
    return this.formBuilder.group({
      path: [null, Validators.pattern(Regex.youtubeLink)],
      PostType: 1,
      userId: this.userState$.userId,
    });
  }

  get userPosts(): FormArray {
    return this.userForm.get('userPosts') as FormArray;
  }

  addLink(): void {
    this.userPosts.push(this.createLink());
  }

  removeLink(index: number): void {
    this.userPosts.removeAt(index);
  }

  validateFile() {
    if (!this.selectedVideo) {
      this.isInvalidFileType = false;
      this.isInvalidDuration = false;
      return;
    }

    // Check file type
    const allowedTypes = ['video/mp4', 'video/mpeg', 'video/quicktime'];
    if (!allowedTypes.includes(this.selectedVideo.type)) {
      this.isInvalidFileType = true;
      return;
    } else {
      this.isInvalidFileType = false;
    }

    const video = document.createElement('video');
    video.preload = 'metadata';
    video.onloadedmetadata = () => {
      if (+video.duration.toFixed(2) > 30) {
        this.isInvalidDuration = true;
        this.toastrService.error(
          'Video should length should be less then or equal to 30 seconds.'
        );
        this.selectedVideo = null;
      } else {
        this.isInvalidDuration = false;
      }
    };
    video.src = URL.createObjectURL(this.selectedVideo);
  }

  editAddress(index: number) {
    this.editAddressIndex = index;
    const itemsArray = this.userForm.get('usercontacts') as FormArray;
    const { country, state } = itemsArray.value[index];
    if (country) {
      this.handleCountryChange(country);
    }
    if (state) {
      this.handleStateChange(state);
    }
  }
}

// Generated by https://quicktype.io

export interface ExpertListResponse {
  message: string;
  data: Datum[];
  error: null;
  messageType: number;
}

export interface Datum {
  userId: string;
  userName: string;
  email: null;
  profileImage: string;
}
