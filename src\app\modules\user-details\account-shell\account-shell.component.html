<app-header [secondNav]="false"></app-header>
<section class="fc-container mb-5">
  <div class="row pt-0 pt-sm-5">
    <div class="col-md-3 p-0 p-sm-3">
      <div id="list" class="list-group sticky-sidebar">
        <!-- <a
          class="list-group-item p-0 list-group-item-action"
          aria-current="true"
        >
          <div class="text-center">
            <div
              class="position-relative banner-wrapper pb-3"
              [ngStyle]="{
                'background-image':
                  'url(' + selectedBanner || './assets/svgs/bg.svg' + ')'
              }"
              style="background-size: cover"
            >
              <div class="position-relative">
                <img
                  [src]="profileImage"
                  class="mb-3"
                  style="width: 84px; height: 84px"
                  alt="Avatar"
                  tooltip="company logo"
                  placement="right"
                  viewType="company"
                  appImg
                />
                <span
                  *ngIf="userState$?.userType == 3"
                  class="position-absolute rounded shadow"
                  (click)="updateProfile()"
                  role="button"
                  tooltip="Change profile picture"
                  style="
                    bottom: 60px;
                    width: 25px;
                    height: 25px;
                    top: 55%;
                    left: 55%;
                    background: white;
                  "
                >
                  <i class="fa fa-camera"></i>
                </span>
              </div>
              <h5 class="mb-2 text-white">
                <strong>
                  {{ userState$.companyName }}
                </strong>
              </h5>
            </div>

            <input
              type="file"
              accept="image/png, image/gif, image/jpeg"
              name="profile"
              class="d-none"
              id="profile"
              #profile
              (change)="uploadProfileImage($event)"
            />

            <div
              class="user-icon position-absolute"
              style="right: 0.5rem; bottom: 0.5rem"
              *ngIf="userState$?.userType == 3"
            >
              <button
                (click)="uploadBanner()"
                tooltip="Change company banner"
                placement="left"
                class="btn btn-primary border rounded"
              >
                <i class="fa fa-pen"></i>
              </button>
              <input
                type="file"
                class="hide d-none"
                #bannerFile
                (change)="uploadBannerImage($event)"
              />
            </div>
          </div>
        </a> -->
        <a class="list-group-item list-group-item-action py-3 user-select-none" *ngFor="let m of menus"
          (click)="navigateTo(m)" [ngClass]="{ 'active-tab active': m.id === activeTab }">
          <div role="button" class="align-items-center d-flex w-100">
            <div class="ps-3 w-100">
              <h5 class="mb-0 text-primary">
                <div class="w-100 d-flex justify-content-between" [innerHTML]="m.title"></div>
              </h5>
              <small class="text-gray">
                {{ m.description }}
              </small>
            </div>
          </div>
        </a>
      </div>
    </div>
    <div class="col-md-9 content mb-0 mb-sm-4 mt-4 mt-sm-0">
      <router-outlet></router-outlet>
    </div>
  </div>
</section>
<app-footer></app-footer>