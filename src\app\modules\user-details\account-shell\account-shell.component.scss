.active-tab {
  background-color: #014681;

  h5 > div{
    color: white !important;
  }

  small{
    color: rgba(255, 255, 255, 0.50) !important;
  }
}
.sticky-sidebar {
  position: -webkit-sticky;
  position: sticky;
  top: 6rem;
  background-color: lightblue;
  /* Rectangle 12321 */

box-sizing: border-box;
width: 255px;
height: auto;
background: #FFFFFF;
border: 1px solid rgba(140, 140, 140, 0.2);
border-radius: 18px;
z-index: 11;
.text-gray{
  color: rgba(0, 0, 0, 0.50);
  font-size: 14px;
}

h5 > div{
  color: black;
  font-size: 1rem;
}
}

@media(max-width:768px){
  .sticky-sidebar{
    flex-direction: row;
    top: 0px;
    width: 100%;
    overflow: auto;
    border-radius: 0px !important;
    overflow-x: scroll;     /* Enable horizontal scroll */
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
    scrollbar-width: none;  /* Firefox */
    
    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, and Edge */
    }

    a{
      white-space: nowrap;
      border-radius: 0px !important;
      border: none;
      border-right: 1px solid #80808045;
    }
    .ps-3{
      padding-left: 0px !important;
    }
    h5 > div{
      font-size: 14px;
    }
    .text-gray{
      font-size: 12px;
    }
  }
}
