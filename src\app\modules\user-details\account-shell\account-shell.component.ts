import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { AccountService } from '../../account/services/account.service';
import { ToastrService } from 'ngx-toastr';
import { UtilsService } from 'src/app/shared/services/utils.service';
import * as signalR from '@microsoft/signalr';
import { ChatService } from 'src/app/shared/services/chat.service';
import { filter } from 'rxjs';
import { filterNonNull } from 'src/app/shared/oprators/filter-null-values';

@Component({
  selector: 'app-account-shell',
  templateUrl: './account-shell.component.html',
  styleUrls: ['./account-shell.component.scss'],
})
export class AccountDetailShellComponent implements OnInit {
  @ViewChild('profile') profile!: ElementRef;
  @ViewChild('bannerFile') bannerInput!: ElementRef;
  activeTab = 1;
  menus: any[] = [
    {
      id: 1,
      icon: 'fa fa-bars',
      title: 'General information',
      description: 'Company, and more',
      url: '/my-account/company-details',
    },
    {
      id: 2,
      icon: 'fa fa-user',
      title: 'User Details',
      description: 'Name, and more',
      url: '/my-account/user-detail',
    },
    {
      id: 3,
      icon: 'fa fa-comments',
      title: 'Chat',
      description: 'Messages and more',
      url: '/my-account/chat',
    },
    {
      id: 4,
      icon: 'fa fa-bell',
      title: 'Notifications',
      description: 'Your email notifications',
      url: '/my-account/notications',
    },
    {
      id: 5,
      icon: 'fa fa-key',
      title: 'Password',
      description: 'Update password',
      url: '/my-account/reset-password',
    },
    {
      id: 6,
      icon: 'fa fa-file',
      title: 'Documents',
      description: 'Verification documents',
      url: '/my-account/manage-documents',
    },
    {
      id: 7,
      icon: 'fa fa-heart',
      title: 'My Connections',
      description: 'Your Selected profiles',
      url: '/my-account/favorites',
    },
    {
      id: 8,
      icon: 'fas fa-id-badge',
      title: 'Your Plans',
      description: 'Your Purchased plans',
      url: '/my-account/view-plans',
    },
  ];
  selectedImage: any = null;
  userState$: any = {};
  selectedBanner: any = {};
  hubConnection!: signalR.HubConnection;
  profileImage = '';
  constructor(
    private router: Router,
    public account: AccountService,
    private toastrService: ToastrService,
    private readonly utils: UtilsService,
    private readonly chatService: ChatService
  ) {
    router.events
      .pipe(filter((e) => e instanceof NavigationEnd))
      .subscribe((router: any) => {
        this.activeTab = this.menus.find((x) => x.url == router.url)?.id || 1;
      });
  }

  ngOnInit(): void {
    // this.account.profileImage$.subscribe((profileImage: string) => {
    //   this.profileImage = this.utils.setProfileImage(profileImage);
    // });
    this.account.user$.pipe(filterNonNull()).subscribe((response) => {
      if (!response) return;
      this.userState$ = response;
      // this.setConversationCount();
      this.profileImage =
        this.userState$?.userType == 3
          ? this.userState$?.companyPhoto
          : this.userState$?.profilePhoto;
      this.selectedBanner = this.userState$?.companyBanner;
      if (this.userState$?.userType == 1) {
        this.menus.shift();
      }
    });
  }

  setConversationCount() {
    this.chatService.unReadMessagesConversationsCount.subscribe((count) => {
      if (count) {
        this.menus.find((x) => x.id == 3).title = `<span>Chat </span>`;
      }
    });
  }

  navigateTo(m: any) {
    if (this.activeTab === m.url) return;
    this.activeTab = m.id;
    if (m.url) {
      this.router.navigateByUrl(m.url);
    }
    this.chatService.getUnreadChatMessagesCount(this.userState$.userId);
    if (window.innerWidth <= 768) {
      // Adjust the width threshold as needed
      setTimeout(() => {
        const contentElement = document.getElementById('content');
        if (contentElement) {
          contentElement.scrollIntoView({ behavior: 'smooth' });
        }
      }, 1000);
    }
  }

  updateProfile() {
    this.profile.nativeElement.click();
  }

  async uploadProfileImage($event: any) {
    const valid = await this.displaySelectedImage($event.target.files[0]);
    if (!valid) return;
    const formData = new FormData();
    formData.append('UserId', this.userState$.userId);
    formData.append('photo', $event.target.files[0]);
    if (this.userState$?.userType === 3) {
      this.account
        .uploadCompanyProfileImage(formData)
        .subscribe((response: any) => {
          if (response.data) {
            this.toastrService.success('Company logo updated successfully');
          }
        });
    } else {
      this.account.uploadProfileImage(formData).subscribe((response) => {});
    }
  }

  uploadBanner() {
    this.bannerInput.nativeElement.click();
  }

  async uploadBannerImage($event: any) {
    const valid = await this.displaySelectedImage(
      $event.target.files[0],
      'banner'
    );
    if (!valid) return;
    const formData = new FormData();
    formData.append('UserId', this.userState$.userId);
    formData.append(
      'file',
      $event.target.files[0],
      this.utils.generateRandomFileName(10)
    );
    this.account
      .uploadBanner(formData, this.userState$.companyId)
      .subscribe((response: any) => {
        if (response.data) {
          this.toastrService.success('Company banner updated successfully');
        }
      });
  }

  displayBannerImage(file: File): void {
    const reader = new FileReader();
    reader.onload = (e) => {
      this.selectedBanner = e.target?.result;
      let user: any = localStorage.getItem('user');
      user = JSON.parse(user);
      user.companyBanner = this.selectedBanner;
      localStorage.setItem('user', JSON.stringify(user));
      // this.account.companyBanner.next(this.selectedImage);
    };
    reader.readAsDataURL(file);
  }

  displaySelectedImage(file: File, fileType = 'profile') {
    return new Promise((resolve, reject) => {
      if (!file.type.startsWith('image/')) {
        this.toastrService.error('Selected file is not an image.');
        return resolve(false);
      }
      const isProfile = fileType == 'profile';

      // Check the file size (in bytes)
      if (isProfile && file.size > this.utils.getProfileImageSize()) {
        this.toastrService.error('Image size should be under 2MB.');
        return resolve(false);
      } else if (file.size > this.utils.getBannerImageSize()) {
        this.toastrService.error('Image size should be under 5MB.');
        return resolve(false);
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const file = e.target?.result;
        let user: any = localStorage.getItem('user');
        if (user) {
          user = JSON.parse(user);
        }
        if (fileType === 'profile') {
          user.companyPhoto = file;
          this.profileImage = user.companyPhoto;
        } else {
          user.companyBanner = file;
          this.selectedBanner = file;
        }
        localStorage.setItem('user', JSON.stringify(user));
      };
      reader.readAsDataURL(file);
      resolve(true);
    });
  }
}
