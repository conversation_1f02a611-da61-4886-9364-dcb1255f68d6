<div class="fc-chat-wrapper">
  <ul class="category-navbar-item gap-5">
    <li
      class="nav-item"
      role="button"
      (click)="setActiveTab('experts')"
      [class.active]="activeTab === 'experts'"
    >
      <a class="" aria-current="page">
        <span>
          <svg
            width="17"
            height="15"
            viewBox="0 0 17 15"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M5.11328 0.958984C3.18058 0.958984 1.61328 2.51018 1.61328 4.42398C1.61328 5.96888 2.22578 9.63548 8.25488 13.342C8.36288 13.4077 8.48686 13.4425 8.61328 13.4425C8.7397 13.4425 8.86368 13.4077 8.97168 13.342C15.0008 9.63548 15.6133 5.96888 15.6133 4.42398C15.6133 2.51018 14.046 0.958984 12.1133 0.958984C10.1806 0.958984 8.61328 3.05898 8.61328 3.05898C8.61328 3.05898 7.04598 0.958984 5.11328 0.958984Z"
              stroke="#014681"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </span>
        Channel Experts
      </a>
    </li>
    <li
      class="nav-item"
      role="button"
      (click)="setActiveTab('endUsers')"
      [class.active]="activeTab === 'endUsers'"
    >
      <a class="" aria-current="page">
        <span>
          <svg
            width="15"
            height="14"
            viewBox="0 0 15 14"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M7.31641 14C3.46641 14 0.316406 10.85 0.316406 7C0.316406 3.15 3.46641 0 7.31641 0C11.1664 0 14.3164 3.15 14.3164 7C14.3164 10.85 11.1664 14 7.31641 14ZM7.31641 1.16667C4.10807 1.16667 1.48307 3.79167 1.48307 7C1.48307 10.2083 4.10807 12.8333 7.31641 12.8333C10.5247 12.8333 13.1497 10.2083 13.1497 7C13.1497 3.79167 10.5247 1.16667 7.31641 1.16667Z"
              fill="#014681"
            />
            <path
              d="M6.73177 9.33317C6.55677 9.33317 6.4401 9.27484 6.32344 9.15817L4.57344 7.40817C4.3401 7.17484 4.3401 6.82484 4.57344 6.5915C4.80677 6.35817 5.15677 6.35817 5.3901 6.5915L7.1401 8.3415C7.37344 8.57484 7.37344 8.92484 7.1401 9.15817C7.02344 9.27484 6.90677 9.33317 6.73177 9.33317Z"
              fill="#014681"
            />
            <path
              d="M6.73177 9.33317C6.55677 9.33317 6.4401 9.27484 6.32344 9.15817C6.0901 8.92484 6.0901 8.57484 6.32344 8.3415L9.82344 4.8415C10.0568 4.60817 10.4068 4.60817 10.6401 4.8415C10.8734 5.07484 10.8734 5.42484 10.6401 5.65817L7.1401 9.15817C7.02344 9.27484 6.90677 9.33317 6.73177 9.33317Z"
              fill="#014681"
            />
          </svg>
        </span>
        Business Users
      </a>
    </li>
  </ul>
</div>

<section *ngIf="activeTab === 'experts'" class="p-0" style="height: 80vh">
  <div class="fc-chat-grid-section">
    <div class="fc-chat-user-list" *ngIf="!receiverUser || !isMobile()">
      <div class="fc-search-input">
        <input
          type="text"
          class="form-control"
          id="inlineFormInputGroup"
          placeholder="Search by name"
          aria-label="search"
          [(ngModel)]="searchUser"
          (ngModelChange)="searchChatItem($event)"
        />
      </div>
      <ng-container *ngIf="!loadingUsers">
        <div class="fc-chat-list">
          <div
            class="fc-user-list"
            *ngFor="let item of onlineUsers"
            (click)="sendMessage(item)"
            [ngClass]="{
              'bg-primary bg-opacity-25 custom-item-selected': item?.selected
            }"
          >
            <div class="fc-user-avtar">
              <img
                [src]="item?.profilePhoto || './assets/svgs/focile.svg'"
                alt="User 1"
                class="img-fluid focile-avatar"
              />
            </div>
            <div class="fc-user-detail">
              <div class="first-line">
                <label>{{ item?.userName }}</label>
                <span>
                  {{ item.lastUpdate | fromNow }}
                </span>
              </div>
              <div class="second-list">
                <label>{{ item.message | slice : 0 : 25 }}</label>
                <span *ngIf="item.isActive" class="msg-status">
                  <div>
                    <svg
                      width="22"
                      height="15"
                      viewBox="0 0 22 15"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M7.90625 8.53125L11.3438 11.9688L19.5938 3.03125M2.40625 8.53125L5.84375 11.9688L2.40625 8.53125ZM10.6562 6.46875L14.0938 3.03125L10.6562 6.46875Z"
                        stroke="#0cfd00"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </div>
                  <div class="count-badge"></div>
                </span>
              </div>
            </div>
          </div>
        </div>
      </ng-container>
    </div>
    <div class="h-100">
      <div class="fc-saparete-chat-section right-column" *ngIf="receiverUser">
        <div class="fc-chat-user-header header">
          <div class="left-">
            <div>
              <img
                class="focile-avatar"
                [src]="receiverUser.profilePhoto || './assets/svgs/focile.svg'"
              />
            </div>
            <div class="d-flex flex-column">
              <label class="user-nm">{{
                receiverUser?.userName | titlecase
              }} - {{ receiverUser?.companyName | titlecase }} </label>
              <!-- <span class="last-seen">Online - Last seen, 2.02pm</span> -->
            </div>
          </div>
          <div class="right-">
            <div class="close-icon">
              <i class="fa fa-times" (click)="closeChat()"></i>
            </div>
          </div>
        </div>
        <div class="messages" id="scrollContainer">
          <div
            *ngFor="let item of receiverUser?.messagesList"
            class="message"
            [class]="item.isSender ? 'outgoing' : 'incoming'"
          >
            <div>{{ item.message }}</div>
            <!-- <label class="time-stemp">{{ item.readAt }}</label> -->
          </div>
        </div>
        <div class="fc-chat-footer">
          <input
            type="text"
            class="form-control"
            aria-label="message…"
            placeholder="Write message…"
            name="message"
            [(ngModel)]="message"
            (keydown.enter)="sendPrivateMessage()"
          />
          <div class="related-chat">
            <div class="emoji-container mt-1 mx-3">
              <i
                (click)="showEmojies = !showEmojies"
                class="far fa-smile emoji-icon"
              ></i>
              <div class="emoji-picker" [class.active]="showEmojies">
                <emoji-mart (emojiClick)="addEmoji($event)"></emoji-mart>
              </div>
            </div>
            <button
              class="bg-primary"
              (click)="sendPrivateMessage()"
              type="submit"
            >
              Send
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<section *ngIf="activeTab === 'endUsers'" class="p-0" style="height: 80vh">
  <div class="fc-chat-grid-section">
    <div class="fc-chat-user-list" *ngIf="!receiverUser || !isMobile()">
      <div class="fc-search-input">
        <input
          type="text"
          class="form-control"
          id="inlineFormInputGroup"
          placeholder="Search by name"
          aria-label="search"
          [(ngModel)]="searchUser"
          (ngModelChange)="searchChatItem($event)"
        />
      </div>
      <ng-container *ngIf="!loadingUsers">
        <div class="fc-chat-list">
          <div
            class="fc-user-list"
            *ngFor="let item of onlineUsers"
            (click)="sendMessage(item)"
            [ngClass]="{
              'bg-primary bg-opacity-25 custom-item-selected': item?.selected
            }"
          >
            <div class="fc-user-avtar">
              <img
                [src]="item?.profilePhoto || './assets/svgs/focile.svg'"
                alt="User 1"
                class="img-fluid focile-avatar"
              />
            </div>
            <div class="fc-user-detail">
              <div class="first-line">
                <label>{{ item?.userName }}</label>
                <span>
                  {{ item.lastUpdate | fromNow }}
                </span>
              </div>
              <div class="second-list">
                <label>{{ item.message | slice : 0 : 25 }}</label>
                <span *ngIf="item.isActive" class="msg-status">
                  <div>
                    <svg
                      width="22"
                      height="15"
                      viewBox="0 0 22 15"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M7.90625 8.53125L11.3438 11.9688L19.5938 3.03125M2.40625 8.53125L5.84375 11.9688L2.40625 8.53125ZM10.6562 6.46875L14.0938 3.03125L10.6562 6.46875Z"
                        stroke="#0cfd00"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </div>
                  <div class="count-badge"></div>
                </span>
              </div>
            </div>
          </div>
        </div>
      </ng-container>
    </div>
    <div class="h-100">
      <div class="fc-saparete-chat-section right-column" *ngIf="receiverUser">
        <div class="fc-chat-user-header header">
          <div class="left-">
            <div>
              <img
                class="focile-avatar"
                [src]="receiverUser.profilePhoto || './assets/svgs/focile.svg'"
              />
            </div>
            <div class="d-flex flex-column">
              <label class="user-nm"
                >{{ receiverUser?.userName | titlecase }} - 
                {{ receiverUser?.companyName | titlecase }}
              </label>
              <span class="last-seen">Online - Last seen, 2.02</span>
            </div>
          </div>
          <div class="right-">
            <div class="close-icon">
              <i class="fa fa-times" (click)="closeChat()"></i>
            </div>
          </div>
        </div>
        <div class="messages" id="scrollContainer">
          <div
            *ngFor="let item of receiverUser?.messagesList"
            class="message"
            [class]="item.isSender ? 'outgoing' : 'incoming'"
          >
            <div>{{ item.message }}</div>
            <!-- <label class="time-stemp">{{ item.readAt }}</label> -->
          </div>
        </div>
        <div class="fc-chat-footer">
          <input
            type="text"
            class="form-control"
            aria-label="message…"
            placeholder="Write message…"
            name="message"
            [(ngModel)]="message"
            (keydown.enter)="sendPrivateMessage()"
          />
          <div class="related-chat">
            <div class="emoji-container mt-1 mx-3">
              <i
                (click)="showEmojies = !showEmojies"
                class="far fa-smile emoji-icon"
              ></i>
              <div class="emoji-picker" [class.active]="showEmojies">
                <emoji-mart (emojiClick)="addEmoji($event)"></emoji-mart>
              </div>
            </div>
            <button
              class="bg-primary"
              (click)="sendPrivateMessage()"
              type="submit"
            >
              Send
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<ng-template #noChatsTemplate>
  <div class="p-1 py-3">
    <div class="alert alert-info">
      no member found in your chat list click on connect on respectives member
      card to chat with them.
    </div>
    <div>
      <button class="btn btn-primary w-100" [routerLink]="['/home']">
        Goto home page
      </button>
    </div>
  </div>
</ng-template>
