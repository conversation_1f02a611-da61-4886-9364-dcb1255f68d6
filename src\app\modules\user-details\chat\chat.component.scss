.chat-grid {
  display: grid;
  grid-template-columns: 30% 70%; /* 30% width for the first column, 70% for the second */
  gap: 10px; /* Optional gap between columns */
  padding: 20px; /* Optional padding around the grid container */
}

.user-list {
  display: flex;
  flex-direction: column;
}
.user {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);

  .user-image {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
  }

  .user-info {
    flex-grow: 1;
    margin-left: 15px;

    .user-name {
      font-weight: bold;
      margin: 0;
    }

    .last-message {
      font-size: 0.85rem;
      color: #666;
      margin-top: 5px;
    }
  }

  .unread-count {
    background-color: #ff5a5f;
    color: white;
    font-size: 0.85rem;
    font-weight: bold;
    padding: 5px 10px;
    border-radius: 20px;
  }
}

.right-column { 
  height: 100%;

  .header {
    text-align: center;
    position: sticky;
    top: 0;
    z-index: 10;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .chat-info {
      display: flex;
      align-items: center;
      gap: 10px;
      width: 100%;
    }

    .user-name {
      font-size: 1.5rem;
    }
  }

  .messages {
    flex-grow: 1;
    overflow-y: auto;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    height: 68vh;
    gap: 1rem;
  }

  .message {
    max-width: 70%;
    margin-bottom: 15px;
    padding:5px 10px;
    border-radius: 8px;
    display: flex;
    position: relative;
    cursor: pointer;

    &.incoming {
      background-color: #e0e0e0;
      align-self: flex-start;
      border-radius: 20px;
      font-size: 14px;      

      &:before{
        content: '';
        width: 10px;
        height: 10px;
        border-radius: 5px;
        background-color:#e0e0e0;
        position: absolute;
        bottom: -5px;
        left:-10px;
      }
    }

    &.outgoing {
      background-color: var(--bs-primary);
      color: white;
      align-self: flex-end;
      border-radius: 20px;
      font-size: 14px;

      
      &:before{
        content: '';
        width: 10px;
        height: 10px;
        border-radius: 5px;
        background-color: var(--bs-primary);
        position: absolute;
        bottom:  -5px;
        right:-10px;
      }

      .time-stemp{
        right: 12px;
        left: auto;
      }
    }

    .time-stemp{
      position: absolute;
      width: auto;
      font-size: 14px !important;
      white-space: nowrap;
      bottom: -20px;
      left: 12px;
      color: #707070;
    }
  }

  .footer {
    display: flex;
    align-items: center;
    border-top: 1px solid #ccc;
    padding: 10px;

    input {
      flex-grow: 1;
      padding: 10px;
      border: 1px solid #ccc;
      border-radius: 20px;
      margin-right: 10px;
    }

    button {
      padding: 10px 20px;
      background-color: #4caf50;
      color: white;
      border: none;
      border-radius: 20px;
      cursor: pointer;

      &:hover {
        background-color: #45a049;
      }
    }
  }
}

.close-icon {
  width: 20px;
  height: 20px;
  cursor: pointer;
  padding: 5px;
  flex: 1;
  display: flex;
  justify-content: end;
}

.unread-count {
  font-size: 0.85rem;
  font-weight: bold;
  padding: 5px 10px;
  border-radius: 20px;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .chat-grid {
    grid-template-columns: 1fr;
  }
  .left-column {
    width: 100%;
  }

  .right-column {
    width: 100%;
  }

  .close-icon {
    width: 20px;
    height: 20px;
    cursor: pointer;
    padding: 5px;
    flex: 1;
    display: flex;
    justify-content: end;
  }
}

.un-read-chat-count {
  background-color: var(--bs-primary);
  color: #fff;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.emoji-container {
  position: relative;
  display: inline-block;

  .emoji-icon {
    cursor: pointer;
    font-size: 24px;
    color: #555; // Adjust the color as needed

    &:hover {
      color: #000; // Change color on hover
    }
  }

  .emoji-picker {
    position: absolute;
    bottom: 40px; // Adjust this based on the height of the emoji picker
    left: 0; // Align the bottom-right corner of the picker with the icon
    z-index: 1000;
    display: none;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
    background-color: white;
    border-radius: 8px;
    padding: 10px;
    width: max-content; // Ensure it wraps to the size of emoji-mart

    &.active {
      display: block;
    }
  }
}

.category-navbar-item{
  display: flex;
  flex-direction: row;
  border-bottom: 1px solid #D9DBE9;
  align-items: center;
  justify-content: center;
  list-style: none;
  padding-left: 0px;

  li{
    width: 25%;
    display: flex;
    align-items: start;
    justify-content: center;
    min-height: 40px;
    border-bottom: 2px solid transparent;


    a{
      text-decoration: none;
      font-size:16px;
      font-weight: 500;
      display: flex;
      flex-direction: row;
      gap: 1rem;
      align-items: center;
      color: #A0A3BD;
    }

    span{
        min-width: 26px;
        min-height: 26px;
        left: 0px;
        top: 0px;
        background: rgba(1, 69, 129, 0.1);
        border-radius: 14px;
        display: flex;
        align-items: center;
        justify-content: center;

        svg{
          width: 14px;
        }
    }

    &.active{
      border-color: #014681;

      a{
        color:  #014681;
      }
    }
  }
}

.fc-chat-grid-section{  
  width: 100%;
  height: 100%;
  background: #FFFFFF;
  box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.1);
  border-radius: 15px;
  margin-bottom: 1rem;
  margin-top: 44px;
  display: flex;
  flex-direction: row;
  display: grid;
  grid-template-columns: 30% 70%;
}
.fc-chat-user-list{
    width: 253px;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    border-right: 1px solid rgba(140, 140, 140, 0.20);
    height: 100%;
    overflow: hidden;
}
.fc-saparete-chat-section{
  width:100%;
  display: flex;
  flex-direction: column;
  max-height: 79vh;
}
.fc-chat-list{
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: auto;
}

.fc-user-list{
  display: flex;
  gap: 6px;
  width: 100%;
  padding: 1rem;
  padding-bottom: 0;
}
.fc-user-avtar{
  width: 35px;
  height: 35px;
  overflow: hidden;
  border: 1px solid rgb(227 227 227);
  border-radius: 20px;

  img{
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
.fc-search-input {
  margin-top: 25px;
  margin-inline: 1rem;
  input {
    background:url(../../../../assets/images/bx_search.svg)#FFFFFF no-repeat 15px 14px;
    box-shadow: 0px 4px 5px 2px rgba(1, 70, 129, 0.1);
    border-radius: 25px;
    border: none;
    padding-left: 3rem;
    font-size: 14px;
  }
}
.fc-user-detail {
  display: flex;
  flex-direction: column;
  width: 181px;
  border-bottom: 1px solid rgba(181, 171, 171, 0.40);
  padding-bottom: 1rem;

  .first-line {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    label{
      font-size: 14px;
      font-weight: 500;  
      margin-bottom: 0px;    
    }
    span{
      font-size: 12px;
      color:#7C7C7C;
      margin-bottom: 0px;
    }
  }

  .second-list {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    label{
      font-size: 12px;
      font-weight: 400;  
      margin-bottom: 0px;   
      color: rgba(0, 0, 0, 0.50); 
    }
    span{
      font-size: 12px;
      color:#7C7C7C;
      margin-bottom: 0px;
    }
    .msg-status{
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      gap: 5px;
    }

    .count-badge{
      background-color:#24f200;
      width: 14px;
      height: 14px;
      border-radius: 10px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 10px;
    }
  }
}

.fc-chat-user-header{
  border-bottom: 1px solid rgba(180, 171, 171, 0.365);
  padding:1rem;
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  .left-{
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 1rem;

    .user-nm{
      font-size: 1rem;
      font-weight: 500;
      text-align: left;
    }
    .last-seen{
      font-size: 14px;
      color: #303030;
      font-weight: 400;
      text-align: left;
    }
  }
}

.fc-scrollcontainer-section{
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: #fff;
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  

  .messages {
    flex-grow: 1;
    overflow-y: auto;
    padding:1.5rem;
    display: flex;
    flex-direction: column;
  }

  .message {
    max-width: 70%;
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 8px;

    &.incoming {
      background-color: #e0e0e0;
      align-self: flex-start;
      border-radius: 20px 20px 20px 0px;
      position: relative;

      &:before{
        content: '';
        width: 10px;
        height: 10px;
        border-radius: 5px;
        background-color:#e0e0e0;
        position: absolute;
        bottom: 10px;
        left:-10px;
      }
    }

    &.outgoing {
      background-color: var(--bs-primary);
      color: white;
      align-self: flex-end;
      border-radius: 20px 20px 0px 20px;
    }
  }
}

.fc-chat-footer{
      display: flex;
      align-items: center;
      padding: 10px;
      position: relative;

      input{
       background-color:#EFF6FC;
       border-radius:50px; 
       border:none;
       padding-left: 1.5rem;
      }
}


.related-chat{
  position: absolute;
  right: 22px;
  display: flex;
  align-items: center;
  top: 17px;

  button{
    border: none;
    color: white;
    border-radius: 15px;
    font-size:14px;
    padding: 5px 10px; 
  }
}

.custom-item-selected{
  .fc-user-detail{
    border-bottom: 0;
  }
}

@media(max-width:768px){
  .category-navbar-item{
    li{
      width: 100%;      
    }
  }
  .fc-chat-grid-section{
    margin-top: 1.5rem;
    grid-template-columns: 100%;
  }
  .fc-chat-user-list{
    width: 100%;
    border-right: 0px;
  }
  .fc-user-detail{
    width: calc(100% - 40px);
  }
}