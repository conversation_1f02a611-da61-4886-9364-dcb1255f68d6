import { Component, HostListener, OnInit } from '@angular/core';
import { ChatService } from 'src/app/shared/services/chat.service';
import { AccountService } from '../../account/services/account.service';
import { catchError, finalize, map, of, take, tap } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ModalService } from 'src/app/shared/services/modal.service';
import { ConfirmationComponent } from 'src/app/shared/modals/confirmation/confirmation.component';
import { ActivatedRoute, Router } from '@angular/router';
import { filterNonNull } from 'src/app/shared/oprators/filter-null-values';

@Component({
  selector: 'app-chat',
  templateUrl: './chat.component.html',
  styleUrls: ['./chat.component.scss'],
})
export class ChatComponent implements OnInit {
  onlineUsers: any[] = [];
  onlineUsersCopy: any[] = [];
  userState$: any = {};
  message: any = '';
  receiverUser: any;
  messagesList: any = [];
  messagesLoading = false;
  searchUser = '';
  loadingUsers = false;
  activeUsers: any = {};
  showEmojies = false;
  isMobileView = false;
  items: any[] = [
    {
      icon: '',
      label: 'Edit',
      id: 'edit',
    },
    {
      icon: '',
      label: 'Delete',
      id: 'delete',
    },
  ];
  editMessagePayload: any = {};
  isEditMode = false;
  isSendingMessage = false;
  navigateTo = '';

  activeTab: string = 'experts';

  constructor(
    private account: AccountService,
    private chatService: ChatService,
    private http: HttpClient,
    private router: Router,
    private modalService: ModalService,
    private activatedRout: ActivatedRoute
  ) {
    this.account.user$.pipe(filterNonNull()).subscribe((userState) => {
      if (!userState) return;
      this.userState$ = userState;
    });
    this.activatedRout.queryParams.pipe(take(1)).subscribe((response) => {
      this.navigateTo = response.conversationId;
    });
  }

  setActiveTab(tab: string): void {
    this.activeTab = tab;
    this.onlineUsers = [];
    this.onlineUsersCopy = [];
    this.getConversations();
  }

  ngOnInit(): void {
    this.checkScreenSize();
    if (!this.userState$?.userId) return;
    this.loadingUsers = true;
    this.getConversations();
    this.chatService.activeUsers.subscribe((users: any) => {
      this.activeUsers = users;
      this.updateUserStates(this.activeUsers);
    });

    this.chatService.updateMessageId$
      .asObservable()
      .subscribe((response: any) => {
        const index = this.receiverUser?.messagesList?.findIndex(
          (x: { id: any }) => x.id == response.messageId
        );
        this.receiverUser.messagesList[index].message = response.updateMessage;
      });

    this.chatService.onNewMessage$.subscribe((message: any) => {
      if (this.receiverUser?.userId) {
        this.onlineUsers.forEach((x) => {
          if (message.userId == x.userId) {
            if (x.messagesList) {
              x.messagesList.push({
                message: message.message,
                createdAt: new Date(),
                id: message.messageId,
              });
            } else {
              x.message = message.message;
              x.id.messageId;
              x.unReadCount += 1;
            }
          }
        });
      } else {
        this.onlineUsers.forEach((x) => {
          if (message.userId == x.userId) {
            x.message = message.message;
            x.unReadCount += 1;
          }
        });
      }
      this.scrollToBottom();
    });
  }

  getConversations() {
    this.loadingUsers = true;

    this.chatService
      .getChatList(this.userState$.userId, this.activeTab === 'endUsers')
      .pipe(
        map((response: any) => response.data),
        finalize(() => {
          this.loadingUsers = false;
          setTimeout(() => {
            this.chatService.refreshUsers.next(true);
          }, 100);
        })
      )
      .subscribe((onlineUsers) => {
        this.onlineUsers = onlineUsers;
        this.onlineUsersCopy = [...onlineUsers];
        if (this.navigateTo) {
          this.receiverUser = this.onlineUsersCopy.find(
            (x) => x.convertionId == this.navigateTo
          );
          this.receiverUser && this.getConversationMessages(this.navigateTo);
        }
        if (Object.keys(this.activeUsers).length) {
          this.updateUserStates(this.activeUsers);
        }
      });
  }

  updateUserStates(users: any) {
    this.onlineUsers.forEach((availableUser) => {
      const connectionId = users[availableUser.userId];
      if (connectionId) {
        availableUser.connectionId = connectionId;
        availableUser.isActive = true;
      } else {
        availableUser.isActive = false;
      }
    });
  }

  sendMessage(item: any) {
    this.onlineUsers.forEach((x) => {
      x.selected = false;
    });
    item.selected = true;
    if (this.receiverUser && this.receiverUser.userId === item.userId) return;
    this.receiverUser = item;
    this.message = [];
    this.messagesList = [];
    this.messagesLoading = true;
    this.chatService.getUnreadChatMessagesCount(this.userState$?.userId);
    this.getConversationMessages(item.convertionId);
  }

  /**
   * Fetches conversation messages for a given conversation ID, updates the receiver's message list,
   * resets the unread count, and determines the sender of each message.
   *
   * @param {string} conversationId - The ID of the conversation to retrieve messages for.
   * @returns {void}
   */
  getConversationMessages(convertionId: string) {
    this.messagesLoading = true;
    this.fetchMessages(convertionId)
      .pipe(
        map((messages) => this.assignSender(messages)),
        tap((messages) => this.updateReceiverMessages(messages)),
        finalize(() => (this.messagesLoading = false)),
        catchError((error) => {
          console.error('Failed to load messages:', error);
          return of([]);
        })
      )
      .subscribe(() => this.scrollToBottom());
  }

  /**
   * Fetches messages for a given conversation ID.
   * @param {string} conversationId - The ID of the conversation.
   * @returns {Observable<Message[]>} - An observable of the fetched messages.
   */
  private fetchMessages(conversationId: string) {
    return this.http
      .get<{ data: any[] }>(
        `Chat/GetConvertionMessages?convertionId=${conversationId}`
      )
      .pipe(map((response) => response.data || []));
  }

  /**
   * Assigns sender information to each message, marking it as sent by the current user if applicable.
   * @param {Message[]} messages - The array of messages.
   * @returns {Message[]} - The array of messages with the sender marked.
   */
  private assignSender(messages: any[]): any[] {
    return messages.map((message) => ({
      ...message,
      isSender:
        message.sender.toLowerCase() === this.userState$.userId.toLowerCase(),
    }));
  }

  /**
   * Updates the receiver's message list and resets the unread count.
   * @param {Message[]} messages - The array of messages to update.
   */
  private updateReceiverMessages(messages: any[]): void {
    if (this.receiverUser?.userId) {
      this.receiverUser.messagesList = [...messages];
      this.receiverUser.unReadCount = 0;
    }
  }

  sendPrivateMessage() {
    const message = JSON.parse(JSON.stringify(this.message));
    this.message = '';
    this.isSendingMessage = true;
    this.chatService.sendMessageAPI(
      this.receiverUser.userId,
      message,
      this.receiverUser.convertionId,
      this.userState$.userId
    );
    this.chatService.messageId$.asObservable().subscribe((id) => {
      this.isSendingMessage = false;
      this.receiverUser?.messagesList.push({
        message,
        sender: this.userState$.userId,
        isSender: true,
        reciverId: this.receiverUser.userId,
        createdAt: new Date(),
        id,
      });
    });
    setTimeout(() => {
      this.setMessageList();
    }, 1000);
  }

  updateMessage() {
    this.chatService
      .updateMessage(
        this.receiverUser.userId,
        this.editMessagePayload.id,
        this.message
      )
      .then(() => {
        const index = this.receiverUser?.messagesList?.findIndex(
          (x: { id: any }) => x.id == this.editMessagePayload.id
        );
        this.receiverUser.messagesList[index].message = this.message;
        this.message = null;
        this.isEditMode = false;
      });
  }

  setMessageList() {
    if (this.receiverUser?.userId) {
      this.receiverUser.messagesList.map((x: any) => {
        x.isSender =
          `${x.sender}`.toLowerCase() ==
          `${this.userState$.userId}`.toLowerCase();
      });
      this.scrollToBottom();
    }
  }

  navigateToUser() {
    this.router.navigate(['/user-details/' + this.receiverUser.userId]);
  }

  /**
   * Scrolls to the bottom of the specified scrollable container.
   *
   * This method waits for a short delay to ensure the DOM has updated
   * before adjusting the scroll position. It scrolls to the bottom of
   * the container with the ID 'scrollContainer'.
   *
   * @returns {void}
   */
  scrollToBottom(): void {
    setTimeout(() => {
      const element: any = document.getElementById('scrollContainer');
      if (element) {
        element.scrollTop = element?.scrollHeight;
      }
    }, 500);
  }

  searchChatItem($event: string) {
    this.onlineUsers = [];
    this.onlineUsersCopy = this.onlineUsersCopy.map((x) => {
      x.selected = this.receiverUser?.userId === x.userId;
      return x;
    });
    if (!$event?.trim()?.length) {
      return (this.onlineUsers = JSON.parse(
        JSON.stringify(this.onlineUsersCopy)
      ));
    }
    this.onlineUsersCopy.forEach((e) => {
      if (`${e.userName}`.toLowerCase().includes($event.toLowerCase())) {
        this.onlineUsers.push(e);
      }
    });
  }

  addEmoji(emoji: any) {
    this.showEmojies = false;
    this.message += emoji.emoji.native;
  }

  handleMessageAction(actionType: string, item: any) {
    this.isEditMode = false;
    if (actionType === 'edit') {
      this.editMessagePayload = item;
      this.isEditMode = true;
      this.message = item.message;
    } else if (actionType === 'delete') {
      this.modalService.openModal('confirmation', {
        initialState: {
          message: `are you want to delete this message ?`,
          firstButtonText: 'Delete now',
          onConfirm: () => {
            this.deleteMessage(item.id);
          },
        },
      });
    }
  }

  deleteMessage(messageId: any) {
    this.http
      .delete('chat/deleteMessage', {
        body: {
          messageId: messageId,
          isDeleteForMe: false,
          isDeleteForAll: true,
        },
      })
      .subscribe((response: any) => {
        if (response.data) {
          const index = this.receiverUser?.messagesList?.findIndex(
            (x: { id: any }) => x.id == messageId
          );
          (this.receiverUser.messagesList as []).splice(index, 1);
        }
        this.modalService.closeModal();
      });
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: Event): void {
    this.checkScreenSize(); // Check screen size on window resize
  }

  // Method to check screen size and update the view state
  checkScreenSize(): void {
    const width = window.innerWidth;
    this.isMobileView = width <= 768; // Adjust the width as needed for mobile view
  }

  isMobile(): boolean {
    return this.isMobileView;
  }

  closeChat() {
    this.receiverUser = null;
    this.onlineUsers.forEach((x) => (x.selected = false));
  }
}
