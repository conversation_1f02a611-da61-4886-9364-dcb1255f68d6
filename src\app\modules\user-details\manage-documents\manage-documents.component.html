<div class="fc-account-detail-content">

 <div class="tab-content p-0" id="nav-tabContent">
    <div *ngIf="!loading" class="tab-pane fade show active">
      <h5 class="fw-bold">Documents</h5>
      <div id="content"></div>
      <div class="mt-4">
        <div class="row gap-4">

          <div class="fc-document-upload-section">
            <div class="fc-ducument-upload-list">
              <div class="col-sm-12">
                 <label class="fc-document-name">Do you have Company Certificate? (if yes then upload)
                 </label>
              </div>
              <div class="col-sm-12 fc-list-of-doc">                
                  <div class="fc-upload-section">
                    <div>
                      <svg width="69" height="60" viewBox="0 0 69 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M36.0271 14.7458L36.1193 14.7733L36.1233 14.7688C36.561 14.8481 36.9951 14.586 37.1237 14.1519C38.2953 10.2152 41.9865 7.46504 46.0989 7.46504C46.5857 7.46504 46.9806 7.07016 46.9806 6.5833C46.9806 6.09643 46.5857 5.70156 46.0989 5.70156C41.0447 5.70156 36.7975 9.06665 35.4338 13.6493C35.2946 14.1162 35.5606 14.6067 36.0271 14.7458Z"
                          fill="#483EA8" stroke="#F9FFF9" stroke-width="0.3" />
                        <path
                          d="M56.3438 42.4384H51.9534C51.5494 42.4384 51.2217 42.1107 51.2217 41.7067C51.2217 41.3027 51.5494 40.9749 51.9534 40.9749H56.3438C62.3956 40.9749 67.3197 36.0509 67.3197 29.999C67.3197 23.9471 62.3956 19.023 56.3438 19.023H56.2382C56.026 19.023 55.8242 18.9311 55.6852 18.7706C55.5462 18.6101 55.4834 18.3974 55.5138 18.1873C55.5791 17.7315 55.612 17.2737 55.612 16.8279C55.612 11.5829 51.3444 7.31531 46.0995 7.31531C44.059 7.31531 42.1131 7.95296 40.4719 9.15978C40.1112 9.42478 39.599 9.30718 39.3905 8.91047C34.7425 0.0596993 22.6023 -1.12887 16.3082 6.57053C13.6568 9.81417 12.615 14.0336 13.4498 18.146C13.5418 18.6002 13.1942 19.0236 12.7327 19.0236H12.4395C6.3876 19.0236 1.46353 23.9477 1.46353 29.9996C1.46353 36.0514 6.3876 40.9755 12.4395 40.9755H16.8298C17.2338 40.9755 17.5615 41.3032 17.5615 41.7072C17.5615 42.1113 17.2338 42.439 16.8298 42.439H12.4395C5.5805 42.439 0 36.8585 0 29.9995C0 23.3329 5.27155 17.8742 11.8651 17.5731C11.2457 13.3066 12.4301 9.00295 15.1751 5.64437C21.9138 -2.5996 34.828 -1.67556 40.2871 7.51707C42.0287 6.42522 44.0215 5.85244 46.0992 5.85244C52.4538 5.85244 57.4892 11.261 57.0486 17.58C63.5813 17.9463 68.7829 23.3763 68.7829 29.999C68.7829 36.8585 63.2024 42.4384 56.3434 42.4384L56.3438 42.4384Z"
                          fill="#483EA8" />
                        <path
                          d="M15.85 41.2935C15.85 51.4634 24.1237 59.737 34.2935 59.737C44.4634 59.737 52.737 51.4633 52.737 41.2935C52.737 31.1235 44.4634 22.85 34.2935 22.85C24.1235 22.85 15.85 31.1237 15.85 41.2935ZM17.6138 41.2935C17.6138 32.0966 25.0964 24.6138 34.2935 24.6138C43.4904 24.6138 50.9732 32.0964 50.9732 41.2935C50.9732 50.4904 43.4904 57.9732 34.2935 57.9732C25.0966 57.9732 17.6138 50.4905 17.6138 41.2935Z"
                          fill="#483EA8" stroke="#F9FFF9" stroke-width="0.3" />
                        <path
                          d="M33.9398 48.6577C33.9398 49.0363 34.2469 49.3434 34.6256 49.3434C35.0041 49.3434 35.3113 49.0367 35.3113 48.6577V34.7291C35.3113 34.3504 35.0042 34.0434 34.6256 34.0434C34.2469 34.0434 33.9398 34.3504 33.9398 34.7291V48.6577Z"
                          fill="#483EA8" stroke="#483EA8" stroke-width="0.3" />
                        <path
                          d="M34.6262 35.7004L30.8255 39.5011L34.6262 35.7004ZM34.6262 35.7004L38.4269 39.5012C38.5606 39.6349 38.7367 39.702 38.9118 39.702L34.6262 35.7004ZM29.8556 39.5012C30.1234 39.769 30.5578 39.7691 30.8254 39.5012L38.9118 39.702C39.0866 39.702 39.2628 39.6355 39.3967 39.5011C39.6645 39.2332 39.6645 38.7992 39.3967 38.5314L35.111 34.2457C34.8432 33.9779 34.4088 33.9778 34.1412 34.2457C34.1412 34.2458 34.1412 34.2458 34.1411 34.2458L29.8556 38.5314C29.5877 38.7993 29.5877 39.2333 29.8556 39.5012Z"
                          fill="#483EA8" stroke="#483EA8" stroke-width="0.3" />
                      </svg>
                    </div>
                    <focile-button (onClick)="handleOnClick('companyCertificate')"></focile-button>
                    <strong>Browse</strong>
                    <label>Supported formates: JPEG, PNG, GIF, MP4, PDF, PSD, AI, Word, PPT</label>
                  </div>
                  <div class="fc-uploaded-file">
                    <label>Uploaded</label>
                    <input
                    #companyCertificate
                    class="d-none"
                    type="file"
                    name="owenrCertificate"
                    id="owenrCertificate"
                    [accept]="accept"
                    (change)="handleFileInput($event.target, 'companyCertificate')"
                  />
                  <input
                    readonly
                    #companyCertificate
                    type="text"
                    class="form-control"
                    [(ngModel)]="documents.companyCertificate.name"
                    name="companyCertificate"
                    id="companyCertificate"
                  />                  
                </div>
              </div>
            </div>
          </div>

          <!-- <div class="col-md-12 mb-4">
            <h6>Do you have Company Certificate? (if yes then upload)</h6>
            <div class="col-md-12 d-flex">
              <div class="W-100 me-3">
                <focile-button (onClick)="handleOnClick('companyCertificate')"
                  >Upload</focile-button
                >
              </div>
              <input
                #companyCertificate
                class="d-none"
                type="file"
                name="owenrCertificate"
                id="owenrCertificate"
                [accept]="accept"
                (change)="handleFileInput($event.target, 'companyCertificate')"
              />
              <input
                readonly
                #companyCertificate
                type="text"
                class="form-control"
                [(ngModel)]="documents.companyCertificate.name"
                name="companyCertificate"
                id="companyCertificate"
              />
            </div>
          </div> -->

          <div class="fc-document-upload-section">
            <div class="fc-ducument-upload-list">
              <div class="col-sm-12">
                 <label class="fc-document-name">Video Verification
                 </label>
              </div>
              <div class="col-sm-12 fc-list-of-doc">                
                  <div class="fc-upload-section">
                    <div>
                      <svg width="69" height="60" viewBox="0 0 69 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M36.0271 14.7458L36.1193 14.7733L36.1233 14.7688C36.561 14.8481 36.9951 14.586 37.1237 14.1519C38.2953 10.2152 41.9865 7.46504 46.0989 7.46504C46.5857 7.46504 46.9806 7.07016 46.9806 6.5833C46.9806 6.09643 46.5857 5.70156 46.0989 5.70156C41.0447 5.70156 36.7975 9.06665 35.4338 13.6493C35.2946 14.1162 35.5606 14.6067 36.0271 14.7458Z"
                          fill="#483EA8" stroke="#F9FFF9" stroke-width="0.3" />
                        <path
                          d="M56.3438 42.4384H51.9534C51.5494 42.4384 51.2217 42.1107 51.2217 41.7067C51.2217 41.3027 51.5494 40.9749 51.9534 40.9749H56.3438C62.3956 40.9749 67.3197 36.0509 67.3197 29.999C67.3197 23.9471 62.3956 19.023 56.3438 19.023H56.2382C56.026 19.023 55.8242 18.9311 55.6852 18.7706C55.5462 18.6101 55.4834 18.3974 55.5138 18.1873C55.5791 17.7315 55.612 17.2737 55.612 16.8279C55.612 11.5829 51.3444 7.31531 46.0995 7.31531C44.059 7.31531 42.1131 7.95296 40.4719 9.15978C40.1112 9.42478 39.599 9.30718 39.3905 8.91047C34.7425 0.0596993 22.6023 -1.12887 16.3082 6.57053C13.6568 9.81417 12.615 14.0336 13.4498 18.146C13.5418 18.6002 13.1942 19.0236 12.7327 19.0236H12.4395C6.3876 19.0236 1.46353 23.9477 1.46353 29.9996C1.46353 36.0514 6.3876 40.9755 12.4395 40.9755H16.8298C17.2338 40.9755 17.5615 41.3032 17.5615 41.7072C17.5615 42.1113 17.2338 42.439 16.8298 42.439H12.4395C5.5805 42.439 0 36.8585 0 29.9995C0 23.3329 5.27155 17.8742 11.8651 17.5731C11.2457 13.3066 12.4301 9.00295 15.1751 5.64437C21.9138 -2.5996 34.828 -1.67556 40.2871 7.51707C42.0287 6.42522 44.0215 5.85244 46.0992 5.85244C52.4538 5.85244 57.4892 11.261 57.0486 17.58C63.5813 17.9463 68.7829 23.3763 68.7829 29.999C68.7829 36.8585 63.2024 42.4384 56.3434 42.4384L56.3438 42.4384Z"
                          fill="#483EA8" />
                        <path
                          d="M15.85 41.2935C15.85 51.4634 24.1237 59.737 34.2935 59.737C44.4634 59.737 52.737 51.4633 52.737 41.2935C52.737 31.1235 44.4634 22.85 34.2935 22.85C24.1235 22.85 15.85 31.1237 15.85 41.2935ZM17.6138 41.2935C17.6138 32.0966 25.0964 24.6138 34.2935 24.6138C43.4904 24.6138 50.9732 32.0964 50.9732 41.2935C50.9732 50.4904 43.4904 57.9732 34.2935 57.9732C25.0966 57.9732 17.6138 50.4905 17.6138 41.2935Z"
                          fill="#483EA8" stroke="#F9FFF9" stroke-width="0.3" />
                        <path
                          d="M33.9398 48.6577C33.9398 49.0363 34.2469 49.3434 34.6256 49.3434C35.0041 49.3434 35.3113 49.0367 35.3113 48.6577V34.7291C35.3113 34.3504 35.0042 34.0434 34.6256 34.0434C34.2469 34.0434 33.9398 34.3504 33.9398 34.7291V48.6577Z"
                          fill="#483EA8" stroke="#483EA8" stroke-width="0.3" />
                        <path
                          d="M34.6262 35.7004L30.8255 39.5011L34.6262 35.7004ZM34.6262 35.7004L38.4269 39.5012C38.5606 39.6349 38.7367 39.702 38.9118 39.702L34.6262 35.7004ZM29.8556 39.5012C30.1234 39.769 30.5578 39.7691 30.8254 39.5012L38.9118 39.702C39.0866 39.702 39.2628 39.6355 39.3967 39.5011C39.6645 39.2332 39.6645 38.7992 39.3967 38.5314L35.111 34.2457C34.8432 33.9779 34.4088 33.9778 34.1412 34.2457C34.1412 34.2458 34.1412 34.2458 34.1411 34.2458L29.8556 38.5314C29.5877 38.7993 29.5877 39.2333 29.8556 39.5012Z"
                          fill="#483EA8" stroke="#483EA8" stroke-width="0.3" />
                      </svg>
                    </div>
                    <focile-button (onClick)="handleOnClick('videoVerification')"></focile-button>
                    <strong>Browse</strong>
                    <label>Supported formates: JPEG, PNG, GIF, MP4, PDF, PSD, AI, Word, PPT</label>
                  </div>
                  <div class="fc-uploaded-file">
                    <label>Uploaded</label>
                    <input
                    #videoVerification
                    class="d-none"
                    type="file"
                    name="owenrCertificate"
                    id="owenrCertificate"
                    [accept]="accept"
                    (change)="handleFileInput($event.target, 'videoVerification')"
                  />
                  <input
                    readonly
                    type="text"
                    class="form-control"
                    [(ngModel)]="documents.videoVerification.name"
                    name="videoVerification"
                    id="videoVerification"
                  />               
                </div>
              </div>
            </div>
          </div>

          <!-- <div class="col-md-12 mb-4">
            <h6>Video Verification</h6>
            <div class="col-md-12 d-flex">
              <div class="W-100 me-3">
                <focile-button (onClick)="handleOnClick('videoVerification')"
                  >Upload</focile-button
                >
              </div>
              <input
                #videoVerification
                class="d-none"
                type="file"
                name="owenrCertificate"
                id="owenrCertificate"
                [accept]="accept"
                (change)="handleFileInput($event.target, 'videoVerification')"
              />
              <input
                readonly
                type="text"
                class="form-control"
                [(ngModel)]="documents.videoVerification.name"
                name="videoVerification"
                id="videoVerification"
              />
            </div>
          </div> -->

          <div class="fc-document-upload-section">
            <div class="fc-ducument-upload-list">
              <div class="col-sm-12">
                 <label class="fc-document-name">Owner Lience</label>
              </div>
              <div class="col-sm-12 fc-list-of-doc">                
                  <div class="fc-upload-section">
                    <div>
                      <svg width="69" height="60" viewBox="0 0 69 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M36.0271 14.7458L36.1193 14.7733L36.1233 14.7688C36.561 14.8481 36.9951 14.586 37.1237 14.1519C38.2953 10.2152 41.9865 7.46504 46.0989 7.46504C46.5857 7.46504 46.9806 7.07016 46.9806 6.5833C46.9806 6.09643 46.5857 5.70156 46.0989 5.70156C41.0447 5.70156 36.7975 9.06665 35.4338 13.6493C35.2946 14.1162 35.5606 14.6067 36.0271 14.7458Z"
                          fill="#483EA8" stroke="#F9FFF9" stroke-width="0.3" />
                        <path
                          d="M56.3438 42.4384H51.9534C51.5494 42.4384 51.2217 42.1107 51.2217 41.7067C51.2217 41.3027 51.5494 40.9749 51.9534 40.9749H56.3438C62.3956 40.9749 67.3197 36.0509 67.3197 29.999C67.3197 23.9471 62.3956 19.023 56.3438 19.023H56.2382C56.026 19.023 55.8242 18.9311 55.6852 18.7706C55.5462 18.6101 55.4834 18.3974 55.5138 18.1873C55.5791 17.7315 55.612 17.2737 55.612 16.8279C55.612 11.5829 51.3444 7.31531 46.0995 7.31531C44.059 7.31531 42.1131 7.95296 40.4719 9.15978C40.1112 9.42478 39.599 9.30718 39.3905 8.91047C34.7425 0.0596993 22.6023 -1.12887 16.3082 6.57053C13.6568 9.81417 12.615 14.0336 13.4498 18.146C13.5418 18.6002 13.1942 19.0236 12.7327 19.0236H12.4395C6.3876 19.0236 1.46353 23.9477 1.46353 29.9996C1.46353 36.0514 6.3876 40.9755 12.4395 40.9755H16.8298C17.2338 40.9755 17.5615 41.3032 17.5615 41.7072C17.5615 42.1113 17.2338 42.439 16.8298 42.439H12.4395C5.5805 42.439 0 36.8585 0 29.9995C0 23.3329 5.27155 17.8742 11.8651 17.5731C11.2457 13.3066 12.4301 9.00295 15.1751 5.64437C21.9138 -2.5996 34.828 -1.67556 40.2871 7.51707C42.0287 6.42522 44.0215 5.85244 46.0992 5.85244C52.4538 5.85244 57.4892 11.261 57.0486 17.58C63.5813 17.9463 68.7829 23.3763 68.7829 29.999C68.7829 36.8585 63.2024 42.4384 56.3434 42.4384L56.3438 42.4384Z"
                          fill="#483EA8" />
                        <path
                          d="M15.85 41.2935C15.85 51.4634 24.1237 59.737 34.2935 59.737C44.4634 59.737 52.737 51.4633 52.737 41.2935C52.737 31.1235 44.4634 22.85 34.2935 22.85C24.1235 22.85 15.85 31.1237 15.85 41.2935ZM17.6138 41.2935C17.6138 32.0966 25.0964 24.6138 34.2935 24.6138C43.4904 24.6138 50.9732 32.0964 50.9732 41.2935C50.9732 50.4904 43.4904 57.9732 34.2935 57.9732C25.0966 57.9732 17.6138 50.4905 17.6138 41.2935Z"
                          fill="#483EA8" stroke="#F9FFF9" stroke-width="0.3" />
                        <path
                          d="M33.9398 48.6577C33.9398 49.0363 34.2469 49.3434 34.6256 49.3434C35.0041 49.3434 35.3113 49.0367 35.3113 48.6577V34.7291C35.3113 34.3504 35.0042 34.0434 34.6256 34.0434C34.2469 34.0434 33.9398 34.3504 33.9398 34.7291V48.6577Z"
                          fill="#483EA8" stroke="#483EA8" stroke-width="0.3" />
                        <path
                          d="M34.6262 35.7004L30.8255 39.5011L34.6262 35.7004ZM34.6262 35.7004L38.4269 39.5012C38.5606 39.6349 38.7367 39.702 38.9118 39.702L34.6262 35.7004ZM29.8556 39.5012C30.1234 39.769 30.5578 39.7691 30.8254 39.5012L38.9118 39.702C39.0866 39.702 39.2628 39.6355 39.3967 39.5011C39.6645 39.2332 39.6645 38.7992 39.3967 38.5314L35.111 34.2457C34.8432 33.9779 34.4088 33.9778 34.1412 34.2457C34.1412 34.2458 34.1412 34.2458 34.1411 34.2458L29.8556 38.5314C29.5877 38.7993 29.5877 39.2333 29.8556 39.5012Z"
                          fill="#483EA8" stroke="#483EA8" stroke-width="0.3" />
                      </svg>
                    </div>
                    <focile-button (onClick)="handleOnClick('videoVerification')"></focile-button>
                    <strong>Browse</strong>
                    <label>Supported formates: JPEG, PNG, GIF, MP4, PDF, PSD, AI, Word, PPT</label>
                  </div>
                  <div class="fc-uploaded-file">
                    <label>Uploaded</label>
                    <input
                #ownerLicence
                class="d-none"
                type="file"
                name="ownerLicence"
                id="ownerLicence"
                [accept]="accept"
                (change)="handleFileInput($event.target, 'ownerLicence')"
              />
              <input
                readonly
                type="text"
                class="form-control"
                [(ngModel)]="documents.ownerLicence.name"
                name="ownerLicence"
                id="ownerLicence"
              />        
                </div>
              </div>
            </div>
          </div>

          <!-- <div class="col-md-12 mb-4">
            <h6>Owner Lience</h6>
            <div class="col-md-12 d-flex">
              <div class="W-100 me-3">
                <focile-button (onClick)="handleOnClick('ownerLicence')"
                  >Upload</focile-button
                >
              </div>
              <input
                #ownerLicence
                class="d-none"
                type="file"
                name="ownerLicence"
                id="ownerLicence"
                [accept]="accept"
                (change)="handleFileInput($event.target, 'ownerLicence')"
              />
              <input
                readonly
                type="text"
                class="form-control"
                [(ngModel)]="documents.ownerLicence.name"
                name="ownerLicence"
                id="ownerLicence"
              />
            </div>
          </div> -->
          

          <div class="fc-document-upload-section">
            <div class="fc-ducument-upload-list">
              <div class="col-sm-12">
                 <label class="fc-document-name">Other Document</label>
              </div>
              <div class="col-sm-12 fc-list-of-doc">                
                  <div class="fc-upload-section">
                    <div>
                      <svg width="69" height="60" viewBox="0 0 69 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M36.0271 14.7458L36.1193 14.7733L36.1233 14.7688C36.561 14.8481 36.9951 14.586 37.1237 14.1519C38.2953 10.2152 41.9865 7.46504 46.0989 7.46504C46.5857 7.46504 46.9806 7.07016 46.9806 6.5833C46.9806 6.09643 46.5857 5.70156 46.0989 5.70156C41.0447 5.70156 36.7975 9.06665 35.4338 13.6493C35.2946 14.1162 35.5606 14.6067 36.0271 14.7458Z"
                          fill="#483EA8" stroke="#F9FFF9" stroke-width="0.3" />
                        <path
                          d="M56.3438 42.4384H51.9534C51.5494 42.4384 51.2217 42.1107 51.2217 41.7067C51.2217 41.3027 51.5494 40.9749 51.9534 40.9749H56.3438C62.3956 40.9749 67.3197 36.0509 67.3197 29.999C67.3197 23.9471 62.3956 19.023 56.3438 19.023H56.2382C56.026 19.023 55.8242 18.9311 55.6852 18.7706C55.5462 18.6101 55.4834 18.3974 55.5138 18.1873C55.5791 17.7315 55.612 17.2737 55.612 16.8279C55.612 11.5829 51.3444 7.31531 46.0995 7.31531C44.059 7.31531 42.1131 7.95296 40.4719 9.15978C40.1112 9.42478 39.599 9.30718 39.3905 8.91047C34.7425 0.0596993 22.6023 -1.12887 16.3082 6.57053C13.6568 9.81417 12.615 14.0336 13.4498 18.146C13.5418 18.6002 13.1942 19.0236 12.7327 19.0236H12.4395C6.3876 19.0236 1.46353 23.9477 1.46353 29.9996C1.46353 36.0514 6.3876 40.9755 12.4395 40.9755H16.8298C17.2338 40.9755 17.5615 41.3032 17.5615 41.7072C17.5615 42.1113 17.2338 42.439 16.8298 42.439H12.4395C5.5805 42.439 0 36.8585 0 29.9995C0 23.3329 5.27155 17.8742 11.8651 17.5731C11.2457 13.3066 12.4301 9.00295 15.1751 5.64437C21.9138 -2.5996 34.828 -1.67556 40.2871 7.51707C42.0287 6.42522 44.0215 5.85244 46.0992 5.85244C52.4538 5.85244 57.4892 11.261 57.0486 17.58C63.5813 17.9463 68.7829 23.3763 68.7829 29.999C68.7829 36.8585 63.2024 42.4384 56.3434 42.4384L56.3438 42.4384Z"
                          fill="#483EA8" />
                        <path
                          d="M15.85 41.2935C15.85 51.4634 24.1237 59.737 34.2935 59.737C44.4634 59.737 52.737 51.4633 52.737 41.2935C52.737 31.1235 44.4634 22.85 34.2935 22.85C24.1235 22.85 15.85 31.1237 15.85 41.2935ZM17.6138 41.2935C17.6138 32.0966 25.0964 24.6138 34.2935 24.6138C43.4904 24.6138 50.9732 32.0964 50.9732 41.2935C50.9732 50.4904 43.4904 57.9732 34.2935 57.9732C25.0966 57.9732 17.6138 50.4905 17.6138 41.2935Z"
                          fill="#483EA8" stroke="#F9FFF9" stroke-width="0.3" />
                        <path
                          d="M33.9398 48.6577C33.9398 49.0363 34.2469 49.3434 34.6256 49.3434C35.0041 49.3434 35.3113 49.0367 35.3113 48.6577V34.7291C35.3113 34.3504 35.0042 34.0434 34.6256 34.0434C34.2469 34.0434 33.9398 34.3504 33.9398 34.7291V48.6577Z"
                          fill="#483EA8" stroke="#483EA8" stroke-width="0.3" />
                        <path
                          d="M34.6262 35.7004L30.8255 39.5011L34.6262 35.7004ZM34.6262 35.7004L38.4269 39.5012C38.5606 39.6349 38.7367 39.702 38.9118 39.702L34.6262 35.7004ZM29.8556 39.5012C30.1234 39.769 30.5578 39.7691 30.8254 39.5012L38.9118 39.702C39.0866 39.702 39.2628 39.6355 39.3967 39.5011C39.6645 39.2332 39.6645 38.7992 39.3967 38.5314L35.111 34.2457C34.8432 33.9779 34.4088 33.9778 34.1412 34.2457C34.1412 34.2458 34.1412 34.2458 34.1411 34.2458L29.8556 38.5314C29.5877 38.7993 29.5877 39.2333 29.8556 39.5012Z"
                          fill="#483EA8" stroke="#483EA8" stroke-width="0.3" />
                      </svg>
                    </div>
                    <focile-button (onClick)="handleOnClick('videoVerification')"></focile-button>
                    <strong>Browse</strong>
                    <label>Supported formates: JPEG, PNG, GIF, MP4, PDF, PSD, AI, Word, PPT</label>
                  </div>
                  <div class="fc-uploaded-file">
                    <label>Uploaded</label>
                    <input
                #otherDocument
                class="d-none"
                type="file"
                name="otherDocument"
                id="otherDocument"
                [accept]="accept"
                (change)="handleFileInput($event.target, 'otherDocument')"
              />
              <input
                readonly
                type="text"
                class="form-control"
                [(ngModel)]="documents.otherDocument.name"
                name="otherDocument"
                id="otherDocument"
              />   
                </div>
              </div>
            </div>
          </div>

          <!-- <div class="col-md-12 mb-4">
            <h6>Other Document</h6>
            <div class="col-md-12 d-flex">
              <div class="W-100 me-3">
                <focile-button (onClick)="handleOnClick('otherDocument')"
                  >Upload</focile-button
                >
              </div>
              <input
                #otherDocument
                class="d-none"
                type="file"
                name="otherDocument"
                id="otherDocument"
                [accept]="accept"
                (change)="handleFileInput($event.target, 'otherDocument')"
              />
              <input
                readonly
                type="text"
                class="form-control"
                [(ngModel)]="documents.otherDocument.name"
                name="otherDocument"
                id="otherDocument"
              />
            </div>
          </div> -->
        </div>
      </div>
      <div class="d-flex mt-4 mt-sm-5 mb-0 mb-sm-4 justify-content-end">
          <focile-button [loading]="saving" (onClick)="uploadFiles()" class="custom-btn"
            >Update Setting</focile-button>        
      </div>
    </div>
    <ng-container *ngIf="loading">
      <app-spinner></app-spinner>
    </ng-container>
  </div>
</div>