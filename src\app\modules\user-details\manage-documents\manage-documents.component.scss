.fc-account-detail-content {
    box-sizing: border-box;
    width: 100%;
    background: #FFFFFF;
    border: 1px solid rgba(140, 140, 140, 0.2);
    border-radius: 18px;
    padding: 1.5rem;
    z-index: 1;
    position: relative;
}

.document-upload-section {
    display: flex;
    flex-direction: row;
}

.fc-upload-section {
    box-sizing: border-box;
    width: 445px;
    height: 202px;
    background: #F8F8FF;
    border: 1px dashed rgba(56, 78, 183, 0.3);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    position: relative;

    strong {
        color: #483EA8;
        text-decoration: none;
        margin-top: 1.5rem;
        margin-bottom: 10px;
        font-weight: 500;
        text-decoration: none;
    }

    label {
        width: 100%;
        height: 18px;
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 18px;
        display: flex;
        align-items: center;
        text-align: center;
        color: #676767;
        justify-content: center;
    }

    ::ng-deep button{
        width: 100%;
        height: 100%;
        background-color: transparent;
        top: 0px;
        left: 0px;
        position: absolute;
        border: none;

        &:hover{
            background-color: transparent;
            box-shadow: none;
            border: none;
        }
        &:focus{
            background-color: transparent;
            box-shadow: none;
            border: none;
        }
    }
}
.fc-list-of-doc{
    display: flex;
    flex-direction: row;
    gap: 24px;
}

.fc-document-name{
    font-size: 14px;
    margin-bottom:10px;
}
.fc-uploaded-file{
    flex: 1;
    label{
        font-weight: 500;
        color:#676767;
        margin-bottom:12px;
    }
}

::ng-deep{
    .custom-btn{
        ::ng-deep button{
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          min-width: 175px;
          height: 50px;
          background: #014681;
          box-shadow: 0px 548px 219px rgba(0, 0, 0, 0.01), 0px 308px 185px rgba(0, 0, 0, 0.04), 0px 137px 137px rgba(0, 0, 0, 0.06), 0px 34px 75px rgba(0, 0, 0, 0.07), 0px 0px 0px rgba(0, 0, 0, 0.07);
          border-radius: 100px;
          border: none;
          text-align: center;
          color: white;
          font-size: 18px;
          font-weight: 500 !important;
        }
    }
}


@media(max-width:768px){
    .fc-list-of-doc{
        flex-direction: column;
        gap: 1rem;
    }
    .fc-upload-section{
        width: 100%;
        label{
            padding: 1rem;
        }
    }
    .fc-uploaded-file label{
        font-size: 15px;
        margin-bottom: 5px;
    }
}