import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { AccountService } from '../../account/services/account.service';
import { finalize, take } from 'rxjs';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-manage-documents',
  templateUrl: './manage-documents.component.html',
  styleUrls: ['./manage-documents.component.scss'],
})
export class ManageDocumentsComponent implements OnInit {
  @ViewChild('companyCertificate') companyCertificate!: ElementRef;
  @ViewChild('videoVerification') videoVerification!: ElementRef;
  @ViewChild('ownerLicence') ownerLicence!: ElementRef;
  @ViewChild('otherDocument') otherDocument!: ElementRef;

  documents: any = {
    companyCertificate: {
      name: '',
    },
    ownerCertificate: {
      name: '',
    },
    ownerLicence: {
      name: '',
    },
    otherDocument: {
      name: '',
    },
    videoVerification: {
      name: '',
    },
  };
  user: any = {};
  accept = '.jpg,.jpeg,.png,.pdf,.doc,.docx,.txt';
  loading = false;
  saving = false;
  constructor(
    private account: AccountService,
    private toastrService: ToastrService
  ) {}

  ngOnInit(): void {
    this.account.user$.pipe(take(1)).subscribe((response) => {
      this.user = response;
      this.loading = true;
      this.account
        .getUserDocuments(response.userId)
        .pipe(finalize(() => (this.loading = false)))
        .subscribe((userDocuments: any) => {
          if (userDocuments.messageType) {
            return this.toastrService.error(userDocuments.message``);
          }
          const documentsList: Array<any>[] = userDocuments.data;
          documentsList.forEach((d: any) => {
            if (d.documentTypeId == 1) {
              this.documents.companyCertificate.name = d.fileName;
            } else if (d.documentTypeId == 2) {
              this.documents.ownerCertificate.name = d.fileName;
            } else if (d.documentTypeId == 3) {
              this.documents.ownerLicence.name = d.fileName;
            } else if (d.documentTypeId == 4) {
              this.documents.otherDocument.name = d.fileName;
            } else if (d.documentTypeId == 5) {
              this.documents.videoVerification.name = d.fileName;
            }
          });
          return;
        });
    });
  }

  handleOnClick(documentType: string = '') {
    const self: any = this;
    self[documentType].nativeElement.click();
  }

  handleFileInput(files: any, documentType: string) {
    const file = files.files[0];
    if (this.isFileTypeValid(file) && this.isFileSizeValid(file)) {
      this.documents[documentType].name = file.name;
      this.documents[documentType].file = file;
    } else {
      this.toastrService.error(
        'Invalid file type. Allowed types are: jpg, jpeg, png, pdf, doc, docx, txt,  Max size: 50KB'
      );
    }
  }

  isFileTypeValid(file: File): boolean {
    const allowedTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
    ];
    return allowedTypes.includes(file.type);
  }

  isFileSizeValid(file: File): boolean {
    const maxSize = 50 * 1024; // 20 KB in bytes
    return file.size <= maxSize;
  }

  uploadFiles() {
    this.saving = true;
    const formData = new FormData();

    for (const iterator of Object.keys(this.documents)) {
      if (this.documents[iterator].file) {
        formData.append(iterator, this.documents[iterator].file);
      }
    }
    formData.append('UserId', this.user.userId);

    this.account
      .saveDocuments(formData)
      .pipe(finalize(() => (this.saving = false)))
      .subscribe((response) => {});
  }
}
