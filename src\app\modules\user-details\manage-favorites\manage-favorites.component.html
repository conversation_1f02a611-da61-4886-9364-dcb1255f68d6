<div class="row p-3">
  <div class="tab-content p-0" id="nav-tabContent">
    <div class="tab-pane fade show active">
      <ul class="category-navbar-item gap-5">
        <li class="nav-item" role="button" (click)="setActiveTab('myFavorite');changeTab(1)"
          [class.active]="activeTab === 'myFavorite'">
          <a class="" aria-current="page">
            <span>
              <svg width="17" height="15" viewBox="0 0 17 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M5.11328 0.958984C3.18058 0.958984 1.61328 2.51018 1.61328 4.42398C1.61328 5.96888 2.22578 9.63548 8.25488 13.342C8.36288 13.4077 8.48686 13.4425 8.61328 13.4425C8.7397 13.4425 8.86368 13.4077 8.97168 13.342C15.0008 9.63548 15.6133 5.96888 15.6133 4.42398C15.6133 2.51018 14.046 0.958984 12.1133 0.958984C10.1806 0.958984 8.61328 3.05898 8.61328 3.05898C8.61328 3.05898 7.04598 0.958984 5.11328 0.958984Z"
                  stroke="#014681" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
            </span>
            My Favorite Connections
          </a>
        </li>
        <li class="nav-item" role="button" (click)="setActiveTab('myPending'); changeTab(3)"
          [class.active]="activeTab === 'myPending'">
          <a class="" aria-current="page">
            <span _ngcontent-kjw-c109=""><svg _ngcontent-kjw-c109="" width="12" height="12" viewBox="0 0 12 12" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <g _ngcontent-kjw-c109="" clip-path="url(#clip0_580_4055)">
                  <path _ngcontent-kjw-c109=""
                    d="M6 0.09375C2.74687 0.09375 0.09375 2.74687 0.09375 6C0.09375 9.25313 2.74687 11.9062 6 11.9062C9.25313 11.9062 11.9062 9.25313 11.9062 6C11.9062 2.74687 9.25313 0.09375 6 0.09375ZM6 11.1562C3.15938 11.1562 0.84375 8.84062 0.84375 6C0.84375 3.15938 3.15938 0.84375 6 0.84375C8.84062 0.84375 11.1562 3.15938 11.1562 6C11.1562 8.84062 8.84062 11.1562 6 11.1562Z"
                    fill="#014681"></path>
                  <path _ngcontent-kjw-c109="" d="M6.375 5.10938H5.625V8.85938H6.375V5.10938Z" fill="#014681"></path>
                  <path _ngcontent-kjw-c109="" d="M6.375 3.32812H5.625V4.07812H6.375V3.32812Z" fill="#014681"></path>
                </g>
                <defs _ngcontent-kjw-c109="">
                  <clipPath _ngcontent-kjw-c109="" id="clip0_580_4055">
                    <rect _ngcontent-kjw-c109="" width="12" height="12" fill="white"></rect>
                  </clipPath>
                </defs>
              </svg></span>
            My Pending Request
          </a>
        </li>
        <li class="nav-item" role="button" (click)="setActiveTab('myApproved'); changeTab(2)"
          [class.active]="activeTab === 'myApproved'">
          <a class="" aria-current="page">
            <span>
              <svg width="15" height="14" viewBox="0 0 15 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M7.31641 14C3.46641 14 0.316406 10.85 0.316406 7C0.316406 3.15 3.46641 0 7.31641 0C11.1664 0 14.3164 3.15 14.3164 7C14.3164 10.85 11.1664 14 7.31641 14ZM7.31641 1.16667C4.10807 1.16667 1.48307 3.79167 1.48307 7C1.48307 10.2083 4.10807 12.8333 7.31641 12.8333C10.5247 12.8333 13.1497 10.2083 13.1497 7C13.1497 3.79167 10.5247 1.16667 7.31641 1.16667Z"
                  fill="#014681" />
                <path
                  d="M6.73177 9.33317C6.55677 9.33317 6.4401 9.27484 6.32344 9.15817L4.57344 7.40817C4.3401 7.17484 4.3401 6.82484 4.57344 6.5915C4.80677 6.35817 5.15677 6.35817 5.3901 6.5915L7.1401 8.3415C7.37344 8.57484 7.37344 8.92484 7.1401 9.15817C7.02344 9.27484 6.90677 9.33317 6.73177 9.33317Z"
                  fill="#014681" />
                <path
                  d="M6.73177 9.33317C6.55677 9.33317 6.4401 9.27484 6.32344 9.15817C6.0901 8.92484 6.0901 8.57484 6.32344 8.3415L9.82344 4.8415C10.0568 4.60817 10.4068 4.60817 10.6401 4.8415C10.8734 5.07484 10.8734 5.42484 10.6401 5.65817L7.1401 9.15817C7.02344 9.27484 6.90677 9.33317 6.73177 9.33317Z"
                  fill="#014681" />
              </svg>
            </span>
            My Approved Connections
          </a>
        </li>
      </ul>

      <div id="content"></div>
      <div *ngIf="activeTab === 'myFavorite'">
        <!-- Loading spinner for My Favorite Connections tab -->
        <div *ngIf="tabLoading.favorites" class="loading-container">
          <div class="loading-spinner"></div>
          <p>Loading your favorite connections...</p>
        </div>

        <!-- Content when not loading -->
        <div *ngIf="!tabLoading.favorites" class="tab-content-wrapper">
          <div class="sub-category-navbar-item">
            <div (click)="setActiveTab1('eCard')" [class.active]="activeTab1 === 'eCard'" role="button">eCard</div>
            <div (click)="setActiveTab1('Expert')" [class.active]="activeTab1 === 'Expert'" role="button">Expert</div>
            <div (click)="setActiveTab1('Business')" [class.active]="activeTab1 === 'Business'" role="button">Business
            </div>
          </div>

        <div class="ms-auto col-sm-3 expert-sub-category-navbar-item mb-3" *ngIf="activeTab1 === 'Expert'">
          <select [(ngModel)]="selectedUserType" (change)="filterDistributors()" class="form-select" id="userType">
            <option value="0">All</option>
            <option value="1">Resellers</option>
            <option value="2">Distributors</option>
            <option value="3">Vendors</option>
            <option value="4">Consultants</option>
          </select>
        </div>

        <ng-container *ngIf="activeTab1 === 'eCard'">
          <div class="row" *ngIf="resellers.length">
            <div class="grid-container">
              <div class="grid-item" *ngFor="let company of resellers; let i = index">
                <app-connection-card [company]="company" (onRemoved)="handleOnRemoved($event, i, 'reseller')">
                </app-connection-card>
              </div>
            </div>
          </div>
        </ng-container>

        <ng-container *ngIf="activeTab1 === 'Expert'">
          <div class="row" *ngIf="distributors.length">
            <div class="grid-container">
              <div class="grid-item" *ngFor="let company of distributors; let i = index">
                <div class="col-sm-4 fc-favorite-list" (click)="navigate(company)">
                  <div class="fc-favorite-card" role="button">
                    <!-- <span class="fc-favorite-i">
                      <svg width="16" height="14" viewBox="0 0 22 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M6 1.08325C3.239 1.08325 1 3.29925 1 6.03325C1 8.24025 1.875 13.4783 10.488 18.7733C10.6423 18.8671 10.8194 18.9168 11 18.9168C11.1806 18.9168 11.3577 18.8671 11.512 18.7733C20.125 13.4783 21 8.24025 21 6.03325C21 3.29925 18.761 1.08325 16 1.08325C13.239 1.08325 11 4.08325 11 4.08325C11 4.08325 8.761 1.08325 6 1.08325Z"
                          stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                      </svg>
                    </span> -->
                    <figure class="fc-favorite-card__figure">
                      <img [src]="company['profilePhoto'] ? company['profilePhoto'] : 'assets/images/user-avatar.svg'"
                        alt="Company Profile Photo" onerror="this.src='assets/images/user-avatar.svg'" />
                    </figure>
                    <h6 class="text-truncate-2-lines">{{ company.firstName }} {{ company.lastName }}</h6>
                    <label class="text-truncate-2-lines">{{ company.companyName }}</label>
                    <div class="expertise-list" *ngIf="company.experties && company.experties.trim()">
                      {{ company.experties }}
                    </div>
                    <div class="d-flex flex-column gap-2 w-100">
                      <div class="fc-call-btn" *ngIf="company['email']" title="{{company['email']}}">
                        <span class="call-icon">
                          <svg _ngcontent-rao-c17="" width="13" height="13" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path _ngcontent-rao-c17=""
                              d="M4 20C3.45 20 2.97917 19.8042 2.5875 19.4125C2.19583 19.0208 2 18.55 2 18V6C2 5.45 2.19583 4.97917 2.5875 4.5875C2.97917 4.19583 3.45 4 4 4H20C20.55 4 21.0208 4.19583 21.4125 4.5875C21.8042 4.97917 22 5.45 22 6V18C22 18.55 21.8042 19.0208 21.4125 19.4125C21.0208 19.8042 20.55 20 20 20H4ZM12 13L4 8V18H20V8L12 13ZM12 11L20 6H4L12 11ZM4 8V6V18V8Z"
                              fill="white"></path>
                          </svg>
                        </span>
                        {{company['email']}}
                      </div>
                      <div class="fc-call-btn" *ngIf="company['workMobileNumer']">
                        <span class="call-icon">
                          <svg width="10" height="10" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M11.7752 12.3332C10.4249 12.3332 9.09076 12.0388 7.77286 11.4501C6.45496 10.8613 5.25588 10.0268 4.17564 8.9466C3.09539 7.86635 2.2609 6.66728 1.67216 5.34937C1.08343 4.03147 0.789062 2.69737 0.789062 1.34706C0.789062 1.15262 0.853877 0.990578 0.983507 0.860948C1.11314 0.731319 1.27517 0.666504 1.46962 0.666504H4.09462C4.24585 0.666504 4.38088 0.717816 4.49971 0.820439C4.61854 0.923063 4.68875 1.04459 4.71036 1.18502L5.13166 3.45354C5.15326 3.62638 5.14786 3.77221 5.11545 3.89104C5.08304 4.00987 5.02363 4.11249 4.93721 4.19891L3.36545 5.78687C3.5815 6.18657 3.83806 6.57275 4.13513 6.94544C4.4322 7.31812 4.75897 7.67731 5.11545 8.02299C5.45033 8.35786 5.80141 8.66843 6.16869 8.9547C6.53598 9.24096 6.92486 9.50292 7.33536 9.74058L8.85851 8.21743C8.95573 8.12021 9.08266 8.04729 9.23929 7.99868C9.39593 7.95007 9.54986 7.93657 9.7011 7.95817L11.9372 8.41187C12.0884 8.45508 12.2127 8.5334 12.3099 8.64683C12.4071 8.76025 12.4557 8.88718 12.4557 9.02761V11.6526C12.4557 11.8471 12.3909 12.0091 12.2613 12.1387C12.1317 12.2684 11.9696 12.3332 11.7752 12.3332ZM2.74971 4.55539L3.81915 3.48595L3.54369 1.9628H2.10156C2.15557 2.4057 2.23119 2.8432 2.32841 3.2753C2.42564 3.7074 2.56607 4.1341 2.74971 4.55539ZM8.55064 10.3563C8.97193 10.54 9.40133 10.6858 9.83883 10.7938C10.2763 10.9018 10.7165 10.9721 11.1594 11.0045V9.57854L9.63628 9.27067L8.55064 10.3563Z"
                              fill="white" />
                          </svg>
                        </span>
                        {{company['workMobileNumer']}}
                      </div>
                    </div>
                  </div>
                </div>
                <!-- <app-connection-card [company]="company" (onRemoved)="handleOnRemoved($event, i, 'distributor')">
                </app-connection-card> -->
              </div>
            </div>
          </div>

          <!-- Show specific messages when no data is found -->
          <ng-container *ngIf="!distributors.length">
            <p class="alert alert-info">
              {{ getNoDataMessage() }}
            </p>
          </ng-container>
        </ng-container>


        <ng-container *ngIf="activeTab1 === 'Business'">
          <div class="grid-container">
            <div class="grid-item fc-favorite-list" *ngFor="let company of business; let i = index">
              <div class="fc-favorite-card" role="button" (click)="gotoDetails(company)" role="button">
                <figure class="fc-favorite-card__figure">
                  <img [src]="company['profilePhoto'] ? company['profilePhoto'] : 'assets/images/user-avatar.svg'"
                    alt="Company Profile Photo" />
                </figure>
                <h6>{{company['firstName']}} {{company['lastName']}}</h6>
                <span class="e-card-tech text-truncate-2-lines">{{company['companyName']}}</span>
              </div>
            </div>
          </div>
        </ng-container>

          <ng-container
            *ngIf="!vendors.length && !distributors.length && !resellers.length && !avConsultants.length && !business.length">
            <p class="alert alert-info">
              There is no favorite profiles click on e-card's heart to add to whish
              list
            </p>
          </ng-container>
        </div> <!-- End of not loading content -->
      </div>

      <div *ngIf="activeTab === 'myApproved'">
        <!-- Loading spinner for My Approved Connections tab -->
        <div *ngIf="tabLoading.approved" class="loading-container">
          <div class="loading-spinner"></div>
          <p>Loading your approved connections...</p>
        </div>

        <!-- Content when not loading -->
        <div *ngIf="!tabLoading.approved" class="tab-content-wrapper">
          <h6 class="my-4 text-left w-100">My Approved Connections</h6>

          <!-- Sub-tabs for Approved Connections -->
          <div class="sub-category-navbar-item">
            <div (click)="setActiveApprovedTab('eCard')" [class.active]="activeApprovedTab === 'eCard'" role="button">eCard</div>
            <div (click)="setActiveApprovedTab('expert')" [class.active]="activeApprovedTab === 'expert'" role="button">Expert</div>
            <div (click)="setActiveApprovedTab('business')" [class.active]="activeApprovedTab === 'business'" role="button">Business</div>
          </div>

          <!-- Filter dropdown for Expert tab in Approved Connections -->
          <div class="ms-auto col-sm-3 expert-sub-category-navbar-item mb-3" *ngIf="activeApprovedTab === 'expert'">
            <select [(ngModel)]="selectedApprovedUserType" (change)="filterApprovedExperts()" class="form-select" id="approvedUserType">
              <option value="0">All</option>
              <option value="1">Resellers</option>
              <option value="2">Distributors</option>
              <option value="3">Vendors</option>
              <option value="4">AV Consultants</option>
            </select>
          </div>

          <!-- Sub-tab loading spinner -->
          <div *ngIf="(activeApprovedTab === 'eCard' && subTabLoading.approved.eCard) ||
                      (activeApprovedTab === 'expert' && subTabLoading.approved.expert) ||
                      (activeApprovedTab === 'business' && subTabLoading.approved.business)"
               class="loading-container">
            <div class="loading-spinner"></div>
            <p>Loading {{ activeApprovedTab }} connections...</p>
          </div>

          <!-- Sub-tab content -->
          <div *ngIf="!(activeApprovedTab === 'eCard' && subTabLoading.approved.eCard) &&
                      !(activeApprovedTab === 'expert' && subTabLoading.approved.expert) &&
                      !(activeApprovedTab === 'business' && subTabLoading.approved.business)"
               class="row">
            <div class="grid-container">
              <div class="grid-item" *ngFor="let company of getApprovedConnectionsForActiveTab(); let i = index">
            <div class="col-sm-4 mb-4 fc-favorite-list">
              <div class="fc-favorite-card" role="button">
                <figure class="fc-favorite-card__figure" (click)="gotoDetails(company)">
                  <img [src]="company['profilePhoto'] ? company['profilePhoto'] : 'assets/images/user-avatar.svg'"
                    alt="Company Profile Photo" alt="" />
                </figure>
                <h6 (click)="gotoDetails(company)">{{company['firstName']}} {{company['lastName']}}</h6>
                <label (click)="gotoDetails(company)">{{company['companyName']}}</label>


                    <div class="d-flex flex-column gap-2 w-100">
      <div class="fc-call-btn">
        <span class="call-icon">
        <svg _ngcontent-rao-c17="" width="13" height="13" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path _ngcontent-rao-c17="" d="M4 20C3.45 20 2.97917 19.8042 2.5875 19.4125C2.19583 19.0208 2 18.55 2 18V6C2 5.45 2.19583 4.97917 2.5875 4.5875C2.97917 4.19583 3.45 4 4 4H20C20.55 4 21.0208 4.19583 21.4125 4.5875C21.8042 4.97917 22 5.45 22 6V18C22 18.55 21.8042 19.0208 21.4125 19.4125C21.0208 19.8042 20.55 20 20 20H4ZM12 13L4 8V18H20V8L12 13ZM12 11L20 6H4L12 11ZM4 8V6V18V8Z" fill="white"></path></svg>
        </span>
        <span class="company-email"> {{company['email']}}</span>
      </div>
      <div class="fc-call-btn">
        <span class="call-icon">
          <svg width="10" height="10" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M11.7752 12.3332C10.4249 12.3332 9.09076 12.0388 7.77286 11.4501C6.45496 10.8613 5.25588 10.0268 4.17564 8.9466C3.09539 7.86635 2.2609 6.66728 1.67216 5.34937C1.08343 4.03147 0.789062 2.69737 0.789062 1.34706C0.789062 1.15262 0.853877 0.990578 0.983507 0.860948C1.11314 0.731319 1.27517 0.666504 1.46962 0.666504H4.09462C4.24585 0.666504 4.38088 0.717816 4.49971 0.820439C4.61854 0.923063 4.68875 1.04459 4.71036 1.18502L5.13166 3.45354C5.15326 3.62638 5.14786 3.77221 5.11545 3.89104C5.08304 4.00987 5.02363 4.11249 4.93721 4.19891L3.36545 5.78687C3.5815 6.18657 3.83806 6.57275 4.13513 6.94544C4.4322 7.31812 4.75897 7.67731 5.11545 8.02299C5.45033 8.35786 5.80141 8.66843 6.16869 8.9547C6.53598 9.24096 6.92486 9.50292 7.33536 9.74058L8.85851 8.21743C8.95573 8.12021 9.08266 8.04729 9.23929 7.99868C9.39593 7.95007 9.54986 7.93657 9.7011 7.95817L11.9372 8.41187C12.0884 8.45508 12.2127 8.5334 12.3099 8.64683C12.4071 8.76025 12.4557 8.88718 12.4557 9.02761V11.6526C12.4557 11.8471 12.3909 12.0091 12.2613 12.1387C12.1317 12.2684 11.9696 12.3332 11.7752 12.3332ZM2.74971 4.55539L3.81915 3.48595L3.54369 1.9628H2.10156C2.15557 2.4057 2.23119 2.8432 2.32841 3.2753C2.42564 3.7074 2.56607 4.1341 2.74971 4.55539ZM8.55064 10.3563C8.97193 10.54 9.40133 10.6858 9.83883 10.7938C10.2763 10.9018 10.7165 10.9721 11.1594 11.0045V9.57854L9.63628 9.27067L8.55064 10.3563Z"
              fill="white" />
          </svg>
        </span>
        <span class="company-email">{{company['workMobileNumber']}}</span>
      </div>
    </div>

                <!-- <div class="fc-call-btn">
                  <a href="tel:{{company['workMobileNumber']}}" class="call-icon"
                    tooltip="{{company['workMobileNumber']}}">
                    <svg width="13" height="13" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M11.7752 12.3332C10.4249 12.3332 9.09076 12.0388 7.77286 11.4501C6.45496 10.8613 5.25588 10.0268 4.17564 8.9466C3.09539 7.86635 2.2609 6.66728 1.67216 5.34937C1.08343 4.03147 0.789062 2.69737 0.789062 1.34706C0.789062 1.15262 0.853877 0.990578 0.983507 0.860948C1.11314 0.731319 1.27517 0.666504 1.46962 0.666504H4.09462C4.24585 0.666504 4.38088 0.717816 4.49971 0.820439C4.61854 0.923063 4.68875 1.04459 4.71036 1.18502L5.13166 3.45354C5.15326 3.62638 5.14786 3.77221 5.11545 3.89104C5.08304 4.00987 5.02363 4.11249 4.93721 4.19891L3.36545 5.78687C3.5815 6.18657 3.83806 6.57275 4.13513 6.94544C4.4322 7.31812 4.75897 7.67731 5.11545 8.02299C5.45033 8.35786 5.80141 8.66843 6.16869 8.9547C6.53598 9.24096 6.92486 9.50292 7.33536 9.74058L8.85851 8.21743C8.95573 8.12021 9.08266 8.04729 9.23929 7.99868C9.39593 7.95007 9.54986 7.93657 9.7011 7.95817L11.9372 8.41187C12.0884 8.45508 12.2127 8.5334 12.3099 8.64683C12.4071 8.76025 12.4557 8.88718 12.4557 9.02761V11.6526C12.4557 11.8471 12.3909 12.0091 12.2613 12.1387C12.1317 12.2684 11.9696 12.3332 11.7752 12.3332ZM2.74971 4.55539L3.81915 3.48595L3.54369 1.9628H2.10156C2.15557 2.4057 2.23119 2.8432 2.32841 3.2753C2.42564 3.7074 2.56607 4.1341 2.74971 4.55539ZM8.55064 10.3563C8.97193 10.54 9.40133 10.6858 9.83883 10.7938C10.2763 10.9018 10.7165 10.9721 11.1594 11.0045V9.57854L9.63628 9.27067L8.55064 10.3563Z"
                        fill="white" />
                    </svg>
                  </a>
                  <a href="mailto:{{company['email']}}" class="call-icon" tooltip="{{company['email']}}">
                    <svg width="16" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M13.6118 13.0002H2.78823C1.8 13.0002 1 12.1968 1 11.2045V3.50179C1 2.50942 1.8 1.70605 2.78823 1.70605H13.6118C14.6 1.70605 15.4 2.50942 15.4 3.50179V11.2045C15.4471 12.1968 14.6 13.0002 13.6118 13.0002Z"
                        stroke="white" stroke-miterlimit="10" stroke-linecap="round" />
                      <path d="M7.99912 8.06958L1.22266 2.48242" stroke="white" stroke-miterlimit="10"
                        stroke-linecap="round" stroke-linejoin="round" />
                      <path d="M14.7765 2.48242L8 8.06958" stroke="white" stroke-miterlimit="10" stroke-linecap="round"
                        stroke-linejoin="round" />
                    </svg>
                  </a>
                </div> -->
              </div>
            </div>    
            </div>
            </div>      
        </div>

          <ng-container *ngIf="getApprovedConnectionsForActiveTab().length === 0">
            <p class="alert alert-info">
              No {{ activeApprovedTab }} connections found.
            </p>
          </ng-container>
        </div> <!-- End of not loading content -->
      </div>

      <div *ngIf="activeTab === 'myPending'">
        <!-- Loading spinner for My Pending Request tab -->
        <div *ngIf="tabLoading.pending" class="loading-container">
          <div class="loading-spinner"></div>
          <p>Loading your pending requests...</p>
        </div>

        <!-- Content when not loading -->
        <div *ngIf="!tabLoading.pending" class="tab-content-wrapper">
          <div class="col-sm-12 my-3 text-left">
            <h6 class="my-4">My Pending Requests</h6>

            <!-- Sub-tabs for Pending Requests -->
            <div class="sub-category-navbar-item">
              <div (click)="setActivePendingTab('eCard')" [class.active]="activePendingTab === 'eCard'" role="button">eCard</div>
              <div (click)="setActivePendingTab('expert')" [class.active]="activePendingTab === 'expert'" role="button">Expert</div>
              <!-- Note: Business tab not available for pending requests as per API documentation -->
            </div>

            <!-- Filter dropdown for Expert tab in Pending Requests -->
            <div class="ms-auto col-sm-3 expert-sub-category-navbar-item mb-3" *ngIf="activePendingTab === 'expert'">
              <select [(ngModel)]="selectedPendingUserType" (change)="filterPendingExperts()" class="form-select" id="pendingUserType">
                <option value="0">All</option>
                <option value="1">Resellers</option>
                <option value="2">Distributors</option>
                <option value="3">Vendors</option>
                <option value="4">AV Consultants</option>
              </select>
            </div>

            <!-- Sub-tab loading spinner -->
            <div *ngIf="(activePendingTab === 'eCard' && subTabLoading.pending.eCard) ||
                        (activePendingTab === 'expert' && subTabLoading.pending.expert)"
                 class="loading-container">
              <div class="loading-spinner"></div>
              <p>Loading {{ activePendingTab }} pending requests...</p>
            </div>

            <!-- Sub-tab content -->
            <div *ngIf="!(activePendingTab === 'eCard' && subTabLoading.pending.eCard) &&
                        !(activePendingTab === 'expert' && subTabLoading.pending.expert)">
              <div class="row" *ngIf="getPendingRequestsForActiveTab().length > 0">
                <div class="grid-container">
                  <div class="grid-item" *ngFor="let request of getPendingRequestsForActiveTab(); let i = index">
                <div class="col-sm-4 fc-favorite-list" (click)="navigatePending(request)">
                  <div class="fc-favorite-card" role="button">
                    <figure class="fc-favorite-card__figure">
                      <img [src]="request.profilePhoto ? request.profilePhoto : 'assets/images/user-avatar.svg'"
                        alt="Profile Photo" onerror="this.src='assets/images/user-avatar.svg'" />
                    </figure>

                    <h6 class="text-truncate-2-lines">{{ request.firstName }} {{ request.lastName }}</h6>
                    <span>{{ request.companyName }}</span>
                    <!-- <label class="e-card-tech">{{ request.userType }}</label> -->
                    <div class="expertise-list" *ngIf="request.experties">
                      <span>
                        <div>
                          {{ request.experties }}
                        </div>
                      </span>
                    </div>
                    <div class="fc-call-btn" *ngIf="request.workMobileNumber">
                      <span class="call-icon">
                        <svg width="13" height="13" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M11.7752 12.3332C10.4249 12.3332 9.09076 12.0388 7.77286 11.4501C6.45496 10.8613 5.25588 10.0268 4.17564 8.9466C3.09539 7.86635 2.2609 6.66728 1.67216 5.34937C1.08343 4.03147 0.789062 2.69737 0.789062 1.34706C0.789062 1.15262 0.853877 0.990578 0.983507 0.860948C1.11314 0.731319 1.27517 0.666504 1.46962 0.666504H4.09462C4.24585 0.666504 4.38088 0.717816 4.49971 0.820439C4.61854 0.923063 4.68875 1.04459 4.71036 1.18502L5.13166 3.45354C5.15326 3.62638 5.14786 3.77221 5.11545 3.89104C5.08304 4.00987 5.02363 4.11249 4.93721 4.19891L3.36545 5.78687C3.5815 6.18657 3.83806 6.57275 4.13513 6.94544C4.4322 7.31812 4.75897 7.67731 5.11545 8.02299C5.45033 8.35786 5.80141 8.66843 6.16869 8.9547C6.53598 9.24096 6.92486 9.50292 7.33536 9.74058L8.85851 8.21743C8.95573 8.12021 9.08266 8.04729 9.23929 7.99868C9.39593 7.95007 9.54986 7.93657 9.7011 7.95817L11.9372 8.41187C12.0884 8.45508 12.2127 8.5334 12.3099 8.64683C12.4071 8.76025 12.4557 8.88718 12.4557 9.02761V11.6526C12.4557 11.8471 12.3909 12.0091 12.2613 12.1387C12.1317 12.2684 11.9696 12.3332 11.7752 12.3332ZM2.74971 4.55539L3.81915 3.48595L3.54369 1.9628H2.10156C2.15557 2.4057 2.23119 2.8432 2.32841 3.2753C2.42564 3.7074 2.56607 4.1341 2.74971 4.55539ZM8.55064 10.3563C8.97193 10.54 9.40133 10.6858 9.83883 10.7938C10.2763 10.9018 10.7165 10.9721 11.1594 11.0045V9.57854L9.63628 9.27067L8.55064 10.3563Z"
                            fill="white" />
                        </svg>
                      </span>
                      {{ request.workMobileNumber }}
                    </div>
                  </div>
                </div>
                <!-- <app-connection-card [company]="company" (onRemoved)="handleOnRemoved($event, i, 'distributor')">
                </app-connection-card> -->
              </div>
            </div>
          </div>

              <!-- Show specific messages when no data is found -->
              <ng-container *ngIf="getPendingRequestsForActiveTab().length === 0">
                <p class="alert alert-info">
                  No {{ activePendingTab }} pending requests found.
                </p>
              </ng-container>
            </div>
          </div>
        </div> <!-- End of not loading content -->
      </div>
    </div>
  </div>
</div>
<div class="d-flex align-items-center justify-content-center">
  <app-spinner *ngIf="loading"></app-spinner>
</div>