// Text truncation utility class for 2 lines
.text-truncate-2-lines {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  max-height: calc(1.4em * 2); // 2 lines with line-height 1.4
}

// Smooth hover animations
* {
  transition: all 0.3s ease-in-out;
}

// Loading spinner animation
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #014681;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin: 20px auto;
}

// Fade-in animation for content
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.tab-content-wrapper {
  animation: fadeIn 0.5s ease-in-out;
}

// Loading container styling
.loading-container {
  min-height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  p {
    color: #666;
    font-size: 14px;
    margin-top: 15px;
  }
}

.category-navbar-item {
  display: flex;
  flex-direction: row;
  border-bottom: 1px solid #d9dbe9;
  align-items: center;
  justify-content: space-evenly;
  list-style: none;
  padding-left: 0px;
  cursor: pointer;

  li {
    width: auto;
    display: flex;
    align-items: start;
    justify-content: center;
    min-height: 40px;
    border-bottom: 2px solid transparent;

    a {
      text-decoration: none;
      font-size: 16px;
      font-weight: 500;
      display: flex;
      flex-direction: row;
      gap: 1rem;
      align-items: center;
      color: #a0a3bd;
    }

    span {
      min-width: 26px;
      min-height: 26px;
      left: 0px;
      top: 0px;
      background: rgba(1, 69, 129, 0.1);
      border-radius: 14px;
      display: flex;
      align-items: center;
      justify-content: center;

      svg {
        width: 14px;
      }
    }

    &:hover {
      // transform: translateY(-2px);

      a {
        color: #014681;
      }
    }

    &.active {
      border-color: #014681;
      // transform: translateY(-2px);

      a {
        color: #014681;
      }
    }
  }
}

.fc-favorite-list {
  gap: 1rem;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: stretch;
  padding: 1rem;
  width: 255.24px;
  min-height: 100%;
  background: #f6f6f6;
  border: 1px solid rgb(0 0 0 / 7%);
  // box-shadow: 0px -1px 10px rgba(0, 0, 0, 0.05);
  border-radius: 20px;
  text-align: left;
  position: relative;
  transition: all 0.3s ease-in-out;
  cursor: pointer;

  &:hover {
    transform: translateY(-5px);
  }
  .fc-favorite-card {
    display: flex;
    flex-direction: column;
    align-items: start;
    justify-content: start;
    height: 100%;
    gap: 1rem;

    label {
      font-size: 14px;
      color: #666666;
    }

    span {
      font-size: 14px;
      color: #666666;
      width: calc(100% - 30px);
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  figure {
    width: 56px;
    height: 56px;
    overflow: hidden;
    border-radius: 30px;
    margin-bottom: 0px;
    border: 1px solid rgba(129, 1, 96, 0.1215686275);
    margin-bottom: 0rem;
    background: white;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  h6 {
    font-size: 18px;
    color: black;
    font-weight: 500;
    margin-bottom: 0px;
    min-height: 21px;
    display: flex;
    margin-bottom: 0rem;
  }
  p {
    margin-bottom: 0px;
    color: #666666;
    font-size: 14px;
  }
  .fc-call-btn {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
    color: black;
    transition: all 0.3s ease-in-out;
    cursor: pointer;

    &:hover {
      // transform: scale(1.05);
      color: #014681;

      .call-icon {
        background-color: #014681;
        color: white;
      }
    }
    font-size: 13px;
    font-weight: 400;
    margin-top: auto;

    a {
      width: 100%;
      display: flex;
      flex-direction: row;
      text-decoration: none;
    }
    .call-icon {
      width: 20px;
      height: 20px;
      background-color: #035891;
      border-radius: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .fc-favorite-i {
    position: absolute;
    right: 1rem;
    top: 1rem;
    background-color: rgba(16, 128, 255, 0.5);
    width: 30px;
    height: 30px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .expertise-name-list {
    display: flex;

    span {
      color: #d1d1d1;
      font-size: 12px;
      display: flex;
      flex-direction: row;
      gap: 5px;
      cursor: pointer;
    }
  }

  &:hover {
    background: rgba(171, 173, 196, 0.4);
    border: 1px solid rgba(140, 140, 140, 0.1);
    box-shadow: 0px -1px 10px rgba(0, 0, 0, 0.05);
    border-radius: 20px;
  }
}

.expertise-list:has(span > div:empty) {
  display: none;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
}

.grid-item {
  display: flex;
  justify-content: center;
  height: 100%;
}

.sub-category-navbar-item {
  display: flex;
  flex-direction: row;
  background-color: #f6f6f6;
  border-radius: 40px;
  align-items: center;
  justify-content: center;
  width: max-content;
  margin: 2rem auto;

  div {
    padding: 1rem;
    border-radius: 40px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;

    &:hover {
      background-color: rgba(1, 70, 129, 0.1);
      color: #014681;
    }
  }

  .active {
    background-color: #014681;
    color: white;
    box-shadow: 0 2px 8px rgba(1, 70, 129, 0.3);
  }
}

.e-card-tech {
  background-color: #a3a3a31c;
  border-radius: 16px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000000;
  padding: 5px 10px;
  margin-top: auto;
  max-height: inherit;
  width: auto !important;
}

.expertise-list {
  font-size: 14px;
  cursor: pointer;
  display: table;
  word-wrap: break-word;
  // word-break: break-all;
  gap: 5px;
  flex-wrap: wrap;
  word-break: break-all;
  // height: 62px;
}

@media (max-width: 768px) {
  .category-navbar-item {
    li {
      width: 33%;
      a {
        font-size: 12px;
        flex-direction: column;
        gap: 10px;
        padding-bottom: 10px;
        justify-content: center;
        text-align: center;
      }
    }
  }
  .fc-favorite-list {
    width: 100%;
  }
  .grid-container {
    display: flex;
    flex-direction: column;

    ::ng-deep app-connection-card {
      width: 100%;
    }
  }
}
