import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { AccountService } from '../../account/services/account.service';
import { finalize, pipe, take, Subject, takeUntil } from 'rxjs';
import { Router } from '@angular/router';
import { Ecard } from 'src/app/utils/e-card';
import { HttpClient } from '@angular/common/http';
import { companyType } from 'src/app/shared/constant';
import { filterNonNull } from 'src/app/shared/oprators/filter-null-values';
import { CompanyService } from 'src/app/shared/services/company.service';
@Component({
  selector: 'app-manage-favorites',
  templateUrl: './manage-favorites.component.html',
  styleUrls: ['./manage-favorites.component.scss'],
})
export class ManageFavoritesComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  isLoggedIn$: any;
  loading = false;
  vendors = [];
  distributors: any[] = [];
  resellers: any[] = [];
  avConsultants = [];
  myConnection = [];
  business = [];
  user: any = {};
  companyType = companyType;
  @Input() company!: Ecard;
  followerFollowingLoading = false;
  @Input() expert: any;
  expertsLoading!: boolean;
  companyExperts: any;

  // Cache flags to prevent redundant API calls
  private dataLoaded = {
    favorites: false,
    approved: false,
    pending: false
  };

  // Individual loading states for each tab
  tabLoading = {
    favorites: false,
    approved: false,
    pending: false
  };

  // Sub-tab states for approved and pending tabs
  activeApprovedTab: string = 'eCard';
  activePendingTab: string = 'eCard';

  // Filter dropdown states
  selectedApprovedUserType: number = 0;
  selectedPendingUserType: number = 0;

  // Data for approved connections sub-tabs
  approvedConnections = {
    eCard: [],
    expert: [],
    business: []
  };

  // Data for pending requests sub-tabs
  pendingRequests = {
    eCard: [],
    expert: [],
    business: []
  };

  // Loading states for sub-tabs
  subTabLoading = {
    approved: {
      eCard: false,
      expert: false,
      business: false
    },
    pending: {
      eCard: false,
      expert: false,
      business: false
    }
  };

  // Cache flags for sub-tabs
  private subTabDataLoaded = {
    approved: {
      eCard: false,
      expert: false,
      business: false
    },
    pending: {
      eCard: false,
      expert: false,
      business: false
    }
  };

  setActiveTab(tab: string): void {
    this.activeTab = tab;

    // Show brief loading state for better UX when switching tabs
    if (tab === 'myFavorite' && this.dataLoaded.favorites) {
      this.tabLoading.favorites = true;
      setTimeout(() => this.tabLoading.favorites = false, 200);
    } else if (tab === 'myApproved' && this.dataLoaded.approved) {
      this.tabLoading.approved = true;
      setTimeout(() => this.tabLoading.approved = false, 200);
    } else if (tab === 'myPending' && this.dataLoaded.pending) {
      this.tabLoading.pending = true;
      setTimeout(() => this.tabLoading.pending = false, 200);
    }
  }
  activeTab: string = 'myFavorite';

  setActiveTab1(tab: string): void {
    this.activeTab1 = tab;

    if (tab === 'eCard') {
      this.fetchECardData();
    } else if (tab === 'Business') {
      this.fetchBusinessData();
    } else if (tab === 'Expert') {
      this.fetchExpertData();
    }
  }
  activeTab1: string = 'Resellers';

  constructor(
    private account: AccountService,
    private http: HttpClient,
    private router: Router,
    private companyService: CompanyService
  ) { }

  ngOnInit(): void {
    this.activeTab1 = 'eCard'; // Set default tab to 'eCard'

    // Single subscription to user$ with proper cleanup
    this.account.user$
      .pipe(
        filterNonNull(),
        takeUntil(this.destroy$)
      )
      .subscribe((response) => {
        this.user = response;
        // Load default tab data only once
        if (!this.dataLoaded.favorites) {
          this.changeTab(1);
        }
      });

    this.account.isLoggedIn$
      .pipe(
        filterNonNull(),
        takeUntil(this.destroy$)
      )
      .subscribe((response) => {
        if (!response) return;
        this.isLoggedIn$ = response;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Method to refresh data when needed
  refreshData(tabNumber: number): void {
    if (tabNumber === 1) {
      this.dataLoaded.favorites = false;
      this.tabLoading.favorites = false;
      this.changeTab(1);
    } else if (tabNumber === 2) {
      this.dataLoaded.approved = false;
      this.tabLoading.approved = false;
      this.changeTab(2);
    } else if (tabNumber === 3) {
      this.dataLoaded.pending = false;
      this.tabLoading.pending = false;
      this.changeTab(3);
    }
  }

  getFavoriteEcard() {
    this.loading = true;
    this.http
      .get('UserFavorite/GetUserFavoriteListV2')
      .pipe(
        finalize(() => (this.loading = false)),
        take(1)
      )
      .subscribe((favoriteCompaniesResponse: any) => {
        if (!favoriteCompaniesResponse.messageType) {
          this.vendors = favoriteCompaniesResponse.data.filter(
            (x: any) => x.companyType === 'V'
          );
          this.distributors = favoriteCompaniesResponse.data.filter(
            (x: any) => x.companyType === 'D'
          );
          this.resellers = favoriteCompaniesResponse.data.filter(
            (x: any) => x.companyType === 'R'
          );
          this.avConsultants = favoriteCompaniesResponse.data.filter(
            (x: any) => x.companyType === 'AV'
          );
        }
      });
  }

  changeTab($event: any) {
    if ($event === 1 && !this.dataLoaded.favorites) {
      this.tabLoading.favorites = true;
      this.account
        .getFavorites(this.user.userId)
        .pipe(
          finalize(() => (this.tabLoading.favorites = false)),
          takeUntil(this.destroy$)
        )
        .subscribe((favoriteCompaniesResponse: any) => {
          if (!favoriteCompaniesResponse.messageType) {
            this.vendors = favoriteCompaniesResponse.data.filter(
              (x: any) => x.companyType === 'Vendor'
            );
            this.distributors = favoriteCompaniesResponse.data.filter(
              (x: any) => x.companyType === 'Distributor'
            );
            this.resellers = favoriteCompaniesResponse.data.filter(
              (x: any) => x.companyType === 'Reseller'
            );
            this.avConsultants = favoriteCompaniesResponse.data.filter(
              (x: any) => x.companyType === 'Consultant'
            );
            this.business = favoriteCompaniesResponse.data.filter(
              (x: any) => x.companyType === 'Business'
            );
            this.dataLoaded.favorites = true;
          }
        });
    } else if ($event === 2 && !this.dataLoaded.approved) {
      this.dataLoaded.approved = true;
      this.loadApprovedConnections(this.activeApprovedTab); // Load default sub-tab
    } else if ($event === 3 && !this.dataLoaded.pending) {
      this.dataLoaded.pending = true;
      this.loadPendingRequests(this.activePendingTab); // Load default sub-tab
    }
  }

  handleOnRemoved($event: any, index: number, type: string) {
    if (!$event) return;
    if (type === 'reseller') {
      this.resellers.splice(index, 1);
    } else if (type === 'distributor') {
      this.distributors.splice(index, 1);
    } else if (type === 'vendor') {
      this.vendors.splice(index, 1);
    }
  }

  gotoDetails(company: any) {
    this.router.navigate([`/enduser-details/${company.id}`]);
  }
  navigate(company: any) {
    this.router.navigate([`/details/${company.id}/expert`]);
  }
  navigatePending(company: any) {
    this.router.navigate([`/details/${company.userId}/expert`]);
  }
  //   gotoDetails() {
  //   !this.isLoggedIn$
  //     ? this.router.navigate(['/account'])
  //     : this.router.navigate([`/details/${this.company.id}/company`]);
  // }

  // fetchBusinessData(): void {
  //   this.loading = true;
  //   this.account.user$.subscribe((response) => {
  //     this.loading = true;
  //     this.account
  //       .getMyBusiness()
  //       .pipe(finalize(() => (this.loading = false)))
  //       .subscribe((favoriteCompaniesResponse: any) => {
  //         this.business = favoriteCompaniesResponse.data;
  //       });
  //   });
  // }
  fetchECardData(): void {
    this.loading = true;
    this.account
      .getFavorites(this.user.userId)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((favoriteCompaniesResponse: any) => {
        if (!favoriteCompaniesResponse.messageType) {
          this.vendors = favoriteCompaniesResponse.data.filter(
            (x: any) => x.companyType === 'Vendor'
          );
          this.distributors = favoriteCompaniesResponse.data.filter(
            (x: any) => x.companyType === 'Distributor'
          );
          this.resellers = favoriteCompaniesResponse.data.filter(
            (x: any) => x.companyType === 'Reseller'
          );
          this.avConsultants = favoriteCompaniesResponse.data.filter(
            (x: any) => x.companyType === 'Consultant'
          );
          this.business = favoriteCompaniesResponse.data.filter(
            (x: any) => x.companyType === 'Business'
          );
        }
      });
  }

  fetchBusinessData(): void {
    this.loading = true;
    this.account
      .getFavoritesByType(1) // Assuming userType 2 corresponds to Business data
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((response: any) => {
        if (!response.messageType) {
          this.business = response.data; // Assuming the response contains Business data
        }
      });
  }

  selectedUserType: string = '0'; // Default to 'All'
  allDistributors: any[] = []; // Store the complete list

  fetchExpertData(): void {
    this.loading = true;
    this.account
      .getFavoritesByType(2) // Assuming userType 2 corresponds to Expert data
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((response: any) => {
        if (!response.messageType) {
          this.allDistributors = response.data;
          this.filterDistributors(); // Apply initial filter
        }
      });
  }

  filterDistributors(): void {
    if (this.selectedUserType === '0') {
      // Show all distributors if "All" is selected
      this.distributors = this.allDistributors;
    } else {
      // Map userType values to companyType strings
      const typeMap: { [key: string]: string } = {
        '1': 'Reseller',
        '2': 'Distributor',
        '3': 'Vendor',
        '4': 'Consultant',
      };
      const selectedType = typeMap[this.selectedUserType];
      this.distributors = this.allDistributors.filter(
        (company) => company.userType === this.selectedUserType
      );
    }
  }

  getNoDataMessage(): string {
    const messages: { [key: string]: string } = {
      '0': 'No data found for all categories.',
      '1': 'No resellers found for the selected category.',
      '2': 'No distributors found for the selected category.',
      '3': 'No vendors found for the selected category.',
      '4': 'No consultants found for the selected category.',
    };
    return messages[this.selectedUserType] || 'No data found.';
  }

  // Methods for setting active sub-tabs
  setActiveApprovedTab(tab: string): void {
    this.activeApprovedTab = tab;
    this.loadApprovedConnections(tab);
  }

  setActivePendingTab(tab: string): void {
    this.activePendingTab = tab;
    this.loadPendingRequests(tab);
  }

  // Load approved connections by sub-tab
  loadApprovedConnections(subTab: string): void {
    const tabKey = subTab.toLowerCase() as keyof typeof this.subTabDataLoaded.approved;

    if (this.subTabDataLoaded.approved[tabKey]) {
      return; // Data already loaded
    }

    this.subTabLoading.approved[tabKey] = true;

    // Map sub-tab to API parameter
    const tabParam = subTab === 'eCard' ? 1 : subTab === 'expert' ? 2 : 3;

    this.account.getApprovedConnectionsByType(tabParam)
      .pipe(
        finalize(() => (this.subTabLoading.approved[tabKey] = false)),
        takeUntil(this.destroy$)
      )
      .subscribe((response: any) => {
        if (!response.messageType) {
          this.approvedConnections[tabKey] = response.data || [];
          this.subTabDataLoaded.approved[tabKey] = true;
        }
      });
  }

  // Load pending requests by sub-tab
  loadPendingRequests(subTab: string): void {
    const tabKey = subTab.toLowerCase() as keyof typeof this.subTabDataLoaded.pending;

    if (this.subTabDataLoaded.pending[tabKey]) {
      return; // Data already loaded
    }

    this.subTabLoading.pending[tabKey] = true;

    // Map sub-tab to API parameter (only eCard and expert for pending)
    const tabParam = subTab === 'eCard' ? 1 : 2;

    this.account.getPendingRequestsByType(tabParam)
      .pipe(
        finalize(() => (this.subTabLoading.pending[tabKey] = false)),
        takeUntil(this.destroy$)
      )
      .subscribe((response: any) => {
        if (!response.messageType) {
          this.pendingRequests[tabKey] = response.data || [];
          this.subTabDataLoaded.pending[tabKey] = true;
        }
      });
  }

  // Helper method to map dropdown selection to userType values
  private getUserTypeFromSelection(selection: number): string | null {
    const userTypeMap: { [key: number]: string } = {
      1: 'R',  // Resellers
      2: 'D',  // Distributors
      3: 'V',  // Vendors
      4: 'AV'  // AV Consultants
    };
    return userTypeMap[selection] || null;
  }

  // Helper methods to get data for active tabs
  getApprovedConnectionsForActiveTab(): any[] {
    const tabKey = this.activeApprovedTab.toLowerCase() as keyof typeof this.approvedConnections;
    let data = this.approvedConnections[tabKey] || [];

    // Apply filter for expert tab
    if (this.activeApprovedTab === 'expert' && this.selectedApprovedUserType !== 0) {
      const filterUserType = this.getUserTypeFromSelection(this.selectedApprovedUserType);
      if (filterUserType) {
        data = data.filter((item: any) => item.userType === filterUserType);
      }
    }

    return data;
  }

  getPendingRequestsForActiveTab(): any[] {
    const tabKey = this.activePendingTab.toLowerCase() as keyof typeof this.pendingRequests;
    let data = this.pendingRequests[tabKey] || [];

    // Apply filter for expert tab
    if (this.activePendingTab === 'expert' && this.selectedPendingUserType !== 0) {
      const filterUserType = this.getUserTypeFromSelection(this.selectedPendingUserType);
      if (filterUserType) {
        data = data.filter((item: any) => item.userType === filterUserType);
      }
    }

    return data;
  }

  // Filter methods for approved and pending experts
  filterApprovedExperts(): void {
    // The filtering is handled in getApprovedConnectionsForActiveTab()
    // This method is called when the dropdown changes to trigger change detection
    // Force change detection by updating the data reference
    this.approvedConnections = { ...this.approvedConnections };
  }

  filterPendingExperts(): void {
    // The filtering is handled in getPendingRequestsForActiveTab()
    // This method is called when the dropdown changes to trigger change detection
    // Force change detection by updating the data reference
    this.pendingRequests = { ...this.pendingRequests };
  }

  // Legacy method - keeping for backward compatibility
  fetchPendingRequests(): void {
    this.tabLoading.pending = true; // Show loading indicator for pending tab
    this.account
      .getPendingRequestList(this.user.userId)
      .pipe(
        finalize(() => (this.tabLoading.pending = false)),
        takeUntil(this.destroy$)
      )
      .subscribe((response: any) => {
        if (!response.messageType) {
          this.pendingRequests = response.data; // Store the pending requests
          this.dataLoaded.pending = true;
        }
      });
  }
}
