<div class="fc-account-detail-content">
<div class="ms-4"> 
  <h5 class="fw-bold mb-0">Notification</h5>
  <label class="fs-6 outer-text">Send me email about</label>
</div>
<div id="content"></div>
<div class="mt-4">
  <div>
    <ul class="list-group">
      <li
        class="list-group-item d-flex justify-content-between align-items-start"
      >
        <div class="w-100 d-flex pb-2">
          <div class="ms-2 me-auto d-flex flex-column gap-2 align-items-start">
            <span class="fs-6 label-shadow"> Follow </span>
            <div class="fw-medium">
              Receive notification if member follows you
            </div>
          </div>
          <div class="form-check form-switch d-flex align-items-center">
            <input
              class="form-check-input fs-5 cursor-pointer"
              type="checkbox"
              id="flexSwitchCheckChecked"
              (ngModelChange)="setUserPreference($event,'followNotification')"
              [(ngModel)]="prefrences.followNotification"
            />
          </div>
        </div>
      </li>
      <li
        class="list-group-item d-flex justify-content-between align-items-start"
      >
        <div class="w-100 d-flex pb-2">
          <div class="ms-2 me-auto d-flex flex-column gap-2 align-items-start">
            <span class="fs-6 label-shadow"> Comment </span>
            <div class="fw-medium">When member adds comments on you profile.</div>
          </div>
          <div class="form-check form-switch d-flex align-items-center">
            <input
              class="form-check-input fs-5 cursor-pointer"
              type="checkbox"
              id="flexSwitchCheckChecked"
              (ngModelChange)="setUserPreference($event, 'commentNotification')"
              [(ngModel)]="prefrences.commentNotification"
            />
          </div>
        </div>
      </li>
      <li
        class="list-group-item d-flex justify-content-between align-items-start border-bottom-0"
      >
        <div class="w-100 d-flex pb-2">
          <div class="ms-2 me-auto d-flex flex-column gap-2 align-items-start">
            <span class="fs-6 label-shadow"> Messages </span>
            <div class="fw-fw-medium">When member sends you message in chat.</div>
          </div>
          <div class="form-check form-switch d-flex align-items-center">
            <input
              class="form-check-input fs-5 cursor-pointer"
              type="checkbox"
              id="flexSwitchCheckChecked"
              [(ngModel)]="prefrences.chatNotification"
              (ngModelChange)="setUserPreference($event, 'chatNotification')"
            />
          </div>
        </div>
      </li>
    </ul>
  </div>
</div>
</div>