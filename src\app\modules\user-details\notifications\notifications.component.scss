.fc-account-detail-content {
    box-sizing: border-box;
    width: 100%;
    background: #FFFFFF;
    border: 1px solid rgba(140, 140, 140, 0.2);
    border-radius: 18px;
    padding-block: 1.5rem;
    z-index: 1;
    position: relative;

    .outer-text{
        color: #7F7F7F;
        font-weight: 300;
    }
    .list-group{
        border: none;
    }
    .list-group-item{
        border: none;
        border-bottom: 1px solid #E8E8E8;
        padding-block: 1rem;
    }

    .label-shadow{
        background: rgba(1, 70, 129, 0.07);
        border-radius: 40px;
        padding: 10px 1rem;
        color: black;
    }


    ::ng-deep  .ng-select.ng-select-opened>.ng-select-container{
        box-sizing: border-box;
      margin: 0 auto;
      height: 56px;
      background: #F6F6F6;
      border: 1px solid rgba(246, 246, 245, 0.4);
      border-radius: 12px;
      flex: none;
      order: 1;
      align-self: stretch;
      flex-grow: 0;
      }
      
      ::ng-deep .form-control, .form-control:not([type=file]){
          box-sizing: border-box;
          margin: 0 auto;
          height: 56px;
          background: #F6F6F6;
          border: 1px solid rgba(246, 246, 245, 0.4);
          border-radius: 12px;
          flex: none;
          order: 1;
          align-self: stretch;
          flex-grow: 0;
          padding: 1rem;   
      }


      .form-control{
        box-sizing: border-box;
        margin: 0 auto;
        height: 56px;
        background: #F6F6F6;
        border: 1px solid rgba(246, 246, 245, 0.4);
        border-radius: 12px;
        flex: none;
        order: 1;
        align-self: stretch;
        flex-grow: 0;
        padding: 1rem;
        
        &::placeholder{
          color: #666666;
        }
        }
        .form-label{
          font-weight: 400;
          font-size: 1rem;
          color: black;
        }
        
}

