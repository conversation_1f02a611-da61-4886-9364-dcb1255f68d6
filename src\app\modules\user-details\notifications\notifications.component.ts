import { Component, OnInit } from '@angular/core';
import { AccountService } from '../../account/services/account.service';
import { UtilsService } from 'src/app/shared/services/utils.service';
import { FormControl } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { filterNonNull } from 'src/app/shared/oprators/filter-null-values';

@Component({
  selector: 'app-notifications',
  templateUrl: './notifications.component.html',
  styleUrls: ['./notifications.component.scss'],
})
export class NotificationsComponent implements OnInit {
  user: any = {};
  prefrences: any = {
    followNotification: false,
    commentNotification: false,
    chatNotification: false,
    userId: '',
    id: this.utils.getEmptyGuid(),
  };
  followNotification = new FormControl(false);
  commentNotification = new FormControl(false);
  chatNotification = new FormControl(false);
  constructor(
    private readonly account: AccountService,
    private readonly utils: UtilsService,
    private readonly toaster: ToastrService
  ) {}

  ngOnInit(): void {
    this.account.user$.pipe(filterNonNull()).subscribe((response) => {
      this.prefrences.userId = response.userId;
      this.getUserPreference(response.userId);
    });
  }

  getUserPreference(userId: string) {
    this.account.getAccountPreference(userId).subscribe((response: any) => {
      this.prefrences = response.data;
    });
  }

  setUserPreference($event: any, type: string) {
    this.prefrences[type] = $event;
    this.toaster.clear();
    this.account
      .setAccountPreference(this.prefrences)
      .subscribe((response: any) => {
        if (response.data) {
          this.toaster.success('Setting saved successfully');
        }
      });
  }
}
