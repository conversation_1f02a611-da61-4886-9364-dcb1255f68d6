<div class="fc-account-detail-content">
<div id="content"></div>
  <div
    [formGroup]="resetPasswordForm"
    class="tab-content p-0"
    id="nav-tabContent"
  >
    <div class="tab-pane fade show active">
      <h5 class="fw-bold">Password</h5>
        <div class="row mt-4">
          <div class="col-md-12">
            <div class="mb-3">
              <label for="currentPassword" class="form-label"
                >Current Password</label
              >
              <app-focile-input
                [type]="showCurrentPassword ? 'text' : 'password'"
                [id]="'currentPassword'"
                [name]="'currentPassword'"
                [placeholder]="'Enter your password'"
                [disabled]="false"
                [iconName]="showCurrentPassword ? 'eye' : 'eye-slash'"
                (onIconClick)="toggleInputType('currentPassword')"
                formControlName="currentPassword"
              ></app-focile-input>
            </div>
          </div>
          <div class="col-md-6">
            <div class="mb-3">
              <label for="password" class="form-label"
                >New Password
                <img src="../../../../assets/svgs/info.svg" alt="info" 
                [popover]="popTemplate"
                (click)="showStrongPasswordPopup = !showStrongPasswordPopup"
                class="fa fa-info-circle ms-2 text-primary"
                placement="top"
                role="button"
                [outsideClick]="true"
                />
                <!-- <i
                  [popover]="popTemplate"
                  (click)="showStrongPasswordPopup = !showStrongPasswordPopup"
                  class="fa fa-info-circle ms-2 text-primary"
                  placement="top"
                  role="button"
                  [outsideClick]="true"
                ></i
              > -->
            </label>
              <app-focile-input
                [type]="showPassword ? 'text' : 'password'"
                [id]="'password'"
                [name]="'password'"
                [placeholder]="'Enter your password'"
                [disabled]="false"
                [iconName]="showPassword ? 'eye' : 'eye-slash'"
                (onIconClick)="toggleInputType('newPassword')"
                formControlName="password"
                [elementClass]="
                  resetPasswordForm.get('password')?.touched &&
                  (resetPasswordForm.get('password')?.errors?.required ||
                    resetPasswordForm.get('password')?.errors?.pattern)
                    ? 'is-invalid'
                    : null
                "
              ></app-focile-input>
              <span
                *ngIf="
                  resetPasswordForm.get('password')?.touched &&
                  resetPasswordForm.get('password')?.errors?.required
                "
                class="text-danger"
              >
                Password is required.
              </span>
              <span
                *ngIf="
                  resetPasswordForm.get('password')?.touched &&
                  resetPasswordForm.get('password')?.errors?.pattern
                "
                class="text-danger"
              >
                Password is not strong.
              </span>
            </div>
          </div>
          <div class="col-md-6">
            <div class="mb-3">
              <label for="confirmPassword" class="form-label"
                >Confirm Password</label
              >
              <app-focile-input
                [type]="showConfirmPassword ? 'text' : 'password'"
                [id]="'password'"
                [name]="'password'"
                [placeholder]="'Enter your password'"
                [disabled]="false"
                [iconName]="showConfirmPassword ? 'eye' : 'eye-slash'"
                (onIconClick)="toggleInputType('confirmPassword')"
                formControlName="confirmPassword"
              ></app-focile-input>
              <span
                *ngIf="
                  resetPasswordForm.get('confirmPassword')?.touched &&
                  resetPasswordForm.get('confirmPassword')?.errors?.required
                "
                class="text-danger"
              >
                Confirm Password is required.
              </span>
              <span
                *ngIf="
                  resetPasswordForm.get('confirmPassword')?.touched &&
                  !resetPasswordForm.get('password')?.errors?.pattern &&
                  resetPasswordForm.get('confirmPassword')?.value !=
                    resetPasswordForm.get('password')?.value
                "
                class="text-danger"
              >
                Both password should be same.
              </span>
            </div>
          </div>
        </div>
        <div class="text-left mt-3">
          <div class="w-100">
            <focile-button
              (onClick)="updatePassword()"
              [disabled]="resetPasswordForm.invalid"
              [loading]="loading"              
              class="custom-btn my-4"
              >Update Setting</focile-button
            >
          </div>
        </div>
    </div>
  </div>
<ng-template #popTemplate let-message="message">
  <ol>
    <li>
      <strong>Uppercase Letters:</strong> Your password should include at least
      one uppercase letter (A-Z).
    </li>
    <li>
      <strong>Lowercase Letters:</strong> Your password should include at least
      one lowercase letter (a-z).
    </li>
    <li>
      <strong>Digits:</strong> Your password should include at least one digit
      (0-9).
    </li>
    <li>
      <strong>Special Characters:</strong> Your password should include at least
      one special character (e.g., @, $, !, %, *, ?, #, ^, etc.). Special
      characters add complexity to the password.
    </li>
  </ol>
</ng-template>

</div>