.fc-account-detail-content {
    box-sizing: border-box;
    width: 100%;
    background: #FFFFFF;
    border: 1px solid rgba(140, 140, 140, 0.2);
    border-radius: 18px;
    padding-block: 1.5rem;
    z-index: 1;
    position: relative;
    padding: 1.5rem;

    .outer-text{
        color: #7F7F7F;
        font-weight: 300;
    }
    .list-group{
        border: none;
    }
    .list-group-item{
        border: none;
        border-bottom: 1px solid #E8E8E8;
        padding-block: 1rem;
    }

    .label-shadow{
        background: rgba(1, 70, 129, 0.07);
        border-radius: 40px;
        padding: 10px 1rem;
        color: black;
    }

    .form-label{
        font-weight: 400;
        font-size: 1rem;
    }

    ::ng-deep .form-control {
        box-sizing: border-box;
        margin: 0 auto;
        height: 56px !important;
        background: #F6F6F6;
        border: 1px solid rgba(246, 246, 245, 0.4);
        border-radius: 12px;
        flex: none;
        order: 1;
        align-self: stretch;
  
        input[type="text"]{
          padding-inline: 0px;
          background: transparent !important;
          border: none !important;
        }
      }
  
      ::ng-deep .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value{
        background-color: #e7e7e7;
        border-radius: 5px;
      }
  
      ::ng-deep .ng-dropdown-panel.ng-select-bottom{
        background: #FFFFFF;
        box-shadow: 0px 41px 89px rgba(0, 0, 0, 0.1), 0px 0px 0px rgba(0, 0, 0, 0.1);
        border-radius: 16px;
        border: none;
      }
  
      ::ng-deep .ng-select .ng-arrow-wrapper{
        width: 35px;
      }

      .custom-btn{
        ::ng-deep button{
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          min-width: 175px;
          height: 50px;
          background: #014681;
          box-shadow: 0px 548px 219px rgba(0, 0, 0, 0.01), 0px 308px 185px rgba(0, 0, 0, 0.04), 0px 137px 137px rgba(0, 0, 0, 0.06), 0px 34px 75px rgba(0, 0, 0, 0.07), 0px 0px 0px rgba(0, 0, 0, 0.07);
          border-radius: 100px;
          border: none;
          text-align: center;
          color: white;
          font-size: 18px;
          font-weight: 500 !important;
        }
    }
}