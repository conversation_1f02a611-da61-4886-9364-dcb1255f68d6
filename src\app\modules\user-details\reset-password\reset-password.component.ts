import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AccountService } from '../../account/services/account.service';
import { ToastrService } from 'ngx-toastr';
import { finalize } from 'rxjs';
import { Regex } from 'src/app/shared/constant';
import { filterNonNull } from 'src/app/shared/oprators/filter-null-values';

@Component({
  selector: 'app-reset-password',
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.scss'],
})
export class ResetPasswordComponent implements OnInit {
  resetPasswordForm!: FormGroup;
  user$: any = {};
  loading = false;
  showPassword = false;
  showCurrentPassword = false;
  showConfirmPassword = false;
  showStrongPasswordPopup = false;
  constructor(
    private readonly formBuilder: FormBuilder,
    private readonly account: AccountService,
    private readonly toastrService: ToastrService
  ) {}

  ngOnInit(): void {
    this.account.user$.pipe(filterNonNull()).subscribe((response) => {
      this.user$ = response;
    });
    this.initForm();
  }

  initForm() {
    this.resetPasswordForm = this.formBuilder.group({
      currentPassword: [null, Validators.required],
      password: [null, [Validators.required,Validators.pattern(Regex.strongPassword)]],
      confirmPassword: [null, Validators.required],
    });

    this.resetPasswordForm
      .get('password')
      ?.valueChanges.subscribe((response: string) => {
        this.resetPasswordForm
          .get('confirmPassword')
          ?.setValidators([Validators.pattern(response)]);
      });
  }

  updatePassword() {
    this.loading = !this.loading;
    const data = {
      userId: this.user$?.userId,
      oldPassword: this.resetPasswordForm.get('currentPassword')?.value,
      newPassword: this.resetPasswordForm.get('password')?.value,
    };

    this.account
      .changeCurrentPassword(data)
      .pipe(finalize(() => (this.loading = !this.loading)))
      .subscribe((response: any) => {
        if (response.data) {
          this.toastrService.success(response.message);
          this.resetPasswordForm.reset();
        }
      });
  }

  toggleInputType(type: string) {
    if (type === 'newPassword') {
      this.showPassword = !this.showPassword;
    } else if (type === 'confirmPassword') {
      this.showConfirmPassword = !this.showConfirmPassword;
    } else if (type === 'currentPassword') {
      this.showCurrentPassword = !this.showCurrentPassword;
    }
  }
}
