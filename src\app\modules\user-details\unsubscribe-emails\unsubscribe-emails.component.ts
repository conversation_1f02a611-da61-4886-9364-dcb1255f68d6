import { CommonModule } from '@angular/common';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { Component, OnDestroy, type OnInit } from '@angular/core';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-unsubscribe-emails',
  standalone: true,
  imports: [CommonModule, RouterModule, HttpClientModule],
  templateUrl: './unsubscribe-emails.component.html',
  styleUrls: ['./unsubscribe-emails.component.scss'],
})
export class UnsubscribeEmailsComponent implements OnInit, OnDestroy {
  activatedRouteSub$!: Subscription;
  data: any = {};
  constructor(
    private httpClient: HttpClient,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private readonly toaster: ToastrService
  ) {}

  ngOnDestroy(): void {
    this.activatedRouteSub$.unsubscribe();
  }
  ngOnInit(): void {
    this.activatedRouteSub$ = this.activatedRoute.queryParams.subscribe(
      (response) => {
        this.data = response;
      }
    );
  }

  ubsubscribeMails() {
    const { email, token, sessionId } = this.data;
    this.httpClient
      .get(
        `UserNotificationSetting/UnSubscribe?email=${email}&token=${token}&sessionId=${sessionId}`
      )
      .subscribe((response: any) => {
        if (response.data) {
          this.toaster.success(`You've successfully unsubscribed from our emails!`);
          this.router.navigate(['home']);
        }
      });
  }
}
