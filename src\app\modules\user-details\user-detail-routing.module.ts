import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AccountDetailShellComponent } from './account-shell/account-shell.component';
import { AccountDetailComponent } from './account-detail/account-detail.component';
import { UserDetailComponent } from './user-detail/user-detail.component';
import { ResetPasswordComponent } from './reset-password/reset-password.component';
import { NotificationsComponent } from './notifications/notifications.component';
import { ManageDocumentsComponent } from './manage-documents/manage-documents.component';
import { ChatListItemComponent } from './components/chat-list-item/chat-list-item.component';
import { ChatComponent } from './chat/chat.component';
import { ManageFavoritesComponent } from './manage-favorites/manage-favorites.component';
import { CompanyDetailsGuard } from 'src/app/guards/company-details.guard';
import { ViewPlansComponent } from './view-plans/view-plans.component';
import { AuthGuard } from 'src/app/guards/auth.guard';

const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    redirectTo: '',
  },
  {
    path: '',
    component: AccountDetailShellComponent,
    canActivate: [AuthGuard],
    children: [
      {
        path: 'company-details',
        component: AccountDetailComponent,
        canActivate: [CompanyDetailsGuard],
      },
      {
        path: 'user-detail',
        component: UserDetailComponent,
      },
      {
        path: 'reset-password',
        component: ResetPasswordComponent,
      },
      {
        path: 'notications',
        component: NotificationsComponent,
      },
      {
        path: 'manage-documents',
        component: ManageDocumentsComponent,
      },
      {
        path: 'chat',
        component: ChatComponent,
      },
      {
        path: 'favorites',
        component: ManageFavoritesComponent,
      },
      {
        path: 'view-plans',
        component: ViewPlansComponent,
      },
    ],
  },
  // {
  //   path: '**',
  //   redirectTo: 'company-details',
  // },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class UserDetailRoutingModule {}
