import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { UserDetailRoutingModule } from './user-detail-routing.module';
import { AccountDetailShellComponent } from './account-shell/account-shell.component';
import { LayoutModule } from 'src/app/layout/layout.module';
import { AccountDetailComponent } from './account-detail/account-detail.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { TabsModule } from 'ngx-bootstrap/tabs';
import { UserDetailComponent } from './user-detail/user-detail.component';
import { ResetPasswordComponent } from './reset-password/reset-password.component';
import { NotificationsComponent } from './notifications/notifications.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ManageDocumentsComponent } from './manage-documents/manage-documents.component';
import { ManageFavoritesComponent } from './manage-favorites/manage-favorites.component';
import { ChatComponent } from './chat/chat.component';
import { ViewPlansComponent } from './view-plans/view-plans.component';
import { SocialMediaLinkFormComponent } from "../../shared/forms/social-media-link-form/social-media-link-form.component";
import { TinyMCEditorComponent } from 'src/app/shared/components/editor/editor.component';

@NgModule({
  declarations: [
    AccountDetailShellComponent,
    AccountDetailComponent,
    UserDetailComponent,
    ResetPasswordComponent,
    NotificationsComponent,
    ManageDocumentsComponent,
    ManageFavoritesComponent,
    ChatComponent,
    ViewPlansComponent,
  ],
  imports: [
    CommonModule,
    UserDetailRoutingModule,
    LayoutModule,
    SharedModule,
    TabsModule.forRoot(),
    FormsModule,
    ReactiveFormsModule,
    SocialMediaLinkFormComponent,
    TinyMCEditorComponent
],
  exports: [AccountDetailComponent],
})
export class UserDetailModule {}
