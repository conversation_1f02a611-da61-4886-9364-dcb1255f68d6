<div class="fc-account-detail-content">
  <div id="content"></div>
  <div *ngIf="!loading">
    <div
      *ngIf="userState$?.userType != companyType.EndUser"
      [formGroup]="userDetailForm"
      class="tab-content p-0"
      id="nav-tabContent"
    >
      <div class="tab-pane fade show active">
        <h5 class="fw-bold mb-4 mt-2">User Details</h5>
        <div class="row">
          <div class="col-md-6">
            <div class="mb-3">
              <label for="firstName" class="form-label"
                >First name <span class="required">*</span>
              </label>
              <input
                type="text"
                class="form-control"
                formControlName="firstName"
                id="firstName"
                placeholder="Type here"
                [ngClass]="{
                  'is-invalid': userDetailForm.get('firstName')?.invalid
                }"
              />
              <div
                *ngIf="
                  userDetailForm.get('firstName')?.touched &&
                  userDetailForm.get('firstName')?.dirty
                "
                class="text-danger"
              >
                <span *ngIf="userDetailForm.get('firstName')?.errors?.required"
                  >First Name is required</span
                >
                <span *ngIf="userDetailForm.get('firstName')?.errors?.pattern"
                  >First Name is not valid</span
                >
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="mb-3">
              <label for="lastName" class="form-label"
                >Last name <span class="required">*</span></label
              >
              <input
                type="email"
                class="form-control"
                formControlName="lastName"
                id="lastName"
                placeholder="Type here"
                [ngClass]="{
                  'is-invalid': userDetailForm.get('lastName')?.invalid
                }"
              />
              <div
                *ngIf="
                  userDetailForm.get('lastName')?.touched &&
                  userDetailForm.get('lastName')?.dirty
                "
                class="text-danger"
              >
                <span *ngIf="userDetailForm.get('lastName')?.errors?.required"
                  >Last Name is required</span
                >
                <span *ngIf="userDetailForm.get('lastName')?.errors?.pattern"
                  >Last Name is not valid</span
                >
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="mb-3">
              <label for="mobileNo" class="form-label"
                >Work Phone Number <span class="required">*</span></label
              >
              <span class="d-flex gap-3">
                <span class="w-150">
                  <focile-dropdown
                    [items]="countryList"
                    formControlName="countryCode"
                    [bindValue]="'description'"
                    [bindLabel]="'description'"
                    [clearable]="false"
                  ></focile-dropdown>
                </span>
                <div class="w-100">
                  <app-focile-input
                    [type]="'text'"
                    [id]="'phoneNumber'"
                    [name]="'phoneNumber'"
                    [disabled]="false"
                    formControlName="phoneNumber"
                    placeholder="(xxx) xxx.xxxx"
                    [elementClass]="
                      (userDetailForm.get('phoneNumber')?.touched &&
                        userDetailForm.get('phoneNumber')?.errors?.required) ||
                      (userDetailForm.get('phoneNumber')?.touched &&
                        userDetailForm.get('phoneNumber')?.errors?.pattern)
                        ? 'is-invalid'
                        : null
                    "
                  ></app-focile-input>
                  <div
                    *ngIf="
                      userDetailForm.get('phoneNumber')?.touched &&
                      userDetailForm.get('phoneNumber')?.dirty
                    "
                    class="text-danger"
                  >
                    <span
                      *ngIf="
                        userDetailForm.get('phoneNumber')?.errors?.required
                      "
                      >Work Mobile number is required</span
                    >
                    <span
                      *ngIf="userDetailForm.get('phoneNumber')?.errors?.pattern"
                      >Work Mobile number is not valid</span
                    >
                  </div>
                </div>
              </span>
            </div>
          </div>
          <div class="col-md-6">
            <div class="mb-3">
              <label for="mobileNo" class="form-label"
                >Mobile Phone Number <span class="required">*</span></label
              >
              <span class="d-flex gap-3">
                <span class="w-150">
                  <focile-dropdown
                    [items]="countryList"
                    formControlName="workMobileNumberCountryCode"
                    [bindValue]="'description'"
                    [bindLabel]="'description'"
                    [clearable]="false"
                  ></focile-dropdown>
                </span>
                <div class="w-100">
                  <app-focile-input
                    [type]="'text'"
                    [id]="'workMobileNumber'"
                    [name]="'workMobileNumber'"
                    [disabled]="false"
                    formControlName="workMobileNumber"
                    placeholder="(xxx) xxx.xxxx"
                    [elementClass]="
                      (userDetailForm.get('workMobileNumber')?.touched &&
                        userDetailForm.get('workMobileNumber')?.errors
                          ?.required) ||
                      (userDetailForm.get('workMobileNumber')?.touched &&
                        userDetailForm.get('workMobileNumber')?.errors?.pattern)
                        ? 'is-invalid'
                        : null
                    "
                  ></app-focile-input>
                  <div
                    *ngIf="
                      userDetailForm.get('workMobileNumber')?.touched &&
                      userDetailForm.get('workMobileNumber')?.dirty
                    "
                    class="text-danger"
                  >
                    <span
                      *ngIf="
                        userDetailForm.get('workMobileNumber')?.errors?.required
                      "
                      >Mobile number is required</span
                    >
                    <span
                      *ngIf="
                        userDetailForm.get('workMobileNumber')?.errors?.pattern
                      "
                      >Mobile number is not valid</span
                    >
                  </div>
                </div>
              </span>
              <div class="form-check mt-2">
                <input
                  class="form-check-input"
                  type="checkbox"
                  id="showWorkMobileNumber"
                  name="showWorkMobileNumber"
                  formControlName="isShowWorkMobileNumber"
                />
                <label
                  class="form-check-label text-muted fs-6"
                  for="flexCheckDefault"
                >
                  Show my number in profile page
                </label>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="mb-3">
              <label for="email" class="form-label">Email Id</label>
              <input
                type="email"
                class="form-control"
                formControlName="email"
                id="email"
                placeholder="Type here"
              />
            </div>
          </div>
          <div class="col-md-6">
            <div class="mb-3">
              <label for="role" class="form-label">Role</label>
              <focile-dropdown
                formControlName="roleId"
                [bindValue]="'idGuid'"
                [items]="roleList"
                [clearable]="false"
              ></focile-dropdown>
            </div>
          </div>
          <div class="col-md-12 mb-4">
            <label for="about" class="form-label">About Me</label>
            <textarea
              class="form-control"
              placeholder="What would you like people to know about you in a brief summary?"
              id="about"
              formControlName="aboutMe"
              maxlength="300"
              style="height: 100px"
            ></textarea>
            <ng-container
              *ngIf="userDetailForm.get('aboutMe')?.errors?.maxlength"
            >
              <span class="text-danger">
                About me should less then 300 words.
              </span>
            </ng-container>
          </div>
          <div class="col-md-12 mb-4">
            <label for="about" class="form-label">Strength and Ability</label>
            <textarea
              class="form-control"
              placeholder="Show the type of expertise you are offering to your audience for them to reach out"
              id="about"
              maxlength="300"
              style="height: 100px"
              formControlName="strengthAndAbility"
            ></textarea>
          </div>
          <div class="col-md-12 mb-4">
            <label for="about" class="form-label">Message to Audience</label>
            <textarea
              class="form-control"
              placeholder="Your little story to your audience or fun fact about you"
              id="about"
              maxlength="300"
              style="height: 100px"
              formControlName="messageToAudience"
            ></textarea>
          </div>

          <div class="col-md-12">
            <div class="your-unique-identification">
              <label for="role" class="form-label"
                >Your unique Identification</label
              >
              <input
                type="text"
                disabled
                class="form-control"
                name="uniqidentification"
                [value]="userState$.aspNetUserId"
                id="uniqidentification"
              />
            </div>
          </div>
        </div>

        <div class="mt-4 elvator-pitch-card">
          <h5 class="fw-medium mb-4">Expert Profile Page Elevator Pitch</h5>
          <div class="" formArrayName="userElevatorPitch">
            <ng-container *ngIf="!pitchesLoading">
              <div
                class="alert alert-warning alert-dismissible fade show"
                role="alert"
              >
                <strong>Note!</strong> Approved elevator pitches are not subject
                to modification.
                <button
                  type="button"
                  class="btn-close"
                  data-bs-dismiss="alert"
                  aria-label="Close"
                ></button>
              </div>
              <ng-container
                *ngFor="let item of userElevatorPitch.controls; let i = index"
                [formGroupName]="i"
              >
                <div
                  class="row"
                  *ngIf="!userElevatorPitch.controls[i].get('isRemoved')?.value"
                  [ngClass]="{ 'mt-3': i > 0 }"
                >
                  <div class="col-sm-12">
                    <label
                      for="elevatorPitch"
                      class="mb-2 d-flex align-items-center gap-2 justify-content-start"
                      *ngIf="i == 0"
                      >Elevator Pitch
                      <span
                        tooltip="Video length should be less then or equal to 30 seconds"
                        class="text-primary"
                      >
                        <img src="../../../../assets/svgs/info.svg" />
                      </span>
                    </label>
                  </div>
                  <div
                    class="col-md-12 d-flex flex-row align-items-center gap-3"
                  >
                    <div class="col-md-9">
                      <div class="form-group">
                        <input
                          class="form-control h-auto"
                          placeholder="Insert your media approved, elevator pitch hyperlink"
                          formControlName="url"
                          type="text"
                        />
                        <div
                          class="text-danger"
                          *ngIf="
                            userElevatorPitch.controls[i].get('url')?.errors
                              ?.pattern
                          "
                        >
                          Not valid link
                        </div>
                      </div>
                    </div>
                    <div class="col-md-3 d-flex flex-row gap-2">
                      <ng-container
                        *ngIf="
                          userElevatorPitch.controls[i].get('isApproved')
                            ?.value === approvePostStatusEnum.Approved
                        "
                      >
                        <button
                          *ngIf="
                            userElevatorPitch.controls[i].get('url')?.disabled
                          "
                          class="check-btn round-btn"
                          tooltip="This is a approved elevator pitch video"
                        >
                          <i class="fa fa-check-circle"></i>
                        </button>

                        <button
                          *ngIf="
                            userElevatorPitch.controls[i].get('url')?.disabled
                          "
                          class="edit-btn round-btn"
                          tooltip="New Elevator Pitch needs to be approved by focile platform"
                          (click)="enableElevatorPitch(i)"
                        >
                          <i class="fa fa-pen"></i>
                        </button>
                      </ng-container>

                      <button
                        *ngIf="userElevatorPitch.controls[i].get('id')?.value"
                        class="remove-btn round-btn"
                        tooltip="Remove Pitch"
                        (click)="confirmationElevatorPitchRemove(i)"
                      >
                        <i class="fa fa-times"></i>
                      </button>

                      <button
                        *ngIf="
                          !userElevatorPitch.controls[i].get('id')?.value &&
                          userElevatorPitch.controls.length > 1
                        "
                        class="remove-btn round-btn"
                        tooltip="Remove Elevator Pitch"
                        (click)="removePitch(i)"
                      >
                        <i class="fa fa-times"></i>
                      </button>

                      <button
                        *ngIf="
                          userElevatorPitch.controls[i].get('url')?.enabled
                        "
                        class="add-btn round-btn"
                        tooltip="Save Elevator Pitch"
                        [disabled]="
                          !userElevatorPitch.controls[i].get('url')?.value ||
                          savingVideo ||
                          userElevatorPitch.controls[i].get('url')?.invalid
                        "
                        (click)="saveElevatorPitch(i)"
                      >
                        <i class="fa fa-check"></i>
                      </button>
                      <button
                        *ngIf="userElevatorPitch.controls.length - 1 == i"
                        class="add-btn round-btn"
                        (click)="addPitch()"
                        tooltip="Add New Pitch"
                      >
                        <i class="fa fa-plus"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </ng-container>
            </ng-container>
            <ng-container *ngIf="pitchesLoading">
              <div class="text-center w-100">
                <app-spinner></app-spinner>
              </div>
            </ng-container>
          </div>
        </div>

        <div class="mt-5">
          <h5 class="fw-bold mb-4">Social Network</h5>
          <app-social-media-link-form></app-social-media-link-form>
        </div>

        <div class="d-flex mt-5 mb-4 justify-content-end">
          <focile-button
            [disabled]="userDetailForm.invalid"
            (onClick)="updateAccountDetail()"
            [loading]="saving"
            class="custom-btn my-0 my-sm-4"
            >Update Setting</focile-button
          >
        </div>
      </div>
    </div>
    <!-- End User Details  -->
    <div
      *ngIf="userState$?.userType == companyType.EndUser"
      [formGroup]="endUserForm"
      class="tab-content p-0"
      id="nav-tabContent"
    >
      <div
        class="tab-pane fade show active"
        *ngIf="!enduserLoading; else loadingTemplate"
      >
        <h5 class="fw-bold my-3">User Details</h5>
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="firstName" class="form-label"
                  >First name <span class="required">*</span></label
                >
                <input
                  type="email"
                  class="form-control"
                  formControlName="firstName"
                  id="firstName"
                  placeholder="Type here"
                  [ngClass]="{
                    'is-invalid': endUserForm.get('firstName')?.invalid
                  }"
                />
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="lastName" class="form-label"
                  >Last name <span class="required">*</span></label
                >
                <input
                  type="email"
                  class="form-control"
                  formControlName="lastName"
                  id="lastName"
                  placeholder="Type here"
                  [ngClass]="{
                    'is-invalid': endUserForm.get('lastName')?.invalid
                  }"
                />
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="mobileNo" class="form-label"
                  >Mobile Number <span class="required">*</span></label
                >
                <span class="d-flex gap-2">
                  <span class="w-25">
                    <focile-dropdown
                      [items]="countries$ | async"
                      formControlName="mobileCountryCode"
                      [bindValue]="'description'"
                      [bindLabel]="'description'"
                      [clearable]="false"
                    ></focile-dropdown>
                  </span>
                  <div class="w-100">
                    <app-focile-input
                      [type]="'text'"
                      [id]="'phoneNumber'"
                      [name]="'phoneNumber'"
                      [disabled]="false"
                      formControlName="phoneNumber"
                      placeholder="(xxx) xxx.xxxx"
                      [elementClass]="
                        (endUserForm.get('phoneNumber')?.touched &&
                          endUserForm.get('phoneNumber')?.errors?.required) ||
                        (endUserForm.get('phoneNumber')?.touched &&
                          endUserForm.get('phoneNumber')?.errors?.pattern)
                          ? 'is-invalid'
                          : null
                      "
                    ></app-focile-input>
                  </div>
                </span>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="email" class="form-label"
                  >Email Id <span class="required">*</span></label
                >
                <input
                  type="email"
                  class="form-control"
                  formControlName="email"
                  id="email"
                  placeholder="Type here"
                  [disabled]="true"
                />
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="name" class="form-label"
                  >Organization Name <span class="text-danger">*</span></label
                >
                <app-focile-input
                  [type]="'text'"
                  [id]="'id'"
                  [name]="'name'"
                  [disabled]="true"
                  formControlName="organizationName"
                  [elementClass]="
                    endUserForm.get('organizationName')?.errors?.required &&
                    endUserForm.get('organizationName')?.touched
                      ? 'is-invalid'
                      : null
                  "
                ></app-focile-input>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="name" class="form-label"
                  >Vertical<span class="text-danger">*</span></label
                >
                <focile-dropdown
                  [placeholder]="'Select an option'"
                  formControlName="organizationTypeId"
                  [bindValue]="'idGuid'"
                  [items]="organizationTypes$ | async"
                ></focile-dropdown>
                <ng-container [ngTemplateOutlet]="errorTemplate" [ngTemplateOutletContext]="{
                  field: 'Vertical',
                  control: endUserForm.get('organizationTypeId')
                }"></ng-container>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-4">
                <label for="companySystemIds" class="form-label">Company Systems <span class="required">*</span></label>
                <focile-dropdown [placeholder]="'Select'" id="companySystemIds" [items]="companySystemList"
                  formControlName="companySystemIds" [multiple]="true" [loading]="settingsLoading"></focile-dropdown>
                <ng-container [ngTemplateOutlet]="errorTemplate" [ngTemplateOutletContext]="{
                                            field: 'Company Systems',
                                            control: endUserForm.get('companySystemIds')
                                          }"></ng-container>
              </div>      </div>
            <div class="col-md-6">
              <div class="mb-4">
                <label for="experties" class="form-label">Expertise <span class="required">*</span></label>
                <focile-dropdown [placeholder]="'Select'" id="focileDropdown" formControlName="expertiseIds" [items]="expertiseList"
                  [multiple]="true" [showOptions]="3" [loading]="settingsLoading"></focile-dropdown>
                <ng-container [ngTemplateOutlet]="errorTemplate" [ngTemplateOutletContext]="{
                                field: 'Expertise',
                                control: endUserForm.get('expertiseIds')
                              }"></ng-container>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-4">
                <label for="solutions" class="form-label">Solution <span class="required">*</span></label>
                <focile-dropdown [placeholder]="'Select'" id="solutions" [items]="solutionList" formControlName="solutionIds"
                  [multiple]="true" [loading]="settingsLoading"></focile-dropdown>
                <ng-container [ngTemplateOutlet]="errorTemplate" [ngTemplateOutletContext]="{
                                field: 'Solution',
                                control: endUserForm.get('solutionIds')
                              }"></ng-container>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-4">
                <label for="products" class="form-label">Products <span class="required">*</span></label>
                <focile-dropdown [placeholder]="'Select'" id="products" [items]="productList" formControlName="productIds"
                  [multiple]="true" [loading]="settingsLoading"></focile-dropdown>
                <ng-container [ngTemplateOutlet]="errorTemplate" [ngTemplateOutletContext]="{
                                field: 'Products',
                                control: endUserForm.get('productIds')
                              }"></ng-container>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-4">
                <label for="services" class="form-label">Services <span class="required">*</span></label>
                <focile-dropdown [placeholder]="'Select'" id="services" [multiple]="true" [items]="servicesList"
                  formControlName="serviceIds" [loading]="settingsLoading"></focile-dropdown>
                <ng-container [ngTemplateOutlet]="errorTemplate" [ngTemplateOutletContext]="{
                                field: 'Services',
                                control: endUserForm.get('serviceIds')
                              }"></ng-container>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-4">
                <label for="industry" class="form-label">Industry <span class="required">*</span></label>
                <focile-dropdown [placeholder]="'Select'" id="industry" [multiple]="true" formControlName="industryIds"
                  [items]="industryList" [loading]="settingsLoading"></focile-dropdown>
                <ng-container [ngTemplateOutlet]="errorTemplate" [ngTemplateOutletContext]="{
                                field: 'Industry',
                                control: endUserForm.get('industryIds')
                              }"></ng-container>
              </div>
            </div>
            <div class="col-md-12">
              <div class="mb-3">
                <label for="name" class="form-label"
                  >Address <span class="text-danger">*</span></label
                >
                <app-focile-input
                  [type]="'text'"
                  [id]="'id'"
                  [name]="'name'"
                  [disabled]="false"
                  formControlName="address"
                  [elementClass]="
                    endUserForm.get('address')?.errors?.required &&
                    endUserForm.get('address')?.touched
                      ? 'is-invalid'
                      : null
                  "
                ></app-focile-input>
                <ng-container [ngTemplateOutlet]="errorTemplate" [ngTemplateOutletContext]="{
                  field: 'Address',
                  control: endUserForm.get('address')
                }"></ng-container>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="name" class="form-label"
                  >Country <span class="text-danger">*</span></label
                >
                <focile-dropdown
                  [placeholder]="'Select an option'"
                  [bindValue]="'id'"
                  [items]="countries$ | async"
                  [loading]="countriesLoading"
                  formControlName="country"
                  (change)="handleCountryChange($event)"
                  [clearable]="false"
                ></focile-dropdown>
                <ng-container [ngTemplateOutlet]="errorTemplate" [ngTemplateOutletContext]="{
                  field: 'Country',
                  control: endUserForm.get('country')
                }"></ng-container>
              </div>
            </div>

            <div class="col-md-6">
              <div class="mb-3">
                <label for="name" class="form-label"
                  >State <span class="text-danger">*</span></label
                >
                <focile-dropdown
                  [placeholder]="'Select an option'"
                  formControlName="state"
                  [bindValue]="'id'"
                  [items]="states"
                  [loading]="statesLoading"
                  (change)="handleStateChange($event)"
                  [clearable]="false"
                ></focile-dropdown>
                <ng-container [ngTemplateOutlet]="errorTemplate" [ngTemplateOutletContext]="{
                  field: 'State',
                  control: endUserForm.get('state')
                }"></ng-container>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="name" class="form-label"
                  >City <span class="text-danger">*</span></label
                >
                <focile-dropdown
                  [placeholder]="'Select an option'"
                  [bindValue]="'id'"
                  [items]="cities"
                  formControlName="city"
                  [clearable]="false"
                  [loading]="citiesLoading"
                ></focile-dropdown>
                <ng-container [ngTemplateOutlet]="errorTemplate" [ngTemplateOutletContext]="{
                  field: 'City',
                  control: endUserForm.get('city')
                }"></ng-container>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="name" class="form-label"
                  >Zipcode <span class="text-danger">*</span></label
                >
                <app-focile-input
                  [type]="'text'"
                  [id]="'zipcode'"
                  [name]="'zipcode'"
                  [disabled]="false"
                  formControlName="zipCode"
                  [elementClass]="
                    endUserForm.get('zipCode')?.errors?.required &&
                    endUserForm.get('zipCode')?.touched
                      ? 'is-invalid'
                      : null
                  "
                ></app-focile-input>
                <ng-container [ngTemplateOutlet]="errorTemplate" [ngTemplateOutletContext]="{
                  field: 'Zipcode',
                  control: endUserForm.get('zipCode')
                }"></ng-container>
              </div>
            </div>

            <div class="col-md-12">
              <label for="about" class="form-label">About Me</label>
              <textarea
                class="form-control"
                placeholder="What would you like people to know about you in a brief summary?"
                id="about"
                formControlName="aboutMe"
                maxlength="300"
                style="height: 100px"
              ></textarea>
              <ng-container
                *ngIf="endUserForm.get('aboutMe')?.errors?.maxlength"
              >
                <span class="text-danger">
                  About me should less then 300 words.
                </span>
              </ng-container>
            </div>

            <div class="col-md-6 mt-3">
              <div class="mb-3">
                <label for="role" class="form-label"
                  >Your unique Identification</label
                >
                <input
                  type="text"
                  disabled
                  class="form-control"
                  name="uniqidentification"
                  [value]="userState$.aspNetUserId"
                  id="uniqidentification"
                />
              </div>
            </div>
          </div>
        <div class="mt-3">
          <h5 class="fw-bold mb-3">Social Network</h5>
          <app-social-media-link-form></app-social-media-link-form>
        </div>

        <div class="d-flex mt-5 mb-4 justify-content-end">
          <focile-button
            [disabled]="endUserForm.invalid"
            (onClick)="updateEndUser()"
            [loading]="saving"
            class="custom-btn my-0 my-sm-4"
            >Update Setting</focile-button
          >
        </div>
      </div>
    </div>

    <!--EOF End User Details  -->
  </div>

  <ng-container *ngIf="loading">
    <app-spinner></app-spinner>
  </ng-container>

  <ng-template #errorTemplate let-field="field" let-control="control">
    <span *ngIf="control?.touched && control?.errors?.required" class="text-danger">
      {{ field }} is required.
    </span>
    <span *ngIf="control?.touched && control?.errors?.pattern" class="text-danger">
      Please enter a valid {{ field }}.
    </span>
  </ng-template>

  <ng-template #urlValidaiton let-control="control" let-name="name">
    <div
      *ngIf="
        userDetailForm.get(control)?.invalid &&
        (userDetailForm.get(control)?.dirty ||
          userDetailForm.get(control)?.touched)
      "
      class="text-danger"
    >
      <div *ngIf="userDetailForm.get(control)?.errors?.['pattern']">
        Please enter a valid {{ name }} URL.
      </div>
    </div>
  </ng-template>
  <ng-template #loadingTemplate>
    <app-spinner></app-spinner>
  </ng-template>
</div>