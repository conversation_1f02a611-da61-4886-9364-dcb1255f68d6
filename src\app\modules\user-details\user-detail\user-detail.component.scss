.fc-account-detail-content {
  box-sizing: border-box;
  width: 100%;
  background: #ffffff;
  border: 1px solid rgba(140, 140, 140, 0.2);
  border-radius: 18px;
  padding: 1.5rem;
  z-index: 1;
  position: relative;

  ::ng-deep .ng-select.ng-select-opened > .ng-select-container {
    box-sizing: border-box;
    margin: 0 auto;
    height: 56px;
    background: #f6f6f6;
    border: 1px solid rgba(246, 246, 245, 0.4);
    border-radius: 12px;
    flex: none;
    order: 1;
    align-self: stretch;
    flex-grow: 0;
  }

  ::ng-deep .form-control,
  .form-control:not([type="file"]) {
    box-sizing: border-box;
    margin: 0 auto;
    height: 56px;
    background: #f6f6f6;
    border: 1px solid rgba(246, 246, 245, 0.4);
    border-radius: 12px;
    flex: none;
    order: 1;
    align-self: stretch;
    flex-grow: 0;
    padding: 1rem;
  }

  .form-control {
    box-sizing: border-box;
    margin: 0 auto;
    height: 56px;
    background: #f6f6f6;
    border: 1px solid rgba(246, 246, 245, 0.4);
    border-radius: 12px;
    flex: none;
    order: 1;
    align-self: stretch;
    flex-grow: 0;
    padding: 1rem;

    &::placeholder {
      color: #666666;
    }
  }

  .form-label {
    font-weight: 400;
    font-size: 1rem;
    color: black;
  }

  ::ng-deep .ng-select .ng-select-container {
    box-sizing: border-box;
    margin: 0 auto;
    height: 56px !important;
    background: #f6f6f6;
    border: 1px solid rgba(246, 246, 245, 0.4);
    border-radius: 12px;
    flex: none;
    order: 1;
    align-self: stretch;

    input[type="text"] {
      padding-inline: 0px;
      background: transparent !important;
      border: none !important;
    }
  }

  ::ng-deep
    .ng-select.ng-select-multiple
    .ng-select-container
    .ng-value-container
    .ng-value {
    background-color: #e7e7e7;
    border-radius: 5px;
  }

  ::ng-deep .ng-dropdown-panel.ng-select-bottom {
    background: #ffffff;
    box-shadow: 0px 41px 89px rgba(0, 0, 0, 0.1), 0px 0px 0px rgba(0, 0, 0, 0.1);
    border-radius: 16px;
    border: none;
  }

  ::ng-deep .ng-select .ng-arrow-wrapper {
    width: 35px;
  }

  .round-btn {
    width: 26px;
    height: 26px;
    background: transparent;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #014681;
    font-size: 10px;
    font-weight: 600;
    border: 1px solid #014681;
  }

  .add-btn {
    border-color: black;
    color: black;
  }

  .remove-btn {
    border-color: #f44336;
    color: #f44336;
  }

  .edit-btn {
    color: #4caf50;
    border-color: #4caf50;
  }

  textarea.form-control,
  textarea.form-control:not([type="file"]) {
    height: auto;
    min-height: 200px;
    resize: none;
    padding: 1rem;
  }

  .your-unique-identification {
    background-color: #014681;
    height: 60px;
    display: flex;
    flex-direction: row;
    border-radius: 12px;

    label {
      border-right: 1px solid rgba(255, 255, 255, 0.2);
      margin-bottom: 0px;
      padding-inline: 1.5rem;
      display: flex;
      white-space: nowrap;
      align-items: center;
      color: white;
    }

    input {
      text-align: center;
      flex: 1;
      background-color: transparent;
      font-weight: 600;
      color: white;
      border: none;
    }
  }

  .elvator-pitch-card {
    background-color: #f6f6f6;
    padding: 1.5rem;
    gap: 1.5rem;
    border-radius: 12px;

    input {
      background-color: white;
      min-height: 56px;

      &::placeholder {
        color: rgba(102, 102, 102, 0.6);
      }
    }

    .edit-btn {
      color: #4caf50;
      border-color: #4caf50;
    }

    .right-btn {
      color: #ec971f;
      border-color: #ec971f;
    }

    h5 {
      font-size: 20px;
      font-weight: 600;
    }

    .alert-warning {
      background-color: #ffeeba;
      color: black;
      font-size: 1rem;

      strong {
        color: black;
      }
    }

    .btn-close {
      font-size: 12px;
      top: 2px;
    }
  }

  .w-150 {
    width: 150px;
  }

  ::ng-deep .ng-select .ng-select-container .ng-value-container {
    padding-left: 1rem;
  }

  ::ng-deep
    .ng-select.ng-select-multiple
    .ng-select-container
    .ng-value-container {
    padding-left: 1rem;
  }
  ::ng-deep
    .ng-select.ng-select-multiple
    .ng-select-container
    .ng-value-container
    .ng-placeholder {
    top: 15px;
  }

  ::ng-deep {
    .social-network-wrap {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 30px;

      input {
        font-size: 14px;
      }
    }
  }

  .custom-btn {
    ::ng-deep button {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      min-width: 175px;
      height: 50px;
      background: #014681;
      box-shadow: 0px 548px 219px rgba(0, 0, 0, 0.01),
        0px 308px 185px rgba(0, 0, 0, 0.04), 0px 137px 137px rgba(0, 0, 0, 0.06),
        0px 34px 75px rgba(0, 0, 0, 0.07), 0px 0px 0px rgba(0, 0, 0, 0.07);
      border-radius: 100px;
      border: none;
      text-align: center;
      color: white;
      font-size: 18px;
      font-weight: 500 !important;
    }
  }

  .add-btn {
    border-color: black;
    color: black;
  }
  .remove-btn {
    border-color: #f44336;
    color: #f44336;
  }

  .edit-btn {
    color: #4caf50;
    border-color: #4caf50;
  }
}

@media (max-width: 768px) {
  .fc-account-detail-content {
    padding: 1rem;
    ::ng-deep .social-network-wrap {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .your-unique-identification {
      flex-direction: column;
      height: auto;
      padding: 1rem;
      padding-bottom: 5px;

      label {
        padding-inline: 0px;
        margin-bottom: 0px;
        border-right: 0px;
      }
      input {
        padding: 0px;
      }
    }
    .elvator-pitch-card {
      padding: 1rem;
    }
  }
}
