import { Component, OnInit } from '@angular/core';
import { AccountService } from '../../account/services/account.service';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { catchError, finalize, forkJoin, map, Observable, of, tap } from 'rxjs';
import {
  ApprovePostStatusEnum,
  ApprovePostTypeEnum,
  companyType,
  Regex,
  socialMediaIds,
} from 'src/app/shared/constant';
import { ModalService } from 'src/app/shared/services/modal.service';
import { ConfirmationComponent } from 'src/app/shared/modals/confirmation/confirmation.component';
import { EndUserService } from 'src/app/shared/services/end-user.service';
import { filterNonNull } from 'src/app/shared/oprators/filter-null-values';

@Component({
  selector: 'app-user-detail',
  templateUrl: './user-detail.component.html',
  styleUrls: ['./user-detail.component.scss'],
})
export class UserDetailComponent implements OnInit {
  userState$: any = {};
  userDetailForm!: FormGroup;
  roleList: any = [];
  saving = false;
  endUserForm!: FormGroup;
  REGEX = Regex;
  countryList: any = [];
  states: any;
  cities: any;
  loading = false;
  organizationTypeList = [];
  showWorkMobileNumber: any = true;
  userElevatorPitches!: FormGroup;
  pitchesLoading = false;
  socialMediaIds = socialMediaIds;
  approvePostStatusEnum = ApprovePostStatusEnum;
  savingVideo = false;
  companyType = companyType;
  countriesLoading = false;
  statesLoading = false;
  citiesLoading = false;

  countries$!: Observable<any>;
  states$!: Observable<any>;
  cities$!: Observable<any>;

  organizationTypes$!: Observable<any>;
  enduserLoading = false;
  activeTab: string = 'companyDetails';
  settingsLoading = false;

  expertiseList = [];
  industryList = [];
  productList = [];
  servicesList = [];
  solutionList = [];
  companySystemList = [];
  socialMediaLinksObj!: FormGroup;

  // Getter to check if end user form is valid
  get isEndUserFormValid(): boolean {
    return this.endUserForm?.valid || false;
  }
  constructor(
    private account: AccountService,
    private formBuilder: FormBuilder,
    private toastrService: ToastrService,
    private modalService: ModalService,
    private enduser: EndUserService
  ) { }

  ngOnInit(): void {
    this.initEndUserForm();
    this.getUserSettings(); // Load dropdown data
    this.account.user$.pipe(filterNonNull()).subscribe((response) => {
      this.userState$ = response;
      if (response.userType == companyType.EndUser) {
        this.getEnduser();
      } else {
        this.initForm();
        this.getAccountDetails(response.userId);
        this.getPitches();
      }
    });
  }
  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }
  getEnduser() {
    this.enduserLoading = true;
    this.enduser
      .getEndUser()
      .pipe(finalize(() => (this.enduserLoading = false)))
      .subscribe((response: any) => {
        if (response.data) {
          console.log('End user data received:', response.data);
          console.log('zipCode in received data:', response.data.zipCode);
          console.log('zipcode in received data:', response.data.zipcode);
          console.log('address in received data:', response.data.address);
          this.initEndUserForm(response.data);
          this.getSocialMediaLinks();
        }
      });
  }

  getUserSettings() {
    this.settingsLoading = true;
    this.account.getUserSettings()
      .pipe(
        catchError((error) => {
          console.error('Error fetching user settings:', error);
          return of({ data: null });
        }),
        finalize(() => (this.settingsLoading = false))
      )
      .subscribe((response: any) => {
        if (response.data) {
          this.expertiseList = response.data.expertiseList || [];
          this.industryList = response.data.industryList || [];
          this.productList = response.data.productList || [];
          this.servicesList = response.data.servicesList || [];
          this.solutionList = response.data.solutionList || [];
          this.companySystemList = response.data.companySystemList || [];
        }
      });
  }

  initEndUserForm(user: any = {}) {
    // Convert comma-separated strings to arrays for dropdown fields
    const convertToArray = (value: any) => {
      if (typeof value === 'string' && value.trim()) {
        return value.split(',').map((item: string) => item.trim()).filter((item: string) => item);
      }
      if (Array.isArray(value)) {
        return value.filter((item: any) => item !== null && item !== undefined && item !== '');
      }
      return [];
    };

    this.endUserForm = this.formBuilder.group({
      id: [user.id],
      firstName: [user.firstName, Validators.required],
      lastName: [user.lastName, Validators.required],
      phoneNumber: [user.phoneNumber],
      email: [{ value: user.email, disabled: true }],
      mobileCountryCode: [user.mobileCountryCode],
      country: [user.country, Validators.required],
      state: [user.state, Validators.required],
      city: [user.city, Validators.required],
      zipCode: [user.zipCode || user.zipcode, Validators.required],
      address: [user.address, Validators.required],
      organizationName: [
        { value: user.organizationName, disabled: true },
        Validators.required,
      ],
      aboutMe: [user.aboutMe],
      organizationTypeId: [user.organizationTypeId, Validators.required],
      expertiseIds: [convertToArray(user.expertiseIds), Validators.required],
      industryIds: [convertToArray(user.industryIds), Validators.required],
      productIds: [convertToArray(user.productIds), Validators.required],
      serviceIds: [convertToArray(user.serviceIds), Validators.required],
      solutionIds: [convertToArray(user.solutionIds), Validators.required],
      companySystemIds: [convertToArray(user.companySystemIds), Validators.required],
      // Removed fields that are not applicable to end users:
      // roleId, typeOfInstallationIds, companySize, technologyIds
      sociallinks: this.getSocialLinksForm(),
    });

    console.log('Form initialized with zipCode:', this.endUserForm.get('zipCode')?.value);

    this.organizationTypes$ = this.account
      .getOrganizationType()
      .pipe(map((response: any) => response.data));

    this.getCountries();
    if (user.state) {
      this.getStates(user.country);
    }
    if (user.city) {
      this.getCtiies(user.state);
    }
  }

  getCountries() {
    this.countriesLoading = true;
    if (this.endUserForm.value.country) {
      this.handleChange(this.endUserForm.value.country);
    }
    if (this.endUserForm.value.state) {
      this.handleStateChange(this.endUserForm.value.state);
    }
    this.countries$ = this.account.getCountries().pipe(
      map((response: any) => response.data),
      catchError((error) => {
        return of([]);
      }),
      finalize(() => {
        this.countriesLoading = false;
      })
    );
  }

  getStates(countryId: number) {
    this.statesLoading = true;
    this.states$ = this.account.getStates(countryId).pipe(
      map((response: any) => response.data),
      catchError((error) => {
        return of([]);
      }),
      finalize(() => {
        this.statesLoading = false;
      })
    );
  }

  getCtiies(stateId: number) {
    this.citiesLoading = true;
    this.cities$ = this.account.getCities(stateId).pipe(
      map((response: any) => response.data),
      catchError((error) => {
        return of([]);
      }),
      finalize(() => {
        this.citiesLoading = false;
      })
    );
  }

  initForm() {
    this.userDetailForm = this.formBuilder.group({
      firstName: [
        null,
        [Validators.required, Validators.pattern(this.REGEX.personName)],
      ],
      lastName: [
        null,
        [Validators.required, Validators.pattern(this.REGEX.personName)],
      ],
      workMobileNumber: [
        null,
        [Validators.required, Validators.pattern(/^\d{6,13}$/)],
      ],
      phoneNumber: [null],
      email: [null, Validators.required],
      roleId: [null],
      countryCode: ['+1'],
      zipCode: [null],
      address: null,
      organizationName: null,
      organizationType: null,
      country: null,
      state: null,
      city: null,
      aboutMe: [null, Validators.maxLength(300)],
      workMobileNumberCountryCode: null,
      isShowWorkMobileNumber: null,
      userElevatorPitch: this.formBuilder.array([]),
      sociallinks: this.getSocialLinksForm(),
      strengthAndAbility: null,
      // messageToAudience: null,
      // expertiseIds: null,
      // companySystemIds: null,
      // solutionIds: null,
      // productIds: null,
      // serviceIds: null,
      // industryIds: null,
      // typeOfInstallationIds: null,
      // companySize: null,
      // technologyIds: null
    });
    if (this.userState$?.userType == companyType.EndUser) {
      this.userDetailForm.get('zipCode')?.setValidators([Validators.required]);
      this.userDetailForm.get('address')?.setValidators([Validators.required]);
      this.userDetailForm.get('organizationName')?.disable();
      this.userDetailForm
        .get('organizationType')
        ?.setValidators([Validators.required]);
      this.userDetailForm.get('country')?.setValidators([Validators.required]);
      this.userDetailForm.get('state')?.setValidators([Validators.required]);
      this.userDetailForm.get('city')?.setValidators([Validators.required]);
    } else {
      this.userDetailForm.get('roleId')?.setValidators([Validators.required]);
    }
  }

  getSocialLinksForm() {
    return this.formBuilder.group({
      [socialMediaIds.Youtube]: [
        null,
        Validators.pattern(Regex.socialMediaUrlRegex),
      ],
      [socialMediaIds.Linkedin]: [
        null,
        Validators.pattern(Regex.socialMediaUrlRegex),
      ],
      [socialMediaIds.Facebook]: [
        null,
        Validators.pattern(Regex.socialMediaUrlRegex),
      ],
      [socialMediaIds.Twitter]: [
        null,
        Validators.pattern(Regex.socialMediaUrlRegex),
      ],
      [socialMediaIds.Instagram]: [
        null,
        Validators.pattern(Regex.socialMediaUrlRegex),
      ],
      [socialMediaIds.Pintrest]: [
        null,
        Validators.pattern(Regex.socialMediaUrlRegex),
      ],
    });
  }

  get userElevatorPitch(): FormArray {
    return this.userDetailForm.get('userElevatorPitch') as FormArray;
  }

  userElevatorPitchFormGroup(response: any = {}) {
    return this.formBuilder.group({
      url: [
        {
          value: response?.url,
          disabled: response?.status == this.approvePostStatusEnum.Approved,
        },
        Validators.pattern(Regex.youtubeLink),
      ],
      id: response?.id,
      isRemoved: false,
      isApproved: response?.status,
      userId: response?.userId,
      approvePostType:
        response?.approvePostType || ApprovePostTypeEnum.ElevatorPitchVideo,
    });
  }

  addPitch(user: any = null) {
    this.userElevatorPitch.push(this.userElevatorPitchFormGroup(user));
  }

  removePitch(index: number) {
    this.userElevatorPitch.removeAt(index);
  }

  getSocialMediaLinks() {
    let form: FormGroup = this.endUserForm;
    if (this.userState$.userType != companyType.EndUser) {
      form = this.userDetailForm;
    }
    const socialLinksMap: any = {};
    Object.values(socialMediaIds).forEach((x) => {
      socialLinksMap[x.toLowerCase()] = x;
    });
    this.account
      .getSocialMedia(this.userState$.userId)
      .subscribe((response: any) => {
        if (response.data?.length) {
          response.data.forEach((link: any) => {
            (form.get('sociallinks') as FormGroup)
              .get(socialLinksMap[`${link.socialConnectionTypeId}`])
              ?.setValue(link.url);
            (form.get('sociallinks') as FormGroup).addControl(
              socialLinksMap[`${link.socialConnectionTypeId}`] + '_id',
              new FormControl(link.id)
            );
          });
        }
      });
  }

  enableElevatorPitch(index: number) {
    this.userElevatorPitch.controls[index].enable();
  }

  disableElevatorPitch(index: number) {
    this.userElevatorPitch.controls[index].disable();
  }

  saveElevatorPitch(index: number) {
    this.userElevatorPitch.controls[index].patchValue({
      userId: this.userState$.userId,
    });
    this.removeElevatorPitch(index);
  }

  confirmationElevatorPitchRemove(index: number) {
    this.modalService.openModal('cropper', {
      initialState: {
        message: `Are you sure you want to remove this elevator pitch?`,
        onConfirm: () => {
          this.userElevatorPitch.controls[index].patchValue({
            isRemoved: true,
          });
          this.removeElevatorPitch(index);
        },
      },
    });
  }

  removeElevatorPitch(index: number) {
    const payload = this.userElevatorPitch.controls[index].getRawValue();
    const formData = new FormData();
    Object.keys(payload).forEach((key) => {
      if (payload[key]?.toString()) {
        formData.append(key, payload[key].toString());
      }
    });
    this.savingVideo = true;
    this.account
      .addUserElevatorPitch(formData)
      .pipe(finalize(() => (this.savingVideo = false)))
      .subscribe((response: any) => {
        if (response?.error) {
          this.toastrService.error(
            'We are unable to save your video',
            'Something went wrong'
          );
        } else {
          if (response.data.statusMessage) {
            if (payload.isRemoved || !payload.url) {
              this.removePitch(index);
              if (!this.userElevatorPitch?.controls?.length) {
                this.addPitch();
              }
            } else {
              this.userElevatorPitch.controls[index].patchValue({
                ...response.data,
              });
            }
            this.toastrService.success(response.data.statusMessage);
            this.modalService.closeModal();
          }
        }
      });
  }

  getAccountDetails(userId: string) {
    this.loading = true;
    this.account
      .getAccountDetails(userId)
      .pipe(
        tap((response: any) => {
          if (response[0]?.data?.country) {
            this.handleChange(response[0].data.country);
          }
          if (response[0]?.data?.state) {
            this.handleStateChange(response[0].data.state);
          }
        }),
        finalize(() => {
          this.loading = false;
          this.getSocialMediaLinks();
        })
      )
      .subscribe((response) => {
        if (response.messageType) {
          return this.toastrService.error(response.message);
        }
        this.roleList = response.data.roleList;
        this.countryList = response.data.countryList;
        this.organizationTypeList = response.data.organizationTypeList;
        try {
          if (this.userState$?.userType === 1) {
            this.userDetailForm.patchValue({
              about: response.data.about,
              firstName: response.data.firstName,
              lastName: response.data.lastName,
              email: response.data.email,
              workMobileNumber: response.data.phoneNumber,
              roleId: response.data.expertDetail.roleId,
              countryCode: response.data.countryCode,
              address: response.data.address,
              organizationName: response.data.organizationName,
              organizationType: response.data.organizationTypeId,
              phoneNumber: response.data.phoneNumber,
              state: response.data.state,
              city: response.data.city,
              zipCode: response.data.zipCode,
              country: response.data.country,
              workMobileNumberCountryCode:
                response.data.expertDetail.workMobileNumberCountryCode,
              isShowWorkMobileNumber:
                response.data.expertDetail.isShowWorkMobileNumber,
              aboutMe: response.data.aboutMe,
              strengthAndAbility: response.data.strengthAndAbility,
              messageToAudience: response.data.messageToAudience,
              expertiseIds: response.data.expertiseIds,
              industryIds: response.data.industryIds,
              productList: response.data.productList,
              roleList: response.data.roleList,
              serviceIds: response.data.serviceIds,
              solutionList: response.data.solutionList
            });
          } else {
            this.userDetailForm.patchValue({
              firstName: response.data.firstName,
              lastName: response.data.lastName,
              email: response.data.email,
              workMobileNumber: response.data.expertDetail.workMobileNumber,
              roleId: response.data.expertDetail.roleId,
              countryCode: response.data.countryCode,
              address: response.data.address,
              organizationName: response.data.organizationName,
              organizationType: response.data.organizationTypeId,
              phoneNumber: response.data.phoneNumber,
              state: response.data.state,
              city: response.data.city,
              zipCode: response.data.zipCode,
              country: response.data.country,
              workMobileNumberCountryCode:
                response.data.expertDetail.workMobileNumberCountryCode,
              isShowWorkMobileNumber:
                response.data.expertDetail.isShowWorkMobileNumber,
              aboutMe: response.data.aboutMe,
              strengthAndAbility: response.data.strengthAndAbility,
              messageToAudience: response.data.messageToAudience,
            });
          }
        } catch (error) { }

        this.userDetailForm.get('email')?.disable();
        return;
      });
  }

  getPitches() {
    this.pitchesLoading = true;
    this.account
      .getUserElevatorVideos(this.userState$.userId)
      .subscribe((response: any) => {
        if (response?.data?.length) {
          this.userElevatorPitch.clear();
          response?.data.forEach((response: any, index: any) => {
            this.addPitch(response);
          });
        } else {
          this.addPitch();
        }
        this.pitchesLoading = false;
      });
  }

  handleChange(changedType?: string) {
    this.account.getStates(changedType).subscribe((response: any) => {
      this.states = response.data;
      this.statesLoading = false;
    });
  }

  handleStateChange($event: any) {
    this.citiesLoading = true;
    this.account
      .getCities($event)
      .pipe(finalize(() => (this.citiesLoading = false)))
      .subscribe((response: any) => {
        this.cities = response.data;
      });
  }

  updateEndUser() {
    // Debug form validation state
    if (this.endUserForm.invalid) {
      console.log('Form is invalid. Errors:', this.getFormValidationErrors());
      this.markAllFieldsAsTouched();
      return;
    }

    this.endUserForm.enable();
    let endUser = this.endUserForm.value;

    // Convert array values to comma-separated strings for API compatibility
    const arrayFields = ['expertiseIds', 'industryIds', 'productIds', 'serviceIds', 'solutionIds', 'companySystemIds'];
    arrayFields.forEach(field => {
      if (Array.isArray(endUser[field]) && endUser[field].length > 0) {
        endUser[field] = endUser[field].join(',');
      } else {
        // Set empty string for empty arrays or null/undefined values
        endUser[field] = '';
      }
    });

    // Handle potential field name mismatch - API might expect 'zipcode' instead of 'zipCode'
    if (endUser.zipCode) {
      endUser.zipcode = endUser.zipCode;
    }

    console.log('End user data being sent to API:', endUser);
    console.log('zipCode being sent:', endUser.zipCode);
    console.log('zipcode being sent:', endUser.zipcode);

    this.socialMediaLinksObj = this.endUserForm.get('sociallinks') as FormGroup;
    this.saveSocialMediaLinks();
    delete endUser.sociallinks;
    this.saving = true;
    this.account
      .updateEndUser(endUser)
      .pipe(
        finalize(() => {
          this.saving = false;
          this.endUserForm.get('email')?.disable();
          this.endUserForm.get('organizationName')?.disable();
        })
      )
      .subscribe((response: any) => {
        if (response.messageType) {
          this.toastrService.error(response.message);
          return;
        }
        this.toastrService.success('Details updated');
        // Refresh the form data to see what was actually saved
        setTimeout(() => {
          this.getEnduser();
        }, 1000);
      });
  }

  // Helper method to debug form validation
  getFormValidationErrors() {
    const formErrors: any = {};
    Object.keys(this.endUserForm.controls).forEach(key => {
      const controlErrors = this.endUserForm.get(key)?.errors;
      if (controlErrors) {
        formErrors[key] = controlErrors;
      }
    });
    return formErrors;
  }

  // Helper method to mark all fields as touched to show validation errors
  markAllFieldsAsTouched() {
    Object.keys(this.endUserForm.controls).forEach(key => {
      this.endUserForm.get(key)?.markAsTouched();
    });
  }

  updateAccountDetail() {
    this.userDetailForm.enable();
    let user = this.userDetailForm.value;
    this.socialMediaLinksObj = this.userDetailForm.get(
      'sociallinks'
    ) as FormGroup;
    user.aspNetId = this.userState$.aspNetUserId;
    user.id = this.userState$.userId;
    user.sideMenuId = 2;
    user.userType = this.userState$?.userType;
    this.saving = !this.saving;
    this.saveSocialMediaLinks();
    this.account
      .updateAccountDetail(user)
      .pipe(
        finalize(() => {
          this.saving = !this.saving;
          this.userDetailForm.get('email')?.disable();
          this.userDetailForm.get('organizationName')?.disable();
        })
      )
      .subscribe((response: any) => {
        if (response.messageType) {
          return this.toastrService.error(response.message);
        }
        return this.toastrService.success('Details updated');
      });
  }

  handleCountryChange($event: any) {
    this.userDetailForm && this.userDetailForm.get('state')?.patchValue(null);
    this.userDetailForm && this.userDetailForm.get('city')?.patchValue(null);
    this.endUserForm && this.endUserForm.get('state')?.patchValue(null);
    this.endUserForm && this.endUserForm.get('city')?.patchValue(null);
    this.statesLoading = true;
    this.account
      .getStates($event)
      .pipe(finalize(() => (this.statesLoading = false)))
      .subscribe((response: any) => {
        this.states = response.data;
      });
  }

  saveSocialMediaLinks() {
    const payloads: any = [];
    const linkControls: any = {};
    Object.keys(this.socialMediaLinksObj.controls).forEach((key) => {
      linkControls[key] = {
        value: this.socialMediaLinksObj.get(key)?.value,
        key,
      };
    });
    Object.keys(linkControls).forEach((key) => {
      if (!key.endsWith('_id')) {
        const payload: any = {
          url: linkControls[key].value || '',
          userId: this.userState$.userId,
          SocialConnectionTypeId: Object.values(linkControls[key])[1],
          id: linkControls[`${key}_id`]?.value || '',
        };
        const formData = new FormData();
        Object.keys(payload).forEach((key) => {
          formData.append(key, payload[key]);
        });
        payloads.push(this.account.saveSocialMedia(formData));
      }
    });

    forkJoin(payloads).subscribe(() => {
      this.getSocialMediaLinks();
    });
  }
}
