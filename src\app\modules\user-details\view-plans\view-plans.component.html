<div id="content"></div>
<div class="row p-3 py-0 py-sm-3">
  <div class="tab-content p-0" id="nav-tabContent">
    <h5 class="fw-bold my-3">Your Plans & History</h5>
  </div>
</div>

<div class="card border-0 shadow-sm mb-5">
  <div class="card-body">
    <div class="table-responsive">
      <table class="table table-centered table-nowrap mb-0 rounded">
        <thead class="thead-light">
          <tr>
            <th class="border-0 rounded-start">#</th>
            <th class="border-0">Plan Name</th>
            <th class="border-0">Description</th>
            <th class="border-0">Connection Limit</th>
            <th class="border-0">Start Date - End Date</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let plan of userPlans$ | async" [ngClass]="{ 'text-decoration-line-through text-muted opacity-50': plan.isExpired === 1 }">
            <td>
              {{ plan.subscriptionPlanId }}
            </td>
            <td>
              {{ plan.name }}
            </td>
            <td>
              {{ plan.description }}
            </td>
            <td>
              {{ plan.connectionLimit }}
            </td>
            <td>
              {{ plan.startDate | date : "medium" }} -
              <span   [ngClass]="{ 'text-danger': plan.isExpired !== 1 }">
                {{ plan.endDate | date : "medium" }}
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
