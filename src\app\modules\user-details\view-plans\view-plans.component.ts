import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { SubscriptionPlanService } from 'src/app/shared/services/subscription-plan.service';
import { AccountService } from '../../account/services/account.service';
import { map } from 'rxjs';

@Component({
  selector: 'app-view-plans',
  templateUrl: './view-plans.component.html',
  styleUrls: ['./view-plans.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ViewPlansComponent implements OnInit {
  userPlans$: any;
  constructor(
    private subscription: SubscriptionPlanService,
    private account: AccountService
  ) {}

  ngOnInit(): void {
    this.account.user$.subscribe((response) => {
      this.userPlans$ = this.subscription
        .getTransations(response.userId)
        .pipe(map((x: any) => x.data));
    });
  }
}
