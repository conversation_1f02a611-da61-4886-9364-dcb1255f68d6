<div class="fc-container">
    <div class="breadcumb my-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a routerLink="/">Home</a></li>
                <li class="breadcrumb-item"><a routerLink="/about-us">About us</a></li>
                <li class="breadcrumb-item active" aria-current="page">Our Culture</li>
            </ol>
        </nav>
    </div>
</div>
<div class="fc-full-container">
    <div class="fc-container">
        <div class="fc-hdr-content">
            <label class="text-transfer-upparcase">About Us</label>
            <h5 class="fc-brand-txt">Our Culture</h5>
        </div>

        <div class="fc-vision-section-box">
            <div class="text-card">
                <label>New Era</label>
                <p>Everyday we notice that our life is getting busier and busier, we realized that in our everyday
                    life we tend to focus and spent truthfully much time connecting with our work colleagues
                    professionally more and more. For our connections to last, we tend to follow certain Policy Picture
                    Standards, so it acts as a guideline for us to respect others and keep our momentum going when
                    building professional relationships.</p>
            </div>

            <div class="text-card">
                <label>Inclusion</label>
                <p>You always come first and your voice matters. You always be the center of focus for our organization.
                    Our team is dedicated to helping you and going the extra mile to facilitate a smooth experience.
                    We demonstrate respect and dignity; we learn from you, aim to evolve, and target to achieve good
                    results with you. If you need help, ask for support, and will be there for you. Creating a profile Picture
                    with Focile is very simple and the information you provide on the platform, will help you get to the
                    right connection, get you noticeable fast, and help our partners within our community to support your needs.</p>
            </div>

            <div class="text-card">
                <label>Diversity</label>
                <p>Diversity is a key, therefore, it is within our culture to re-create the way we work between our
                    diverse members and bring that fun part to the professional connection, so to strengthen that bonding,
                    create a trusted means, and provide a good synergy which will be the foundation for the ongoing relationship. Picture</p>
                    <p>and voice to share and be involved. We are all ears to our member's community and strive to implement ideas
                        that can increase the satisfaction to other members and make others happy.</p>
            </div>

            <div class="text-card">
                <label>Open-Minded</label>
                <p>Flexibility and open to feedback is part of our culture, our current improvement is due to lots of feedback
                    we have received from our channel partners and end user early adopters that have participated in various
                    communications with our team for suggestions and remarques to help with constructive criticism, fixes and Picture
                    suggestions to bring the portal to what it is now. We are always open to your suggestions and comments,
                    as this platform is your portal that helps get your business noticeable to bring the right and wise connection
                    to be added to your favorite bucket for future needs</p>
            </div>
            <div class="text-card">
                <label>Social Interaction</label>
                <p>We believe, it needs to be enjoyable, create fun moments within our diverse community, to keep the interest  
                    going in your relationships and create a sense of bonding. Everyday search for a connection can be overwhelming,   Picture
                    therefore, we try to make it simple for you to search, connect, chat about your needs, refer or share the expert
                    contact with your favorite connection that needs help.  </p>
            </div>
        </div>
    </div>
</div>

<app-join-help-card
*ngIf="!(isLoggedIn$ | async)"
[title]="'Why Join Us?'"
[description]="'Connect with professionals and expand your business network.'"
[buttonText]="'Sign Up Now'"
[buttonLink]="'/account/choose-option'"
[imageUrl]="'../../../../../assets/images/join-now.jpg'">
</app-join-help-card>