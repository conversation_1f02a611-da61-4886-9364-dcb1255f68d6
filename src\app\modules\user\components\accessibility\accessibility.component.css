h4  {
  color: var(--bs-primary);
}

.card {
  height: 100%;
}


.fc-content-card{
  align-items: center;


  img{
    max-width: 90%;
  }

  label{
    color: black;
  }

  h5{
    margin-bottom: 1rem;
  }

  p{
     line-height: 30px;
     color: black;
  }

  .read-more-btn{    
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 21px 46px;  
  width:max-content;
  height: 52px;
  background: #000000;
  border-radius: 200px;
  color: white;
  text-decoration: none;
  }
}

.fc-content-row{
  margin-top: 80px;
  background: #FFFFFF;
  border: 1px solid rgba(140, 140, 140, 0.1);
  box-shadow: 0px -1px 10px rgba(0, 0, 0, 0.05);
  border-radius: 20px;
  padding: 3rem;
  margin-bottom: -200px;
  z-index: 1;
  position: relative;
}

.fc-full-container{
background: #12141D;
position: relative;
min-height: auto;
overflow: hidden;
padding-block:100px;
display: flex;
align-items: end;

> div{
  z-index: 1;
  position: relative;
}
&::after{
  position: absolute;
  width: 447px;
  height: 443px;
  right:0px;
  top: 3px;
  background: #014681;
  filter: blur(262px);
  content: '';
}

&::before{
  position: absolute;
  width: 464px;
  height: 468px;
  left: 0px;
  top: 742px;
  background: #014681;
  filter: blur(262px);
  content: '';
}

p{
  color: white;
  line-height: 30px;
  font-size: 16px;
}

}

.fc-vision-section-box{
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 47px;
  margin-top: 48px;

  .text-card{     
    position:relative;
    width: 100%;  
    height: auto;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.125) 68.53%);
    backdrop-filter: blur(16px);
    border-radius: 16px;   
    padding:2rem;
    color: white;

    label{
      font-size: 24px;
      font-weight: 600;
    }

    p{
      font-size: 16px;
      line-height: 30px;
      font-weight: 300;
      margin-top: 1rem;

      b{
        color: #FFA500;
        font-weight: 600;
      }
    }
  }
}

.invite-favorites-wrap {
  width: 100%;
  background: url(../../../../../assets/images/object-light.png) no-repeat 100% 100%;
  padding-block: 131px;
}

.invite-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 128px 64px 64px;
  gap: 47px;
  width: 1110px;
  min-height: 597px;
  background: #F2F2F2;
  border-radius: 15px;
  margin: 0px auto;
  position: relative;

  .dot-matrik {
    position: absolute;
    width: 190px;
    height: 177px;
    top: -35px;
    left: -43px;
    z-index: -1;
    background: url(../../../../../assets/images/light-dot-pattern.svg) no-repeat center center;
  }
}

.fc-hdr-content {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  text-align: center;

  color: white;

  .text-transfer-upparcase {
    text-transform: uppercase;
    font-size: 25px;
  }

  .fc-brand-txt {
    font-size: 40px;
    font-weight: 600;
    margin-bottom: 0;
    line-height: 1.5;
  }

  p {
    color: #FFFFFF;
    line-height: 30px;
    text-align: center;
    font-weight: 300;
    font-size: 18px;
    margin-top: 1rem;
  }
}

.fc-join-help-card{
  label{
    font-size: 18px; 
  }

  p{
    font-size: 32px;
    line-height: 1.5;
    font-weight: 600;
    color: #000000;
  }
}

.join-now-btn{
  background-color:#000000;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 21px 46px;
  gap: 10px;
  width: max-content;
  height: 52px;
  border-radius: 200px;
  text-align: center;
  color: white;
  text-decoration: none;
  font-size: 18px;
  margin-top: 2rem;
}

@media(max-width:768px){
  .fc-content-card{
    text-align: center;

    img{
      max-width: 100%;
    }
    h5{
      font-size: 1.25rem !important;
      margin-bottom: 0.5rem;
    }
    p{
      line-height:2;
    }
    .fc-order-2{
      order: 1;
    }
    .fc-order-1{
      order: 2;
    }    
  }
  .fc-content-box{
    text-align: left;
    margin-top: 1.5rem;
  }

  .fc-full-container{
    padding-block: 2.5rem;
    padding-bottom: 1rem;
  }
  .fc-hdr-content{
     .fc-brand-txt{
      font-size: 1.25rem;
     }
     
  }
  .fc-full-container p{
    font-size: 14px;
    line-height: 2;
  }

  .fc-vision-section-box{
    grid-template-columns: repeat(1, 1fr);
    gap: 20px;
    margin-top:40px;
   

    p{
      font-size: 14px;
      margin-top: 1rem;
      margin-bottom: 0px;
    }

    .text-card{
      padding: 1rem;

      label{
        font-size: 16px;
      }
      p{
        font-size: 14px;
      }
    }
  }
  .fc-vision-section-box{
    margin-top:0px
  }
  .fc-paragraph-card{
    h4{
      font-size: 1.25rem;
    }
  }
}