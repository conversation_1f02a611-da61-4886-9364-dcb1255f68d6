.card {
    height: 100%;
  }
  
  .fs-32{
    font-size: 32px;
  }
  .fs-18{
    font-size:18px;
  }
  
  p{
    line-height: 30px;
  }
  
  .fc-full-container{
    background: #12141D;
    position: relative;
    min-height: auto;
    overflow: hidden;
    padding-block:100px;
    display: flex;
    align-items: end;
    
    > div{
      z-index: 1;
      position: relative;
    }
    &::after{
      position: absolute;
      width: 447px;
      height: 443px;
      right:0px;
      top: 3px;
      background: #014681;
      filter: blur(262px);
      content: '';
    }
    
    &::before{
      position: absolute;
      width: 464px;
      height: 468px;
      left: 0px;
      top: 742px;
      background: #014681;
      filter: blur(262px);
      content: '';
    }
    
    }
    
    .fc-vision-section-box{
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      gap: 47px;
      margin-top: 88px;
  
      ol{
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 3rem;
  
  
        li{
          color: white;
          line-height:30px;
          background: linear-gradient(180deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.125) 68.53%);
          backdrop-filter: blur(16px);
          border-radius: 16px;   
          padding:2rem;
          color: white;
        }
      }
    
      .text-card{     
        position:relative;
        width: 100%;  
        height: auto;
        background: linear-gradient(180deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.125) 68.53%);
        backdrop-filter: blur(16px);
        border-radius: 16px;   
        padding:2rem;
        color: white;
    
        label{
          font-size: 24px;
          font-weight: 600;
        }
    
        p{
          font-size: 16px;
          line-height: 30px;
          font-weight: 300;
          margin-top: 1rem;
    
          b{
            color: #FFA500;
            font-weight: 600;
          }
        }
      }
    }
  
    .fc-hdr-content {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      text-align: center;
    
      color: white;
    
      .text-transfer-upparcase {
        text-transform: uppercase;
        font-size: 25px;
      }
    
      .fc-brand-txt {
        font-size: 40px;
        font-weight: 600;
        margin-bottom: 0;
        line-height: 1.5;
      }
    
      p {
        color: #FFFFFF;
        line-height: 30px;
        text-align: center;
        font-weight: 300;
        font-size: 18px;
        margin-top: 1rem;
      }
    }
  
    .fc-hdr-txt{
      font-size:1.5rem;
      text-align: left;    
      margin-bottom: 1rem;
    }
  
    .fc-vision-card{
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 47px;
    
      .text-card{     
        position:relative;
        width: 100%;  
        height: auto;
        background: linear-gradient(180deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.125) 68.53%);
        backdrop-filter: blur(16px);
        border-radius: 16px;   
        padding:2rem;
        color: white;
    
        label{
          font-size: 24px;
          font-weight: 600;
        }
    
        p{
          font-size: 16px;
          line-height: 30px;
          font-weight: 300;
          margin-top: 1rem;
    
          b{
            color: #FFA500;
            font-weight: 600;
          }
        }
      }
    }
  
    .copyiright-claim-card{
      .fc-hdr-content{
        margin-bottom: 3rem;
      }
      p{
        font-size: 16px;
        line-height: 30px;
        font-weight: 300;
        margin-top: 1rem;
        color: white;
      }
      ol{
        list-style: none;
        display: flex;
        flex-direction: column;
        gap: 1rem;
        padding-left: 0px;
        margin-bottom: 0px;
      }
      li{
        display: flex;
        flex: 1;
        gap: 10px;
        color: white;
        padding-left: 2rem;
        
        &:before{
          background: url(../../../../../assets/images/ok-icon-orange.svg) no-repeat left center;
          width: 20px;
          height: 20px;
          content:'';
          position: absolute;    
          left: 13px;
        }
      }
    }

    .sign-up-btn{
        min-width: 150px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
        text-decoration: none;
        padding: 1rem;
        color: white;    
        background-color: #014681;
        border-radius:35px;    
        max-width: max-content;
        border: none;
        margin:2rem auto 0;
    }

   
    .category-navbar-item {
      display: flex;
      flex-direction: row;
      border-bottom: 1px solid #D9DBE9;
      align-items: center;
      justify-content: center;
      list-style: none;
      padding-left: 0px;
    
      li {
        width: 25%;
        display: flex;
        align-items: start;
        justify-content: center;
        min-height: 72px;          
    
        a {
          text-decoration: none;
          font-size: 18px;
          font-weight: 500;
          display: flex;
          flex-direction: row;
          gap: 1rem;
          align-items: center;
          color: #A0A3BD;
          min-height: 72px;
        }
    
        span {
          min-width: 46.37px;
          min-height: 48px;
          left: 0px;
          top: 0px;
          background: rgba(1, 69, 129, 0.1);
          border-radius: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
    
        &.active {
          border-color: #014681;
    
          a {
            color: #014681;
          }
        }
      }
    }
    .category-navbar-item a {
      cursor: pointer;
      text-decoration: none;
      color: inherit;
      border-bottom: 2px solid transparent;
    }
    
    .category-navbar-item a.active {
      font-weight: bold;
      border-bottom: 2px solid #014681; /* Example active tab style */
    }

    .fc-paragraph-card{
      padding-top:32px;
      ol{
        list-style: none;
        display: flex;
        flex-direction: column;
        gap: 1rem;
        padding-left: 0px;
        margin-bottom: 0px;
      }
      li{
        display: flex;
        flex: 1;
        gap: 10px;
        position: relative;
        padding-left: 2rem;
        flex-direction: column;
        
        &:before{
          background: url(../../../../../assets/images/ok-icon.svg) no-repeat left center;
          width: 20px;
          height: 20px;
          content:'';
          position: absolute;    
          left: 0px;
        }
      }
  
      p:last-child{
        margin-bottom: 0px;
      }
    }