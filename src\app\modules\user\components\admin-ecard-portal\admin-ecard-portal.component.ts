import { Component } from '@angular/core';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-admin-ecard-portal',
  templateUrl: './admin-ecard-portal.component.html',
  styleUrls: ['./admin-ecard-portal.component.scss']
})
export class AdminEcardPortalComponent {
  isLoggedIn$: Observable<boolean>;

  constructor(private accountService: AccountService) {
    this.isLoggedIn$ = this.accountService.isLoggedIn$;
  }
}
