<div class="fc-container">
  <div class="breadcumb my-4">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a routerLink="/">Home</a></li>
        <li class="breadcrumb-item active" aria-current="page">API Terms</li>
      </ol>
    </nav>
  </div>
</div>
<section class="fc-container mb-5 mt-3" appFootercardtitle>
  <div class="fs-32 mb-3 fw-bold text-left text-black">
    API Terms of Use
  </div>
  <p>
    These API Terms of Use (the “API Terms”), dated as of June 1, 2024 (the “Effective Date”), are made and entered into
    by and between Focile Inc., a Texas corporation and its affiliates (“Focile” or “we”), and you (“Developer” or
    “you”). Focile and Developer are sometimes referred to in the API Terms individually as a “Party” and collectively
    as the “Parties”. Focile and Developer hereby agree as follows:
  </p>


  <div class="fc-paragraph-card">
    <h4 class="fc-hdr-txt">
      Agreement to the api terms
    </h4>
    <p>By registering a software application, website, or product you create or service that you offer (a “Developer
      Application”), you agree to be bound by the API Terms. You must read, agree to, and accept all of the terms and
      conditions contained in the API Terms in order to use the Focile API. Additionally, you agree to and accept the
      Focile Terms of Service, available at www.focile.com/Terms -of-Use. If you disagree with any of the terms of the
      API Terms or the Focile Terms of Service, Focile does not grant you a license to use the Focile API. If you are
      using the Focile API on behalf of a company or any other entity, you represent and warrant that you have full
      legal authority to register a Developer Application on behalf of that entity and bind it to the API Terms and the
      Focile Terms of Service. If you are not authorized, you may not accept the API Terms or register a Developer
      Application for someone else.
    </p>
  </div>

  <div class="fc-paragraph-card">
    <h4 class="fc-hdr-txt">
      Definitions
    </h4>
    <ol>
      <li>Capitalized terms not defined in the API Terms have the meanings given to them in the Focile Terms of Service.
      </li>
      <li>Focile API. A set of web-based services providing programmatic access to Focile systems and data, together
        with
        all updates, revisions, and copies thereof. This also includes any associated tools and documentation that
        Focile
        makes available under the API Terms.</li>
      <li>Focile Content. Any data, content, or executables of or associated with the Focile API or Site Services (as
        defined in the Focile Terms of Service). This includes all Focile User Data.</li>
      <li>Focile User Data. Any content, information, and other data about persons who use the Focile services received
        or
        collected by Developer through any instance of the Developer Application, the Focile API, or otherwise in
        connection with the API Terms.</li>
    </ol>
  </div>

  <div class="fc-paragraph-card">
    <h4 class="fc-hdr-txt">
      Developer’s use of the focile api
    </h4>

    <p>Developer wishes to use and access the Focile API in connection with the development and distribution of a
      Developer Application and agrees to the additional requirements herein. Developer and Focile hereby agree as
      follows:</p>
    <p>Developer Registration. Before using the Focile API, Developer must provide accurate information identifying its
      organization and the individual representative who will serve as a point of contact for Focile. The registration
      may be completed and accepted by Focile on the Site. Upon successful registration, Focile shall make Access
      Credentials available to Developer. “Access Credentials” means the necessary security keys, secrets, tokens, and
      other credentials to access the Focile API. All activities that occur using your Access Credentials are your
      responsibility. Access Credentials are specific to you and are confidential information. Keep them secret. You
      must not sell, transfer, or sublicense the Access Credentials. Do not try to circumvent them and do not require
      your users, employees, contractors, or agents to obtain their own Access Credentials to build, maintain, or use
      the Developer Application. Developer itself must request any Access Credentials to be used by any employee,
      contractor, or agent of Developer. </p>
    <p>Development of the Developer Application. Subject to the terms of these Terms of Use, Developer will develop one
      or more Developer Applications. Developer will ensure that all Developer Applications are and remain in compliance
      with the API Terms and the Focile Terms of Service.</p>
    <p>Permitted Uses of the Focile API. Your use of the Focile API is limited to the purpose of facilitating your own
      or your Users’ use of the Focile Site and Site Services. Some examples of permitted uses of the Focile API would
      be to create Applications that:</p>

    <ol>
      <li>Allow Focile Users to search for and browse user profiles on the Focile Platform;</li>
      <li>Allow Focile Users to manage their active contacts and connections on the Focile Platform;
      </li>
      <li>Allow Focile Users to create, edit, modify, post, and view video advertisements and other posts on the Focile
        Platform;
      </li>
      <li>Allow Focile Users to collaborate on the development and sales of software applications; and
      </li>
      <li>Allow Focile Users to manage communications on the Focile Platform. </li>
    </ol>
    <p class="mt-3"><strong>Prohibited Uses of the Focile API. Developer must never do any of the following:</strong>
    </p>
    <ol>
      <li>Use the Focile API in any Developer Application that includes adult content, promotes gambling, involves the
        sale of tobacco or alcohol to persons under 21 years of age, promotes or offers malicious code, or violates any
        applicable law or regulation.</li>
      <li>Use the Focile API to retrieve Focile Content that is then aggregated with third-party search results in such
        a way that a user cannot attribute the Focile Content to Focile (such as aggregated search results).
      </li>
      <li>Distribute or allow access to the Focile API to anyone other than the entity on whose behalf Developer agreed
        to these Terms, or create an application programming interface that enables access to Focile Content
      </li>
    </ol>
    <p class="mt-3"><strong>Prohibited Uses/Functions of Developer Application. Developer and the Developer Application
        must not do the following:</strong></p>
    <ol>
      <li>Implement features or business practices that unlawfully harm the professional reputation or relationships of
        Focile or users of the Focile Platform.
      </li>
      <li>Use Focile Content received from the Focile API in any manner that facilitates bias, discrimination, or data
        “redlining,” whether intentional or inadvertent, based on sensitive or protected categories or characteristics.
      </li>
      <li>Impersonate Focile or a Focile user or intentionally misrepresent Focile or any Focile user or other third
        party when requesting or publishing information.</li>
      <li>Obfuscate or hide any Focile buttons, sign-in functionality, or consent or authorization flows from your
        users.</li>
      <li>Proxy, request, or use Focile account user names or passwords in any fashion for any reason.</li>
      <li>Request from the Focile API more than the minimum data fields and application permissions the Developer
        Application needs.</li>
      <li>Try to exceed or circumvent limitations on calls and use. This includes creating multiple Developer
        Applications for identical, or largely similar, purposes.</li>
      <li>Download, scrape, post, or transmit, in any form or by any means, any part of the Focile Platform, Site or
        Site Services, including data retrieved by web browser plugins.</li>
      <li>Copy, reformat, reverse-engineer, or otherwise modify the Focile API, Access Credentials, the Focile Platform,
        the Site, the Site Services, or any Focile Content.</li>
      <li>Promote or operate any product or service that competes with the Focile Site Services.</li>
      <li>Interfere with or disrupt Focile services, Focile servers or networks connected to Focile services, or disobey
        any requirements, procedures, policies or regulations of networks connected to the Focile Platform, Focile Site
        or Site Services.</li>
      <li>Engage in any conduct that fails to comply with, or is otherwise inconsistent with, any agreement between
        Developer and Focile or any written instructions provided by Focile.</li>
    </ol>
  </div>


  <div class="fc-paragraph-card">
    <h4 class="fc-hdr-txt">
      Api license
    </h4>
    <p>
      Using the Focile API. As part of the API Terms, Focile grants you a non-exclusive, non- transferable, and
      non-sublicensable (except as expressly permitted herein) license to use the Focile API solely to do the following
      and subject to the restrictions set forth in the API Terms:
    </p>
    <ol>
      <li>Enable your Application to interact with Focile’s databases to retrieve information necessary to facilitate
        your
        own or Your Users’ use of the Site and Site Services through your Application;</li>
      <li>Make limited intermediate copies of Focile Content only as necessary to perform activity permitted under the
        API
        Terms. You must delete all intermediate copies when they are no longer required for the purpose for which they
        were created;</li>
      <li>Rearrange or reorganize Focile Content within your Application; and</li>
      <li>Display in your Application Focile Content consistent with this Agreement.</li>
    </ol>

    <p>Access Credentials. Focile will provide you with Access Credentials that permit you to access the Focile API. The
      Access Credentials are the property of Focile and may be revoked if you share them with any third party (other
      than
      as allowed in the API Terms), if they are compromised, if you violate the API Terms or the Focile Terms of
      Service,
      or if Focile terminates the API Terms. </p>
  </div>


  <div class="fc-paragraph-card">
    <h4 class="fc-hdr-txt">
      Certification
    </h4>
    <p>At its discretion, Focile may require or offer Certification for certain Applications. Certification will consist
      of confirmation by Focile or a third party it designates that your Application’s technology complies with the API
      Terms and the Focile Terms of Service. You will be responsible for all costs associated with certification and any
      modifications necessary to meet the certification requirements and you may not be permitted to access the Focile
      API in certain cases until certification is complete. At Focile’s discretion, future modifications of your
      Application or use or display of Focile Content may be subject to re-certification. If Focile requires
      certification, your failure to maintain certification is cause for immediate termination of the API Terms.
    </p>
  </div>

  <div class="fc-paragraph-card">
    <h4 class="fc-hdr-txt">
      Data use
    </h4>
    <p>
      Obligations to Focile. Developer may only use, disclose, and otherwise process Focile User Data in accordance with
      the written instructions of Focile and applicable laws, rules and regulations.
    </p>
    <p><strong>Prohibited Uses. Developer will not:</strong></p>
    <ol>
      <li>Use Focile Content for any purpose except to the extent necessary for (1) the purpose of processing and
        supporting a specific transaction for the applicable Focile user or (2) the purpose of providing services to
        Focile.</li>
      <li>Use Focile Content for any research or publication purpose without prior written consent and a license from
        Focile to research or publish, as applicable, Focile Content.</li>
      <li>Use Focile Content for any other purpose (e.g., for Developer’s own benefit or for the benefit of any other
        entity or person).</li>
      <li>Use Focile Content for user profiling purposes or for advertising purposes.</li>
      <li>Sell, lease, sublicense, or otherwise transfer, directly or indirectly, Focile Content to any third party.
      </li>
      <li>Augment, commingle, or supplement Focile Content with personally identifiable or confidential information
        (including any mobile device identifier or other unique identifier that identifies any particular user, browser,
        computer or device) from any third-party source.</li>
      <li>Use the user identification or authentication codes connected to any Focile user to disclose information
        related to that user to any third party.</li>
    </ol>
  </div>

  <div class="fc-paragraph-card">
    <h4 class="fc-hdr-txt">
      DATA STORAGE
    </h4>
    <p>Except as provided in the API Terms, Developer may not copy or store any Focile Content, or any information
      expressed by or representing Focile Content (such as hashed or otherwise transformed data).</p>

    <p>Authentication Tokens. Developer may store any Developer Application-specific alphanumeric user identification
      codes that Focile provides to Developer for identifying individual users of the Developer Application or any
      tokens that Focile provides to Developer when a Focile user authenticates the Developer Application for the Focile
      user’s account.</p>

    <p>Cached Content. Solely for the purpose of improving user experience, Developer may cache Focile Content for no
      more than twenty-four (24) hours.</p>

    <p>Deletion. Developer must promptly and securely delete all Focile User Data collected from Focile users upon
      request of the Focile user, when the Focile user deactivates or uninstalls the Developer Application, when the
      Focile user closes their account with Developer, and before, when possible, or promptly upon termination of the
      API Terms. Developer will promptly and securely delete all Focile Content after completion of all activities
      reasonably necessary to operate or maintain the Developer Application and upon termination of the API Terms.</p>
  </div>

  <div class="fc-paragraph-card">
    <h4 class="fc-hdr-txt">
      USER CONSENT AND AGREEMENT
    </h4>
    <p>
      User Agreement and Privacy Policy. The Developer Application must
      include your own user agreement and privacy policy. Your user
      agreement and privacy policy must be prominently identified or located
      where members download or access the Development Application. Your
      privacy policy must meet applicable legal standards and accurately
      describe the collection, use, storage and sharing of data. You must
      promptly notify us of any breaches of your user agreement or privacy
      policy that impact or may impact Focile users.
    </p>
    <p>
      User Consent. Before obtaining information from Focile users, you must
      obtain their informed consent by telling them what information you
      collect and how it will be used, stored or shared, and by taking any
      additional steps required by law or regulation.
    </p>
  </div>


  <div class="fc-paragraph-card">
    <h4 class="fc-hdr-txt">
      DATA SAFEGUARDS
    </h4>
    <p>
      Developer will protect Focile User Data in accordance with all
      applicable laws. Your network, operating system, and software of your
      web servers, databases, and computer systems (collectively “Systems”)
      must be properly configured to securely operate the Developer
      Application and Focile User Data. The Developer Application must use
      reasonable security measures to protect any Focile User Data and any
      elements or components of the Focile API. You shall not architect,
      design, or select Systems in a manner to avoid these obligations. You
      must promptly report to Focile security deficiencies in, or intrusions
      into, your Systems that you discover to Focile. You will work with
      Focile to correct any security deficiency and disconnect any
      intrusions or intruders as soon as practicable upon discovery of any
      such deficiency or intrusion. In the event of any security deficiency
      or intrusion involving the Developer Application or Focile User Data,
      you will make no public statements without prior written and express
      permission from Focile in each instance, unless otherwise required by
      law.
    </p>
  </div>


  <div class="fc-paragraph-card">
    <h4 class="fc-hdr-txt">
      ACCESS TO THE FOCILE API
    </h4>
    <p>
      Subject to the API Terms, Focile may, in its sole discretion, make
      specific instances or versions of the Focile API available to
      Developer for use in connection with Developer Applications. Focile
      may terminate such access to the Focile API, in whole or certain
      features, functions, or services thereof, for convenience, at any
      time. The Focile API will be deemed to be a part of the Site Services
      as set forth in the Focile Terms of Service.
    </p>
  </div>


  <div class="fc-paragraph-card">
    <h4 class="fc-hdr-txt">
      FOCILE API SUPPORT AND CHANGES
    </h4>
    <p>
      Focile may provide technical support, upgrades, or modifications of
      the Focile API in Focile’s sole discretion. Focile may cease providing
      technical support, upgrades, or modifications of the Focile API at any
      time and for any reason without notice or liability to Developer.
      Focile may release new versions of the Focile API and require
      Developer to use the new version of the Focile API. Developer’s use of
      new releases or versions of the Focile API will be acceptance of all
      modifications of the Focile API.
    </p>
  </div>


  <div class="fc-paragraph-card">
    <h4 class="fc-hdr-txt">
      INTELLECTUAL PROPERTY
    </h4>
    <p>
      Focile Materials. Focile owns all rights, title, and interest,
      including all intellectual property rights, in and to, the Focile API;
      all elements, components, and executables of the Focile API; and all
      elements, components, and executables of the Site Services
      (collectively, the “Focile Materials”). The only exception to this is
      any information or Content which you as a Focile user have licensed to
      Focile under the Focile Terms of Service. Except for the express
      licenses granted in the API Terms, Focile does not grant you any
      right, title, or interest in the Focile Materials. Developer agrees to
      take such actions as Focile may reasonably request to perfect Focile’s
      rights to the Focile Materials.
    </p>
    <p>
      Developer Property. Except to the extent the Developer Application
      contains Focile Materials, Focile claims no ownership or control over
      your Developer Application. During the term of the API Terms you
      hereby grant Focile a paid-up, royalty-free, non-exclusive, worldwide,
      irrevocable right and license, under all of your intellectual property
      rights, to: (1) use, perform, and display the Developer Application
      and its content for purposes of marketing, demonstrating, and making
      the Developer Application available to Focile users; (2) link to and
      direct Focile users to the Developer Application; and (3) sublicense
      the foregoing rights to our affiliates.
    </p>
    <p>
      Feedback. Developer may, but is not required to, provide suggestions,
      comments, ideas, or know‐how, in any form, to Focile related to Focile
      products, services or technology (“Feedback”). To the extent Developer
      provides Feedback to Focile, Developer grants Focile the right to use
      such Feedback without any right to compensation from Focile.
    </p>
  </div>

  <div class="fc-paragraph-card">
    <h4 class="fc-hdr-txt">
      Independent development by focile
    </h4>
    <p>
      Developer understands and acknowledges that Focile may be independently creating applications, content, and other
      products or services that may be similar to or competitive with the Developer Application. Nothing in the API
      Terms will be construed as restricting or preventing Focile from creating and fully exploiting any applications,
      content, and other products or services, without any obligation to Developer. This paragraph will survive the
      termination or expiration of the API Terms.
    </p>
  </div>


  <div class="fc-paragraph-card">
    <h4 class="fc-hdr-txt">
      Confidential information
    </h4>
    <p>
      Developer agrees that the content of the Focile API, and any Focile Content that by its nature should reasonably
      be known to be confidential, is deemed to be confidential information of Focile and Developer will maintain the
      same in strict confidence and not disclose the same to any third party (other than employees, agents and
      contractors of Developer for the sole purpose of providing services to Developer to complete work permitted
      hereunder) or use the same for any purpose other than its performance under the Agreement. To the extent that you
      retain any Focile Content after the termination or expiration of the API Terms, this paragraph will survive the
      termination or expiration of the API Terms.
    </p>
  </div>


  <div class="fc-paragraph-card">
    <h4 class="fc-hdr-txt">
      Effect
    </h4>
    <p>
      The provisions of the API Terms will control over any inconsistent provisions of the Focile Terms of Service, and
      the Focile Terms of Service, as modified and supplemented by the API Terms, will remain in full force and effect.
      Any “shrink wrap” or “click wrap” license agreement or any other terms and conditions associated with Developer’s
      Application is null and void, is not applicable hereunder, and is expressly excluded.
    </p>
  </div>

  <div class="fc-paragraph-card">
    <h4 class="fc-hdr-txt">
      Fees
    </h4>
    <p>
      Focile currently provides the Focile API without charge. However, Focile reserves the right, at Focile’s sole
      discretion, to charge fees for future use of or access to the Focile API. Developer may not charge any fee to
      Focile users for the Developer’s Application without the express written permission or agreement of Focile.
    </p>
  </div>

  <div class="fc-paragraph-card">
    <h4 class="fc-hdr-txt">TERM AND TERMINATION.</h4>
    <p>
      Term. The term of the API Terms will commence on the date upon which
      you agree to the API Terms and will continue until terminated as set
      forth below.
    </p>
    <p>
      Developer’s Termination Rights. Developer may terminate the API Terms
      by discontinuing use of the Focile API and returning all Focile
      Content, including Focile User Data.
    </p>
    <p>
      Suspension and Termination. Focile may suspend or terminate
      Developer’s use of the Focile API at any time if we believe you have
      violated the API Terms or Focile Terms of Service, or if we believe
      the availability of the Focile API in the Developer Application is not
      in our or our users’ best interests. Immediately upon termination of
      the API Terms, all licenses granted to Developer will cease and all
      data retrieved from the Focile API or Site Services must be deleted.
    </p>
    <p>Effect of Termination. Upon termination of these Terms:</p>
    <ol>
      <li>
        all rights and licenses granted to Developer will terminate
        immediately;
      </li>
      <li>
        Developer will promptly and securely destroy Focile Content in your
        possession or control;
      </li>
      <li>
        neither party is liable to the other party solely because the API
        Terms have been terminated;
      </li>
      <li>
        unless we agree otherwise in writing or as stated explicitly in the
        API Terms, Developer must permanently delete all Focile Content and
        Focile User Data or other data which you stored pursuant to your use
        of the Focile API. Focile may require that you certify in writing
        your compliance with this section; and
      </li>
      <li>
        Focile will make commercially reasonable efforts to remove all
        references and links to the Developer Application from the Site
        (Focile has no other obligation to delete copies of, references to,
        or links to the Developer Application).
      </li>
    </ol>
  </div>

  <div class="fc-paragraph-card">
    <h4 class="fc-hdr-txt">
      Warranties and liabilities
    </h4>
    <p>
      Disclaimer of Warranties. FOCILE PROVIDES THE FOCILE API, FOCILE CONTENT, AND ALL OTHER INFORMATION AND SERVICES
      ON AN “AS IS'' AND “AS AVAILABLE” BASIS WITH NO WARRANTIES, EITHER EXPRESS OR IMPLIED, OF ANY KIND. TO THE FULLEST
      EXTENT PERMISSIBLE UNDER APPLICABLE LAW, FOCILE DISCLAIMS ANY AND ALL WARRANTIES AND REPRESENTATIONS, INCLUDING,
      WITHOUT LIMITATION, ANY IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, TITLE, ACCURACY
      OF DATA, AND NON-INFRINGEMENT. FOCILE DOES NOT GUARANTEE THAT THE FOCILE API WILL FUNCTION WITHOUT INTERRUPTION OR
      ERRORS IN FUNCTIONING. IN PARTICULAR, THE OPERATION OF THE FOCILE API MAY BE INTERRUPTED DUE TO MAINTENANCE,
      UPDATES, OR SYSTEM OR NETWORK FAILURES. FOCILE DISCLAIMS ALL LIABILITY FOR DAMAGES CAUSED BY ANY SUCH INTERRUPTION
      OR ERRORS IN FUNCTIONING. FURTHERMORE, FOCILE DISCLAIMS ALL LIABILITY FOR ANY MALFUNCTIONING, IMPOSSIBILITY OF
      ACCESS, OR POOR USE CONDITIONS OF THE FOCILE API DUE TO INAPPROPRIATE EQUIPMENT, DISTURBANCES RELATED TO INTERNET
      SERVICE PROVIDERS, TO THE SATURATION OF THE INTERNET NETWORK, AND FOR ANY OTHER REASON.
    </p>
    <p>
      Limitations of Liability. FOCILE AND OUR EMPLOYEES, AGENTS, SHAREHOLDERS, OR DIRECTORS, SHALL NOT BE LIABLE FOR
      ANY SPECIAL, INCIDENTAL, INDIRECT, PUNITIVE, OR CONSEQUENTIAL DAMAGES OR LOSS OF USE, PROFIT, REVENUE OR DATA TO
      YOU OR ANY THIRD PERSON ARISING FROM YOUR USE OF THE FOCILE API. THIS LIMITATION OF LIABILITY SHALL APPLY
      REGARDLESS OF WHETHER (i) YOU BASE YOUR CLAIM ON CONTRACT, TORT, STATUTE, OR ANY OTHER LEGAL THEORY, (ii) WE KNEW
      OR SHOULD HAVE KNOWN ABOUT THE POSSIBILITY OF SUCH DAMAGES, OR (iii) THE LIMITED REMEDIES PROVIDED IN THIS SECTION
      FAIL OF THEIR ESSENTIAL PURPOSE. THIS LIMITATION OF LIABILITY SHALL NOT APPLY TO ANY DAMAGE THAT FOCILE MAY CAUSE
      YOU INTENTIONALLY OR KNOWINGLY IN VIOLATION OF THE API TERMS OR APPLICABLE LAW.
    </p>
  </div>


  <div class="fc-paragraph-card mb-5">
    <h4 class="fc-hdr-txt">
      General
    </h4>
    <p>
      Relationship of the Parties. The parties are agreeing to the API Terms as independent contractors, and the API
      Terms will not be construed to create a partnership, joint venture or employment relationship between them.
      Neither party is authorized to or will represent itself to be an employee or agent of the other or enter into any
      agreement on the other’s behalf of or in the other’s name.
    </p>
    <p>
      Assignability. You may not assign the API Terms, nor any of your rights or obligations hereunder, without Focile’s
      prior written consent. Focile may freely assign the API Terms without your consent. Any attempted assignment or
      transfer in violation of this section will be null and void. Subject to the foregoing restrictions, the API Terms
      will inure to the benefit of successors and permitted assigns of the parties.
    </p>
    <p>Severability. If and to the extent any provision of the API Terms is held illegal, invalid, or unenforceable in
      whole or in part under applicable law, such provision or such portion thereof shall be ineffective as to the
      jurisdiction in which it is illegal, invalid, or unenforceable to the extent of its illegality, invalidity, or
      unenforceability, and shall be deemed modified to the extent necessary to conform to applicable law so as to give
      the maximum effect to the intent of the parties. The illegality, invalidity, or enforceability of such provision
      in that jurisdiction shall not in any way affect the legality, validity, or enforceability of such provision in
      any other jurisdiction or of any other provision in any jurisdiction.
    </p>

    <p><strong>ARBITRATION AGREEMENT.</strong></p>
    <p>ANY DISPUTE ARISING OUT OF THIS AGREEMENT SHALL BE RESOLVED AND DETERMINED BY ARBITRATION UNDER THE THEN-CURRENT
      RULES OF THE AMERICAN ARBITRATION ASSOCIATION. THE ARBITRATION PROCEEDINGS SHALL BE HELD IN TRAVES COUNTY, AUSTIN,
      TX. ALL QUESTIONS OF LAW SHALL BE DECIDED IN ACCORDANCE WITH THE LAWS OF TRAVES COUNTY, AUSTIN, TX. TO THE EXTENT
      POSSIBLE, THE PARTIES SHALL UTILIZE THE SIMPLIFIED RULES OF ARBITRATION AS SET FORTH BY THE AMERICAN ARBITRATION
      ASSOCIATION.</p>

    <p>YOU AND FOCILE AGREE THAT EACH MAY BRING CLAIMS AGAINST THE OTHER ONLY IN YOUR OR ITS INDIVIDUAL CAPACITY, AND
      NOT AS A PLAINTIFF OR CLASS MEMBER IN ANY PURPORTED CLASS OR REPRESENTATIVE PROCEEDING.</p>

    <p>Further, unless both you and Focile agree otherwise, the arbitrator may not consolidate more than one person’s
      claims with your claims, and may not otherwise preside over any form of a representative or class proceeding. If
      this specific provision is found to be unenforceable, then the entirety of this arbitration provision shall be
      null and void. The arbitrator may award declaratory or injunctive relief only in favor of the individual party
      seeking relief and only to the extent necessary to provide relief warranted by that party’s individual claim.</p>

    <p>No Waiver. The failure or delay of either party to exercise or enforce any right or claim does not constitute a
      waiver of such right or claim and shall in no way affect that party’s right to later enforce or exercise it,
      unless such party issues an express written waiver, signed by a duly authorized representative of each party.</p>

    <p>Miscellaneous. The API Terms set forth the entire agreement and understanding of the parties relating to their
      subject matter and cancel and supersede any prior or contemporaneous discussions, agreements, representations,
      warranties, and other communications between them. No modification or amendment to the API Terms shall be binding
      upon Focile unless in a written instrument signed by a duly authorized representative of Focile.</p>
  </div>
</section>