<div class="fc-container">
  <div class="breadcumb my-4">
      <nav aria-label="breadcrumb">
          <ol class="breadcrumb">
              <li class="breadcrumb-item"><a routerLink="/">Home</a></li>
              <li class="breadcrumb-item active" aria-current="page">Become Partner</li>
          </ol>
      </nav>
  </div>
</div>

<div class="fc-become-partner-wrap">
  <div class="fc-full-container mt-4">
    <div class="fc-container">
      <div class="col-sm-12">
        <div class="row">
          <div class="col-sm-12">
            <label class="text-white fs-5 fw-light">Focile for Business</label>
            <h2 class="fw-bold text-white">Business Partners</h2>
            <p class="text-white">Focile is a search engine and connection networking platform designed to empower
              individuals and SMB
              businesses to connect with the dedicated AV Channel Partner Sales Experts, Channel Managers, and Pre-Sales
              Technical Experts to build a powerful circle of professional network aiming for an enhanced performance,
              rapid support, and enduring relationships. </p>
              <div class="d-flex w-100 justify-content-center">
              <button class="btn pricing-plan-btn mt-4 fs-6" routerLink="/subscription-plans">              
                Pricing
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="fc-container mt-3 mt-sm-5">
  <div class="fc-become-partner-section">
    <form [formGroup]="partnerForm" (ngSubmit)="onSubmit()">
      <div>
        <label class="label-text fw-bold">Become a partner</label>
        <h4>Register for free as a Channel Partner, 
          Consultant, or an Expert.
        </h4>
      </div>
      <div class="d-flex flex-column gap-3 align-items-center mb-4">
        <input type="text" formControlName="email" placeholder="Enter your work email" class="mt-4">
        <div *ngIf="partnerForm.controls.email.invalid && partnerForm.controls.email.touched" class="text-danger">
          Please enter a valid email.
        </div>
        <div class="custom-checkbox mt-3">
          <input type="checkbox" id="checkbox1" formControlName="agree">
         <label for="checkbox1">I have read and agreed to Focile <a href="#" routerLink="/terms-and-condtions">Terms</a>, <a routerLink="/privacy-policy">Privacy</a>, and the <a
              href="#" routerLink="/agreements">Partner
              Agreement</a>.</label>
        </div>
        <div *ngIf="partnerForm.controls.agree.invalid && partnerForm.controls.agree.touched" class="text-danger">
          You must agree to the terms.
        </div>
      </div>
      <button class="become-btn" type="submit" [disabled]="partnerForm.invalid">Register</button>
    </form>
  </div>
</div>

<div class="fc-full-container mt-4">
  <div class="fc-container">
    <div class="col-sm-12 mt-0 mt-sm-5">
      <h1 class="fw-bold text-white mb-0 mb-sm-3 text-center">What's in it for you?</h1>
    </div>
    <div class="col-sm-12 mt-5">
      <div class="row fc-content-card align-items-center">
        <div class="col-sm-6 text-center text-sm-start"><img src="../../../../../assets/images/admin-1.svg"></div>
        <div class="col-sm-6 fc-content-box">
          <h4 class="fs-5 fw-bold text-white">By Partnering with Focile, you will get the following:</h4>
          <ul class="text-white">
            <li>Connected by other Channel Partners for partnership,</li>
            <li>Instant communications with your main point of contact.</li>
            <li>Connected by end user business prospects needing your services.</li>
            <li>Build wise connections with a diversity of network members.</li>
            <li>Create favorite list of close connections for easier outreach.</li>
          </ul>
        </div>
      </div>
    </div>

    <div class="col-sm-12 mt-5">
      <div class="row fc-content-card">
        <div class="col-sm-6 order-1 order-sm-2 text-center text-sm-start"><img src="../../../../../assets/images/admin-2.svg"></div>
        <div class="col-sm-6 fc-content-box order-2 order-sm-1">
          <h4 class="fs-5 fw-bold text-white">Marketing Platform</h4>
          <p class="text-white">The Focile platform is also a media marketing portal for Channel Partners and businesses
            allowing you to advertise your brands, services, using a short video-clip to allow quicker introduction to
            users, counterpart experts, and simplifying the business inquiries.
          </p>
        </div>
      </div>
    </div>

    <div class="col-sm-12 mt-5">
      <div class="row fc-content-card align-items-center">
        <div class="col-sm-6 text-center text-sm-start"><img src="../../../../../assets/images/admin-3.svg"></div>
        <div class="col-sm-6 fc-content-box">
          <h4 class="fs-5 fw-bold text-white">Simplified Connection</h4>
          <p class="text-white">We aspire to introduce a new format of connection within our platform by enhancing the
            way business professionals, Colleagues, and individuals interact to connect socially with each other using a
            single platform to build a circle of confidence, close network groups for greater outcome.</p>
          <p class="text-white">We offer a very simple and quicker way to explore and locate your friendly expert or
            dedicated connections, sync to engage, and directly collaborate with all type of SMB Technology Channel
            partners ranged from Resellers, Vendors, Independent Agents, or Distributors to support end user businesses
            needs.</p>
        </div>
      </div>
    </div>

    <div class="col-sm-12 mt-5">
      <div class="row fc-content-card">
        <div class="col-sm-6 order-1 order-sm-2 text-center text-sm-start"><img src="../../../../../assets/images/admin-1.svg"></div>
        <div class="col-sm-6 fc-content-box order-2 order-sm-1">
          <h4 class="fs-5 fw-bold text-white">Partnership</h4>
          <p class="text-white">This is a Partnership between Focile, its Channel Partners, and user businesses.
            We are helping you to build circle of connections to simplify your business relations.
            Your connection matters! Stay engaged and build wise relations.
          </p>
        </div>
      </div>
    </div>

    <div class="col-sm-12 mt-5">
      <div class="row fc-content-card align-items-center">
        <div class="col-sm-6 text-center text-sm-start"><img src="../../../../../assets/images/admin-2.svg"></div>
        <div class="col-sm-6 fc-content-box">
          <h4 class="fs-5 fw-bold text-white">Importance of completing your profile
          </h4>
          <p class="text-white">By creating and completing your free profile with the required information for your
            general business information and your user details, your company and member eCard will be created and appear
            in the landing pages. It will help AV Channel Partners, Individuals, and Businesses to easily locate your
            brand, services, and connect with you instantly.</p>
          <p class="text-white">*******</p>
          <p class="text-white">Failure to complete the required field, your eCard will not be created and your status
            will be hidden from all viewers.
          </p>
        </div>
      </div>
    </div>

  </div>
</div>

<div class="fc-container">
  <div class="fc-become-partner-section">
    <form [formGroup]="partnerForm" (ngSubmit)="onSubmit()">
      <div>
        <label class="label-text fw-bold">Become a partner</label>
        <h4>Register for free as a Channel Partner, Consultant, or an Expert.</h4>
      </div>
      <div class="d-flex flex-column gap-3 align-items-center mb-4">
        <input type="text" formControlName="email" placeholder="Enter your work email" class="mt-4">
        <div *ngIf="partnerForm.controls.email.invalid && partnerForm.controls.email.touched" class="text-danger">
          Please enter a valid email.
        </div>
        <div class="custom-checkbox mt-3 gap-2">
          <input type="checkbox" id="checkbox1" formControlName="agree">
          <label for="checkbox1">I have read and agreed to Focile <a href="#" routerLink="/terms-and-condtions">Terms</a>, <a routerLink="/privacy-policy">Privacy</a>, and the <a
              href="#" routerLink="/agreements">Partner
              Agreement</a>.</label>
        </div>
        <div *ngIf="partnerForm.controls.agree.invalid && partnerForm.controls.agree.touched" class="text-danger">
          You must agree to the terms.
        </div>
      </div>
      <button class="become-btn" type="submit" [disabled]="partnerForm.invalid">Register</button>
    </form>
  </div>
</div>