import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { finalize } from 'rxjs';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { UtilsService } from 'src/app/shared/services/utils.service';

@Component({
  selector: 'app-become-partner',
  templateUrl: './become-partner.component.html',
  styleUrls: ['./become-partner.component.scss'],
})
export class BecomePartnerComponent {
  isFirstStepComplete = false;
  level = 'Standard';
  sendMail: any = {};
  loading = false;
  mailSended = false;
  partnerForm: FormGroup;

  constructor(
    private account: AccountService,
    private toaster: ToastrService,
    private utils: UtilsService,
    private fb: FormBuilder, 
    private router: Router
  ) {
    this.partnerForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      agree: [false, Validators.requiredTrue]
    });
  }

  inviteUsers() {
    const payload = {
      id: this.utils.getEmptyGuid(),
      email: `<EMAIL>`,
      body: `
      <div>
      <p>
          <strong>${this.sendMail.firstName} ${this.sendMail.lastName}</strong> is interested in becoming a partner.
      </p>
      <p>Contact Information:</p>
      <ul>
          <li><strong>Company Name:</strong> ${this.sendMail.companyName}</li>
          <li><strong>Company Email:</strong> ${this.sendMail.companyMail}</li>
          <li><strong>Direct Phone:</strong> ${this.sendMail.directPhone}</li>
          <li><strong>Level :</strong> ${this.level}</li>
      </ul>
  </div>
      `,
      subject: 'Become Partner',
      cc: '',
    };
    this.loading = true;
    this.account
      .buldInvite(payload)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((response: any) => {
        if (response.data) {
          this.mailSended = true;
          this.sendMail = {};
          setTimeout(() => {
            this.mailSended = false;
          }, 5000);
        } else {
          this.toaster.error('', response.message);
        }
      });
  }

  handleNextClick() {
    return;
    this.inviteUsers();
  }

  onSubmit() {
    if (this.partnerForm.valid) {
      this.router.navigate(['/account/register'], { queryParams: { userType: 2 } });
    }
  }
}
