<div class="fc-container">
    <div class="breadcumb my-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a routerLink="/">Home</a></li>
                <li class="breadcrumb-item"><a routerLink="/blog-list">Blog</a></li>
                <li class="breadcrumb-item active" aria-current="page" *ngIf="blog">{{ blog.title }}</li>
                <li class="breadcrumb-item active" aria-current="page" *ngIf="loading">Loading...</li>
                <li class="breadcrumb-item active" aria-current="page" *ngIf="error">Blog Not Found</li>
            </ol>
        </nav>
    </div>
</div>
<div class="fc-container">
    <!-- Loading state -->
    <div *ngIf="loading" class="fc-loading-state">
        <div class="fc-spinner"></div>
        <p>Loading blog...</p>
    </div>

    <!-- Error state -->
    <div *ngIf="error && !loading" class="fc-error-state">
        <h3>Blog Not Found</h3>
        <p>The blog post you're looking for doesn't exist or has been removed.</p>
        <button class="btn btn-primary" (click)="navigateBack()">Back to Blog List</button>
    </div>

    <!-- Blog content -->
    <div *ngIf="blog && !loading && !error" class="fc-blog-detail">
        <h5 class="fc-artical-name">{{ blog.title }}</h5>
        <div class="fc-by-release">
            BY <span>Focile Inc</span>
            <span *ngIf="blog.publishedAt">ON {{ formatDate(blog.publishedAt) }}</span>
        </div>

        <div class="fc-blog-artical-text">
            <ul class="fc-socail-follow">
                <!-- OPTION 1: Font Awesome Icons (Currently Active) -->
                <!-- Facebook Share -->
                <li><a href="#"
                       (click)="shareOnFacebook(); $event.preventDefault()"
                       title="Share on Facebook"
                       class="fc-share-btn facebook">
                    <i class="fab fa-facebook-f"></i>
                </a></li>

                <!-- LinkedIn Share -->
                <li><a href="#"
                       (click)="shareOnLinkedIn(); $event.preventDefault()"
                       title="Share on LinkedIn"
                       class="fc-share-btn linkedin">
                    <i class="fab fa-linkedin-in"></i>
                </a></li>

                <!-- Pinterest Share -->
                <li><a href="#"
                       (click)="shareOnPinterest(); $event.preventDefault()"
                       title="Share on Pinterest"
                       class="fc-share-btn pinterest">
                    <i class="fab fa-pinterest-p"></i>
                </a></li>

                <!-- X (Twitter) Share -->
                <li><a href="#"
                       (click)="shareOnTwitter(); $event.preventDefault()"
                       title="Share on X (Twitter)"
                       class="fc-share-btn twitter">
                    <i class="fab fa-x-twitter"></i>
                </a></li>

                <!-- Copy Link -->
                <li><a href="#"
                       (click)="copyToClipboard(); $event.preventDefault()"
                       title="Copy link to clipboard"
                       class="fc-share-btn copy-link">
                    <i class="fas fa-link"></i>
                </a></li>
            </ul>
            <div class="fc-related-blog-content">
            
                <!-- Dynamic Blog Content Section -->
                <div class="fc-dynamic-blog-content">
                    <!-- Subtitle - Only show if available -->
                    <p *ngIf="hasSubtitle()" class="fc-blog-subtitle mb-4">
                        <strong>{{ blog.subTitle }}</strong>
                    </p>
                    <!-- Featured Image - Only show if imageUrl is available -->
                    <img *ngIf="hasImage()" [src]="blog.imageUrl" [alt]="blog.title" class="w-100 fc-blog-featured-image"
                        (error)="onImageError($event)" loading="lazy">
                    <!-- Blog Content - Render HTML content -->
                    <div *ngIf="hasContent()" class="fc-blog-content mt-4" [innerHTML]="blog.content">
                    </div>
                    <!-- Tags - Only show if available -->
                    <div *ngIf="hasTags()" class="fc-blog-tags mt-4">
                        <strong>Tags: </strong>
                        <span class="fc-tag" *ngFor="let tag of getTags()">{{ tag }}</span>
                    </div>
                </div>

                <div class="fc-blog-posted-by">
                    <img src="../../../../../assets/images/Pic.png" alt="blog author">
                    <label>Focile Inc</label>
                    <span>Follow us <a href="">&#64;FocileInc</a></span>
                </div>
            </div>

            <ul class="fc-likes-read">
                <!-- Like Button -->
                <li>
                    <a href="#"
                       (click)="toggleLike(); $event.preventDefault()"
                       [class.liked]="blog.isLiked"
                       [class.loading]="likeLoading"
                       title="Like this post">
                        <svg width="32" height="27" viewBox="0 0 32 27" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M29.6531 3.16639C24.9672 -2.96807 15.7453 0.651643 15.7453 6.88136C15.7453 0.651643 6.52273 -2.96837 1.83618 3.16639C-3.0075 9.50847 1.76658 20.6182 15.745 27C29.7227 20.6182 34.4968 9.50847 29.6531 3.16639Z"
                                  [attr.fill]="blog.isLiked ? '#FF5480' : '#9B9B9B'"/>
                        </svg>
                        <span class="fc-count">{{ formatCount(blog.likeCount || 0) }}</span>
                        <div *ngIf="likeLoading" class="fc-like-spinner"></div>
                    </a>
                </li>

                <!-- View Count -->
                <li>
                    <a href="#" (click)="$event.preventDefault()" title="View count">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"
                                  fill="#9B9B9B"/>
                        </svg>
                        <span class="fc-count">{{ formatCount(blog.viewCount || 0) }}</span>
                    </a>
                </li>
            </ul>
        </div>        
    </div>
</div>