.breadcumb {
  li {
    a {
      text-decoration: none;
    }

    &.breadcrumb-item + .breadcrumb-item::before {
      content: "-";
    }
  }
}
.fc-by-release {
  color: #9b9b9b;

  span {
    margin: 0px 10px;
    color: black;
  }
}

.fc-artical-name {
  font-size: 2.1rem;
  color: black;
  font-weight: 700;
}

.fc-blog-artical-text {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.fc-socail-follow {
  display: flex;
  list-style: none;
  flex-direction: column;
  gap: 1.5rem;
  width: 35px;
  list-style: none;
  padding-left: 0px;

  li {
    a {
      width: 32px;
      height: 32px;
      background-color: #9b9b9b;
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      text-decoration: none;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: #014681;
        transform: scale(1.1);
        box-shadow: 0 4px 15px rgba(1, 70, 129, 0.3);
      }

      &.facebook:hover {
        background-color: #1877f2;
      }

      &.twitter:hover {
        background-color: #000000;
      }

      &.linkedin:hover {
        background-color: #0077b5;
      }

      &.pinterest:hover {
        background-color: #bd081c;
      }

      &.copy-link:hover {
        background-color: #28a745;
      }

      // Font Awesome Icons
      i {
        font-size: 16px;
        transition: all 0.3s ease;
        color: white;
      }

      &:hover i {
        transform: scale(1.1);
      }

      // Google Material Icons (Alternative)
      .material-symbols-outlined {
        font-size: 18px;
        transition: all 0.3s ease;
        color: white;
        font-weight: 400;
      }

      &:hover .material-symbols-outlined {
        transform: scale(1.1);
      }
    }
  }
}

.fc-likes-read {
  display: flex;
  list-style: none;
  flex-direction: column;
  gap: 1.5rem;
  width: 35px;
  list-style: none;
  padding-left: 0px;
  li {
    a {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      text-decoration: none;
      white-space: nowrap;
      position: relative;
      cursor: pointer;
      transition: all 0.3s ease;
      padding: 0.5rem;
      border-radius: 8px;

      // &:hover {
      //   background-color: rgba(0, 0, 0, 0.05);
      //   transform: scale(1.05);
      // }

      &.liked {
        svg path {
          fill: #ff5480 !important;
        }

        &:hover svg path {
          fill: #e04570 !important;
        }
      }

      &.loading {
        pointer-events: none;
        opacity: 0.7;
      }

      .fc-count {
        font-size: 12px;
        font-weight: 600;
        color: #666;
        margin-top: 4px;
      }

      .fc-like-spinner {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 16px;
        height: 16px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #ff5480;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }
}

.fc-blog-artical-text {
  margin-top: 2rem;
  display: flex;
  flex-direction: row;
  gap: 40px;
}

.fc-related-blog-content {
  width: 100%;
  p {
    color: #4a4a4a;
    font-size: 20px;
    line-height: 40px;
  }
}

.fc-blog-posted-by {
  width: max-content;
  margin: 2rem auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;

  img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 40px;
  }

  label {
    font-size: 18px;
    color: #111210;
    margin-bottom: 0px;
  }

  span {
    color: #4a4a4a;
    font-size: 16px;
    a {
      color: #27aeff;
      text-decoration: none;
      font-size: 18px;
    }
  }
}

// Blog Detail Specific Styles
.fc-loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;

  .fc-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #014681;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  p {
    color: #666;
    font-size: 18px;
  }
}

.fc-error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;

  h3 {
    color: #dc3545;
    margin-bottom: 1rem;
  }

  p {
    color: #666;
    margin-bottom: 2rem;
  }

  .btn {
    background-color: #014681;
    border-color: #014681;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    text-decoration: none;
    transition: all 0.3s ease;

    &:hover {
      background-color: #0056b3;
      transform: translateY(-2px);
    }
  }
}

.fc-blog-tags {
  .fc-tag {
    display: inline-block;
    background-color: #f8f9fa;
    color: #014681;
    padding: 0.25rem 0.75rem;
    margin: 0.25rem 0.25rem 0.25rem 0;
    border-radius: 15px;
    font-size: 0.875rem;
    font-weight: 500;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;

    &:hover {
      background-color: #014681;
      color: white;
    }
  }
}

.fc-blog-content {
  line-height: 1.8;
  font-size: 16px;
  color: #333;

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: #014681;
    margin-top: 2rem;
    margin-bottom: 1rem;
  }

  p {
    margin-bottom: 1.5rem;
  }

  img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 1.5rem 0;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  table {
    width: 100%;
    border-collapse: collapse;
    margin: 1.5rem 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

    th,
    td {
      padding: 0.75rem;
      text-align: left;
      border: 1px solid #ddd;
    }

    th {
      background-color: #f8f9fa;
      font-weight: 600;
      color: #014681;
    }

    tr:nth-child(even) {
      background-color: #f8f9fa;
    }
  }
}

.fc-blog-subtitle {
  font-size: 1.25rem;
  color: #666;
  font-style: italic;
}

.fc-blog-featured-image {
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
}

.fc-dynamic-blog-content {
  margin: 0rem 0 0rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
