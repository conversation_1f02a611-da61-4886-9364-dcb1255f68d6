import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { finalize, catchError, of } from 'rxjs';

// Blog interface based on API response
interface BlogDetail {
  id: string;
  title: string;
  subTitle: string;
  slug: string | null;
  content: string | null;
  imageUrl: string | null;
  tags: string | null;
  publishedAt: string;
  likeCount?: number;
  viewCount?: number;
  isLiked?: boolean;
}

@Component({
  selector: 'app-blog-detail',
  templateUrl: './blog-detail.component.html',
  styleUrls: ['./blog-detail.component.scss']
})
export class BlogDetailComponent implements OnInit {
  blog: BlogDetail | null = null;
  loading = false;
  error = false;
  defaultBlogImage = 'assets/images/e-card-default.svg';
  likeLoading = false;
  viewIncremented = false;
  showCopyToast = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private accountService: AccountService
  ) { }

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      const slug = params['slug'];
      if (slug) {
        this.loadBlogDetail(slug);
      } else {
        this.error = true;
      }
    });
  }

  loadBlogDetail(slug: string): void {
    this.loading = true;
    this.error = false;

    this.accountService.getBlogBySlug(slug)
      .pipe(
        catchError((error) => {
          console.error('Error loading blog detail:', error);
          this.error = true;
          return of(null);
        }),
        finalize(() => this.loading = false)
      )
      .subscribe({
        next: (response: BlogDetail) => {
          if (response) {
            this.blog = response;
            console.log('Blog detail loaded:', this.blog);
            // Increment view count when blog is loaded
            this.incrementViewCount();
          } else {
            this.error = true;
          }
        },
        error: (error) => {
          console.error('Unexpected error loading blog detail:', error);
          this.error = true;
        }
      });
  }

  // Get blog image with fallback to default
  getBlogImage(): string {
    if (this.blog?.imageUrl && this.blog.imageUrl.trim() !== '') {
      return this.blog.imageUrl;
    }
    return this.defaultBlogImage;
  }

  // Check if blog has image
  hasImage(): boolean {
    return !!(this.blog?.imageUrl && this.blog.imageUrl.trim() !== '');
  }

  // Format published date
  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  // Parse tags string into array (handles "tag1 , tag2 , tag3 " format)
  getTags(): string[] {
    if (!this.blog?.tags || this.blog.tags.trim() === '') return [];
    return this.blog.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
  }

  // Check if blog has tags
  hasTags(): boolean {
    return this.getTags().length > 0;
  }

  // Check if blog has subtitle
  hasSubtitle(): boolean {
    return !!(this.blog?.subTitle && this.blog.subTitle.trim() !== '');
  }

  // Check if blog has content
  hasContent(): boolean {
    return !!(this.blog?.content && this.blog.content.trim() !== '');
  }

  // Navigate back to blog list
  navigateBack(): void {
    this.router.navigate(['/blog-list']);
  }

  // Handle image error
  onImageError(event: any): void {
    event.target.src = this.defaultBlogImage;
  }

  // Toggle like/unlike blog
  toggleLike(): void {
    if (!this.blog || this.likeLoading) return;

    this.likeLoading = true;
    const isCurrentlyLiked = this.blog.isLiked;
    const apiCall = isCurrentlyLiked
      ? this.accountService.unlikeBlog(this.blog.id)
      : this.accountService.likeBlog(this.blog.id);

    apiCall.pipe(
      catchError((error) => {
        console.error('Error toggling like:', error);
        return of(null);
      }),
      finalize(() => this.likeLoading = false)
    ).subscribe({
      next: (response) => {
        if (response && this.blog) {
          // Update local state
          this.blog.isLiked = !isCurrentlyLiked;
          this.blog.likeCount = (this.blog.likeCount || 0) + (isCurrentlyLiked ? -1 : 1);
        }
      },
      error: (error) => {
        console.error('Unexpected error toggling like:', error);
      }
    });
  }

  // Increment view count
  incrementViewCount(): void {
    if (!this.blog || this.viewIncremented) return;

    this.viewIncremented = true;
    this.accountService.incrementBlogView(this.blog.id)
      .pipe(
        catchError((error) => {
          console.error('Error incrementing view count:', error);
          return of(null);
        })
      )
      .subscribe({
        next: (response) => {
          if (response && this.blog) {
            this.blog.viewCount = (this.blog.viewCount || 0) + 1;
          }
        },
        error: (error) => {
          console.error('Unexpected error incrementing view count:', error);
        }
      });
  }

  // Format count for display (e.g., 1200 -> 1.2K)
  formatCount(count: number): string {
    if (count >= 1000000) {
      return (count / 1000000).toFixed(1) + 'M';
    } else if (count >= 1000) {
      return (count / 1000).toFixed(1) + 'K';
    }
    return count.toString();
  }

  // Social sharing methods
  shareOnFacebook(): void {
    if (!this.blog) return;

    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(this.blog.title);
    const description = encodeURIComponent(this.blog.subTitle || 'Check out this blog post');

    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}&quote=${title} - ${description}`;
    this.openShareWindow(facebookUrl, 'Facebook');
  }

  shareOnTwitter(): void {
    if (!this.blog) return;

    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(this.blog.title);
    const hashtags = this.getTags().join(',');

    const twitterUrl = `https://twitter.com/intent/tweet?url=${url}&text=${title}&hashtags=${hashtags}&via=FocileInc`;
    this.openShareWindow(twitterUrl, 'Twitter');
  }

  shareOnLinkedIn(): void {
    if (!this.blog) return;

    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(this.blog.title);
    const summary = encodeURIComponent(this.blog.subTitle || 'Check out this blog post');

    const linkedInUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${url}&title=${title}&summary=${summary}`;
    this.openShareWindow(linkedInUrl, 'LinkedIn');
  }

  shareOnPinterest(): void {
    if (!this.blog) return;

    const url = encodeURIComponent(window.location.href);
    const description = encodeURIComponent(this.blog.title);
    const media = encodeURIComponent(this.getBlogImage());

    const pinterestUrl = `https://pinterest.com/pin/create/button/?url=${url}&media=${media}&description=${description}`;
    this.openShareWindow(pinterestUrl, 'Pinterest');
  }

  copyToClipboard(): void {
    const url = window.location.href;

    if (navigator.clipboard && window.isSecureContext) {
      // Use modern clipboard API
      navigator.clipboard.writeText(url).then(() => {
        this.showCopySuccess();
      }).catch(err => {
        console.error('Failed to copy: ', err);
        this.fallbackCopyToClipboard(url);
      });
    } else {
      // Fallback for older browsers
      this.fallbackCopyToClipboard(url);
    }
  }

  private openShareWindow(url: string, platform: string): void {
    const width = 600;
    const height = 400;
    const left = (window.innerWidth - width) / 2;
    const top = (window.innerHeight - height) / 2;

    window.open(
      url,
      `share-${platform}`,
      `width=${width},height=${height},left=${left},top=${top},scrollbars=yes,resizable=yes`
    );
  }

  private fallbackCopyToClipboard(text: string): void {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
      document.execCommand('copy');
      this.showCopySuccess();
    } catch (err) {
      console.error('Fallback: Oops, unable to copy', err);
    }

    document.body.removeChild(textArea);
  }

  private showCopySuccess(): void {
    this.showCopyToast = true;
    setTimeout(() => {
      this.showCopyToast = false;
    }, 3000);
  }
}
