<div class="fc-container">
  <div class="breadcumb my-4">
      <nav aria-label="breadcrumb">
          <ol class="breadcrumb">
              <li class="breadcrumb-item"><a routerLink="/">Home</a></li>
              <li class="breadcrumb-item active" aria-current="page">Blog</li>
          </ol>
      </nav>
  </div>
</div>
    
<div class="fc-container">
    <div class="fc-blog-search-filter">
        <div class="fc-blog-search">
            <input type="text"
                   placeholder="Search by title or tags..."
                   [(ngModel)]="searchTerm"
                   name="blogSearch">
            <span class="search-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_363_3510)">
                        <path
                            d="M14 15.5V14.71L13.73 14.43C12.59 15.41 11.11 16 9.5 16C5.91 16 3 13.09 3 9.5C3 5.91 5.91 3 9.5 3C13.09 3 16 5.91 16 9.5C16 11.11 15.41 12.59 14.43 13.73L14.71 14H15.5L20.49 19L19 20.49L14 15.5ZM14 9.5C14 7.01 11.99 5 9.5 5C7.01 5 5 7.01 5 9.5C5 11.99 7.01 14 9.5 14C11.99 14 14 11.99 14 9.5Z"
                            fill="#666666" />
                    </g>
                    <defs>
                        <clipPath id="clip0_363_3510">
                            <rect width="24" height="24" fill="white" transform="matrix(0 1 1 0 0 0)" />
                        </clipPath>
                    </defs>
                </svg>
            </span>
            <!-- Clear search button -->
            <span *ngIf="searchTerm && searchTerm.trim()"
                  class="clear-search-icon"
                  (click)="clearSearch()"
                  role="button"
                  title="Clear search">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18 6L6 18M6 6L18 18" stroke="#666666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </span>
        </div>
        <div class="fc-blog-categories" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
            <span *ngIf="!hasSelectedCategory()">Choose Categories</span>
            <span *ngIf="hasSelectedCategory()" class="selected-category">{{ selectedCategory?.name }}</span>
            <span class="filter-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M11 21V15H13V17H21V19H13V21H11ZM3 19V17H9V19H3ZM7 15V13H3V11H7V9H9V15H7ZM11 13V11H21V13H11ZM15 9V3H17V5H21V7H17V9H15ZM3 7V5H13V7H3Z"
                        fill="#666666" />
                </svg>
            </span>
            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                <!-- Loading state for categories -->
                <li *ngIf="categoriesLoading">
                  <span class="dropdown-item-text">Loading categories...</span>
                </li>

                <!-- All categories option -->
                <li *ngIf="!categoriesLoading">
                  <a class="dropdown-item"
                     [class.active]="!hasSelectedCategory()"
                     (click)="clearCategoryFilter()"
                     role="button">
                    All Categories
                  </a>
                </li>

                <!-- Dynamic categories from API -->
                <ng-container *ngIf="!categoriesLoading">
                  <li *ngFor="let category of categories">
                    <a class="dropdown-item"
                       [class.active]="isCategorySelected(category)"
                       (click)="selectCategory(category)"
                       role="button">
                      {{ category.name }}
                    </a>
                  </li>
                </ng-container>

                <!-- No categories message -->
                <li *ngIf="!categoriesLoading && categories.length === 0">
                  <span class="dropdown-item-text">No categories available</span>
                </li>
              </ul>
        </div>
    </div>

    <!-- Loading state -->
    <div *ngIf="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>Loading blogs...</p>
    </div>

    <!-- Blog list -->
    <div *ngIf="!loading" class="fc-blog-list-row" [class.filtered]="hasActiveFilters()">
        <div *ngFor="let blog of getFilteredBlogs()" (click)="navigateToBlog(blog)">

            <div class="fc-blog-card">
                <img
                  [src]="getBlogImage(blog)"
                  [alt]="blog.title"
                  (error)="onImageError($event)"
                  loading="lazy" appImg/>
                <div class="fc-overlay-text">
                  <div class="post-date">Focile Inc • {{ formatDate(blog.publishedAt) }}</div>
                  <div class="fc-related-blog">
                    <span *ngFor="let tag of getTags(blog.tags)">{{ tag }}</span>
                  </div>
                  <div class="fc-related-name">
                    {{ blog.title }}
                    <span>
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M7 17L17 7M17 7H7M17 7V17"
                          stroke="white"
                          stroke-width="2"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                    </span>
                  </div>
                  <!-- Subtitle if available -->
                  <!-- <div *ngIf="blog.subTitle" class="fc-blog-subtitle">
                    {{ blog.subTitle }}
                  </div> -->
                </div>
              </div>
          </div>
    </div>

    <!-- No blogs message -->
    <div *ngIf="!loading && getFilteredBlogs().length === 0 && blogs.length > 0" class="no-blogs-message d-flex flex-column">
      <p *ngIf="searchTerm && searchTerm.trim() && !selectedCategory">
        No blogs found matching "{{ searchTerm }}".
      </p>
      <p *ngIf="!searchTerm && selectedCategory">
        No blogs found for the selected category.
      </p>
      <p *ngIf="searchTerm && searchTerm.trim() && selectedCategory">
        No blogs found matching "{{ searchTerm }}" in the selected category.
      </p>
      <div class="mt-2">
        <button *ngIf="searchTerm && searchTerm.trim()"
                class="btn btn-outline-primary btn-sm me-2"
                (click)="clearSearch()">
          Clear Search
        </button>
        <button *ngIf="selectedCategory"
                class="btn btn-outline-primary btn-sm me-2"
                (click)="clearCategoryFilter()">
          Clear Category
        </button>
        <button *ngIf="hasActiveFilters()"
                class="btn btn-primary btn-sm"
                (click)="clearAllFilters()">
          Show All Blogs
        </button>
      </div>
    </div>

    <!-- No blogs at all message -->
    <div *ngIf="!loading && blogs.length === 0" class="no-blogs-message">
      <p>No blogs available at the moment.</p>
    </div>
    <div class="col-sm-12 pt-3 mt-3 pb-3 mb-3 pt-sm-4 mt-sm-5 pb-sm-3 mb-sm-5">
        <!-- <button class="more-blog-btn">More Blog
            <svg width="34" height="35" viewBox="0 0 34 35" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_257_1630)">
                    <path d="M9.89219 17.7986H24.0343M24.0343 17.7986L16.9633 10.7275M24.0343 17.7986L16.9633 24.8697"
                        stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </g>
                <defs>
                    <clipPath id="clip0_257_1630">
                        <rect width="24" height="24" fill="white" transform="translate(16.9633 0.828125) rotate(45)" />
                    </clipPath>
                </defs>
            </svg>
        </button> -->
    </div>
</div>