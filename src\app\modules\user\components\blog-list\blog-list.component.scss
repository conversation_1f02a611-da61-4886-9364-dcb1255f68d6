.breadcumb {
  li {
    a {
      text-decoration: none;
    }

    &.breadcrumb-item + .breadcrumb-item::before {
      content: "-";
    }
  }
}

.fc-blog-search-filter {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.fc-blog-search {
  display: flex;
  position: relative;

  input {
    box-sizing: border-box;
    width: 350px;
    height: 56px;
    border: 1px solid rgba(102, 102, 102, 0.35);
    border-radius: 12px;
    background-color: white;
    color: black;
    font-size: 1rem;
    padding-left: 52px;
    padding-right: 3rem; // Add space for clear button

    &::placeholder {
      color: rgba(102, 102, 102, 0.6);
    }

    &:focus {
      outline: none;
      border-color: #014681;
      box-shadow: 0 0 0 2px rgba(1, 70, 129, 0.1);
    }
  }

  .search-icon {
    position: absolute;
    left: 1rem;
    top: 1rem;
    pointer-events: none;
  }

  .clear-search-icon {
    position: absolute;
    right: 1rem;
    top: 1.125rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba(102, 102, 102, 0.1);

      svg path {
        stroke: #014681;
      }
    }
  }
}

.fc-blog-categories {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 239px;
  height: 56px;
  border: 1px solid rgba(102, 102, 102, 0.35);
  border-radius: 12px;
  color: rgba(102, 102, 102, 0.6);
  font-size: 1rem;
  gap: 1rem;
  padding: 1rem;
  justify-content: center;
  background-color: white;
}

.fc-blog-list-row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin-top: 50px;
}

.fc-blog-card {
  position: relative;
  display: flex;
  height: 400px;
  overflow: hidden;
  border-radius: 20px;

  img {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    object-fit: cover;
    height: 100%;
  }

  .fc-overlay-text {
    position: relative;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      180deg,
      rgba(18, 18, 18, 0.0001) 0%,
      #121212 100%
    );
    padding-top: initial;
    padding-bottom: 0px;
    bottom: 0%;
    top: auto;
    display: flex;
    flex-direction: column;
    justify-content: end;
    padding: 1rem;
    color: white;
    gap: 1rem;

    .post-date {
      font-size: 14px;
      color: white;
      font-weight: 500;
    }

    .fc-related-blog {
      display: flex;
      flex-direction: row;
      gap: 10px;

      span {
        background: rgba(255, 255, 255, 0.28);
        border-radius: 19px;
        color: white;
        font-size: 14px;
        padding: 10px 1rem;
      }
    }

    .fc-related-name {
      width: 306px;
      font-style: normal;
      font-weight: 500;
      font-size: 25px;
      line-height: 35px;
      color: #ffffff;
    }
  }
}

.more-blog-btn {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 16px 32px;
  gap: 16px;
  width: 223.76px;
  height: 68.77px;
  background: #014681;
  box-shadow: 0px 548px 219px rgba(0, 0, 0, 0.01),
    0px 308px 185px rgba(0, 0, 0, 0.04), 0px 137px 137px rgba(0, 0, 0, 0.06),
    0px 34px 75px rgba(0, 0, 0, 0.07), 0px 0px 0px rgba(0, 0, 0, 0.07);
  border-radius: 100px;
  border: none;
  justify-content: center;
  color: white;
  margin: 0px auto;
  font-size: 20px;
  font-weight: 600;
}

.fc-blog-categories {
  ul {
    width: 239px;
  }
}

@media (max-width: 768px) {
  .fc-blog-search-filter {
    flex-direction: column;
    gap: 0.5rem;
  }
  .fc-blog-categories {
    width: 100%;
  }
  .fc-blog-list-row {
    margin-top: 1.5rem;
  }
  .fc-blog-list-row {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  .fc-blog-card .fc-overlay-text .fc-related-name {
    font-size: 1.25rem;
    line-height: 1.5rem;
  }
}

// Loading state styles
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #014681;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  p {
    color: #666;
    font-size: 16px;
    margin: 0;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// No blogs message styles
.no-blogs-message {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;

  p {
    color: #666;
    font-size: 18px;
    margin: 0;
  }
}

// Blog subtitle styles
.fc-blog-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin-top: 0.5rem;
  line-height: 1.4;
}

// Enhanced blog card hover effect
.fc-blog-card {
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }
}

// Category dropdown styles
.dropdown-menu {
  .dropdown-item {
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background-color: #f8f9fa;
      color: #014681;
    }

    &.active {
      background-color: #014681;
      color: white;
      font-weight: 600;

      &:hover {
        background-color: #013a6b;
        color: white;
      }
    }
  }

  .dropdown-item-text {
    color: #6c757d;
    font-style: italic;
    padding: 0.375rem 1rem;
  }
}

// Category filter indicator
.fc-blog-list-row {
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: -10px;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #014681 0%, transparent 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &.filtered::before {
    opacity: 1;
  }
}

// Enhanced no blogs message
.no-blogs-message {
  .btn {
    border-radius: 20px;
    padding: 0.5rem 1.5rem;
    font-size: 14px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(1, 70, 129, 0.3);
    }
  }
}

// Selected category styling
.fc-blog-categories {
  .selected-category {
    color: #014681;
    font-weight: 600;
  }

  // Enhanced dropdown styling
  transition: all 0.3s ease;

  &:hover {
    background-color: #f8f9fa;
    border-color: #014681;
  }

  &[aria-expanded="true"] {
    background-color: #014681;
    color: white;

    .selected-category {
      color: white;
    }

    .filter-icon svg path {
      fill: white;
    }
  }
}

// Category tooltip styling
.dropdown-item[title] {
  position: relative;

  &:hover::after {
    content: attr(title);
    position: absolute;
    left: 100%;
    top: 0;
    background: #333;
    color: white;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    margin-left: 0.5rem;
    max-width: 200px;
    word-wrap: break-word;
    white-space: normal;
  }
}
