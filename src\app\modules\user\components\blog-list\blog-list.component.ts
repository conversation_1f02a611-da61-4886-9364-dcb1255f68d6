import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { finalize, catchError, of } from 'rxjs';

// Blog interface based on API response
interface Blog {
  id: string;
  title: string;
  subTitle: string;
  slug: string;
  content: string | null;
  imageUrl: string | null;
  tags: string;
  publishedAt: string;
  likeCount?: number;
  viewCount?: number;
  isLiked?: boolean;
}

// Blog Category interface based on API response
interface BlogCategory {
  id: string;
  name: string;
  slug: string;
  description: string;
  isActive: boolean;
  createdAt: string;
}

@Component({
  selector: 'app-blog-list',
  templateUrl: './blog-list.component.html',
  styleUrls: ['./blog-list.component.scss']
})
export class BlogListComponent implements OnInit {
  blogs: Blog[] = [];
  categories: BlogCategory[] = [];
  selectedCategory: BlogCategory | null = null;
  searchTerm: string = '';
  loading = false;
  categoriesLoading = false;
  defaultBlogImage = 'assets/images/e-card-default.svg'; // Default image path

  constructor(
    private accountService: AccountService,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.loadBlogs();
    this.loadCategories();
  }

  loadBlogs(): void {
    this.loading = true;
    this.accountService.getAllBlogs()
      .pipe(
        catchError((error) => {
          console.error('Error loading blogs:', error);
          // Return empty array instead of letting error propagate
          return of([]);
        }),
        finalize(() => this.loading = false)
      )
      .subscribe({
        next: (response: Blog[]) => {
          this.blogs = response || [];
          console.log('Blogs loaded:', this.blogs);
        },
        error: (error) => {
          console.error('Unexpected error loading blogs:', error);
          this.blogs = [];
        }
      });
  }

  loadCategories(): void {
    this.categoriesLoading = true;
    this.accountService.getAllBlogCategories()
      .pipe(
        catchError((error) => {
          console.error('Error loading categories:', error);
          // Return empty array instead of letting error propagate
          return of([]);
        }),
        finalize(() => this.categoriesLoading = false)
      )
      .subscribe({
        next: (response: BlogCategory[]) => {
          // Filter only active categories
          this.categories = (response || []).filter(category => category.isActive);
          console.log('Categories loaded:', this.categories);
        },
        error: (error) => {
          console.error('Unexpected error loading categories:', error);
          this.categories = [];
        }
      });
  }

  // Get blog image with fallback to default
  getBlogImage(blog: Blog): string {
    return blog.imageUrl || this.defaultBlogImage;
  }

  // Format published date
  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  // Parse tags string into array
  getTags(tagsString: string): string[] {
    if (!tagsString) return [];
    return tagsString.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
  }

  // Handle image load error
  onImageError(event: any): void {
    event.target.src = this.defaultBlogImage;
  }



  // Category filtering methods
  selectCategory(category: BlogCategory): void {
    this.selectedCategory = category;
    console.log('Selected category:', category);
  }

  // Get filtered blogs based on selected category and search term
  getFilteredBlogs(): Blog[] {
    let filteredBlogs = this.blogs;

    // Filter by search term (tags and title)
    if (this.searchTerm && this.searchTerm.trim()) {
      const searchLower = this.searchTerm.toLowerCase().trim();
      filteredBlogs = filteredBlogs.filter(blog => {
        // Search in title
        const titleMatch = blog.title.toLowerCase().includes(searchLower);

        // Search in tags
        const blogTags = this.getTags(blog.tags);
        const tagMatch = blogTags.some(tag =>
          tag.toLowerCase().includes(searchLower)
        );

        return titleMatch || tagMatch;
      });
    }

    // Filter by selected category
    if (this.selectedCategory) {
      filteredBlogs = filteredBlogs.filter(blog => {
        const blogTags = this.getTags(blog.tags);
        const categoryName = this.selectedCategory!.name.toLowerCase();
        const categorySlug = this.selectedCategory!.slug.toLowerCase();

        return blogTags.some(tag => {
          const tagLower = tag.toLowerCase();
          return tagLower.includes(categoryName) ||
            categoryName.includes(tagLower) ||
            tagLower.includes(categorySlug) ||
            categorySlug.includes(tagLower);
        });
      });
    }

    return filteredBlogs;
  }

  // Clear category filter
  clearCategoryFilter(): void {
    this.selectedCategory = null;
  }

  // Check if category is selected
  isCategorySelected(category: BlogCategory): boolean {
    return this.selectedCategory?.id === category.id;
  }

  // Check if any category is selected
  hasSelectedCategory(): boolean {
    return this.selectedCategory !== null;
  }

  // Check if any filters are active
  hasActiveFilters(): boolean {
    return this.selectedCategory !== null || (this.searchTerm && this.searchTerm.trim().length > 0) === true;
  }

  // Clear search term
  clearSearch(): void {
    this.searchTerm = '';
  }

  // Clear all filters
  clearAllFilters(): void {
    this.selectedCategory = null;
    this.searchTerm = '';
  }

  // Navigate to blog detail page
  navigateToBlog(blog: Blog): void {
    if (blog.slug) {
      this.router.navigate(['/blog-detail', blog.slug]);
    } else {
      // Fallback to ID if slug is not available
      this.router.navigate(['/blog-detail', blog.id]);
    }
  }

  // Format count for display (e.g., 1200 -> 1.2K)
  formatCount(count: number): string {
    if (count >= 1000000) {
      return (count / 1000000).toFixed(1) + 'M';
    } else if (count >= 1000) {
      return (count / 1000).toFixed(1) + 'K';
    }
    return count.toString();
  }
}
