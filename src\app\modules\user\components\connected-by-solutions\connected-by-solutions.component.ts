import { Component, OnInit } from '@angular/core';
import { OwlOptions } from 'ngx-owl-carousel-o';
import { SolutionService } from 'src/app/shared/services/solution.service';

@Component({
  selector: 'app-connected-by-solutions',
  template: `
    <section class="solutions-container" *ngIf="solutions.length">
      <div class="fc-container">
        <div class="fc-only-header">
          <div class="left-content">
            <h3>Connect by Solution</h3>
            <p>
              These are the top solutions you can select from to uncover your
              partners of choice.
            </p>
          </div>
        </div>
        <div class="fc-easy-card-row">
          <owl-carousel-o [options]="carouselOptions" class="full-width">
            <ng-template carouselSlide *ngFor="let card of solutions">
              <div
                class="fc-easy-card"
                [routerLink]="['/solution', card.idGuid]"
                role="button"
              >
                <figure>
                  <!-- <img [src]="card.image" > -->
                  <img
                    [src]="card.image"
                    appImg
                    viewType="white-logo"
                    type="white"
                    style="width: 35%"
                    alt="{{ card.text }}"
                  />
                </figure>
                <div>
                  <h5 [innerHTML]="card.text"></h5>
                </div>
                <a
                  class="read-more-btn"
                  [routerLink]="['/solution', card.idGuid]"
                >
                  {{ card.buttonText || 'More' }}
                </a>
              </div>
            </ng-template>
          </owl-carousel-o>
        </div>
      </div>
    </section>
  `,
  styleUrls: ['./connected-by-solutions.component.scss'],
})
export class ConnectedBySolutionsComponent implements OnInit {
  itemsPerSlide = 5;
  singleSlideOffset = true;
  errorImage = './assets/svgs/focile-white.svg';
  slides = [
    {
      image: './assets/png/customer-service.svg',
      text: `Video <br />  Conference`,
    },
    { image: './assets/png/headphones.svg', text: `Headsets` },
    {
      image: './assets/png/old-typical-phone.svg',
      text: `Buisness <br />  Phones`,
    },
    {
      image: './assets/png/virtual-meeting.svg',
      text: `Video <br /> Conferencing`,
    },
    { image: './assets/png/process.svg', text: `Software` },
    { image: './assets/png/service.svg', text: `Services` },
  ];
  solutions: any[] = [];
  constructor(private readonly solution: SolutionService) {}

  ngOnInit(): void {
    this.updateItemsPerSlide();
  }

  getSolutions() {
    this.solution.solutions$.subscribe((solutionsResponse: any) => {
      if (solutionsResponse.data) {
        this.solutions = solutionsResponse.data.map((solution: any) => {
          solution.image = solution.description;
          solution.text = solution.name;
          return solution;
        });
      }
    });
  }

  updateItemsPerSlide() {
    const width = window.innerWidth;

    if (width >= 1200) {
      this.itemsPerSlide = 3;
    } else if (width >= 992) {
      this.itemsPerSlide = 3;
    } else if (width >= 768) {
      this.itemsPerSlide = 2;
    } else {
      this.itemsPerSlide = 1;
    }
  }

  carouselOptions: OwlOptions = {
    loop: false,
    margin: 50,
    center: false,
    dots: false,
    autoplay: true,
    nav: true,
    navText: [
      '<img src="../../../../../assets/images/arrow-left.svg" alt="Previous" class="custom-nav-arrow" />',
      '<img src="../../../../../assets/images/black-arrow.svg" alt="Next" class="custom-nav-arrow" />',
    ],
    // autoplayTimeout: 10000,
    responsive: {
      0: { items: 1 },
      600: { items: 3 },
      1000: { items: 3 },
    },
    // autoWidth: true,
  };
}
