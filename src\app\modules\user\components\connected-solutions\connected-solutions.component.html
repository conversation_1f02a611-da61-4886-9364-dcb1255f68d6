<div class="solutions-container" *ngIf="solutions.length">
    <div class="fc-container hide-text">Welcome to</div>
    <div class="fc-what-we-do-container fc-container">
        <div class="fc-only-header">
            <div class="left-content">
                <h3>Connect by Solution</h3>
                <p>These are the top solutions you can select from to uncover your partners of choice.</p>
            </div>
        </div>
        <div class="fc-easy-card-row">
            <owl-carousel-o [options]="carouselOptions" class="full-width">
                <ng-template carouselSlide *ngFor="let card of solutions">
                    <div class="fc-easy-card" [routerLink]="['/solution', card.idGuid]" role="button">
                        <figure>
                            <!-- <img [src]="card.image" > -->
                            <img [src]="card.image" appImg viewType="white-logo" type="white" style="width: 35%"
                                alt="{{ card.text }}" />
                        </figure>
                        <div>
                            <h5 [innerHTML]="card.text"></h5>
                        </div>
                        <a class="read-more-btn" [routerLink]="['/solution', card.idGuid]">
                            {{ card.buttonText || 'More' }}
                        </a>
                    </div>
                </ng-template>
            </owl-carousel-o>
        </div>
    </div>
</div>