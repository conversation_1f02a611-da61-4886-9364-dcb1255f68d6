import {
  Component,
  ElementRef,
  OnInit,
  Renderer2,
  ViewChild,
} from '@angular/core';
import { OwlOptions } from 'ngx-owl-carousel-o';
import { SolutionService } from 'src/app/shared/services/solution.service';

@Component({
  selector: 'app-connected-solutions',
  templateUrl: './connected-solutions.component.html',
  styleUrls: ['./connected-solutions.component.scss'],
})
export class ConnectedSolutionsComponent implements OnInit {
  @ViewChild('fcSlideCategories') fcSlideCategories!: ElementRef;
  @ViewChild('fcSlideCategories1') fcSlideCategories1!: ElementRef;
  @ViewChild('fcSlideCategories2') fcSlideCategories2!: ElementRef;
  @ViewChild('fcContainer') fcContainer: ElementRef | undefined;
  marginLeft: number = 0;

  constructor(
    private renderer: Renderer2,
    private readonly solution: SolutionService
  ) {}
  carouselOptions: OwlOptions = {
    loop: false,
    margin: 50,
    center: false,
    dots: false,
    autoplay: true,
    nav: true,
    navText: [
      '<img src="../../../../../assets/images/arrow-left.svg" alt="Previous" class="custom-nav-arrow" />',
      '<img src="../../../../../assets/images/black-arrow.svg" alt="Next" class="custom-nav-arrow" />',
    ],
    // autoplayTimeout: 10000,
    responsive: {
      0: { items: 1 },
      600: { items: 3 },
      1000: { items: 3 },
    },
    // autoWidth: true,
  };

  ngAfterViewChecked(): void {
    this.calculateMarginLeft();
  }

  calculateMarginLeft(): void {
    if (this.fcContainer && this.fcSlideCategories) {
      const rect = this.fcContainer.nativeElement.getBoundingClientRect();
      this.marginLeft = rect.left;
      this.renderer.setStyle(
        this.fcSlideCategories1.nativeElement,
        'margin-left',
        `${this.marginLeft}px`
      );
      const calculatedWidth = `calc(100vw - ${this.marginLeft}px)`;
      this.renderer.setStyle(
        this.fcSlideCategories.nativeElement,
        'width',
        calculatedWidth
      );
    }
  }

  singleSlideOffset = true;
  errorImage = './assets/svgs/focile-white.svg';
  solutions: any[] = [];

  ngOnInit(): void {
    this.getSolutions();
  }

  getSolutions() {
    this.solution.solutions$.subscribe((solutionsResponse: any) => {
      if (solutionsResponse.data) {
        this.solutions = solutionsResponse.data.map((solution: any) => {
          solution.image = solution.description;
          solution.text = solution.name;
          return solution;
        });
      }
    });
  }


}
