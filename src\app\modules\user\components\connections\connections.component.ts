import { Component } from '@angular/core';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-connections',
  templateUrl: './connections.component.html',
  styleUrls: ['./connections.component.scss']
})
export class ConnectionsComponent {
  isLoggedIn$: Observable<boolean>;

  accordionItems = [
    {
      title: 'How to use our platform?', content: `Lorem Ipsum is simply dummy text of the printing and typesetting industry.
Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type
and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised.
` },
    { title: 'Do you offer custom invoicing?', content: 'This is the content for item 2.' },
    { title: 'What are the limits for each plan?', content: 'This is the content for item 3.' },
  ];

  constructor(private accountService: AccountService) {
    this.isLoggedIn$ = this.accountService.isLoggedIn$;
  }
}
