<div class="fc-container">
  <div class="breadcumb my-4">
      <nav aria-label="breadcrumb">
          <ol class="breadcrumb">
              <li class="breadcrumb-item"><a routerLink="/">Home</a></li>
              <li class="breadcrumb-item active" aria-current="page">Contact us</li>
          </ol>
      </nav>
  </div>
</div>
  <div class="fc-container">
    <div class="mb-3 mb-sm-5 fc-contact-header">      
        <h5>How can we help ?</h5>
        <span>Focile is here to help you facilitate your connections</span>
        <p class="m-auto lh-lg text-muted mb-0 mb-sm-5">
          Please submit your request below and one of our team members will reach
          out to you soon.
        </p>
    </div>
      <section class="technical-support-container">
          <form [formGroup]="contactUsForm">
            <div class="row">
              <div class="col-md-4">
                <div class="form-group mb-3">
                  <label for="firstname" class="form-label"
                    >First Name <span class="required">*</span></label
                  >
                  <input
                    type="text"
                    class="form-control"
                    name="firstname"
                    id="firstname"
                    placeholder="Enter your first name"
                    formControlName="firstName"
                  />
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group mb-3">
                  <label for="lastname" class="form-label"
                    >Last Name <span class="required">*</span></label
                  >
                  <input
                    type="text"
                    class="form-control"
                    name="lastname"
                    id="lastname"
                    placeholder="Enter your last name"
                    formControlName="lastName"
                  />
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group mb-3">
                  <label for="mobile" class="form-label"
                    >Mobile No <span class="required">*</span></label
                  >
                  <input
                    type="text"
                    class="form-control"
                    name="mobile"
                    id="mobile"
                    placeholder="Enter your mobile number"
                    formControlName="mobileNumber"
                  />
                </div>
              </div>
            </div>
            <div class="row mt-0 mt-sm-3">
              <div class="col-md-4">
                <div class="form-group mb-3">
                  <label for="email" class="form-label"
                    >Email <span class="required">*</span></label
                  >
                  <input
                    type="text"
                    class="form-control"
                    name="email"
                    id="email"
                    placeholder="Enter your email"
                    formControlName="email"
                  />
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group mb-3">
                  <label for="company" class="form-label"
                    >Company <span class="required">*</span></label
                  >
                  <input
                    type="text"
                    class="form-control"
                    name="company"
                    id="company"
                    placeholder="Enter your company"
                    formControlName="company"
                  />
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group mb-3">
                  <label for="jobTitle" class="form-label"
                    >Job Title <span class="required">*</span></label
                  >
                  <ng-select
                    [items]="jobTitles"
                    [searchable]="true"
                    [addTag]="true"
                    bindLabel="name"
                    bindValue="id"
                    placeholder="Select a job title or enter new one"
                    [addTagText]="'Add Job Title'"
                    (add)="onAdd($event)"
                    [closeOnSelect]="true"
                    (change)="handlechange($event)"
                    [loading]="jobTitlesLoading"
                  ></ng-select>
                </div>
              </div>
            </div>
            <div class="row mt-0 mt-sm-3">
              <div class="col-md-6">
                <div class="form-group mb-3">
                  <label for="country" class="form-label"
                    >Country <span class="required">*</span></label
                  >
                <ng-select
                  [items]="countries"
                  [placeholder]="'placeholder'"
                  [class]="'h-48'"
                  formControlName="country"
                ></ng-select>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="productIntrust" class="form-label"
                    >Area of Interest</label
                  >
                  <focile-dropdown
                    class="w-100"
                    [placeholder]="'Select'"
                    [items]="productInterests"
                    [loading]="productInterestsLoading"
                    bindLabel="name"
                    bindValue="id"
                    formControlName="productInterest"
                  ></focile-dropdown>
                </div>
              </div>
            </div>
              <div class="col-md-12 mt-3 mt-sm-0">
                <div class="form-group">
                  <label for="comment" class="form-label"
                    >Comment <span class="required">*</span></label
                  >
                  <textarea
                    name="comment"
                    class="form-control"
                    id="commect"
                    cols="30"
                    rows="10"
                    formControlName="comment"
                  ></textarea>
                </div>
              </div>
              <div class="col-md-12 mt-3">
                <div class="form-check">
                  <input
                    class="form-check-input"
                    type="checkbox"
                    value=""
                    id="isAcceptPrivacy"
                    formControlName="isAcceptPrivacy"
                  />
                  <label class="form-check-label" for="isAcceptPrivacy">
                    I have read and agreed to Focile <a href="#" routerLink="/terms-and-condtions">terms of use</a> and
                    <a href="#" routerLink="/privacy-policy"> Privacy Policy</a>.
                  </label>
                </div>
              </div>
              <div class="col-md-12 mt-3">
                <div class="form-check">
                  <input
                    class="form-check-input"
                    type="checkbox"
                    value=""
                    id="isAllowContact"
                    formControlName="isAllowContact"
                  />
                  <label class="form-check-label" for="isAllowContact">
                    Focile may contact me with personalized offers, support or firmware updates, and news by email.
                  </label>
                </div>
              </div>
              <div class="col-md-12 d-flex justify-content-between mt-3">
                <div></div>
                
                <focile-button
                  [btnType]="'primary'"
                  [loading]="false"
                  [disabled]="contactUsForm.invalid"
                  (onClick)="handleSubmit()"
                >
                  Submit</focile-button
                >
              </div>
          </form>
      </section>

      <div class="fc-contact-address">
        <div class="cont-if-box">
          <label>Contact Info</label>
          <h3>We are always<br>
            happy to assist you</h3>
        </div>
        <div class="fc-cont-info">
          <div class="fc-lf">
            <label>Corporate Email</label>
            <div class="dash"></div>
            <label>contact&#64;focile.com</label>
            <p>Assistance hours: <br>
              Monday - Friday 6 am to 8 pm EST</p>
          </div>

          <div class="fc-lf">
            <label>Technical Assistance</label>
            <div class="dash"></div>
            <label>help&#64;focile.com</label>
            <p>Assistance hours: <br>
              Monday - Friday 6 am to 8 pm EST</p>
          </div>
        </div>
      </div>
  </div>   