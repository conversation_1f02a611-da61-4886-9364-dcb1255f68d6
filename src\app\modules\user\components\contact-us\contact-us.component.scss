.breadcumb{
    li{
      a{
        text-decoration: none;
      }
  
      &.breadcrumb-item + .breadcrumb-item::before{
        content:'-';
      }
    }
  }

.fc-contact-header{
    h5{
    width: 100%;    
    font-style: normal;
    font-weight: 700;
    font-size: 48px;
    line-height: 120%;
    color: #000000;
    }
    span{
        font-style: normal;
        font-weight: 400;
        font-size: 28px;
        line-height: 48px;
        color: #000000;
        margin-top: 1rem;
    }

    p{
        font-style: normal;
        font-weight: 300;
        font-size: 16px;
        line-height: 30px;
        color: #000000;
        opacity: 0.6; 
    }

}

.technical-support-container{
    box-sizing: border-box;
    background: #FFFFFF;
    border: 1px solid rgba(140, 140, 140, 0.2);
    border-radius: 18px;
    padding: 35px;
    position: relative;
    z-index: 1;

    label{
        font-weight: 400;
        color: black;
        font-size: 1rem;
    }

        ::ng-deep {
            input[type="text"] {
                box-sizing: border-box;
                width: 100%;                
                background: #F6F6F6;
                border: 1px solid rgba(246, 246, 245, 0.4);
                border-radius: 12px;
                flex: none;
                order: 1;
                align-self: stretch;
                flex-grow: 0;
                padding-inline: 1rem;

                &::placeholder{
                    color: rgba(102, 102, 102, 0.60);
                }
            }

            .ng-select-searchable{
                box-sizing: border-box;
                width: 100%;                
                background: #F6F6F6;
                border: 1px solid rgba(246, 246, 245, 0.4);     
                border-radius: 12px;            
                padding-inline: 6px;

                &::placeholder{
                    color: rgba(102, 102, 102, 0.60);
                }
            }
            .ng-select.ng-select-single .ng-select-container{
                background: transparent;
                border: none;
            }
            .ng-select .ng-select-container .ng-value-container .ng-input>input{
                height: 42px;
            }

            textarea.form-control{
                box-sizing: border-box;
                width: 100%;                
                background: #F6F6F6;
                border: 1px solid rgba(246, 246, 245, 0.4);
                border-radius: 12px;
                resize: none;  padding-inline: 1rem;
                &::placeholder{
                    color: rgba(102, 102, 102, 0.60);
                }
            }

            button{
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    gap: 16px;
                    isolation: isolate;
                    min-width: 197px;
                    height: 61px;
                    background: #014681;
                    box-shadow: 0px 548px 219px rgba(0, 0, 0, 0.01), 0px 308px 185px rgba(0, 0, 0, 0.04), 0px 137px 137px rgba(0, 0, 0, 0.06), 0px 34px 75px rgba(0, 0, 0, 0.07), 0px 0px 0px rgba(0, 0, 0, 0.07);
                    border-radius: 100px;
                    text-align: center;
                    justify-content: center;    
                    font-weight: 400 !important;
                    font-size: 1.2rem;                    
            }
        }
}

.fc-contact-address{
    display: flex;
    flex-direction: row;
    margin: 100px 0px;
    align-items: center;
    justify-content: space-between;


    .fc-cont-info{
        display: flex;
    }

    .cont-if-box{
        display: flex;
        flex-direction: column;
        width: 50%;

        h3{
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 0px;
        }

        label{
            font-size:1.2rem;
            color: black;
        }
    }
    .fc-cont-info{
        width: 50%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
    }

    .fc-lf{

        .dash{
            width: 27px;
            height: 3px;
            background-color: black;
            margin: 25px 0px;
        }

        label{
            font-size: 1.2rem;
            color: black;
            font-weight: 500;
        }

        p{
            margin-top:25px;
            max-width: 246px;
            margin-bottom: 0px;
        }
    }
}

@media(max-width:768px){
    .fc-contact-header{
        h5{
            font-size: 1.25rem;
        }
        span{
            font-size: 1rem;
            line-height: 1.5rem;
        }
        p{
            font-size: 14px;
            line-height: 2;
        }
    }
    .technical-support-container{
        padding: 1.5rem 1rem;
        ::ng-deep button{
            font-size: 1rem;
            height: 45px;
        }
    }
    .fc-contact-address{
        flex-direction: column;
        margin: 1rem 0px;
        gap: 1rem;

        .cont-if-box{
            width: 100%;

            h3{
                font-size: 1.25rem;
            }
            label{
                font-size: 1rem;
            }
        }
        .fc-cont-info{
            width: 100%;
            flex-direction: column;
            gap: 1rem;
        }
        .fc-lf{
            label{
                font-size: 1rem;
            }
            .dash{
                margin: 1rem 0;
            }
            p{
                margin-top: 5px;
            }
        }
    }


    ::ng-deep focile-button{
        width: 100%;
        button{
            width: 100%;
            height: 50px;
        }
    }

}