import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { finalize } from 'rxjs';

@Component({
  selector: 'app-contact-us',
  templateUrl: './contact-us.component.html',
  styleUrls: ['./contact-us.component.scss'],
})
export class ContactUsComponent implements OnInit {
  contactUsForm!: FormGroup;
  jobTitle = [];
  productInterests = [];
  productInterestsLoading = false;
  countries: any = [];
  jobTitles: any = [];
  jobTitlesLoading: any = [];
  constructor(
    private formBuilder: FormBuilder,
    private http: HttpClient,
    private readonly toaster: ToastrService
  ) {}

  ngOnInit() {
    this.getProductOfInterest();
    this.getCountryLookup();
    this.getJobTitleLookup();
    this.contactUsForm = this.formBuilder.group({
      firstName: [null, Validators.required],
      lastName: [null, Validators.required],
      email: [null, [Validators.required, Validators.email]],
      mobileNumber: [null, Validators.required],
      jobTitle: [null, Validators.required],
      productInterest: [1, Validators.required],
      company: [null, Validators.required],
      country: [null, Validators.required],
      comment: [null, Validators.required],
      isAcceptPrivacy: [false, Validators.requiredTrue],
      isAllowContact: [false, Validators.requiredTrue],
    });
  }

  getProductOfInterest() {
    this.productInterestsLoading = true;
    this.http
      .get('ContactUs/GetProductInterest')
      .pipe(finalize(() => (this.productInterestsLoading = false)))
      .subscribe((response: any) => {
        this.productInterests = response.data;
      });
  }

  getJobTitleLookup() {
    this.jobTitlesLoading = true;
    this.http
      .get('ContactUs/GetJobTitle')
      .pipe(finalize(() => (this.jobTitlesLoading = false)))
      .subscribe((response: any) => {
        this.jobTitles = response.data;
        this.jobTitles = this.jobTitles.map((x: any) => {
          return {
            id: x,
            name: x,
          };
        });
      });
  }

  getCountryLookup() {
    this.http.get<any>('ContactUs/GetAllCountries').subscribe(
      (response) => {
        // Handle successful response
        this.countries = response;
      },
      (error) => {
        // Handle error
        console.error('Error occurred:', error);
      }
    );
  }

  handleSubmit() {
    this.http
      .post('ContactUs/AddContactUs', this.contactUsForm.value)
      .subscribe((response: any) => {
        this.contactUsForm.reset();
        this.contactUsForm.patchValue({
          firstName: null,
          lastName: null,
          email: null,
          mobileNumber: null,
          jobTitle: null,
          productInterest: [1],
          company: null,
          country: [null],
          comment: null,
          isAcceptPrivacy: false,
          isAllowContact: false,
        });
        this.toaster.success(
          'Thank you! Your feedback is highly valuable to us!  We will review your input and connect with you asap.'
        );
      });
  }

  onAdd($event: any) {
    this.contactUsForm.patchValue({
      jobTitle: $event,
    });
  }

  handlechange(ev: any) {
    this.contactUsForm.patchValue({
      jobTitle: ev.name,
    });
  }
}
