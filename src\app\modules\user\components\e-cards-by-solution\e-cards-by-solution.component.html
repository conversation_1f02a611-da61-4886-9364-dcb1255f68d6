<div class="landing-page">
  <app-focile-banners *ngIf="!(isLoggedIn | async)"></app-focile-banners>

  <div class="container-fluid mt-5">
    <div class="d-flex justify-content-center pt-3">
      <img
        class="search-focile-logo"
        src="./assets/svgs/focile.svg"
        alt="focile-logo"
      />
    </div>

    <div class="text-center mb-5">
      <div class="text-center text-primary mb-5">
        <span> We found companies for your needs </span>
      </div>
    </div>
  </div>

  <div class="fc-container">
    <div class="row p-0">
      <ng-container *ngIf="!companiesLoading">
        <div class="col-md-4" *ngFor="let company of companies | slice : 0 : 3">
          <app-e-card [company]="company"></app-e-card>
        </div>
      </ng-container>
      <ng-container *ngIf="companiesLoading">
        <div class="col-md-12 text-center">
          <app-spinner></app-spinner>
        </div>
      </ng-container>
    </div>
  </div>  
  <!-- <app-connected-by-solutions></app-connected-by-solutions>  
  <app-recently-joined-partners></app-recently-joined-partners> -->

  <app-connected-solutions *ngIf="isLoggedIn | async"></app-connected-solutions>
  <div class="my-5">
    <app-recent-joined-partners-testimonail></app-recent-joined-partners-testimonail>
  </div>

  <app-bulk-invite *ngIf="isLoggedIn"></app-bulk-invite>
  <app-our-blog *ngIf="isLoggedIn"></app-our-blog>
  <!-- <app-connected-solutions></app-connected-soluticompaniesons> -->
  <!-- <app-free-trial-promote></app-free-trial-promote> -->
  </div>
