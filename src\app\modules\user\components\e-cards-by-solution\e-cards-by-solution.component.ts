import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { forkJoin } from 'rxjs';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { filterNonNull } from 'src/app/shared/oprators/filter-null-values';

@Component({
  selector: 'app-e-cards-by-solution',
  templateUrl: './e-cards-by-solution.component.html',
  styleUrls: ['./e-cards-by-solution.component.scss'],
})
export class ECardsBySolutionComponent implements OnInit {
  companiesLoading = false;
  companies: any[] = [];
  isLoggedIn = this.account.isLoggedIn$.asObservable();
  mainFilters: any = {
    productIds: null,
    serviceIds: null,
    solutionIds: null,
    industryIds: null,
    isCertified: true,
    role: null,
    countryId: null,
    searchQuery: '',
  };
  userState$: any;
  constructor(
    private http: HttpClient,
    private activatedRounte: ActivatedRoute,
    private account: AccountService
  ) {}

  ngOnInit(): void {
    this.account.user$.pipe(filterNonNull()).subscribe((response) => {
      this.userState$ = response;
      this.activatedRounte.params.subscribe((response) => {
        const id = response.id;
        this.mainFilters.solutionIds = id;
        this.getUsers();
      });
    });
  }

  getUsers() {
    if (!this.userState$.userId) return;
    const filteredParams = Object.fromEntries(
      Object.entries(this.mainFilters).filter(([_, value]) => value)
    );
    const apis = [
      this.http.get('ECard/V2/GetCardList', {
        params: {
          loginUserId: this.userState$.userId,
          expertType: 1,
          pageNumber: 1,
          pageSize: 1000,
          ...filteredParams,
        },
      }),
      this.http.get('ECard/V2/GetCardList', {
        params: {
          loginUserId: this.userState$.userId,
          expertType: 2,
          pageNumber: 1,
          pageSize: 1000,
          ...filteredParams,
        },
      }),
      this.http.get('ECard/V2/GetCardList', {
        params: {
          loginUserId: this.userState$.userId,
          expertType: 3,
          pageNumber: 1,
          pageSize: 1000,
          ...filteredParams,
        },
      }),
      this.http.get('ECard/V2/GetCardList', {
        params: {
          loginUserId: this.userState$.userId,
          expertType: 4,
          pageNumber: 1,
          pageSize: 1000,
          ...filteredParams,
        },
      }),
    ];
    forkJoin(apis).subscribe((response) => {
      console.log({ response });
      response.forEach((x: any) => {
        this.companies = [...this.companies, ...x.data];
      });
    });
  }
}
