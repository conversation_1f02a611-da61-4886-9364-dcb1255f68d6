.fc-only-header{
    width: 100%;
    display: flex;
    flex-direction: row;
    margin-bottom: 2.5rem;

    label{
        color: #014681;        
        min-width: 397px;
        height: 28px;
        font-style: normal;
        font-weight: 700;
        font-size: 23px;
        line-height: 120%;
        letter-spacing: 0.2em;
        text-transform: uppercase;
        color: #014681;
        flex: none;
        order: 0;
        flex-grow: 0;
    }

    h3{
        min-width: 825px;
        font-style: normal;
        font-weight: 700;
        font-size: 44px;
        line-height: 120%;
        color: #191825;
        flex: none;
        order: 1;
        flex-grow: 0;
        margin-bottom: 20px;
    }

    p{
        max-width: 825px;
        font-style: normal;
        font-weight: 300;
        font-size: 16px;
        line-height:1.5;
        color: rgba(25, 24, 37, 0.5);
        flex: none;
        order: 1;
        flex-grow: 0;
    }
}

.fc-easy-card-row{
    display: flex;
    flex-direction: row;
    height: auto;
    gap: 2.5rem; 
    max-width: 100vw;

    .fc-easy-card{
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        width: 100%;
        background: #FFFFFF;                
        // box-shadow: 0px 513px 205px rgba(0, 0, 0, 0.01), 0px 288px 173px rgba(0, 0, 0, 0.03), 0px 128px 128px rgba(0, 0, 0, 0.04), 0px 32px 71px rgba(0, 0, 0, 0.05), 0px 0px 0px rgba(0, 0, 0, 0.05);
        border-radius:32px;
        flex: none;
        order: 1;
        flex-grow: 0;
        border: 1px solid rgb(0 0 0 / 20%);
        overflow: hidden;
        border-radius:32px 32px 20px;


        figure{
            border: none;
            margin: 0px;
            height: 350px;
            width:100%;
        }
        img{
            height: 350px;        
            object-fit:cover;
        }

        div{
            padding: 2rem;

            p{                
                max-width: 100%;
                min-height:auto;                
                font-style: normal;
                font-weight: 400;
                font-size: 16px;
                line-height: 160%;
                color: rgba(25, 24, 37, 0.75);
                flex: none;
                order: 1;
                flex-grow: 0;
                margin-bottom: 0px;
            }
            h4{
                color: #191825;
                font-size: 23px;
                margin-bottom: 1rem;
            }
        }
    }
}

.full-width{
    width: 100%;    

    ::ng-deep .owl-carousel.owl-drag .owl-item{
        // transform:inherit;
        // transition:inherit;
        display: flex;
        justify-content: start;
        align-items: center;
        opacity: 1;        
        align-items: stretch;
    
        &.active.center {
            transform: inherit;
            opacity: 1;
            z-index: 10;
          }
      }
      ::ng-deep .owl-carousel .owl-stage{
        display: flex;
        flex-wrap: nowrap;
        align-items: stretch;
      }

      ::ng-deep .owl-theme .owl-nav {
        max-width: 1180px;
        margin: 0;
        text-align: right;
        line-height: 60px;
        display: flex;
        justify-content: end;
        top: -171px;
        position: absolute;
        left: 940px;
        text-align: right;
        bottom: auto;
      }
      
      .owl-nav button {
        background-color: #333;
        color: #fff;
        border: none;
        padding: 10px;
        border-radius: 50%;
        cursor: pointer;
      }
      
      .owl-nav button:hover {
        background-color: #555;
      }
    
      ::ng-deep {
        .owl-theme .owl-dots{
        max-width: 1244px;
        margin: 0px auto;
        text-align: left;
        margin-top: -66px;
      }
    
      .owl-theme .owl-nav [class*=owl-] {
            width: 100px;
            height: 100px;
            left: 232px;
            top: 0px;
            background: white;
            border: 1px solid rgb(0 0 0 / 10%);
            border-radius: 100px;
            transform: matrix(-1, 0, 0, 1, 0, 0);
            color: black;
            text-align: center;
            justify-content: center;
            display: flex;
            transform: inherit;
            &:hover{
                background-color: #014681;
                color: white;
            }
    
            img{
                max-width: 20px;
            }
      }
      }
    
      ::ng-deep  .owl-theme .owl-dots .owl-dot span{
        width: 20px;
        height: 20px;
        background: #ffffff;
      }
    
      ::ng-deep .owl-theme .owl-dots .owl-dot.active span, ::ng-deep .owl-theme .owl-dots .owl-dot:hover span{
        background: #014681;
      }     
}

.hide-text{
  visibility: hidden;
}

.custom-nav-arrow {
  width: 24px;
  height: 24px;
  stroke: #000; /* Adjust color */
}

// ::ng-deep {
//   .owl-theme .owl-dots {
//     max-width: 1244px;
//     margin: 0px auto;
//     text-align: left;
//     margin-top: -66px;
//   }

//   .owl-theme .owl-nav [class*=owl-] {
//     width: 100px;
//     height: 100px;
//     left: 232px;
//     top: 0px;
//     background: #FFFFFF;
//     border-radius: 100px;
//     transform: matrix(-1, 0, 0, 1, 0, 0);
//     color: black;
//     text-align: center;
//     justify-content: center;
//     display: flex;
//     transform: inherit;

//     &:hover {
//       background-color: #014681;
//       color: white;
//     }

//     img {
//       max-width: 20px;
//     }
//   }
// }


@media(max-width:768px) {
  .fc-what-we-do-container {
    padding: 1rem;
    background-color: #f5f5f5;
    padding-block: 3rem;
    padding-bottom: 2rem;
  }

  .fc-only-header {
    margin-bottom: 0px;

    h3 {
      font-size: 1.25rem;
      min-width: auto;
      line-height: 1.5;
    }

    label{
      font-size: 1rem;
      min-width: 100%;
    }
    p{
      font-size: 14px;
    }
  }

  .fc-easy-card-row .fc-easy-card {
    max-width: 100%;
    border-radius: 1rem;
    div{
    h4{
      font-size: 1.25rem;
    }
  }
  }

  .full-width {
    ::ng-deep.owl-carousel.owl-drag .owl-item {
      width: 100% !important;
    }
    ::ng-deep .owl-theme .owl-nav{
      position: relative;
      left:0%;
      text-align: right;
      bottom: auto;
      right: auto;
      top:1rem;
      justify-content: center;
    }

    ::ng-deep .owl-theme .owl-nav [class*=owl-] {
      width: 50px;
      height: 50px;
    }
  }

  .fc-easy-card-row .fc-easy-card div p {
    width: 100% !important;
    font-size: 14px;
  }

  .fc-easy-card-row .fc-easy-card figure {
    height: 250px;

    img {
      height: 100%;
    }
  }

  .fc-easy-card-row .fc-easy-card div {
    padding: 1rem;
  }
  .hide-text{
    display: none;
  }
}