import { Component, ElementRef, OnInit, Renderer2, ViewChild } from '@angular/core';
import { OwlOptions } from 'ngx-owl-carousel-o';

@Component({
  selector: 'app-easy-connection',
  templateUrl: './easy-connection.component.html',
  styleUrls: ['./easy-connection.component.scss']
})
export class EasyConnectionComponent {

  @ViewChild('fcSlideCategories') fcSlideCategories!: ElementRef;
  @ViewChild('fcSlideCategories1') fcSlideCategories1!: ElementRef;
  @ViewChild('fcSlideCategories2') fcSlideCategories2!: ElementRef;
  @ViewChild('fcContainer') fcContainer: ElementRef | undefined;
  marginLeft: number = 0;

  constructor(private renderer: Renderer2) { }

  //  ngOnInit(): void {
  //   setTimeout(() => {
  //     const owlItems = document.querySelectorAll('.owl-item');
  //     owlItems.forEach(item => {
  //       item.setAttribute('style', 'width: 515px !important; margin-right: 40px !important;');
  //     });
  //   }, 100);
  //  }
  carouselOptions: OwlOptions = {
    loop: false,
    margin: 50,
    center: false,
    dots: false,
    autoplay: false,
    nav:true,
    navText: [
      '<img src="../../../../../assets/images/arrow-left.svg" alt="Previous" class="custom-nav-arrow" />',
      '<img src="../../../../../assets/images/black-arrow.svg" alt="Next" class="custom-nav-arrow" />'
    ],
    // autoplayTimeout: 10000,
    responsive: {
      0: { items: 1 },
      600: { items: 3 },
      1000: { items:3 },
    },
    // autoWidth: true,
  };

  easyCards = [
    {
      image: '../../../../../assets/images/connection-matter.webp',
      title: 'Your connection matters!',
      description: `In today's digital era, reputation is everything. Every connection will
have the potential to drive success and a gateway to valuable referrals.
Embracing a positive attitude as diversity is crucial to our values.`
    },
    {
      image: '../../../../../assets/images/stay-ahead.webp',
      title: 'Stay ahead of the curve',
      description: `Be the first to join as a channel partner and explore the potential
of your e-Card brand and get an instant connection by end user businesses
looking for sales inquiries and long term relations!.`
    },
    {
      image: '../../../../../assets/images/handshake.webp',
      title: 'Save your connections',
      description: `Valuable resources and expert connections are key to long term relationships. Businesses are thriving
to rely on wise connections, great expertise, on-time
response, for great customer experience!`
    },
    {
      image: '../../../../../assets/images/direct-communication.webp',
      title: 'Enhanced communication',
      description: `Not to criticise the multi-threads to get to your point of contact,
but we are offering you a simplified direct communication with your
dedicated POC members and end-user businesses on the platform.`
    },
    {
      image: '../../../../../assets/images/build-network.webp',
      title: 'Build a favorite network',
      description: `Explore multi profile e-Cards Partners, Like a preferred Expert,
Connect with your channel of choice, add them to your circle of
favorite profile page connections for an easy reach.`
    },
    {
      image: '../../../../../assets/images/dedicated-referrals.webp',
      title: 'Dedicated referrals',
      description: `Enjoy a platform that provides you with an easy process of receiving and providing dedicated  and meaningful referrals directly from your favorite connections`
    },
    {
      image: '../../../../../assets/images/social-interaction.webp',
      title: 'Social interaction',
      description: `We believe enjoyable experiences are essential for building connections in our community. Our platform simplifies the search for meaningful relationships, enabling you to engage and stay socially connected.`
    },
    {
      image: '../../../../../assets/images/secure-portal.webp',
      title: 'Secure portal',
      description: `Our platform securely connects individuals and businesses with professionals and their colleagues. Easily engage with dedicated experts and like-minded users to build a network of trusted connections.`
    }
  ];


  ngAfterViewChecked(): void {
    this.calculateMarginLeft();
  }

  calculateMarginLeft(): void {
    if (this.fcContainer && this.fcSlideCategories) {
      const rect = this.fcContainer.nativeElement.getBoundingClientRect();
      this.marginLeft = rect.left;
      this.renderer.setStyle(this.fcSlideCategories1.nativeElement, 'margin-left', `${this.marginLeft}px`);
      
      const owlStage = document.querySelector('.owl-stage');
    if (owlStage) {
      this.renderer.setStyle(owlStage, 'padding-right', `${this.marginLeft}px`);
    }

      // const calculatedWidth = `calc(100vw - ${this.marginLeft}px)`;
      // this.renderer.setStyle(this.fcSlideCategories.nativeElement, 'width', calculatedWidth);
    }
  }
}
