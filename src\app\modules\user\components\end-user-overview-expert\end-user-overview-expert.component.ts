import { Component, ElementRef, Input, ViewChild, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { forkJoin, finalize, take } from 'rxjs';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { EMPTY_GUID, socialMediaImages } from 'src/app/shared/constant';
import { ModalService } from 'src/app/shared/services/modal.service';
import { UtilsService } from 'src/app/shared/services/utils.service';
import { companyType } from 'src/app/shared/constant';
import { ChatService } from 'src/app/shared/services/chat.service';
import {
  DEFAULT_CONNCTION_REQUEST_LIMIT,
  DropMessageType,
  FollowStatus,
} from 'src/app/shared/constant';
import { HubService } from 'src/app/shared/services/hub.service';
import { filterNonNull } from 'src/app/shared/oprators/filter-null-values';

export interface UserResponse {
  message: string;
  data: UserResponseData;
  error: null;
  messageType: number;
}

export interface UserResponseData {
  firstName: string;
  lastName: string;
  address: string;
  userType: number;
  userId: string;
  organizationName: string;
  organizationTypeId: string;
  aboutMe: null;
  mobileCountryCode: string;
  phoneNumber: string;
  email: string;
  verticalName: string;
  createdAt: string;
}

export interface List {
  id: number;
  idLong: number;
  idGuid: string;
  name: string;
  description: null;
  selected: boolean;
}

@Component({
  selector: 'app-end-user-overview-expert',
  templateUrl: './end-user-overview-expert.component.html',
  styleUrls: ['./end-user-overview-expert.component.scss'],
})
export class EndUserOverviewExpertComponent implements OnInit {
  @Input() endUser: UserResponseData = {} as UserResponseData;
  linkedCopied = false;
  socialMediaTypes = socialMediaImages;
  socialMediaLinks: any[] = [];
  ratingModel = null;
  maxRating = 5;

  vm: any = {};
  isCompany = false;
  isLoading = false;
  userComment = null;
  addingComment = false;
  userState$: any = {};
  expert: any;
  selectedImage: any;
  companyId: string = '';
  experties: any[] = [];
  loading = false;
  userId: any = '';
  companyType = companyType;
  connectionsAndReferrals: any[] = [];
  connectLoading = false;
  FollowStatus = FollowStatus;
  followStatus$ = this.hubService.getFollowStatus();
  @ViewChild('profile') profile!: ElementRef;
  @ViewChild('bannerFile') bannerInput!: ElementRef;
  showAllConnections: boolean = false;
  showAllExpertise: boolean = false;
  endUserExpertiseData: any = {};
  loggedInUserId: string = '';
  isOwnProfile: boolean = false;
  hideConnectButton = false;
  constructor(
    private account: AccountService,
    private router: Router,
    private toaster: ToastrService,
    private utils: UtilsService,
    private modalService: ModalService,
    private chatService: ChatService,
    private hubService: HubService,
    private activedRoute: ActivatedRoute,
  ) { }

  activeTab: string = 'aboutus'; // Default active tab

  setActiveTab(tab: string) {
    this.activeTab = tab;
  }

  toggleShowAll() {
    this.showAllExpertise = !this.showAllExpertise;
  }

  ngOnInit(): void {
    // this.getEndUserDetails();
    this.getConnectionsAndReferrals();
    this.account.user$.pipe(filterNonNull()).subscribe((user) => {
      this.userState$ = user;
      this.getSocialMediaLinks(this.endUser.userId);
    });

    this.activedRoute.params.subscribe((params: any) => {
      const { id } = params;
      if (id) {
        this.loadEndUserDetails(id); // Pass the id to your data loader
        this.getConnectionsAndReferrals();
        this.account.user$.pipe(filterNonNull()).subscribe((user) => {
          this.userState$ = user;
          this.getSocialMediaLinks(id);
        });
      } else {
        this.handleError('Oops, the resource you are looking for is not available.');
      }
    });
  }
  handleError(message: string) {
    this.toaster.error(message);
    this.router.navigate(['/']);
  }
  getSocialMediaLinks(id: any) {
    this.account.getSocialMedia(id).subscribe((response: any) => {
      if (response.data?.length) {
        response.data.forEach((x: any) => {
          if (x.url) {
            x.imageUrl = this.socialMediaTypes[x.socialConnectionTypeId];
            this.socialMediaLinks.push(x);
          }
        });
      }
    });
  }

  loadEndUserDetails(userId: string) {
    this.loading = true;
    forkJoin([
      this.account.getEndUserDetails(userId),
      this.account.getCompanyComments(userId),
      this.account.getUserSettings() // Get the dropdown data for filtering
    ])
      .pipe(finalize(() => (this.loading = false)))
      .subscribe({
        next: ([userDetailResponse, userCommentResponse, settingsResponse]: any) => {
          console.log('End user API response:', userDetailResponse);
          console.log('Settings API response:', settingsResponse);

          if (userDetailResponse && userDetailResponse.data) {
            this.endUser = userDetailResponse.data;
            this.selectedImage = userDetailResponse.data.banner || './assets/svgs/bg.svg';
            this.userId = userDetailResponse.data.id;
            this.vm.profilePhoto = userDetailResponse.data.profilePhoto || './assets/images/create-profile.jpg';
            this.vm.companyName = userDetailResponse.data.organizationName;

            console.log('End user data set:', this.endUser);

            // Prepare expertise data for end user
            if (settingsResponse && settingsResponse.data) {
              this.prepareEndUserExpertiseData(userDetailResponse.data, settingsResponse.data);
            } else {
              console.warn('No settings data available for expertise filtering');
            }
          } else {
            console.error('No end user data in API response');
          }
          if (userDetailResponse.messageType) {
            this.router.navigate(['/home']);
            this.toaster.error(userDetailResponse.message);
          }
        },
        error: (error) => {
          console.error('Error loading end user details:', error);
          this.toaster.error('Failed to load user details');
        }
      });
  }

  prepareEndUserExpertiseData(userData: any, settingsData: any) {
    console.log('End user data for expertise:', userData);
    console.log('Settings data for filtering:', settingsData);

    // Helper function to filter items by IDs
    const filterByIds = (list: any[], ids: string) => {
      if (!ids || !list) {
        console.log(`No data to filter - list: ${list?.length || 0} items, ids: ${ids}`);
        return [];
      }
      const idArray = ids.split(',').map((id: string) => id.trim()).filter(id => id);
      console.log(`Filtering ${list.length} items with IDs: ${idArray}`);
      const filtered = list.filter((item: any) => idArray.includes(item.idGuid || item.id));
      console.log(`Filtered result: ${filtered.length} items`);
      return filtered;
    };

    // Helper function to extract names from filtered items
    const extractNames = (items: any[]) => {
      const names = items.map((item: any) => item.name || item.description || item.title);
      console.log(`Extracted names: ${names}`);
      return names;
    };

    // Use settings data for filtering if available, otherwise use empty arrays
    console.log('Filtering expertise...');
    const expertiseItems = filterByIds(settingsData?.expertiseList || [], userData.expertiseIds);
    console.log('Filtering industries...');
    const industryItems = filterByIds(settingsData?.industryList || [], userData.industryIds);
    console.log('Filtering products...');
    const productItems = filterByIds(settingsData?.productList || [], userData.productIds);
    console.log('Filtering services...');
    const serviceItems = filterByIds(settingsData?.servicesList || [], userData.serviceIds);
    console.log('Filtering solutions...');
    const solutionItems = filterByIds(settingsData?.solutionList || [], userData.solutionIds);
    console.log('Filtering company systems...');
    const companySystemItems = filterByIds(settingsData?.companySystemList || [], userData.companySystemIds);

    this.endUserExpertiseData = {
      expertise: extractNames(expertiseItems),
      industries: extractNames(industryItems),
      products: extractNames(productItems),
      services: extractNames(serviceItems),
      solutions: extractNames(solutionItems),
      companySystem: extractNames(companySystemItems),
      organizationName: userData.organizationName,
      companyName: userData.organizationName
    };

    // Also update the experties array for backward compatibility
    this.experties = expertiseItems.map(item => ({
      name: item.name || item.description || item.title,
      id: item.idGuid || item.id
    }));

    console.log('Final prepared end user expertise data:', this.endUserExpertiseData);
    console.log('Updated experties array for My Expertise section:', this.experties);
  }

  // getEndUserDetails() {
  //   this.loading = true;

  //   forkJoin([
  //     this.account.getUserDetails(this.endUser.userId),
  //     this.account.getCompanyComments(this.endUser.userId),
  //   ])
  //     .pipe(finalize(() => (this.loading = false)))
  //     .subscribe(([userDetailResponse, userCommentResponse]) => {
  //       let response: any = userDetailResponse as any;
  //       let comments: any = userCommentResponse as any;
  //       this.selectedImage = response.data.banner || './assets/svgs/bg.svg';
  //       this.companyId = response.data.expertDetail.companyId;
  //       this.userId = response.data.userId;
  //       this.vm.companyName = response.data.expertDetail.companyName;
  //       this.vm.profilePhoto = response.data.profilePhoto;

  //       this.experties = response.data?.expertiseList?.filter((x: any) =>
  //         response.data.expertDetail?.expertiseIds
  //           ?.split(',')
  //           ?.includes(x?.idGuid)
  //       );

  //       if (response.data.userType == `1`) {
  //         this.endUser = response.data;
  //       } else if (
  //         response.data.userType == `2` ||
  //         response.data.userType == `3`
  //       ) {
  //         this.expert = response.data;
  //         this.expert.comments = comments.data;
  //       }

  //       if (response.messageType) {
  //         this.router.navigate(['/home']);
  //         return this.toaster.error(response.message);
  //       }
  //       return;
  //     });
  // }

  getConnectionsAndReferrals() {
    this.account
      .getConnectionAndReferrals(1, 100)
      .subscribe((response: any) => {
        const dynamicKey = Object.keys(response).find((key) =>
          key.startsWith('item')
        );
        if (dynamicKey && response[dynamicKey].result) {
          this.connectionsAndReferrals = response[dynamicKey].result;
        } else {
          this.connectionsAndReferrals = [];
        }
      });
  }

  getExpertComments() {
    // this.account
    //   .getCompanyComments(this.userId)
    //   .subscribe((response: any) => {});
  }

  // uploadBanner() {
  //   this.modalService.openModal('cropper', {
  //     class: 'modal-xl',
  //     initialState: {
  //       onCrop: (data: string) => {
  //         this.selectedImage = data;
  //         this.modalService.closeModal();
  //       },
  //     },
  //   });
  //   // this.bannerInput.nativeElement.click();
  // }

  // async uploadBannerImage($event: any) {
  //   const valid = await this.displaySelectedImage(
  //     $event.target.files[0],
  //     'banner'
  //   );
  //   if (!valid) return;
  //   const formData = new FormData();
  //   formData.append('UserId', this.userState$.userId);
  //   formData.append(
  //     'photo',
  //     $event.target.files[0],
  //     this.utils.generateRandomFileName()
  //   );
  //   this.account.uploadUserBanner(formData).subscribe((response) => {});
  // }

  // updateProfile() {
  //   this.profile.nativeElement.click();
  // }

  // async uploadProfileImage($event: any) {
  //   const valid = await this.displaySelectedImage($event.target.files[0]);
  //   if (!valid) return;
  //   const formData = new FormData();
  //   formData.append('UserId', this.userId);
  //   formData.append(
  //     'photo',
  //     $event.target.files[0],
  //     this.utils.generateRandomFileName()
  //   );
  //   this.account.uploadProfileImage(formData).subscribe((response) => {});
  // }

  // displaySelectedImage(file: File, fileType = 'profile') {
  //   return new Promise((resolve, reject) => {
  //     if (!file.type.startsWith('image/')) {
  //       this.toaster.error('Selected file is not an image.');
  //       return resolve(false);
  //     }
  //     const isProfile = fileType == 'profile';

  //     // Check the file size (in bytes)
  //     if (isProfile && file.size > this.utils.getProfileImageSize()) {
  //       this.toaster.error('Image size should be under 2MB.');
  //       return resolve(false);
  //     } else if (file.size > this.utils.getProfileImageSize()) {
  //       this.toaster.error('Image size should be under 5MB.');
  //       return resolve(false);
  //     }

  //     const reader = new FileReader();
  //     reader.onload = (e) => {
  //       const file = e.target?.result;
  //       this.account.profileImage$.next(this.vm.profilePhoto);
  //       let user: any = localStorage.getItem('user');
  //       if (user) {
  //         user = JSON.parse(user);
  //       }
  //       if (fileType === 'profile') {
  //         user.profilePhoto = file;
  //         this.vm.profilePhoto = file;
  //         this.account.profileImage$.next(user.profilePhoto);
  //       } else {
  //         user.banner = file;
  //         this.selectedImage = file;
  //       }
  //       localStorage.setItem('user', JSON.stringify(user));
  //     };
  //     reader.readAsDataURL(file);
  //     resolve(true);
  //   });
  // }

  navigateToCompany() {
    this.router.navigate([`/details/${this.companyId}/company`]);
  }

  connectUser(connection: any): any {
    if (
      `${this.userState$.userId}`.toString() == `${connection.id}`.toString()
    ) {
      return this.toaster.info('You cannot send request to this admin');
    }
    const payload = {
      userId: connection.id,
      followerUserId: this.userState$.userId,
      loginUserId: this.userState$.userId,
    };
    this.connectLoading = true;
    this.account
      .connectExpert(payload)
      .pipe(finalize(() => (this.connectLoading = false)))
      .subscribe((response: any) => {
        if (response.messageType == DropMessageType.Success) {
          this.chatService.sendNotification(
            connection.id,
            response.data.text,
            this.userState$.userId,
            response.data.id
          );
          this.toaster.success(response.message);
          connection.isCompleted = true;
        } else if (response.messageType == DropMessageType.Info) {
          this.toaster.info(response.message);
        } else {
          this.toaster.error(response.message);
        }
        if (
          response.messageType &&
          response.messageType == DEFAULT_CONNCTION_REQUEST_LIMIT
        ) {
          this.modalService.openModal('connection-limit-exceed');
        }
      });
  }

  copyLink(): void {
    const currentUrl = window.location.href;
    navigator.clipboard
      ?.writeText(currentUrl)
      .then(() => {
        this.linkedCopied = true;
        setTimeout(() => (this.linkedCopied = false), 2000);
      })
      .catch(() => {
        const textArea = document.createElement('textarea');
        textArea.value = currentUrl;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        this.linkedCopied = true;
        setTimeout(() => (this.linkedCopied = false), 2000);
      });
  }

  navigate(connection: any) {
    this.router.navigate([`/details/${connection.id}/expert`]);
  }

  showAll() {
    this.showAllConnections = !this.showAllConnections;
  }
  back() {
    this.showAllConnections = !this.showAllConnections;
  }
}
