import { Component, ElementRef, Input, ViewChild, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { forkJoin, finalize } from 'rxjs';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { socialMediaImages } from 'src/app/shared/constant';
import { ModalService } from 'src/app/shared/services/modal.service';
import { UtilsService } from 'src/app/shared/services/utils.service';
import { companyType } from 'src/app/shared/constant';
import { ChatService } from 'src/app/shared/services/chat.service';
import {
  DEFAULT_CONNCTION_REQUEST_LIMIT,
  DropMessageType,
  FollowStatus,
} from 'src/app/shared/constant';
import { HubService } from 'src/app/shared/services/hub.service';
import { filterNonNull } from 'src/app/shared/oprators/filter-null-values';

export interface UserResponse {
  message: string;
  data: UserResponseData;
  error: null;
  messageType: number;
}

export interface UserResponseData {
  firstName: string;
  lastName: string;
  address: string;
  userType: number;
  userId: string;
  organizationName: string;
  organizationTypeId: string;
  aboutMe: null;
  mobileCountryCode: string;
  phoneNumber: string;
  email: string;
  verticalName: string;
  createdAt: string;
}

export interface List {
  id: number;
  idLong: number;
  idGuid: string;
  name: string;
  description: null;
  selected: boolean;
}

@Component({
  selector: 'app-end-user-overview',
  templateUrl: './end-user-overview.component.html',
  styleUrls: ['./end-user-overview.component.scss'],
})
export class EndUserOverviewComponent implements OnInit {
  @Input() endUser: UserResponseData = {} as UserResponseData;
  linkedCopied = false;
  socialMediaTypes = socialMediaImages;
  socialMediaLinks: any[] = [];
  ratingModel = null;
  maxRating = 5;

  vm: any = {};
  isCompany = false;
  isLoading = false;
  userComment = null;
  addingComment = false;
  userState$: any = {};
  expert: any;
  selectedImage: any;
  companyId: string = '';
  experties: any[] = [];
  loading = false;
  userId: any = '';
  companyType = companyType;
  connectionsAndReferrals: any[] = [];
  connectLoading = false;
  FollowStatus = FollowStatus;
  followStatus$ = this.hubService.getFollowStatus();
  @ViewChild('profile') profile!: ElementRef;
  @ViewChild('bannerFile') bannerInput!: ElementRef;
  showAllConnections: boolean = false;
  showAllExpertise: boolean = false;
  endUserExpertiseData: any = {};
  constructor(
    private account: AccountService,
    private router: Router,
    private toaster: ToastrService,
    private utils: UtilsService,
    private modalService: ModalService,
    private chatService: ChatService,
    private hubService: HubService
  ) { }

  activeTab: string = 'aboutus'; // Default active tab

  setActiveTab(tab: string) {
    this.activeTab = tab;
  }

  toggleShowAll() {
    this.showAllExpertise = !this.showAllExpertise;
  }

  ngOnInit(): void {
    this.account.user$.pipe(filterNonNull()).subscribe((user) => {
      this.userState$ = user;
      console.log('User state:', this.userState$);

      // For end users, use the logged-in user's ID
      if (user.userType === 1) { // End user
        this.endUser.userId = user.userId;
        this.getEndUserDetails();
        this.getSocialMediaLinks(user.userId);
      } else {
        // For experts, use the provided endUser.userId
        this.getEndUserDetails();
        this.getSocialMediaLinks(this.endUser.userId);
      }

      this.getConnectionsAndReferrals();
    });
  }

  getSocialMediaLinks(id: any) {
    this.account.getSocialMedia(id).subscribe((response: any) => {
      if (response.data?.length) {
        response.data.forEach((x: any) => {
          if (x.url) {
            x.imageUrl = this.socialMediaTypes[x.socialConnectionTypeId];
            this.socialMediaLinks.push(x);
          }
        });
      }
    });
  }

  getEndUserDetails() {
    this.loading = true;

    // For end users, we need to get both the end user details and the dropdown data
    forkJoin([
      this.account.getEndUserDetails(this.endUser.userId),
      this.account.getCompanyComments(this.endUser.userId),
      this.account.getUserSettings() // Get the dropdown data for filtering
    ])
      .pipe(finalize(() => (this.loading = false)))
      .subscribe({
        next: ([userDetailResponse, userCommentResponse, settingsResponse]) => {
          let response: any = userDetailResponse as any;
          let comments: any = userCommentResponse as any;
          let settings: any = settingsResponse as any;

          console.log('End user details response:', response);
          console.log('Settings response:', settings);

          this.selectedImage = response.data?.banner || './assets/svgs/bg.svg';
          this.userId = response.data?.id || response.data?.userId;
          this.vm.profilePhoto = response.data?.profilePhoto || './assets/images/create-profile.jpg';
          this.vm.companyName = response.data?.organizationName;

          // Set the vm.userId for the service-activities component
          this.vm.userId = response.data?.userId || this.endUser.userId;
          this.vm.firstName = response.data?.firstName;
          this.vm.lastName = response.data?.lastName;

          if (response.data) {
            this.endUser = response.data;
            console.log('End user data:', this.endUser);
            console.log('VM object after setting userId:', this.vm);
            // Prepare expertise data for end user using both user data and settings
            this.prepareEndUserExpertiseDataWithSettings(response.data, settings.data);
          } else {
            console.error('No end user data received');
          }

          if (response.messageType) {
            this.router.navigate(['/home']);
            this.toaster.error(response.message);
            return;
          }
        },
        error: (error) => {
          console.error('Error loading end user details:', error);
          this.toaster.error('Failed to load user details');
        }
      });
  }

  prepareEndUserExpertiseData(userData: any) {
    console.log('End user data for expertise:', userData);

    // Helper function to filter items by IDs
    const filterByIds = (list: any[], ids: string) => {
      if (!ids || !list) return [];
      const idArray = ids.split(',').map((id: string) => id.trim());
      return list.filter((item: any) => idArray.includes(item.idGuid || item.id));
    };

    // Helper function to extract names from filtered items
    const extractNames = (items: any[]) => {
      return items.map((item: any) => item.name || item.description || item.title);
    };

    // Filter and extract data based on end user's selections
    const expertiseItems = filterByIds(userData.expertiseList, userData.expertiseIds);
    const industryItems = filterByIds(userData.industryList, userData.industryIds);
    const productItems = filterByIds(userData.productList, userData.productIds);
    const serviceItems = filterByIds(userData.servicesList, userData.serviceIds);
    const solutionItems = filterByIds(userData.solutionList, userData.solutionIds);
    const companySystemItems = filterByIds(userData.companySystemList, userData.companySystemIds);

    this.endUserExpertiseData = {
      expertise: extractNames(expertiseItems),
      industries: extractNames(industryItems),
      products: extractNames(productItems),
      services: extractNames(serviceItems),
      solutions: extractNames(solutionItems),
      companySystem: extractNames(companySystemItems),
      organizationName: userData.organizationName,
      companyName: userData.organizationName
    };

    console.log('Prepared end user expertise data:', this.endUserExpertiseData);
  }

  prepareEndUserExpertiseDataWithSettings(userData: any, settingsData: any) {
    console.log('End user data for expertise:', userData);
    console.log('Settings data for filtering:', settingsData);

    // Helper function to filter items by IDs
    const filterByIds = (list: any[], ids: string) => {
      if (!ids || !list) {
        console.log(`No data to filter - list: ${list?.length || 0} items, ids: ${ids}`);
        return [];
      }
      const idArray = ids.split(',').map((id: string) => id.trim()).filter(id => id);
      console.log(`Filtering ${list.length} items with IDs: ${idArray}`);
      const filtered = list.filter((item: any) => idArray.includes(item.idGuid || item.id));
      console.log(`Filtered result: ${filtered.length} items`);
      return filtered;
    };

    // Helper function to extract names from filtered items
    const extractNames = (items: any[]) => {
      const names = items.map((item: any) => item.name || item.description || item.title);
      console.log(`Extracted names: ${names}`);
      return names;
    };

    // Use settings data for filtering if available, otherwise use empty arrays
    console.log('Filtering expertise...');
    const expertiseItems = filterByIds(settingsData?.expertiseList || [], userData.expertiseIds);
    console.log('Filtering industries...');
    const industryItems = filterByIds(settingsData?.industryList || [], userData.industryIds);
    console.log('Filtering products...');
    const productItems = filterByIds(settingsData?.productList || [], userData.productIds);
    console.log('Filtering services...');
    const serviceItems = filterByIds(settingsData?.servicesList || [], userData.serviceIds);
    console.log('Filtering solutions...');
    const solutionItems = filterByIds(settingsData?.solutionList || [], userData.solutionIds);
    console.log('Filtering company systems...');
    const companySystemItems = filterByIds(settingsData?.companySystemList || [], userData.companySystemIds);

    this.endUserExpertiseData = {
      expertise: extractNames(expertiseItems),
      industries: extractNames(industryItems),
      products: extractNames(productItems),
      services: extractNames(serviceItems),
      solutions: extractNames(solutionItems),
      companySystem: extractNames(companySystemItems),
      organizationName: userData.organizationName,
      companyName: userData.organizationName
    };

    // Also update the experties array for backward compatibility with "My Expertise" section
    this.experties = expertiseItems.map(item => ({
      name: item.name || item.description || item.title,
      id: item.idGuid || item.id
    }));

    console.log('Final prepared end user expertise data:', this.endUserExpertiseData);
    console.log('Updated experties array for My Expertise section:', this.experties);

    // Force change detection
    setTimeout(() => {
      console.log('Triggering change detection for expertise data');
    }, 100);
  }

  getConnectionsAndReferrals() {
    this.account
      .getConnectionAndReferrals(1, 100)
      .subscribe((response: any) => {
        const dynamicKey = Object.keys(response).find((key) =>
          key.startsWith('item')
        );
        if (dynamicKey && response[dynamicKey].result) {
          this.connectionsAndReferrals = response[dynamicKey].result;
        } else {
          this.connectionsAndReferrals = [];
        }
      });
  }

  getExpertComments() {
    // this.account
    //   .getCompanyComments(this.userId)
    //   .subscribe((response: any) => {});
  }

  uploadBanner() {
    this.modalService.openModal('cropper', {
      class: 'modal-xl',
      initialState: {
        onCrop: (data: string) => {
          this.selectedImage = data;
          this.modalService.closeModal();
        },
      },
    });
    // this.bannerInput.nativeElement.click();
  }

  async uploadBannerImage($event: any) {
    const valid = await this.displaySelectedImage(
      $event.target.files[0],
      'banner'
    );
    if (!valid) return;
    const formData = new FormData();
    formData.append('UserId', this.userState$.userId);
    formData.append(
      'photo',
      $event.target.files[0],
      this.utils.generateRandomFileName()
    );
    this.account.uploadUserBanner(formData).subscribe((response) => { });
  }

  updateProfile() {
    this.profile.nativeElement.click();
  }

  async uploadProfileImage($event: any) {
    const valid = await this.displaySelectedImage($event.target.files[0]);
    if (!valid) return;
    const formData = new FormData();
    formData.append('UserId', this.userId);
    formData.append(
      'photo',
      $event.target.files[0],
      this.utils.generateRandomFileName()
    );
    this.account.uploadProfileImage(formData).subscribe((response) => { });
  }

  displaySelectedImage(file: File, fileType = 'profile') {
    return new Promise((resolve, reject) => {
      if (!file.type.startsWith('image/')) {
        this.toaster.error('Selected file is not an image.');
        return resolve(false);
      }
      const isProfile = fileType == 'profile';

      // Check the file size (in bytes)
      if (isProfile && file.size > this.utils.getProfileImageSize()) {
        this.toaster.error('Image size should be under 2MB.');
        return resolve(false);
      } else if (file.size > this.utils.getProfileImageSize()) {
        this.toaster.error('Image size should be under 5MB.');
        return resolve(false);
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const file = e.target?.result;
        this.account.profileImage$.next(this.vm.profilePhoto);
        let user: any = localStorage.getItem('user');
        if (user) {
          user = JSON.parse(user);
        }
        if (fileType === 'profile') {
          user.profilePhoto = file;
          this.vm.profilePhoto = file;
          this.account.profileImage$.next(user.profilePhoto);
        } else {
          user.banner = file;
          this.selectedImage = file;
        }
        localStorage.setItem('user', JSON.stringify(user));
      };
      reader.readAsDataURL(file);
      resolve(true);
    });
  }

  navigateToCompany() {
    this.router.navigate([`/details/${this.companyId}/company`]);
  }

  connectUser(connection: any): any {
    if (
      `${this.userState$.userId}`.toString() == `${connection.id}`.toString()
    ) {
      return this.toaster.info('You cannot send request to this admin');
    }
    const payload = {
      userId: connection.id,
      followerUserId: this.userState$.userId,
      loginUserId: this.userState$.userId,
    };
    this.connectLoading = true;
    this.account
      .connectExpert(payload)
      .pipe(finalize(() => (this.connectLoading = false)))
      .subscribe((response: any) => {
        if (response.messageType == DropMessageType.Success) {
          this.chatService.sendNotification(
            connection.id,
            response.data.text,
            this.userState$.userId,
            response.data.id
          );
          this.toaster.success(response.message);
          connection.isCompleted = true;
        } else if (response.messageType == DropMessageType.Info) {
          this.toaster.info(response.message);
        } else {
          this.toaster.error(response.message);
        }
        if (
          response.messageType &&
          response.messageType == DEFAULT_CONNCTION_REQUEST_LIMIT
        ) {
          this.modalService.openModal('connection-limit-exceed');
        }
      });
  }

  copyLink(): void {
    const currentUrl = window.location.href;
    navigator.clipboard
      ?.writeText(currentUrl)
      .then(() => {
        this.linkedCopied = true;
        setTimeout(() => (this.linkedCopied = false), 2000);
      })
      .catch(() => {
        const textArea = document.createElement('textarea');
        textArea.value = currentUrl;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        this.linkedCopied = true;
        setTimeout(() => (this.linkedCopied = false), 2000);
      });
  }

  navigate(connection: any) {
    this.router.navigate([`/details/${connection.id}/expert`]);
  }

  showAll() {
    this.showAllConnections = !this.showAllConnections;
  }
  back() {
    this.showAllConnections = !this.showAllConnections;
  }
}
