<div class="lading-page" style="margin-top: 11% !important">
  <div class="d-flex justify-content-center">
    <img
      class="search-focile-logo"
      src="./assets/svgs/focile.svg"
      alt="focile-logo"
    />
  </div>
  <h2 class="fw-bold text-center pt-4">Experts E-Card and Partner Locator</h2>

  <div class="container-fluid">
    <div class="row">
      <div class="col-md-4">
        <form [formGroup]="filtersForm" (ngSubmit)="handleSubmit()">
          <div class="col-md-12 mb-3">
            <div class="form-group">
              <div class="position-relative">
                <input
                  type="search"
                  class="form-control shadow-none"
                  placeholder="Search ..."
                  name="globalSearch"
                  id="globalSearch"
                  formControlName="search"
                />
              </div>
            </div>
          </div>
          <div class="col-md-12 mb-3">
            <div class="form-group">
              <focile-dropdown
                [placeholder]="'Select Country'"
                bindValue="id"
                [isRequired]="false"
                [items]="countries$ | async"
                [loading]="countriesLoading"
                formControlName="countryId"
                (change)="getStates($event)"
                [clearable]="true"
              ></focile-dropdown>
            </div>
          </div>
          <div class="col-md-12 mb-3">
            <div class="form-group">
              <focile-dropdown
                [placeholder]="'Select State'"
                class="w-100 rounded-3"
                [isRequired]="false"
                container="null"
                bindValue="id"
                formControlName="stateId"
                [loading]="statesLoading$ | async"
                (change)="handleStateChange($event)"
                [items]="states"
                [clearable]="true"
              ></focile-dropdown>
            </div>
          </div>
          <div class="col-md-12 mb-3">
            <div class="form-group">
              <focile-dropdown
                [placeholder]="'Select City'"
                class="w-100 rounded-3"
                [isRequired]="false"
                container="null"
                bindValue="id"
                formControlName="cityId"
                [disabled]="!filtersForm.get('stateId')?.value"
                [items]="cities"
                [clearable]="true"
              ></focile-dropdown>
            </div>
          </div>
          <div class="col-md-12 mb-3">
            <div class="form-group">
              <input
                [id]="'zipcode'"
                [name]="'zipcode'"
                class="form-control"
                [placeholder]="'Enter zipcode'"
              />
            </div>
          </div>
          <div class="col-md-12">
            <focile-button
              [btnType]="'primary'"
              [btnClass]="'w-100'"
              [loading]="loading"
              [type]="'submit'"
            >
              Search</focile-button
            >
          </div>
        </form>
      </div>
      <div class="col-md-8">
        <div style="min-height: 30px" class="card">
          <div class="card-body">
            <div style="width: 100%" id="map" #resultContainer></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="container-fluid mt-4">
    <div class="row">
      <ng-container *ngIf="!companiesLoading">
        <div *ngFor="let item of companies" class="col-md-3">
          <app-e-card
            (locationClick)="handleLocationClick($event)"
            [company]="item"
          ></app-e-card>
        </div>
      </ng-container>
      <ng-container *ngIf="companiesLoading">
        <div class="col-md-12 text-center">
          <app-spinner></app-spinner>
        </div>
      </ng-container>
      <ng-container *ngIf="searchComplete && !companies?.length">
        <div class="col-md-12 text-center">
          <app-helper-text [message]="'No companies found'"> </app-helper-text>
        </div>
      </ng-container>
    </div>
  </div>
</div>
