import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import {
  BehaviorSubject,
  Observable,
  Subject,
  Subscriber,
  finalize,
  map,
  switchMap,
  tap,
} from 'rxjs';
import * as L from 'leaflet';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { UtilsService } from 'src/app/shared/services/utils.service';
import { UsersService } from 'src/app/shared/services/user.service';
import { ActivatedRoute } from '@angular/router';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { filterNonNull } from 'src/app/shared/oprators/filter-null-values';

@Component({
  selector: 'app-expert-locator',
  templateUrl: './expert-locator.component.html',
  styleUrls: ['./expert-locator.component.scss'],
})
export class ExpertLocatorComponent implements OnInit {
  @ViewChild('resultContainer') resultContainer!: ElementRef;
  map!: L.Map;
  companies: any = [];
  companiesCopy: any = [];
  companiesLoading = false;
  longitude: any;
  latitude: any;
  userType: any;
  user: any = {};
  searchField$: any;
  model: any;
  searching = false;
  searchFailed = false;
  resellers: any[] = [];
  vendors: any[] = [];
  distributors: any[] = [];
  countries$!: Observable<any>;
  countriesId = new Subject();
  states$: Observable<any> = this.countriesId.pipe(
    tap(console.log),
    switchMap((countryId) =>
      this.account.getStates(countryId).pipe(
        map((response: any) => response.data),
        finalize(() => this.statesLoading$.next(false))
      )
    )
  );
  statesLoading$: any = new BehaviorSubject<boolean>(false);
  countriesLoading: any;
  states = [];
  cities = [];
  filters: any = {
    country: null,
    state: null,
    city: null,
    isCertified: false,
    zipcode: null,
    certified: false,
  };
  searchTerm = '';
  filtersForm!: FormGroup;
  loading = false;
  searchComplete = false;
  constructor(
    private account: AccountService,
    private utils: UtilsService,
    private readonly users: UsersService,
    private activatRoute: ActivatedRoute,
    private formBuilder: FormBuilder
  ) {
    this.navgiate = this.navgiate.bind(this);
  }
  ngOnInit(): void {
    this.initForm();
    this.getCountries();
    this.utils.location$.subscribe((locations) => {
      this.latitude = locations.latitude;
      this.longitude = locations.longitude;
      this.account.user$.pipe(filterNonNull()).subscribe((response) => {
        this.user = response;
      });
    });
  }

  getCountries() {
    this.countriesLoading = true;
    this.countries$ = this.account.getCountries().pipe(
      finalize(() => (this.countriesLoading = false)),
      map((response: any) => response.data)
    );
  }

  initForm() {
    this.filtersForm = this.formBuilder.group({
      isCertified: false,
      countryId: null,
      stateId: null,
      cityId: null,
      search: null,
    });
  }

  handleSubmit() {
    this.loading = true;
    this.searchComplete = false;

    this.account
      .getFilterEcards(this.filtersForm.value)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((response: any) => {
        this.companies = response.data;
        setTimeout(() => {
          this.searchComplete = true;
          this.resultContainer.nativeElement.scrollIntoView();
        }, 100);
      });
  }

  ngAfterViewInit() {
    this.loadMap();
  }

  private getCurrentPosition(): any {
    return new Observable((observer: Subscriber<any>) => {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition((position: any) => {
          observer.next({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
          });
          observer.complete();
        });
      } else {
        observer.error();
      }
    });
  }

  private loadMap(): void {
    this.map = L.map('map').setView([51.505, -0.09], 13);

    L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution:
        '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
    }).addTo(this.map);
  }

  getUsers() {
    const expertType = this.activatRoute.snapshot.queryParamMap.get('q');
    this.companiesLoading = true;
    this.companies = null;
    if (expertType && +expertType > 0 && +expertType < 4) {
      this.users
        .getUsers({
          longitude: this.longitude,
          latitude: this.latitude,
          expertType,
          loginUserId: this.user?.userId,
        })
        .pipe(finalize(() => (this.companiesLoading = false)))
        .subscribe((response: any) => {
          this.companies = [...response.data];
        });
    }
  }

  generatePopupContent(markerData: any): string {
    return `
          <div class="card px-2 border-light mb-4 map-" style="width:9rem">
              <div class="card-header (click)="navgiate(markerData)" justify-content-center align-items-center border-0 bg-transparent w-100 d-flex">
                  <div>
                    <span class="chip beta fs-12 chip-primary"> ${
                      markerData.companyType
                    } </span>
                  </div>
              </div>
            <div class="card-body p-0 px-2">
              <div class="row col text-center my-2">
                <img src="${
                  markerData.profilePhoto || './assets/svgs/focile.svg'
                }" role="button" class="img pb-3" style="height: 6.25rem;">
              </div>
            <div class="row">
              <div class="col-md-12 ps-3 text-center">
                  <span class="fw-bold fs-12"> ${markerData.companyName} </span>
            </div>
          </div>
    `;
  }

  addMarkers(): void {
    const icon = L.icon({
      iconUrl: './assets/images/marker-icon.png',
      shadowUrl: './assets/images/marker-shadow.png',
      popupAnchor: [13, 0],
    });

    for (const iterator of this.companies) {
      const latLong = iterator.latLong.split(',');
      const marker1 = L.marker([+latLong[0], +latLong[1]], {
        icon,
      }).addTo(this.map);
      marker1.bindPopup(this.generatePopupContent(iterator));
    }
  }

  handleLocationClick(latLong: any[]) {
    this.map.flyTo([+latLong[0], +latLong[1]], 15);
    document.getElementById('map')?.focus();
  }

  getStates(countryId: number) {
    this.account.getStates(countryId).subscribe((response: any) => {
      if (response.data.length) {
        this.states = response.data;
      }
    });
  }

  handleStateChange(stateId: any) {
    this.filters.state = stateId;
    this.account.getCities(stateId).subscribe((response: any) => {
      this.cities = response.data;
    });
  }

  handleSearchChange(searchTerm: string) {}

  searchCompanies() {}

  navgiate(markerData: any) {}
}
