<div class="person-info-wrapper container-fluid">
  <!-- <div class="meta-data-grid">
    <div class="action d-flex gap-3"></div>
  </div> -->
  <div class="meta-details justify-content-between fs-12">
    <div class="d-flex align-items-center justify-content-end">
      <ng-container>
        <div></div>
      </ng-container>
    </div>
  </div>
  <div class="row">
  </div>
  <div class="row">
    <app-recently-joined-partners *ngIf="vm?.technology" [title]="'Our Technology Partners'" [fontSize]="'18px'"
      [textCenter]="false" [items]="vm.technology"></app-recently-joined-partners>
  </div>
</div>

<div class="fc-expert-overview-wrapper pb-3 pb-sm-5">
  <div class="fc-full-common-banner">
    <img [src]="selectedImage" />

    <div class="user-icon position-absolute" style="right: 0rem; top: 1rem">
      <button (click)="uploadBanner()" class="change-banner-picture">
        <i class="fa fa-pen"></i>
      </button>
      <input type="file" class="hide d-none" accept="image/jpeg, image/png" #bannerFile
        (change)="uploadBannerImage($event)" />
    </div>
  </div>
  <div class="fc-user-profle-view">
    <div class="fc-container">
      <div class="fc-top-personal-detail">
        <div class="top-line">
          <div class="fc-user-avtar">
            <div class="user-img">
              <div class="profile-container">
                <img [src]="vm.profilePhoto" alt="Profile Picture" appImg />
                <span class="p-1 position-absolute d-flex justify-content-center change-profile-picture"
                  (click)="updateProfile()" role="button" tooltip="Change profile picture">
                  <i class="fa fa-pen"></i>
                </span>
                <input type="file" accept="image/png, image/jpeg" name="profile" class="d-none" id="profile" #profile
                  (change)="uploadProfileImage($event)" />
              </div>
            </div>
            <div class="fc-user-name" *ngIf="userState$.userType != companyType.EndUser">
              <label>
                {{ vm.firstName | titlecase }}
                {{ vm.lastName | titlecase }}
                <span *ngIf="vm?.admin?.userType == 3" class="fs-12 text-muted">( Admin )</span>
              </label>
              <span>
                <p class="mt-0 mb-0">
                  <span (click)="navigateToCompany()" tooltip="View {{ vm?.companyName }}" class="fw-semibold">{{
                    vm?.companyName }}</span>
                  |
                  <span *ngFor="
                      let item of experties | slice : 0 : 3;
                      let i = index
                    ">
                    <span *ngIf="i !== 0"> {{ "||" }} </span> {{ item.name }}
                  </span>
                </p>
              </span>
            </div>
            <div class="fc-user-name" *ngIf="userState$.userType == companyType.EndUser">
              <label>{{ endUser.organizationName }}</label>
            </div>
          </div>
          <div class="fc-socile-profile">
            <!-- <div *ngIf="vm.userType === 3">
              <ng-container>
                <div class="favorite-btn" role="button" [tooltip]="'favorite'">
                  <i
                    class="fa-heart text-danger"
                    [ngClass]="{ fa: vm.isFavorite, far: !vm.isFavorite }"
                  ></i>
                </div>
              </ng-container>
            </div> -->
            <button role="button" class="chat-btn" routerLink="/my-account/chat">
              <svg width="15" height="17" viewBox="0 0 15 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M12.8001 2.19327C9.8759 -0.731221 5.11751 -0.730956 2.19327 2.19327C-0.731089 5.11762 -0.731089 9.87588 2.19327 12.8001C3.82907 14.4362 6.152 15.2256 8.44583 14.9354L11.035 16.8478C11.1216 16.9118 11.2257 16.9453 11.3311 16.9453C11.3754 16.9453 11.4198 16.9394 11.4633 16.9275C11.6107 16.8868 11.7315 16.7809 11.7907 16.6399L12.5292 14.8829C12.6359 14.6291 12.5166 14.3369 12.2628 14.2302C12.0089 14.1237 11.7165 14.2428 11.6101 14.4966L11.1173 15.6691L8.87424 14.0125C8.76727 13.9334 8.63253 13.8997 8.50154 13.9209C6.45331 14.2389 4.35963 13.5567 2.89817 12.0952C0.3626 9.55966 0.3626 5.43385 2.89817 2.89815C5.4336 0.362715 9.55928 0.362467 12.0952 2.89815C13.7066 4.50981 14.3514 6.79743 13.82 9.01753C13.8187 9.02299 13.8174 9.02843 13.8163 9.03389C13.7418 9.3409 13.6446 9.64403 13.53 9.92831L12.3654 12.6994C12.2587 12.9532 12.378 13.2454 12.6318 13.3521C12.8856 13.4587 13.1779 13.3395 13.2845 13.0857L14.4518 10.308C14.5881 9.97025 14.7008 9.61845 14.7816 9.28262C14.789 9.25913 14.7946 9.23549 14.7985 9.21173C15.3949 6.66372 14.6495 4.04263 12.8001 2.19327Z"
                  fill="white" />
                <path
                  d="M10.6573 5.16162H4.6527C4.43241 5.16162 4.25391 5.33837 4.25391 5.55866C4.25391 5.77896 4.43241 5.95571 4.6527 5.95571H10.6573C10.8776 5.95571 11.0561 5.77896 11.0561 5.55866C11.0561 5.33837 10.8776 5.16162 10.6573 5.16162Z"
                  fill="white" />
                <path
                  d="M11.0561 7.84138C11.0561 7.62109 10.8776 7.44434 10.6573 7.44434H4.6527C4.43241 7.44434 4.25391 7.62109 4.25391 7.84138C4.25391 8.06167 4.43241 8.23842 4.6527 8.23842H10.6573C10.8776 8.23842 11.0561 8.06167 11.0561 7.84138Z"
                  fill="white" />
                <path
                  d="M4.6527 9.72803C4.43241 9.72803 4.25391 9.90478 4.25391 10.1251C4.25391 10.3454 4.43241 10.5221 4.6527 10.5221H8.08529C8.30558 10.5221 8.48409 10.3454 8.48409 10.1251C8.48409 9.90478 8.30558 9.72803 8.08529 9.72803H4.6527Z"
                  fill="white" />
              </svg>
            </button>
            <div class="share-btn" [tooltip]="tooltip" (click)="copyURL()">
              <svg width="14" height="16" viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M11.5572 15.1391C10.9601 15.1391 10.4525 14.9301 10.0345 14.5121C9.61649 14.0941 9.40749 13.5865 9.40749 12.9894C9.40749 12.9177 9.4254 12.7505 9.46123 12.4878L4.4273 9.54983C4.23621 9.72897 4.01526 9.8693 3.76446 9.97081C3.51366 10.0723 3.24495 10.1231 2.95832 10.1231C2.36117 10.1231 1.8536 9.91408 1.4356 9.49608C1.01759 9.07808 0.808594 8.57051 0.808594 7.97336C0.808594 7.37622 1.01759 6.86864 1.4356 6.45064C1.8536 6.03264 2.36117 5.82364 2.95832 5.82364C3.24495 5.82364 3.51366 5.8744 3.76446 5.97591C4.01526 6.07743 4.23621 6.21775 4.4273 6.3969L9.46123 3.45894C9.43735 3.37534 9.42242 3.29473 9.41645 3.2171C9.41047 3.13947 9.40749 3.05288 9.40749 2.95734C9.40749 2.3602 9.61649 1.85262 10.0345 1.43462C10.4525 1.01662 10.9601 0.807617 11.5572 0.807617C12.1544 0.807617 12.6619 1.01662 13.0799 1.43462C13.4979 1.85262 13.7069 2.3602 13.7069 2.95734C13.7069 3.55449 13.4979 4.06206 13.0799 4.48006C12.6619 4.89806 12.1544 5.10706 11.5572 5.10706C11.2706 5.10706 11.0019 5.05631 10.7511 4.95479C10.5003 4.85328 10.2793 4.71295 10.0882 4.5338L5.0543 7.47176C5.07818 7.55536 5.09311 7.63597 5.09908 7.7136C5.10506 7.79123 5.10804 7.87782 5.10804 7.97336C5.10804 8.06891 5.10506 8.15549 5.09908 8.23312C5.09311 8.31075 5.07818 8.39136 5.0543 8.47496L10.0882 11.4129C10.2793 11.2338 10.5003 11.0934 10.7511 10.9919C11.0019 10.8904 11.2706 10.8397 11.5572 10.8397C12.1544 10.8397 12.6619 11.0487 13.0799 11.4667C13.4979 11.8847 13.7069 12.3922 13.7069 12.9894C13.7069 13.5865 13.4979 14.0941 13.0799 14.5121C12.6619 14.9301 12.1544 15.1391 11.5572 15.1391ZM11.5572 13.706C11.7602 13.706 11.9304 13.6373 12.0678 13.4999C12.2051 13.3626 12.2738 13.1924 12.2738 12.9894C12.2738 12.7864 12.2051 12.6162 12.0678 12.4788C11.9304 12.3415 11.7602 12.2728 11.5572 12.2728C11.3542 12.2728 11.184 12.3415 11.0467 12.4788C10.9093 12.6162 10.8406 12.7864 10.8406 12.9894C10.8406 13.1924 10.9093 13.3626 11.0467 13.4999C11.184 13.6373 11.3542 13.706 11.5572 13.706ZM2.95832 8.68994C3.16135 8.68994 3.33153 8.62126 3.46888 8.48392C3.60622 8.34658 3.67489 8.17639 3.67489 7.97336C3.67489 7.77033 3.60622 7.60015 3.46888 7.4628C3.33153 7.32546 3.16135 7.25679 2.95832 7.25679C2.75529 7.25679 2.5851 7.32546 2.44776 7.4628C2.31041 7.60015 2.24174 7.77033 2.24174 7.97336C2.24174 8.17639 2.31041 8.34658 2.44776 8.48392C2.5851 8.62126 2.75529 8.68994 2.95832 8.68994ZM11.5572 3.67392C11.7602 3.67392 11.9304 3.60524 12.0678 3.4679C12.2051 3.33056 12.2738 3.16037 12.2738 2.95734C12.2738 2.75431 12.2051 2.58412 12.0678 2.44678C11.9304 2.30944 11.7602 2.24077 11.5572 2.24077C11.3542 2.24077 11.184 2.30944 11.0467 2.44678C10.9093 2.58412 10.8406 2.75431 10.8406 2.95734C10.8406 3.16037 10.9093 3.33056 11.0467 3.4679C11.184 3.60524 11.3542 3.67392 11.5572 3.67392Z"
                  fill="white" />
              </svg>
            </div>
            <ng-template #tooltip>
              {{
              !linkedCopied
              ? "Click to copy link"
              : "link copied to you clipboard!"
              }}
            </ng-template>
          </div>
        </div>
        <div class="fc-address-detail">
          <div class="as-user">{{ vm.expertDetail.companyType }}</div>
          <div class="fc-text-bar">
            <label>Join Date</label>
            <span> {{ vm?.createdAt | date : "MMM dd yyyy" }}</span>
          </div>
          <div class="fc-text-bar">
            <label>Office number</label>
            <span>
              <div role="button" [tooltip]="
                  vm.expertDetail.workMobileNumer ? 'Work Mobile number' : ''
                ">
                <a href="tel:{{ vm.expertDetail.workMobileNumberCountryCode }}{{
                    vm.expertDetail.workMobileNumer
                  }}" *ngIf="
                    vm.expertDetail.workMobileNumber;
                    else noWorkMobileNumber
                  " role="button">
                  {{ vm.expertDetail.workMobileNumberCountryCode }}
                  {{ vm.expertDetail.workMobileNumber | phoneFormat }}</a>
                <ng-template #noWorkMobileNumber> Not Available. </ng-template>
              </div>
            </span>
          </div>
          <div class="fc-text-bar">
            <label>Direct Number</label>
            <span>
              <div role="button" tooltip="Phone number">
                <a href="tel:{{ vm.countryCode }}{{ vm.phoneNumber }}" role="button">
                  {{ vm.countryCode }}
                  {{ vm.phoneNumber | phoneFormat }}</a>
              </div>
            </span>
          </div>
          <div class="fc-text-bar website-link">
            <label>Website</label>
            <span>
              <div tooltip="Visit {{ vm.expertDetail.companyWebsite }}">
                <i class="fa fa-globe text-primary"></i> &nbsp;
                <a target="_blank" role="button">{{
                  vm.expertDetail.companyWebsite
                  }}</a>
              </div>
            </span>
          </div>
          <!-- <div class="fc-text-bar">
              <label>Organization Vertical</label>
              <span>Healthcare</span>
            </div>             -->
        </div>
      </div>
    </div>
  </div>
  <div class="fc-container">
    <div class="fc-proile-tab-section">
      <div class="fc-left-bar">
        <div class="fc-card d-flex flex-column gap-2">

          <div class="text-up back-profile" *ngIf="!isCompany" (click)="navigateToCompany()" role="button">
            <div class="company-profile">
              <img [src]="vm.companyImage" class="img-thumbnail " appImg />
            </div>
            <div class="d-flex flex-column company-name justify-content-center">
              <b>{{ vm.companyName | titlecase }}</b>
            </div>
          </div>

          <div class="text-up">
            <span><svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M6 7C6.69223 7 7.36892 6.79473 7.9445 6.41015C8.52007 6.02556 8.96867 5.47894 9.23358 4.83939C9.49849 4.19985 9.5678 3.49612 9.43275 2.81719C9.2977 2.13825 8.96436 1.51461 8.47487 1.02513C7.98539 0.535644 7.36175 0.202301 6.68282 0.0672531C6.00388 -0.0677952 5.30015 0.00151649 4.66061 0.266423C4.02107 0.53133 3.47444 0.979934 3.08986 1.55551C2.70527 2.13108 2.5 2.80777 2.5 3.5C2.5 4.42826 2.86875 5.3185 3.52513 5.97488C4.1815 6.63125 5.07174 7 6 7ZM6 1C6.49445 1 6.9778 1.14662 7.38893 1.42133C7.80005 1.69603 8.12048 2.08648 8.3097 2.54329C8.49892 3.00011 8.54843 3.50278 8.45196 3.98773C8.3555 4.47268 8.1174 4.91814 7.76777 5.26777C7.41814 5.6174 6.97268 5.8555 6.48773 5.95197C6.00277 6.04843 5.50011 5.99892 5.04329 5.8097C4.58648 5.62048 4.19603 5.30005 3.92133 4.88893C3.64662 4.4778 3.5 3.99446 3.5 3.5C3.5 2.83696 3.76339 2.20108 4.23223 1.73223C4.70107 1.26339 5.33696 1 6 1Z"
                  fill="#014681" />
                <path
                  d="M6.5 8H5.5C4.04131 8 2.64236 8.57946 1.61091 9.61091C0.579463 10.6424 0 12.0413 0 13.5C0 13.6326 0.0526785 13.7598 0.146447 13.8536C0.240215 13.9473 0.367392 14 0.5 14H11.5C11.6326 14 11.7598 13.9473 11.8536 13.8536C11.9473 13.7598 12 13.6326 12 13.5C12 12.0413 11.4205 10.6424 10.3891 9.61091C9.35764 8.57946 7.95869 8 6.5 8ZM1.03 13C1.15295 11.9003 1.67676 10.8845 2.50134 10.1466C3.32592 9.40873 4.39347 9.00053 5.5 9H6.5C7.60653 9.00053 8.67408 9.40873 9.49866 10.1466C10.3232 10.8845 10.8471 11.9003 10.97 13H1.03Z"
                  fill="#014681" />
              </svg>
            </span>
            <label>{{ vm.expertDetail.roleName }} at
              {{ vm.expertDetail.companyName }}</label>
          </div>

          <div class="text-up">
            <span>
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_907_4297)">
                  <path
                    d="M8.00018 16.0003C7.88687 16.0008 7.77456 15.979 7.66968 15.9361C7.5648 15.8932 7.4694 15.8301 7.38893 15.7503L3.27893 11.6403C2.63 10.995 2.11633 10.2267 1.76797 9.38035C1.41962 8.53404 1.24359 7.62674 1.25018 6.71157C1.24738 5.8255 1.42139 4.94778 1.76202 4.1298C2.10265 3.31182 2.60305 2.57001 3.23393 1.94782C4.50808 0.702865 6.21878 0.00585937 8.00018 0.00585938C9.78158 0.00585938 11.4923 0.702865 12.7664 1.94782C13.3973 2.57001 13.8977 3.31182 14.2383 4.1298C14.579 4.94778 14.753 5.8255 14.7502 6.71157C14.7568 7.62674 14.5807 8.53404 14.2324 9.38035C13.884 10.2267 13.3704 10.995 12.7214 11.6403L8.61143 15.7428C8.53158 15.824 8.43646 15.8885 8.33155 15.9327C8.22664 15.9769 8.11402 15.9999 8.00018 16.0003ZM8.00018 0.750321C6.41403 0.746668 4.89046 1.36876 3.76018 2.48157C3.20023 3.03441 2.75616 3.69337 2.45395 4.41991C2.15175 5.14644 1.99748 5.92595 2.00018 6.71282C1.99418 7.52906 2.15106 8.33831 2.46163 9.09318C2.77221 9.84805 3.23024 10.5334 3.80893 11.1091L7.91768 15.2116C7.92829 15.2226 7.94101 15.2313 7.95507 15.2373C7.96914 15.2433 7.98427 15.2464 7.99955 15.2464C8.01484 15.2464 8.02996 15.2433 8.04403 15.2373C8.0581 15.2313 8.07082 15.2226 8.08143 15.2116L12.1914 11.1091C12.7701 10.5334 13.2281 9.84805 13.5387 9.09318C13.8493 8.33831 14.0062 7.52906 14.0002 6.71282C14.0029 5.92595 13.8486 5.14644 13.5464 4.41991C13.2442 3.69337 12.8001 3.03441 12.2402 2.48157C11.1099 1.36876 9.58632 0.746668 8.00018 0.750321Z"
                    fill="#014681" />
                  <path
                    d="M8.00037 10.1758C7.21253 10.1754 6.44917 9.90196 5.8403 9.40198C5.23143 8.902 4.81471 8.20642 4.66109 7.4337C4.50747 6.66098 4.62646 5.8589 4.9978 5.16405C5.36914 4.46921 5.96986 3.92457 6.69765 3.62289C7.42545 3.32122 8.23532 3.28115 8.98934 3.50953C9.74336 3.7379 10.3949 4.22059 10.833 4.8754C11.2711 5.5302 11.4687 6.31662 11.3921 7.10073C11.3155 7.88484 10.9694 8.61815 10.4129 9.17578C10.0964 9.49308 9.72034 9.74476 9.30634 9.91636C8.89234 10.088 8.44853 10.1761 8.00037 10.1758ZM8.00037 4.11453C7.38567 4.11551 6.79033 4.32955 6.31572 4.7202C5.84112 5.11084 5.51662 5.65394 5.39748 6.25698C5.27835 6.86003 5.37195 7.48572 5.66235 8.0275C5.95275 8.56927 6.42198 8.99363 6.99013 9.22828C7.55828 9.46293 8.1902 9.49337 8.77827 9.31442C9.36634 9.13546 9.87419 8.75817 10.2153 8.24682C10.5564 7.73546 10.7098 7.12166 10.6491 6.50996C10.5885 5.89826 10.3177 5.32649 9.88287 4.89203C9.63567 4.64496 9.34214 4.4491 9.0191 4.31568C8.69607 4.18226 8.34988 4.11391 8.00037 4.11453Z"
                    fill="#014681" />
                </g>
                <defs>
                  <clipPath id="clip0_907_4297">
                    <rect width="16" height="16" fill="white" />
                  </clipPath>
                </defs>
              </svg>
            </span>
            <label>{{ fullAddress | titlecase }}</label>
          </div>

          <div class="text-up">
            <span><svg width="16" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M13.6118 13.0002H2.78823C1.8 13.0002 1 12.1968 1 11.2045V3.50179C1 2.50942 1.8 1.70605 2.78823 1.70605H13.6118C14.6 1.70605 15.4 2.50942 15.4 3.50179V11.2045C15.4471 12.1968 14.6 13.0002 13.6118 13.0002Z"
                  stroke="#014681" stroke-miterlimit="10" stroke-linecap="round" />
                <path d="M7.99912 8.06958L1.22266 2.48242" stroke="#014681" stroke-miterlimit="10"
                  stroke-linecap="round" stroke-linejoin="round" />
                <path d="M14.7765 2.48242L8 8.06958" stroke="#014681" stroke-miterlimit="10" stroke-linecap="round"
                  stroke-linejoin="round" />
              </svg>
            </span>
            <a class="m-0" href="mailto:{{ vm.email }}"> {{ vm.email }}</a>
          </div>
        </div>

        <div class="fc-card p-0" *ngIf="vm.isCertified">
          <div class="fc-connection-card">
            <span class="chip beta chip-success"> Certified </span>
          </div>
        </div>

        <!-- <div class="fc-card p-0">
          <div class="fc-connection-card">
            <h4 class="pt-3 mb-0 fw-semibold">Connection and referrals</h4>
            <div class="list-of-connection">
              <div class="ref-profile">
                <img src="../../../../../assets/images/create-profile.jpg">
              </div>
              <div class="refer-name">
                <label>Steve Jerbic</label>
                <div class="refer-post"><b>Apple</b> | <span class="single-expertise">VOIP</span></div>
              </div>
              <div class="fc-connect-user">                
                <button class="add-connection-btn">
                  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8.06664 13.167H9.73987V9.83366H13.0863V8.16699H9.73987V4.83366H8.06664V8.16699H4.72018V9.83366H8.06664V13.167ZM8.90325 17.3337C7.74593 17.3337 6.65834 17.1149 5.64046 16.6774C4.62258 16.2399 3.73716 15.6462 2.98421 14.8962C2.23125 14.1462 1.63517 13.2642 1.19594 12.2503C0.756721 11.2364 0.537109 10.1531 0.537109 9.00033C0.537109 7.84755 0.756721 6.76421 1.19594 5.75033C1.63517 4.73644 2.23125 3.85449 2.98421 3.10449C3.73716 2.35449 4.62258 1.76074 5.64046 1.32324C6.65834 0.885742 7.74593 0.666992 8.90325 0.666992C10.0606 0.666992 11.1482 0.885742 12.166 1.32324C13.1839 1.76074 14.0693 2.35449 14.8223 3.10449C15.5752 3.85449 16.1713 4.73644 16.6106 5.75033C17.0498 6.76421 17.2694 7.84755 17.2694 9.00033C17.2694 10.1531 17.0498 11.2364 16.6106 12.2503C16.1713 13.2642 15.5752 14.1462 14.8223 14.8962C14.0693 15.6462 13.1839 16.2399 12.166 16.6774C11.1482 17.1149 10.0606 17.3337 8.90325 17.3337ZM8.90325 15.667C10.7717 15.667 12.3543 15.0212 13.651 13.7295C14.9478 12.4378 15.5962 10.8614 15.5962 9.00033C15.5962 7.13921 14.9478 5.56283 13.651 4.27116C12.3543 2.97949 10.7717 2.33366 8.90325 2.33366C7.03481 2.33366 5.45222 2.97949 4.15547 4.27116C2.85871 5.56283 2.21034 7.13921 2.21034 9.00033C2.21034 10.8614 2.85871 12.4378 4.15547 13.7295C5.45222 15.0212 7.03481 15.667 8.90325 15.667Z" fill="#014681"/>
                    </svg>                    
                </button>
              </div>
            </div>

            <div class="list-of-connection">
              <div class="ref-profile">
                <img src="../../../../../assets/images/create-profile.jpg">
              </div>
              <div class="refer-name">
                <label>Steve Jerbic</label>
                <div class="refer-post"><b>Apple</b> | <span class="single-expertise">VOIP</span></div>
              </div>
              <div class="fc-connect-user">                
                <button class="add-connection-btn">
                  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8.06664 13.167H9.73987V9.83366H13.0863V8.16699H9.73987V4.83366H8.06664V8.16699H4.72018V9.83366H8.06664V13.167ZM8.90325 17.3337C7.74593 17.3337 6.65834 17.1149 5.64046 16.6774C4.62258 16.2399 3.73716 15.6462 2.98421 14.8962C2.23125 14.1462 1.63517 13.2642 1.19594 12.2503C0.756721 11.2364 0.537109 10.1531 0.537109 9.00033C0.537109 7.84755 0.756721 6.76421 1.19594 5.75033C1.63517 4.73644 2.23125 3.85449 2.98421 3.10449C3.73716 2.35449 4.62258 1.76074 5.64046 1.32324C6.65834 0.885742 7.74593 0.666992 8.90325 0.666992C10.0606 0.666992 11.1482 0.885742 12.166 1.32324C13.1839 1.76074 14.0693 2.35449 14.8223 3.10449C15.5752 3.85449 16.1713 4.73644 16.6106 5.75033C17.0498 6.76421 17.2694 7.84755 17.2694 9.00033C17.2694 10.1531 17.0498 11.2364 16.6106 12.2503C16.1713 13.2642 15.5752 14.1462 14.8223 14.8962C14.0693 15.6462 13.1839 16.2399 12.166 16.6774C11.1482 17.1149 10.0606 17.3337 8.90325 17.3337ZM8.90325 15.667C10.7717 15.667 12.3543 15.0212 13.651 13.7295C14.9478 12.4378 15.5962 10.8614 15.5962 9.00033C15.5962 7.13921 14.9478 5.56283 13.651 4.27116C12.3543 2.97949 10.7717 2.33366 8.90325 2.33366C7.03481 2.33366 5.45222 2.97949 4.15547 4.27116C2.85871 5.56283 2.21034 7.13921 2.21034 9.00033C2.21034 10.8614 2.85871 12.4378 4.15547 13.7295C5.45222 15.0212 7.03481 15.667 8.90325 15.667Z" fill="#014681"/>
                    </svg>                    
                </button>
              </div>
            </div>

            <div class="list-of-connection">
              <div class="ref-profile">
                <img src="../../../../../assets/images/create-profile.jpg">
              </div>
              <div class="refer-name">
                <label>Steve Jerbic</label>
                <div class="refer-post"><b>Apple</b> | <span class="single-expertise">VOIP</span></div>
              </div>
              <div class="fc-connect-user">                
                <span class="online"></span>
                <span class="varified-connection">
                  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8.31336 12.4374C8.22288 12.4379 8.13319 12.4206 8.04942 12.3864C7.96566 12.3521 7.88947 12.3017 7.82523 12.238L5.07523 9.48801C4.94578 9.35855 4.87305 9.18297 4.87305 8.99988C4.87305 8.8168 4.94578 8.64122 5.07523 8.51176C5.20469 8.3823 5.38028 8.30957 5.56336 8.30957C5.74644 8.30957 5.92203 8.3823 6.05148 8.51176L8.31336 10.7805L12.6377 6.44926C12.7672 6.3198 12.9428 6.24707 13.1259 6.24707C13.3089 6.24707 13.4845 6.3198 13.614 6.44926C13.7434 6.57872 13.8162 6.7543 13.8162 6.93738C13.8162 7.12047 13.7434 7.29605 13.614 7.42551L8.80148 12.238C8.73724 12.3017 8.66106 12.3521 8.57729 12.3864C8.49353 12.4206 8.40384 12.4379 8.31336 12.4374Z" fill="#8A9A5B"/>
                    <path d="M9 17.9375C7.23233 17.9375 5.50436 17.4133 4.0346 16.4313C2.56483 15.4492 1.41929 14.0534 0.742831 12.4202C0.0663725 10.7871 -0.11062 8.99009 0.234236 7.25638C0.579091 5.52268 1.43031 3.93017 2.68024 2.68024C3.93017 1.43031 5.52268 0.579091 7.25638 0.234236C8.99009 -0.11062 10.7871 0.0663725 12.4202 0.742831C14.0534 1.41929 15.4492 2.56483 16.4313 4.0346C17.4133 5.50436 17.9375 7.23233 17.9375 9C17.9375 11.3704 16.9959 13.6437 15.3198 15.3198C13.6437 16.9959 11.3704 17.9375 9 17.9375ZM9 1.4375C7.50428 1.4375 6.04215 1.88104 4.7985 2.71201C3.55486 3.54299 2.58555 4.72409 2.01316 6.10596C1.44078 7.48783 1.29101 9.00839 1.58282 10.4754C1.87462 11.9424 2.59487 13.2899 3.65251 14.3475C4.71014 15.4051 6.05765 16.1254 7.52463 16.4172C8.99162 16.709 10.5122 16.5592 11.894 15.9868C13.2759 15.4145 14.457 14.4452 15.288 13.2015C16.119 11.9579 16.5625 10.4957 16.5625 9C16.5625 6.9943 15.7657 5.07075 14.3475 3.65251C12.9293 2.23427 11.0057 1.4375 9 1.4375Z" fill="#8A9A5B"/>
                    </svg>                    
                </span>
              </div>
            </div>

            <div class="see-all-bar">
              Sell All
            </div>
          </div>
        </div> -->

        <div class="fc-card p-0 pt-3 company-offer-card">
          <label class="fc-company-offer-hdr mb-3">Company Offering</label>
          <app-expertise [companyId]="vm.expertDetail.companyId"></app-expertise>
        </div>
      </div>
      <div class="fc-center-bar">
        <div *ngIf="showAllConnections">
          <span class="back-icon" (click)="back()" role="button"><img src="../../../../../assets/images/arrow_back.svg">
            Back to About Us</span>
        </div>

        <!-- Connections and Referrals Section -->
        <div class="fc-card connectionsAndReferrals-list-here" *ngIf="showAllConnections">
            <div class="fc-connection-card-header">
              <h6 class="pt-3 mb-0 fw-semibold">Connections and Referrals</h6>
              <label class="fs-12 mb-0">Only you can see this lead section</label>
            </div>
          <div class="list-of-connection" *ngFor="let connection of connectionsAndReferrals">
            <div class="ref-profile">
              <!-- <img [src]="connection.profilePhoto || './assets/images/create-profile.jpg'"> -->
              <img [src]="connection.profilePhoto ? connection.profilePhoto : 'assets/images/create-profile.jpg'"
                alt="Company Profile Photo" onerror="this.src='assets/images/create-profile.jpg'" />
            </div>
            <div class="refer-name" (click)="navigateToExpertProfile(connection)">
              <label>{{ connection.name }}</label>
              <div class="refer-post">
                <b>{{ connection.companyName }}</b> | <span class="single-expertise">{{ connection.expertise }}</span>
              </div>
              <!-- ...existing code... -->
            </div>
            <div class="fc-connect-user">
              <button class="add-connection-btn">
                <ng-container *ngIf="connection.isCompleted">
                  <span class="request-accepted-btn">
                    <svg width="23" height="22" viewBox="0 0 23 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M11.2528 14.4374C11.1623 14.4379 11.0726 14.4206 10.9889 14.3864C10.9051 14.3521 10.8289 14.3017 10.7647 14.238L8.01469 11.488C7.88523 11.3585 7.8125 11.183 7.8125 10.9999C7.8125 10.8168 7.88523 10.6412 8.01469 10.5118C8.14415 10.3823 8.31973 10.3096 8.50281 10.3096C8.6859 10.3096 8.86148 10.3823 8.99094 10.5118L11.2528 12.7805L15.5772 8.44926C15.7066 8.3198 15.8822 8.24707 16.0653 8.24707C16.2484 8.24707 16.424 8.3198 16.5534 8.44926C16.6829 8.57872 16.7556 8.7543 16.7556 8.93738C16.7556 9.12047 16.6829 9.29605 16.5534 9.42551L11.7409 14.238C11.6767 14.3017 11.6005 14.3521 11.5167 14.3864C11.433 14.4206 11.3433 14.4379 11.2528 14.4374Z"
                        fill="#8A9A5B" />
                      <path
                        d="M11.9395 19.9375C10.1718 19.9375 8.44381 19.4133 6.97405 18.4313C5.50428 17.4492 4.35874 16.0534 3.68228 14.4202C3.00583 12.7871 2.82883 10.9901 3.17369 9.25638C3.51854 7.52268 4.36976 5.93017 5.61969 4.68024C6.86962 3.43031 8.46213 2.57909 10.1958 2.23424C11.9295 1.88938 13.7266 2.06637 15.3597 2.74283C16.9928 3.41929 18.3887 4.56483 19.3707 6.0346C20.3528 7.50436 20.877 9.23233 20.877 11C20.877 13.3704 19.9353 15.6437 18.2592 17.3198C16.5831 18.9959 14.3098 19.9375 11.9395 19.9375ZM11.9395 3.4375C10.4437 3.4375 8.9816 3.88104 7.73796 4.71201C6.49431 5.54299 5.52501 6.72409 4.95262 8.10596C4.38023 9.48783 4.23047 11.0084 4.52227 12.4754C4.81407 13.9424 5.53433 15.2899 6.59196 16.3475C7.6496 17.4051 8.99711 18.1254 10.4641 18.4172C11.9311 18.709 13.4516 18.5592 14.8335 17.9868C16.2154 17.4145 17.3965 16.4452 18.2274 15.2015C19.0584 13.9579 19.502 12.4957 19.502 11C19.502 8.9943 18.7052 7.07075 17.287 5.65251C15.8687 4.23427 13.9452 3.4375 11.9395 3.4375Z"
                        fill="#8A9A5B" />
                    </svg>
                  </span>
                </ng-container>
                <ng-container
                  *ngIf="!connection.isRequestReceived && !connection.isRequestSent && !connection.isCompleted">
                  <span class="connect-request-btn" (click)="connectUser(connection)">
                    <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M10.0061 14.167H11.6793V10.8337H15.0258V9.16699H11.6793V5.83366H10.0061V9.16699H6.65963V10.8337H10.0061V14.167ZM10.8427 18.3337C9.68539 18.3337 8.59779 18.1149 7.57991 17.6774C6.56203 17.2399 5.67661 16.6462 4.92366 15.8962C4.17071 15.1462 3.57462 14.2642 3.1354 13.2503C2.69617 12.2364 2.47656 11.1531 2.47656 10.0003C2.47656 8.84755 2.69617 7.76421 3.1354 6.75033C3.57462 5.73644 4.17071 4.85449 4.92366 4.10449C5.67661 3.35449 6.56203 2.76074 7.57991 2.32324C8.59779 1.88574 9.68539 1.66699 10.8427 1.66699C12 1.66699 13.0876 1.88574 14.1055 2.32324C15.1234 2.76074 16.0088 3.35449 16.7617 4.10449C17.5147 4.85449 18.1108 5.73644 18.55 6.75033C18.9892 7.76421 19.2088 8.84755 19.2088 10.0003C19.2088 11.1531 18.9892 12.2364 18.55 13.2503C18.1108 14.2642 17.5147 15.1462 16.7617 15.8962C16.0088 16.6462 15.1234 17.2399 14.1055 17.6774C13.0876 18.1149 12 18.3337 10.8427 18.3337ZM10.8427 16.667C12.7111 16.667 14.2937 16.0212 15.5905 14.7295C16.8872 13.4378 17.5356 11.8614 17.5356 10.0003C17.5356 8.13921 16.8872 6.56283 15.5905 5.27116C14.2937 3.97949 12.7111 3.33366 10.8427 3.33366C8.97427 3.33366 7.39167 3.97949 6.09492 5.27116C4.79817 6.56283 4.14979 8.13921 4.14979 10.0003C4.14979 11.8614 4.79817 13.4378 6.09492 14.7295C7.39167 16.0212 8.97427 16.667 10.8427 16.667Z"
                        fill="#014681" />
                    </svg>
                  </span>
                </ng-container>
              </button>
            </div>
          </div>
        </div>

        <!-- about section -->
        <div class="d-flex flex-column gap-3 gap-sm-4" *ngIf="!showAboutSection">
          <ul class="category-navbar-item">
            <li class="nav-item" role="button" (click)="setActiveTab('aboutus')"
              [class.active]="activeTab === 'aboutus'">
              <a class="" aria-current="page">
                <span>
                  <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_580_4055)">
                      <path
                        d="M6 0.09375C2.74687 0.09375 0.09375 2.74687 0.09375 6C0.09375 9.25313 2.74687 11.9062 6 11.9062C9.25313 11.9062 11.9062 9.25313 11.9062 6C11.9062 2.74687 9.25313 0.09375 6 0.09375ZM6 11.1562C3.15938 11.1562 0.84375 8.84062 0.84375 6C0.84375 3.15938 3.15938 0.84375 6 0.84375C8.84062 0.84375 11.1562 3.15938 11.1562 6C11.1562 8.84062 8.84062 11.1562 6 11.1562Z"
                        fill="#014681" />
                      <path d="M6.375 5.10938H5.625V8.85938H6.375V5.10938Z" fill="#014681" />
                      <path d="M6.375 3.32812H5.625V4.07812H6.375V3.32812Z" fill="#014681" />
                    </g>
                    <defs>
                      <clipPath id="clip0_580_4055">
                        <rect width="12" height="12" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                </span>
                About us
              </a>
            </li>
            <li class="nav-item" role="button" (click)="setActiveTab('request-services')"
              [class.active]="activeTab === 'request-services'">
              <a class="" aria-current="page">
                <span>
                  <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_580_4060)">
                      <path
                        d="M8.25 8.25V10.5H2.25V1.5H6V0.75H2.25C2.05109 0.75 1.86032 0.829018 1.71967 0.96967C1.57902 1.11032 1.5 1.30109 1.5 1.5V10.5C1.5 10.6989 1.57902 10.8897 1.71967 11.0303C1.86032 11.171 2.05109 11.25 2.25 11.25H8.25C8.44891 11.25 8.63968 11.171 8.78033 11.0303C8.92098 10.8897 9 10.6989 9 10.5V8.25H8.25Z"
                        fill="#014681" />
                      <path
                        d="M11.0775 2.15999L9.84 0.922491C9.72784 0.812555 9.57705 0.750977 9.42 0.750977C9.26295 0.750977 9.11216 0.812555 9 0.922491L3.75 6.17249V8.24999H5.82375L11.0738 2.99999C11.1837 2.88783 11.2453 2.73704 11.2453 2.57999C11.2453 2.42294 11.1837 2.27215 11.0738 2.15999H11.0775ZM5.5125 7.49999H4.5V6.48749L8.04 2.94374L9.05625 3.95999L5.5125 7.49999ZM9.585 3.43124L8.56875 2.41499L9.42 1.56374L10.4362 2.57999L9.585 3.43124Z"
                        fill="#014681" />
                    </g>
                    <defs>
                      <clipPath id="clip0_580_4060">
                        <rect width="12" height="12" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                </span>
                Request a service
              </a>
            </li>
            <li class="nav-item" role="button" (click)="setActiveTab('add-review')"
              [class.active]="activeTab === 'add-review'">
              <a class="" aria-current="page">
                <span>
                  <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_580_4096)">
                      <path
                        d="M3.00071 11.6251C2.84313 11.6249 2.68961 11.575 2.56196 11.4826C2.45195 11.3992 2.36545 11.2886 2.3109 11.1618C2.25635 11.0349 2.23561 10.8961 2.25071 10.7589L2.52446 7.6876L0.563215 5.3776C0.474612 5.27221 0.41462 5.14578 0.389006 5.01049C0.363393 4.8752 0.373021 4.7356 0.416965 4.6051C0.457458 4.47793 0.530212 4.36341 0.628135 4.27271C0.726058 4.18202 0.845809 4.11824 0.975715 4.0876L3.83321 3.4126L5.33322 0.787605C5.39991 0.668128 5.49728 0.568618 5.61528 0.499347C5.73328 0.430075 5.86763 0.393555 6.00447 0.393555C6.1413 0.393555 6.27565 0.430075 6.39365 0.499347C6.51165 0.568618 6.60902 0.668128 6.67571 0.787605L8.17571 3.43135L11.0332 4.10635C11.1631 4.13699 11.2829 4.20077 11.3808 4.29146C11.4787 4.38216 11.5515 4.49668 11.592 4.62385C11.6359 4.75435 11.6455 4.89395 11.6199 5.02924C11.5943 5.16453 11.5343 5.29096 11.4457 5.39635L9.49572 7.6876L9.76947 10.7589C9.78248 10.8978 9.75886 11.0377 9.70096 11.1647C9.64307 11.2917 9.55291 11.4013 9.43947 11.4826C9.33219 11.5602 9.2063 11.608 9.07457 11.6212C8.94284 11.6343 8.80997 11.6124 8.68947 11.5576L6.00071 10.3426L3.31571 11.5576C3.21684 11.6027 3.10936 11.6257 3.00071 11.6251ZM6.01946 1.1251L4.40696 3.91135C4.3815 3.95626 4.34705 3.99543 4.30575 4.0264C4.26445 4.05737 4.2172 4.07949 4.16696 4.09135L1.16696 4.8001C1.14446 4.8376 1.14071 4.8601 1.16696 4.87885L3.21071 7.3126C3.24236 7.35034 3.26627 7.39393 3.28107 7.4409C3.29587 7.48787 3.30127 7.5373 3.29696 7.58635L3.00821 10.8114C2.97446 10.8751 3.00071 10.8751 3.00071 10.8751L5.84321 9.5926C5.89156 9.57087 5.94396 9.55964 5.99696 9.55964C6.04997 9.55964 6.10237 9.57087 6.15071 9.5926L9.00072 10.8751C9.00072 10.8751 9.03446 10.8751 9.03071 10.8264L8.74197 7.60135C8.73766 7.5523 8.74306 7.50287 8.75786 7.4559C8.77266 7.40893 8.79657 7.36534 8.82822 7.32761L10.8757 4.89385C10.88 4.88576 10.8822 4.87675 10.8822 4.8676C10.8822 4.85846 10.88 4.84945 10.8757 4.84135L7.83821 4.09135C7.78798 4.07949 7.74073 4.05737 7.69943 4.0264C7.65813 3.99543 7.62368 3.95626 7.59822 3.91135L6.01946 1.1251Z"
                        fill="#170F49" />
                    </g>
                    <defs>
                      <clipPath id="clip0_580_4096">
                        <rect width="12" height="12" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>

                </span>
                Review
              </a>
            </li>
          </ul>
          <div *ngIf="activeTab === 'aboutus'" class="tab-content">
            <div class="fc-card">
              <div class="about-us-card">
                <label>About</label>
                <p *ngIf="vm.expertDetail.aboutMe">{{ vm.expertDetail.aboutMe || "" }}</p>
                <div class="empty-state" *ngIf="!vm.expertDetail.aboutMe">
                  <div class="empty-state-content">
                    <h2>No About Available</h2>
                    <p>
                      There is currently no about us to display. Please check back
                      later.
                    </p>
                  </div>
                </div>
                <label>Strength and Ability</label>
                <p>{{ vm.expertDetail.strengthAndAbility || "" }}</p>

                <label>Message to audience</label>
                <p>{{ vm.expertDetail.messageToAudience || "" }}</p>
              </div>
            </div>
            <div class="fc-self-intro mt-3">
              <label class="fw-semibold fs-6 mb-2">Videos</label>
              <div class="about-container" *ngIf="!videosLoading">
                <carousel [showIndicators]="false" *ngIf="elevatorPitches?.length" [noPause]="false" [noWrap]="true">
                  <slide *ngFor="let link of elevatorPitches" style="height: 100%">
                    <app-yt-video [link]="link.url"></app-yt-video>
                  </slide>
                </carousel>
              </div>
              <div class="empty-state" *ngIf="!elevatorPitches?.length && !videosLoading">
                <div class="empty-state-content">
                  <h2>No Videos Available</h2>
                  <p>There is currently no videos available to display.</p>
                </div>
              </div>
              <div class="text-center" *ngIf="videosLoading">
                <app-spinner></app-spinner>
              </div>

            </div>
          </div>
          <div *ngIf="activeTab === 'request-services'" class="tab-content">
            <div class="fc-card p-3 p-sm-4">
              <div class="col-sm-12">
                <div class="row mb-0 mb-sm-3">
                  <div class="col-sm-6 mb-3">
                    <div class="form-group">
                      <label class="form-label">First Name</label>
                      <input type="text" placeholder="Enter your first name" class="form-control">
                    </div>
                  </div>
                  <div class="col-sm-6 mb-3">
                    <div class="form-group">
                      <label class="form-label">Last Name</label>
                      <input type="text" placeholder="Enter your last name" class="form-control">
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-sm-12">
                <div class="row mb-0 mb-sm-3">
                  <div class="col-sm-6 mb-3 mb-sm-0">
                    <div class="form-group">
                      <label class="form-label">Phone Number</label>
                      <input type="text" placeholder="Enter your Phone" class="form-control">
                    </div>
                  </div>
                  <div class="col-sm-6 mb-3 mb-sm-0">
                    <div class="form-group">
                      <label class="form-label">Email</label>
                      <input type="text" placeholder="Enter your email" class="form-control">
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-sm-12">
                <div class="row mb-0 mb-sm-3">
                  <div class="col-sm-6 mb-3 mb-sm-0">
                    <div class="form-group">
                      <label class="form-label">Company Name</label>
                      <input type="text" placeholder="Enter company name" class="form-control">
                    </div>
                  </div>
                  <div class="col-sm-6 mb-3 mb-sm-0">
                    <div class="form-group">
                      <label class="form-label">Postal/Zip code</label>
                      <input type="text" placeholder="Enter Zip code" class="form-control">
                    </div>
                  </div>
                </div>
              </div>

              <div class="col-sm-12 mb-3">
                <div class="form-group">
                  <label class="form-label">Services item</label>
                  <select class="form-control">
                    <option>Choose your services</option>
                    <option>Sales inquiries</option>
                  </select>
                </div>
              </div>

              <div class="col-sm-12 mt-4 d-flex justify-content-end">
                <button class="request-btn">Submit</button>
              </div>
            </div>

            <div class="fc-write-your-thought">
            </div>
            <label class="fw-semibold fs-6 mt-3 mb-2">Service activities</label>
            <app-service-activities
              [userId]="vm.userId"
              [userProfilePhoto]="vm.profilePhoto"
              [userName]="vm.firstName + ' ' + vm.lastName"
              [userRole]="vm.expertDetail?.roleName"
              [isLoggedIn]="!!userState$.userId"
              [currentUserId]="userState$.userId"
              [currentUserPhoto]="userState$.profilePhoto"
              [currentUserName]="userState$.firstName + ' ' + userState$.lastName">
            </app-service-activities>
          </div>
          <div *ngIf="activeTab === 'add-review'" class="tab-content d-flex flex-column gap-3">
            <!-- <div class="fc-card fc-give-ratings p-4">
              <label>How would you rate connecting with Focile?</label>
              <div class="fc-give-star">
                <rating [(ngModel)]="ratingModel" [max]="maxRating" class="rating-b">
                </rating>
              </div>
              <textarea placeholder="Write your review..."></textarea>
              <button type="submit" class="submit-btn">Submit</button>
            </div> -->

            <div class="shorting-dropdown">
              <select>
                <option>Sort by newest review</option>
                <option>Latest</option>
                <option>Middle</option>
                <option>Old</option>
              </select>
            </div>


            <div class="fc-card">
              <div class="fc-company-review-section">
                <label class="fw-semibold mb-3">Company Reviews</label>
                <!-- ...existing code... -->
                <div class="fc-div--review">
                  <div class="fc--review-left">
                    <h5>{{ ratingData.avrageStarRatingCount }}</h5>
                    <div class="rating-start">
                      <rating [(ngModel)]="ratingData.avrageStarRatingCount" class="rating-b active" [readonly]="true">
                      </rating>
                    </div>
                    <label class="total-review">({{ ratingData.totalRatings }} Review)</label>
                  </div>
                  <div class="fc--percentage-right">
                    <div class="fc-pro-line">
                      <label class="star-label">5 stars</label>
                      <div class="progress-bar">
                        <div class="orange-bar" [style.width.%]="getPercentage(ratingData.fiveStarRatingCount)"></div>
                      </div>
                      <div class="each-count">{{ ratingData.fiveStarRatingCount }}</div>
                    </div>
                    <div class="fc-pro-line">
                      <label class="star-label">4 stars</label>
                      <div class="progress-bar">
                        <div class="orange-bar" [style.width.%]="getPercentage(ratingData.fourStarRatingCount)"></div>
                      </div>
                      <div class="each-count">{{ ratingData.fourStarRatingCount }}</div>
                    </div>
                    <div class="fc-pro-line">
                      <label class="star-label">3 stars</label>
                      <div class="progress-bar">
                        <div class="orange-bar" [style.width.%]="getPercentage(ratingData.threeStarRatingCount)"></div>
                      </div>
                      <div class="each-count">{{ ratingData.threeStarRatingCount }}</div>
                    </div>
                    <div class="fc-pro-line">
                      <label class="star-label">2 stars</label>
                      <div class="progress-bar">
                        <div class="orange-bar" [style.width.%]="getPercentage(ratingData.twoStarRatingCount)"></div>
                      </div>
                      <div class="each-count">{{ ratingData.twoStarRatingCount }}</div>
                    </div>
                    <div class="fc-pro-line">
                      <label class="star-label">1 stars</label>
                      <div class="progress-bar">
                        <div class="orange-bar" [style.width.%]="getPercentage(ratingData.oneStarRatingCount)"></div>
                      </div>
                      <div class="each-count">{{ ratingData.oneStarRatingCount }}</div>
                    </div>
                  </div>
                </div>
                <!-- ...existing code... -->
              </div>
            </div>

            <div class="fc-card">
              <ng-container *ngIf="vm?.comments?.length; else noCommentes">
                <div class="user-given-review" *ngFor="let c of vm.comments">
                  <div class="review-section">
                    <label>Created on: About {{ c.commentDate | fromNow }}</label>
                    <div class="separate-rate">
                      <rating [(ngModel)]="c.ratting" [readonly]="true"></rating>
                    </div>

                    <div class="user-review-avtar">
                      <img src="{{ c.addComentUserProfileImage }}" alt="User" class="me-3 rounded-4" width="50"
                        height="50" appImg class="img-avatar" />
                      <label class="user-name">{{ c.addComentUserName }}</label>
                    </div>
                    <p>{{ c.commentText }}</p>
                  </div>
                </div>
              </ng-container>
              <ng-template #noCommentes>
                <div class="alert alert-info">No comments yet on your profile.</div>
              </ng-template>
            </div>
          </div>
        </div>
      </div>

      <div class="fc-right-bar">
        <!-- right connection card-->
        <div class="fc-card p-0" *ngIf="!showAllConnections">
          <div class="fc-connection-card" [ngClass]="{'pb-3': (connectionsAndReferrals | slice: 0:3).length < 3}">
            <div class="fc-connection-card-header">
              <h4 class="pt-3 mb-0 fw-semibold">Connections and Referrals</h4>
              <label class="fs-12 mb-0">Only you can see this lead section</label>
            </div>
            <div class="list-of-connection" *ngFor="let connection of connectionsAndReferrals | slice: 0:3">
              <div class="ref-profile">
                <!-- <img [src]="connection.profilePhoto || './assets/images/create-profile.jpg'"> -->
                <img [src]="connection.profilePhoto ? connection.profilePhoto : 'assets/images/create-profile.jpg'"
                  alt="Company Profile Photo" onerror="this.src='assets/images/create-profile.jpg'" />
              </div>
              <div class="refer-name" role="button" (click)="navigateToExpertProfile(connection)">
                <label>{{ connection.name }}</label>
                <div class="refer-post"><b>{{ connection.companyName }}</b> | <span class="single-expertise">{{
                    connection.expertise }}</span></div>
              </div>
              <div class="fc-connect-user">
                <button class="add-connection-btn">
                  <ng-container *ngIf="connection.isCompleted">
                    <span class="request-accepted-btn">
                      <svg width="23" height="22" viewBox="0 0 23 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M11.2528 14.4374C11.1623 14.4379 11.0726 14.4206 10.9889 14.3864C10.9051 14.3521 10.8289 14.3017 10.7647 14.238L8.01469 11.488C7.88523 11.3585 7.8125 11.183 7.8125 10.9999C7.8125 10.8168 7.88523 10.6412 8.01469 10.5118C8.14415 10.3823 8.31973 10.3096 8.50281 10.3096C8.6859 10.3096 8.86148 10.3823 8.99094 10.5118L11.2528 12.7805L15.5772 8.44926C15.7066 8.3198 15.8822 8.24707 16.0653 8.24707C16.2484 8.24707 16.424 8.3198 16.5534 8.44926C16.6829 8.57872 16.7556 8.7543 16.7556 8.93738C16.7556 9.12047 16.6829 9.29605 16.5534 9.42551L11.7409 14.238C11.6767 14.3017 11.6005 14.3521 11.5167 14.3864C11.433 14.4206 11.3433 14.4379 11.2528 14.4374Z"
                          fill="#8A9A5B" />
                        <path
                          d="M11.9395 19.9375C10.1718 19.9375 8.44381 19.4133 6.97405 18.4313C5.50428 17.4492 4.35874 16.0534 3.68228 14.4202C3.00583 12.7871 2.82883 10.9901 3.17369 9.25638C3.51854 7.52268 4.36976 5.93017 5.61969 4.68024C6.86962 3.43031 8.46213 2.57909 10.1958 2.23424C11.9295 1.88938 13.7266 2.06637 15.3597 2.74283C16.9928 3.41929 18.3887 4.56483 19.3707 6.0346C20.3528 7.50436 20.877 9.23233 20.877 11C20.877 13.3704 19.9353 15.6437 18.2592 17.3198C16.5831 18.9959 14.3098 19.9375 11.9395 19.9375ZM11.9395 3.4375C10.4437 3.4375 8.9816 3.88104 7.73796 4.71201C6.49431 5.54299 5.52501 6.72409 4.95262 8.10596C4.38023 9.48783 4.23047 11.0084 4.52227 12.4754C4.81407 13.9424 5.53433 15.2899 6.59196 16.3475C7.6496 17.4051 8.99711 18.1254 10.4641 18.4172C11.9311 18.709 13.4516 18.5592 14.8335 17.9868C16.2154 17.4145 17.3965 16.4452 18.2274 15.2015C19.0584 13.9579 19.502 12.4957 19.502 11C19.502 8.9943 18.7052 7.07075 17.287 5.65251C15.8687 4.23427 13.9452 3.4375 11.9395 3.4375Z"
                          fill="#8A9A5B" />
                      </svg>
                    </span>
                  </ng-container>
                  <ng-container
                    *ngIf="!connection.isRequestReceived && !connection.isRequestSent && !connection.isCompleted">
                    <span class="connect-request-btn" (click)="connectUser(connection)">
                      <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M10.0061 14.167H11.6793V10.8337H15.0258V9.16699H11.6793V5.83366H10.0061V9.16699H6.65963V10.8337H10.0061V14.167ZM10.8427 18.3337C9.68539 18.3337 8.59779 18.1149 7.57991 17.6774C6.56203 17.2399 5.67661 16.6462 4.92366 15.8962C4.17071 15.1462 3.57462 14.2642 3.1354 13.2503C2.69617 12.2364 2.47656 11.1531 2.47656 10.0003C2.47656 8.84755 2.69617 7.76421 3.1354 6.75033C3.57462 5.73644 4.17071 4.85449 4.92366 4.10449C5.67661 3.35449 6.56203 2.76074 7.57991 2.32324C8.59779 1.88574 9.68539 1.66699 10.8427 1.66699C12 1.66699 13.0876 1.88574 14.1055 2.32324C15.1234 2.76074 16.0088 3.35449 16.7617 4.10449C17.5147 4.85449 18.1108 5.73644 18.55 6.75033C18.9892 7.76421 19.2088 8.84755 19.2088 10.0003C19.2088 11.1531 18.9892 12.2364 18.55 13.2503C18.1108 14.2642 17.5147 15.1462 16.7617 15.8962C16.0088 16.6462 15.1234 17.2399 14.1055 17.6774C13.0876 18.1149 12 18.3337 10.8427 18.3337ZM10.8427 16.667C12.7111 16.667 14.2937 16.0212 15.5905 14.7295C16.8872 13.4378 17.5356 11.8614 17.5356 10.0003C17.5356 8.13921 16.8872 6.56283 15.5905 5.27116C14.2937 3.97949 12.7111 3.33366 10.8427 3.33366C8.97427 3.33366 7.39167 3.97949 6.09492 5.27116C4.79817 6.56283 4.14979 8.13921 4.14979 10.0003C4.14979 11.8614 4.79817 13.4378 6.09492 14.7295C7.39167 16.0212 8.97427 16.667 10.8427 16.667Z"
                          fill="#014681" />
                      </svg>
                    </span>
                  </ng-container>
                </button>
              </div>
            </div>
            <div class="see-all-bar" role="button" *ngIf="connectionsAndReferrals.length > 3" (click)="showAll()">
              See All
            </div>
          </div>
        </div>
        <div class="fc-card">
          <div class="fc-my-expertise">
            <div class="heading-label">
              <label class="fw-semibold">My Expertise</label>
              <p>Please reach out if you are looking for:</p>
            </div>
            <div class="fc-expertise-tag">
              <ul *ngIf="experties.length > 0; else noExpertise">
                <li *ngFor="let expertise of showAllExpertise ? experties : (experties | slice: 0:3)">
                  #{{ expertise.name }}
                </li>
              </ul>
              <ng-template #noExpertise>
                <p>No expertise available at the moment.</p>
              </ng-template>
              <button *ngIf="experties.length > 2" (click)="toggleShowAll()" class="see-all-bar pt-3 h-auto">
                {{ showAllExpertise ? 'Show Less' : 'Show All' }}
              </button>
            </div>
          </div>
        </div>
        <div class="fc-card">
          <div class="my-follow">
            <div class="d-flex justify-content-between">
              <label>Following</label>
              <span>{{ vm.following || 0 }}</span>
            </div>
            <div class="d-flex justify-content-between">
              <label>Follower</label>
              <span>{{ vm.followers || 0 }}</span>
            </div>
          </div>
          <div class="fs-social-follow">
            <div tooltip="Visit" class="text-center" *ngFor="let item of socialMediaLinks">
              <a [href]="item.url" target="_blank">
                <img height="36" width="36" [src]="item.imageUrl" role="button" />
              </a>
            </div>
          </div>
        </div>

        <!--- Chat Section -->
        <div class="fc-card"
          [ngClass]="{'chat-enable': userState$.userId === vm.userId, 'chat-disable': userState$.userId !== vm.userId}">
          <div class="fc-saparete-chat-card">
            <div class="fc-expert-chat-row">
              <div class="fc-chat-icon">
                <span><img src="assets/images/chat-icon.svg" alt="Chat Icon">chat with {{ vm.firstName }}
                  {{ vm.lastName }}</span>
                <!-- <span><img src="assets/images/expand.svg" alt="Chat Icon"></span> -->
              </div>
            </div>

            <div id="scrollContainer" class="messages">
              <!-- <div class="message incoming">
                <div>Hello, Adel, How are you?</div><label class="time-stemp">Today, 8.30pm</label>
              </div>
              <div class="message outgoing">
                <div>Hi Dragon, can you please login to your portal and update the data to your expertise? </div><label
                  class="time-stemp">Today, 8.30pm</label>
              </div> -->
            </div>
            <div class="fc-chat-footer">
              <input type="text" class="form-control" aria-label="message…" placeholder="Write message…"
                name="message" />
              <div class="related-chat">
                <button type="submit">
                  <img src="assets/images/send.svg" alt="send">
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>