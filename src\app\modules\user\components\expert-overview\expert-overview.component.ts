import { Location } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { finalize, forkJoin } from 'rxjs';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { socialMediaImages } from 'src/app/shared/constant';
import { companyType, UserState } from 'src/app/shared/constant';
import { UserResponseData } from '../end-user-overview/end-user-overview.component';
import { Router } from '@angular/router';
import { CropperComponent } from 'angular-cropperjs';
import { ToastrService } from 'ngx-toastr';
import { EndUserService } from 'src/app/shared/services/end-user.service';
import { ModalService } from 'src/app/shared/services/modal.service';
import { UtilsService } from 'src/app/shared/services/utils.service';
import { ChatService } from 'src/app/shared/services/chat.service';
import { HubService } from 'src/app/shared/services/hub.service';
import {
  DEFAULT_CONNCTION_REQUEST_LIMIT,
  DropMessageType,
  FollowStatus,
} from 'src/app/shared/constant';
import { filterNonNull } from 'src/app/shared/oprators/filter-null-values';

@Component({
  selector: 'app-expert-overview',
  templateUrl: './expert-overview.component.html',
  styleUrls: ['./expert-overview.component.scss'],
})
export class ExpertOverviewComponent implements OnInit {
  @Input() expert!: any;
  vm: any;
  isAdmin = false;
  linkedCopied = false;
  videosLoading!: boolean;
  elevatorPitches!: any;
  userState$: any = {};
  socialMediaLinks: any[] = [];
  socialMediaTypes = socialMediaImages;
  fullAddress: string = '';
  ratingModel = null;
  maxRating = 5;
  connectLoading = false;
  connectionsAndReferrals: any[] = [];
  FollowStatus = FollowStatus;
  followStatus$ = this.hubService.getFollowStatus();
  showAllConnections: boolean = false;
  @ViewChild('profile') profile!: ElementRef;
  @ViewChild('bannerFile') bannerInput!: ElementRef;
  isCompany = false;
  isLoading = false;
  userComment = null;
  addingComment = false;
  // userState$!: UserState;
  endUser!: UserResponseData;
  // expert: any;
  selectedImage: any;
  companyId: string = '';
  experties: any[] = [];
  loading = false;
  userId: any = '';
  companyType = companyType;
  ratingData: any = {};
  staticRatings = 3;
  constructor(
    private account: AccountService,
    private location: Location,
    private httpClient: HttpClient,
    private router: Router,
    private toaster: ToastrService,
    private utils: UtilsService,
    private endUserService: EndUserService,
    private modalService: ModalService,
    private chatService: ChatService,
    private hubService: HubService,
  ) { }

  ngOnInit(): void {
    this.account.user$.pipe(filterNonNull()).subscribe((response) => {
      this.isAdmin = response.userType == 3;
      this.userState$ = response;
      this.vm = this.expert;
      this.vm.userId = this.expert?.userId; // Ensure vm.userId is set
      this.getSocialMediaLinks();
      this.getPitches();
      this.getEndUserDetails();
      // Use the expert's userId instead of the logged-in user's userId
      this.getRatingData(this.expert?.userId || this.userState$.userId);
    });
    this.vm = this.expert;
    if (this.expert?.expertDetail?.userContacts?.length) {
      const primaryAddress = this.expert?.expertDetail?.userContacts.find(
        (x: any) => x.isPrimary
      );
      this.fullAddress += `${primaryAddress.cityName} - ${primaryAddress.zipCode}`;
    }
    this.getConnectionsAndReferrals();
  }



  // getServiceActivities() {
  //   const userId = this.userState$.userId || this.expert?.userId;
  //   console.log('Calling getServiceActivities with userId:', userId);
  //   if (!userId) {
  //     console.warn('No userId provided!');
  //     return;
  //   }
  //   this.serviceActivitiesLoading = true;
  //   this.account.getServiceActivities(userId).subscribe({
  //     next: (response: any) => {
  //       console.log('Service activities response:', response);
  //       this.serviceActivities = response || [];
  //       this.serviceActivitiesLoading = false;
  //     },
  //     error: (err) => {
  //       console.error('Service activities error:', err);
  //       this.serviceActivities = [];
  //       this.serviceActivitiesLoading = false;
  //     }
  //   });
  // }


  getRatingData(userId: string): void {
    // Use the expert's userId instead of the logged-in user's userId
    const targetUserId = this.expert?.userId || userId;
    this.account.getCompanyRatingData(targetUserId).subscribe((response: any) => {
      this.ratingData = response.data;
    });
  }



  getPercentage(count: number): number {
    const total = this.ratingData.totalRatings || 1; // Avoid division by zero
    return (count / total) * 100;
  }

  getPitches() {
    this.videosLoading = true;
    // Use the expert's userId instead of the logged-in user's userId
    const targetUserId = this.expert?.userId || this.userState$.userId;
    this.account
      .getUsersApprovedElevatorPitches(targetUserId)
      .pipe(finalize(() => (this.videosLoading = false)))
      .subscribe((response: any) => {
        this.elevatorPitches = response.data.filter(
          (x: any) => x.approvePostType == 1
        );
      });
  }

  navigateTo(url: string) {
    window.location.href = url;
  }

  copyURL() {
    let url = document.location.href;

    navigator.clipboard.writeText(url).then(
      (response) => {
        this.linkedCopied = true;
        setTimeout(() => {
          this.linkedCopied = false;
        }, 2000);
      },
      (e) => { }
    );
  }

  navigateBack() {
    this.location.back();
  }
  getSocialMediaLinks() {
    // Use the expert's userId instead of the logged-in user's userId
    const targetUserId = this.expert?.userId || this.userState$.userId;
    this.httpClient
      .get(
        `UserSocialConnection/GetUserSocialConnections?userId=${targetUserId}`
      )
      .subscribe((response: any) => {
        response.data.forEach((x: any) => {
          if (x.url) {
            x.imageUrl = this.socialMediaTypes[x.socialConnectionTypeId];
            this.socialMediaLinks.push(x);
          }
        });
      });
  }

  activeTab: string = 'aboutus';

  setActiveTab(tab: string) {
    this.activeTab = tab;
  }



  getEnduser() {
    this.endUserService.getEndUser().subscribe((response: any) => {
      if (response.data) {
        this.endUser = response.data;
        this.endUser.userId = this.userState$.userId;
        this.selectedImage = response.data.banner || './assets/svgs/bg.svg';
      }
    });
  }
  getEndUserDetails() {
    this.loading = true;

    // Use the expert's userId instead of the logged-in user's userId
    const targetUserId = this.expert?.userId || this.userState$.userId;

    forkJoin([
      this.account.getUserDetails(targetUserId),
      this.account.getCompanyComments(targetUserId),
    ])
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(([userDetailResponse, userCommentResponse]) => {
        let response: any = userDetailResponse as any;
        let comments: any = userCommentResponse as any;
        this.selectedImage = response.data.banner || './assets/svgs/bg.svg';
        this.companyId = response.data.expertDetail.companyId;
        this.userId = response.data.userId;
        this.vm.companyName = response.data.expertDetail.companyName;
        this.vm.profilePhoto = response.data.profilePhoto;

        this.experties = response.data?.expertiseList?.filter((x: any) =>
          response.data.expertDetail?.expertiseIds
            ?.split(',')
            ?.includes(x?.idGuid)
        );

        if (response.data.userType == `1`) {
          this.endUser = response.data;
        } else if (
          response.data.userType == `2` ||
          response.data.userType == `3`
        ) {
          this.expert = response.data;
          this.expert.comments = comments.data;
        }

        if (response.messageType) {
          this.router.navigate(['/home']);
          return this.toaster.error(response.message);
        }
        return;
      });
  }

  getExpertComments() {
    // this.account
    //   .getCompanyComments(this.userId)
    //   .subscribe((response: any) => {});
  }

  uploadBanner() {
    this.modalService.openModal('cropper', {
      class: 'modal-xl',
      initialState: {
        onCrop: (data: string) => {
          this.selectedImage = data;
          this.modalService.closeModal();
        },
      },
    });
    // this.bannerInput.nativeElement.click();
  }

  async uploadBannerImage($event: any) {
    const valid = await this.displaySelectedImage(
      $event.target.files[0],
      'banner'
    );
    if (!valid) return;
    const formData = new FormData();
    formData.append('UserId', this.userState$.userId);
    formData.append(
      'photo',
      $event.target.files[0],
      this.utils.generateRandomFileName()
    );
    this.account.uploadUserBanner(formData).subscribe((response) => { });
  }

  updateProfile() {
    this.profile.nativeElement.click();
  }

  async uploadProfileImage($event: any) {
    const valid = await this.displaySelectedImage($event.target.files[0]);
    if (!valid) return;
    const formData = new FormData();
    formData.append('UserId', this.userId);
    formData.append(
      'photo',
      $event.target.files[0],
      this.utils.generateRandomFileName()
    );
    this.account.uploadProfileImage(formData).subscribe((response) => { });
  }

  displaySelectedImage(file: File, fileType = 'profile') {
    return new Promise((resolve, reject) => {
      if (!file.type.startsWith('image/')) {
        this.toaster.error('Selected file is not an image.');
        return resolve(false);
      }
      const isProfile = fileType == 'profile';

      // Check the file size (in bytes)
      if (isProfile && file.size > this.utils.getProfileImageSize()) {
        this.toaster.error('Image size should be under 2MB.');
        return resolve(false);
      } else if (file.size > this.utils.getProfileImageSize()) {
        this.toaster.error('Image size should be under 5MB.');
        return resolve(false);
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const file = e.target?.result;
        this.account.profileImage$.next(this.vm.profilePhoto);
        let user: any = localStorage.getItem('user');
        if (user) {
          user = JSON.parse(user);
        }
        if (fileType === 'profile') {
          user.profilePhoto = file;
          this.vm.profilePhoto = file;
          this.account.profileImage$.next(user.profilePhoto);
        } else {
          user.banner = file;
          this.selectedImage = file;
        }
        localStorage.setItem('user', JSON.stringify(user));
      };
      reader.readAsDataURL(file);
      resolve(true);
    });
  }

  navigateToCompany() {
    this.router.navigate([`/details/${this.companyId}/company`]);
  }

  connectUser(connection: any): any {
    if (
      `${this.userState$.userId}`.toString() == `${connection.id}`.toString()
    ) {
      return this.toaster.info('You cannot send request to this admin');
    }
    const payload = {
      userId: connection.id,
      followerUserId: this.userState$.userId,
      loginUserId: this.userState$.userId,
    };
    this.connectLoading = true;
    this.account
      .connectExpert(payload)
      .pipe(finalize(() => (this.connectLoading = false)))
      .subscribe((response: any) => {
        if (response.messageType == DropMessageType.Success) {
          this.chatService.sendNotification(
            connection.id,
            response.data.text,
            this.userState$.userId,
            response.data.id
          );
          this.toaster.success(response.message);
          connection.isCompleted = true;
        } else if (response.messageType == DropMessageType.Info) {
          this.toaster.info(response.message);
        } else {
          this.toaster.error(response.message);
        }
        if (
          response.messageType &&
          response.messageType == DEFAULT_CONNCTION_REQUEST_LIMIT
        ) {
          this.modalService.openModal('connection-limit-exceed');
        }
      });
  }
  getConnectionsAndReferrals() {
    this.account.getConnectionAndReferrals(1, 11).subscribe((response: any) => {
      const dynamicKey = Object.keys(response).find(key => key.startsWith('item'));
      if (dynamicKey && response[dynamicKey].result) {
        this.connectionsAndReferrals = response[dynamicKey].result;
      } else {
        this.connectionsAndReferrals = [];
      }
    });
  }
  showAboutSection: boolean = false;

  showAll() {
    this.showAllConnections = !this.showAllConnections;
    if (this.showAllConnections) {
      this.showAboutSection = true;
    } else {
      this.showAboutSection = false;
    }
  }
  back() {
    this.showAboutSection = !this.showAboutSection;
    if (this.showAboutSection) {
      this.showAllConnections = true;
    } else {
      this.showAllConnections = false;
    }
  }

  showAllExpertise: boolean = false;

  toggleShowAll(): void {
    this.showAllExpertise = !this.showAllExpertise;
  }

  navigateToExpertProfile(connection: any) {
    this.router.navigate([`/details/${connection.id}/expert`]);
  }
}