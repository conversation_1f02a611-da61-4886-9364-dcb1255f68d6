<section class="banner-section">
  <div class="fc-container d-flex">
    <div class="left-content">
      <button class="become-partner-btn" (click)="navigateToBecomePartner()">Become a partner <svg width="24"
          height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" clip-rule="evenodd"
            d="M10.7044 3.51898C10.034 3.51898 9.46373 3.9848 9.30365 4.61265H14.6863C14.5263 3.9848 13.956 3.51898 13.2856 3.51898H10.7044ZM16.2071 4.61264H18.1881C20.2891 4.61264 22 6.34428 22 8.47085C22 8.47085 21.94 9.3711 21.92 10.6248C21.918 10.724 21.8699 10.8212 21.7909 10.88C21.3097 11.2354 20.8694 11.5291 20.8294 11.5493C19.1686 12.6632 17.2386 13.447 15.1826 13.8369C15.0485 13.8632 14.9165 13.7934 14.8484 13.6739C14.2721 12.6754 13.1956 12.0253 11.995 12.0253C10.8024 12.0253 9.71586 12.6683 9.12256 13.6678C9.05353 13.7853 8.92346 13.8531 8.7904 13.8278C6.75138 13.4369 4.82141 12.6541 3.17059 11.5594L2.21011 10.8911C2.13007 10.8405 2.08004 10.7493 2.08004 10.6481C2.05003 10.1316 2 8.47085 2 8.47085C2 6.34428 3.71086 4.61264 5.81191 4.61264H7.78289C7.97299 3.1443 9.2036 2 10.7044 2H13.2856C14.7864 2 16.017 3.1443 16.2071 4.61264ZM21.6598 12.8152L21.6198 12.8355C19.5988 14.1924 17.1676 15.0937 14.6163 15.4684C14.2561 15.519 13.8959 15.2861 13.7959 14.9216C13.5758 14.0912 12.8654 13.5443 12.015 13.5443H12.005H11.985C11.1346 13.5443 10.4242 14.0912 10.2041 14.9216C10.1041 15.2861 9.74387 15.519 9.38369 15.4684C6.83242 15.0937 4.4012 14.1924 2.38019 12.8355C2.37019 12.8254 2.27014 12.7646 2.1901 12.8152C2.10005 12.8659 2.10005 12.9874 2.10005 12.9874L2.17009 18.1519C2.17009 20.2785 3.87094 22 5.97199 22H18.018C20.1191 22 21.8199 20.2785 21.8199 18.1519L21.9 12.9874C21.9 12.9874 21.9 12.8659 21.8099 12.8152C21.7599 12.7849 21.6999 12.795 21.6598 12.8152ZM12.7454 17.0583C12.7454 17.4836 12.4152 17.8177 11.995 17.8177C11.5848 17.8177 11.2446 17.4836 11.2446 17.0583V15.7519C11.2446 15.3367 11.5848 14.9924 11.995 14.9924C12.4152 14.9924 12.7454 15.3367 12.7454 15.7519V17.0583Z"
            fill="#014681" />
        </svg>
      </button>

      <div class="power-text">
        <h4>The<br>
          <strong>Power of </strong>
          Connection
        </h4>
        <p>A simplified connection platform that assists
          technology channel partners in making valuable
          business connections and promoting their brands
          and services to end-users.
        </p>
        <p>
          We help dedicated Experts to connect with
          Businesses for effective, instant communication,
          collaboration, and business relationships.</p>
      </div>

      <div class="get-started-btn">
        <button class="get-started" role="button" (click)="navigateToSignIn()">Get started</button>
        <button class="watch-demo" (click)="openVideo(template)"> <svg width="24" height="24" viewBox="0 0 24 24"
            fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M11.9688 2C6.44875 2 1.96875 6.48 1.96875 12C1.96875 17.52 6.44875 22 11.9688 22C17.4888 22 21.9688 17.52 21.9688 12C21.9688 6.48 17.4988 2 11.9688 2ZM14.9688 14.23L12.0687 15.9C11.7087 16.11 11.3088 16.21 10.9187 16.21C10.5188 16.21 10.1287 16.11 9.76875 15.9C9.04875 15.48 8.61875 14.74 8.61875 13.9V10.55C8.61875 9.72 9.04875 8.97 9.76875 8.55C10.4888 8.13 11.3487 8.13 12.0787 8.55L14.9787 10.22C15.6987 10.64 16.1287 11.38 16.1287 12.22C16.1287 13.06 15.6987 13.81 14.9688 14.23Z"
              fill="#014681" />
          </svg>
          Request a Demo</button>
      </div>
    </div>
    <div class="right-content">
      <img src="../../../../../assets/banners/banner-avatar.svg">
    </div>
  </div>
  <div class="blur-dot"></div>
</section>

<!-- <section class="lading-page mb-5">
  <div class="w-100 video-container" style="overflow: hidden">
    <video autoplay muted loop playsinline preload="auto" class="w-100">
      <source src="./assets/banners/banner.mp4" type="video/mp4" />
      <source src="./assets/banners/banner.webm" type="video/webm" />
      Your browser does not support the video tag.
    </video>
    <div class="fade-text text-container align-items-center visible h-100 w-100">
      <div class="text-center title">
        <h2 class="responsive-heading">
          <strong> The Power of Connection! </strong>
        </h2>
      </div>
      <div class="text-center">
        <a id="play-video" (click)="openVideo(template)" role="button">
          <svg version="1.1" id="play" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
            x="0px" y="0px" height="100px" width="100px" viewBox="0 0 100 100" enable-background="new 0 0 100 100"
            xml:space="preserve">
            <path class="stroke-solid" fill="none" stroke="white" d="M49.9,2.5C23.6,2.8,2.1,24.4,2.5,50.4C2.9,76.5,24.7,98,50.3,97.5c26.4-0.6,47.4-21.8,47.2-47.7
                C97.3,23.7,75.7,2.3,49.9,2.5" />
            <path class="stroke-dotted" fill="none" stroke="white" d="M49.9,2.5C23.6,2.8,2.1,24.4,2.5,50.4C2.9,76.5,24.7,98,50.3,97.5c26.4-0.6,47.4-21.8,47.2-47.7
                C97.3,23.7,75.7,2.3,49.9,2.5" />
            <path class="icon" fill="white"
              d="M38,69c-1,0.5-1.8,0-1.8-1.1V32.1c0-1.1,0.8-1.6,1.8-1.1l34,18c1,0.5,1,1.4,0,1.9L38,69z" />
          </svg>
        </a>
      </div>
      <div class="text-center title">
        <h2 class="text-warning">Your Ultimate Lead Generation Platform</h2>
        <p class="text-description fw-bolder">
          A Simplified Connection Platform for AV Partners with End-Users
          <br />
          Market your Company's Brand & Services
          <br />
          Socialize with Wise Connections!
        </p>
      </div>
    </div>
  </div>
</section> -->

<ng-template #template>
  <div class="container">
    <div class="white-bg d-flex justify-content-center align-items-center flex-column">
      <h5 class="text-center pt-3 mt-3">Request a Demo</h5>
      <form #demoForm="ngForm" (ngSubmit)="onSubmit()" class="needs-validation" novalidate>
        <div class="request-demo-block row g-3">
          <div class="col-md-6">
            <label for="firstName" class="form-label">First Name:</label>
            <input type="text" id="firstName" name="firstName" [(ngModel)]="firstName" required #firstNameRef="ngModel"
              class="form-control"
              [class.is-invalid]="firstNameRef.invalid && (firstNameRef.touched || formSubmitted)" />
            <div *ngIf="firstNameRef.invalid && (firstNameRef.touched || formSubmitted)" class="invalid-feedback">
              First name is required.
            </div>
          </div>

          <div class="col-md-6">
            <label for="lastName" class="form-label">Last Name:</label>
            <input type="text" id="lastName" name="lastName" [(ngModel)]="lastName" required #lastNameRef="ngModel"
              class="form-control" [class.is-invalid]="lastNameRef.invalid && (lastNameRef.touched || formSubmitted)" />
            <div *ngIf="lastNameRef.invalid && (lastNameRef.touched || formSubmitted)" class="invalid-feedback">
              Last name is required.
            </div>
          </div>

          <div class="col-md-6">
            <label for="email" class="form-label">Email:</label>
            <input type="email" id="email" name="email" [(ngModel)]="email" #emailField="ngModel" class="form-control"
              required email />
            <div *ngIf="formSubmitted && emailField.invalid" class="invalid-feedback d-block">
              <div *ngIf="emailField.errors?.['required']">
                Email is required.
              </div>
              <div *ngIf="emailField.errors?.['email']">
                Please enter a valid email address.
              </div>
            </div>
          </div>


          <div class="col-md-6">
            <label for="phoneNumber" class="form-label">Phone Number:</label>
            <input type="text" id="phoneNumber" name="phoneNumber" [(ngModel)]="phoneNumber" #phoneField="ngModel"
              class="form-control" pattern="^[0-9]{10,15}$" required />
            <div *ngIf="formSubmitted && phoneField.invalid" class="invalid-feedback d-block">
              <div *ngIf="phoneField.errors?.['required']">
                Phone number is required.
              </div>
              <div *ngIf="phoneField.errors?.['pattern']">
                Please enter a valid phone number (10–15 digits).
              </div>
            </div>
          </div>


          <div class="col-md-6">
            <label for="companyName" class="form-label">Company Name:</label>
            <input type="text" id="companyName" name="companyName" [(ngModel)]="companyName" required
              #companyNameRef="ngModel" class="form-control"
              [class.is-invalid]="companyNameRef.invalid && (companyNameRef.touched || formSubmitted)" />
            <div *ngIf="companyNameRef.invalid && (companyNameRef.touched || formSubmitted)" class="invalid-feedback">
              Company name is required.
            </div>
          </div>

          <div class="col-md-6">
            <label for="countryName" class="form-label">Country</label>
            <select id="countryName" name="countryName" [(ngModel)]="countryName" class="form-control" required>
              <option value="" disabled selected>Select a country</option>
              <option *ngFor="let country of countries" [value]="country.name">
                {{ country.name }}
              </option>
            </select>
          </div>

          <div class="col-md-12 mt-4">
            <div class="form-check">
              <input
                class="form-check-input"
                type="checkbox"
                id="termsAndConditions"
                name="termsAndConditions"
                required
                [(ngModel)]="termsAndConditions"
                [ngClass]="{'is-invalid': formSubmitted && !termsAndConditions}"
              />
              <label class="form-check-label text-black cursor-pointer" for="termsAndConditions">
                I have read and agreed to Focile
                <a (click)="redirectTo('/terms-and-conditions', $event)">Terms</a>,
                <a (click)="redirectTo('/privacy-policy', $event)">Privacy</a>
                <span>, and </span>
                <a (click)="redirectTo('/agreements', $event)">Agreements</a>&nbsp;
              </label>
              <div *ngIf="formSubmitted && !termsAndConditions" class="invalid-feedback d-block">
                You must agree to the terms and conditions.
              </div>
            </div>
          </div>
          

          <div class="mt-4 col-sm-12">
            <button type="submit" class="submit-btn" [disabled]="isSubmitting">
              Submit
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</ng-template>