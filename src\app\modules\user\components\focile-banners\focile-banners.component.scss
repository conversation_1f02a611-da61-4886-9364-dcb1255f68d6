.left-content {
  display: flex;
  width: 470px;
  flex-direction: column;
  gap: 43px;
  justify-content: center;

  .become-partner-btn {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 16px 32px;
    gap: 16px;
    width: max-content;
    height: 56px;
    background: #FFFFFF;
    box-shadow: 0px 548px 219px rgba(0, 0, 0, 0.01), 0px 308px 185px rgba(0, 0, 0, 0.04), 0px 137px 137px rgba(0, 0, 0, 0.06), 0px 34px 75px rgba(0, 0, 0, 0.07), 0px 0px 0px rgba(0, 0, 0, 0.07);
    border-radius: 100px;
    flex: none;
    order: 0;
    flex-grow: 0;
    color: #014681;
    font-weight: 600;
    font-family: 14px;
    gap: 1rem;
    border: none;
    z-index: 1;
  }

  h4 {
    font-style: normal;
    font-weight: 600;
    font-size: 65px;
    line-height: 120%;
    color: #000000;
    flex: none;
    order: 1;
    flex-grow: 0;
    margin-bottom: 2.5rem;

    strong {
      color: #014681;
      font-weight: 600;
    }
  }

  p {

    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 160%;
    color: rgba(25, 24, 37, 0.5);
    flex: none;
    order: 2;
    flex-grow: 0;
  }

  .get-started-btn {
    display: flex;
    flex-direction: row;
    justify-content: start;
    gap: 1rem;

    button {
      border: none;
    }

    .get-started {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      gap: 8px;
      min-width: 146px;
      height: 49px;
      background: #014681;
      box-shadow: 0px 81px 32px rgba(0, 0, 0, 0.01), 0px 45px 27px rgba(0, 0, 0, 0.05), 0px 20px 20px rgba(0, 0, 0, 0.09), 0px 5px 11px rgba(0, 0, 0, 0.1), 0px 0px 0px rgba(0, 0, 0, 0.1);
      border-radius: 100px;
      flex: none;
      order: 0;
      flex-grow: 0;
      color: white;
      font-weight: 500;
      line-height: 1.5;
      cursor: pointer;
    }

    .watch-demo {
      box-sizing: border-box;
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 16px 32px;
      gap: 8px;
      min-width: 190px;
      height: 56px;
      background: #FFFFFF;
      border: 1px solid #EEEEEE;
      border-radius: 100px;
      flex: none;
      order: 1;
      flex-grow: 0;
      cursor: pointer;
      transition: 0.3s;
      justify-content: center;
      color: black;

      &:hover {
        background-color: white;
        box-shadow: 0px 11px 29px rgba(0, 0, 0, 0.1), 0px 0px 0px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

.banner-section {
  margin-block: 43px;
}

/* End */



$font-size-large: 5rem; // Equivalent to 80px
$font-size-small: 1.5rem; // Equivalent to 40px

.lading-page {
  margin-top: 30px;
}

.video-container {
  position: relative;
  width: 100%;
  height: 75vh;
  overflow: hidden;

  .fade-text {
    position: absolute;
    top: 70%; // Start slightly below the center
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 2rem;
    opacity: 0;
    transition: opacity 1s, top 1s; // Animate opacity and position

    &.visible {
      opacity: 1;
      top: 50%; // Move to the center
    }
  }

  .text-container {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    padding-top: 165px;
    gap: 0rem;

    .text-description {
      line-height: 30px;
      font-size: 1.125rem;
    }

    .responsive-heading {
      font-size: $font-size-large;
      font-weight: 900;
      opacity: 1;
      transition: opacity 1s ease-in-out;

      @media (max-width: 768px) {
        font-size: clamp(1.5rem, 5vw + 1rem, $font-size-small);
      }
    }
  }
}

@media (max-width: 768px) {
  .lading-page {
    margin-top: 150px;
  }

  .responsive-heading {
    font-size: var(--font-size-small);
    /* Adjust the font size for mobile view */
  }

  .video-container {
    height: auto;

    .text-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 0.5rem;

      .text-description {
        font-size: .775rem;
        line-height: 12px;
      }
    }
  }

  #play-video {
    img {
      height: 50px;
    }
  }

  .fade-text {
    padding-top: 0px !important;
  }
}

#play-video {
  z-index: 10;
}

@-webkit-keyframes spin {
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes spin {
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.stroke-dotted {
  stroke-width: 4px;
  opacity: 1;
  stroke-dasharray: 8, 10;
  -webkit-transform-origin: 50% 50%;
  transform-origin: 50% 50%;
  -webkit-animation: spin 4s infinite linear;
  animation: spin 4s infinite linear;
  -webkit-transition: opacity 1s ease, stroke-width 1s ease;
  transition: opacity 1s ease, stroke-width 1s ease;
}

.stroke-solid {
  stroke-dashoffset: 0;
  stroke-dasharray: 300;
  stroke-width: 4px;
  -webkit-transition: stroke-dashoffset 1s ease, opacity 1s ease;
  transition: stroke-dashoffset 1s ease, opacity 1s ease;
  opacity: 0;
}

.icon {
  -webkit-transform-origin: 50% 50%;
  transform-origin: 50% 50%;
  -webkit-transition: -webkit-transform 200ms ease-out;
  transition: -webkit-transform 200ms ease-out;
  transition: transform 200ms ease-out;
  transition: transform 200ms ease-out, -webkit-transform 200ms ease-out;
}


#play:hover .stroke-solid {
  opacity: 0;
}

#play:hover .icon {
  -webkit-transform: scale(1.05);
  transform: scale(1.05);
}

.request-demo-block {
  box-sizing: border-box;
  // border: 1px solid rgba(140, 140, 140, 0.2);
  border-radius: 18px;
  padding: 35px;
  position: relative;
  z-index: 1;

  label{
    font-size: 1rem;
    font-weight: 500;
  }

  input[type=text],
  input[type=email] {
    box-sizing: border-box;
    width: 100%;
    background: #F6F6F6;
    border: 1px solid rgba(246, 246, 245, 0.4);
    border-radius: 12px;
    flex: none;
    order: 1;
    align-self: stretch;
    flex-grow: 0;
    padding-inline: 1rem;
  }

  select {
    box-sizing: border-box;
    width: 100%;
    background: #F6F6F6;
    border: 1px solid rgba(246, 246, 245, 0.4);
    border-radius: 12px;
    flex: none;
    order: 1;
    align-self: stretch;
    flex-grow: 0;
    padding-inline: 1rem;
  }

  .form-check{
    label{
      font-weight: 400;
    }

    a{
      color: #013c6e;
    }
  }
}

.submit-btn{
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 119px;
  height: 49px;
  background: #014681;
  border-radius: 100px;
  flex: none;
  order: 1;
  flex-grow: 0;
  font-size: 14px;
  color: #fff;
  text-decoration: none;
  font-weight: 700;
  border: none;
}

@media(max-width:768px) {
  .banner-section {
    margin-block: 2rem;
  }

  .power-text {
    h4 {
      font-size: 1.5rem;
      margin-bottom: 1rem;
    }
  }

  .right-content {
    display: none;
  }

  .left-content {
    gap: 1rem;
    z-index: 1;

    p{
      font-size: 14px;
    }
  }

  .left-content .get-started-btn .get-started {
    height: 56px;
    box-shadow: none;

  }
  .left-content .get-started-btn{
    gap: 1rem;
    .watch-demo{
      padding:0px 5px;
      flex: 1;
    }
  }

  .fc-slide-categories {
    padding: 2rem 1rem;
  }
  .request-demo-block{
    padding: 10px;
  }
}