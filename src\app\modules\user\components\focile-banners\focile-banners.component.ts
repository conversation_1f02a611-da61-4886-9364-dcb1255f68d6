import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BsModalService } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { filterNonNull } from 'src/app/shared/oprators/filter-null-values';

@Component({
  selector: 'app-focile-banners',
  templateUrl: './focile-banners.component.html',
  styleUrls: ['./focile-banners.component.scss'],
})
export class FocileBannersComponent implements OnInit {
  countries: { id: string; name: string }[] = [];
  countryName: string = '';
  user: any;
  isLoaded = false;
  inviting = false;
  invitationSuccess = false;
  firstName: string = ''; // Ensure it's always a string
  lastName: string = '';
  email: string = '';
  companyName: string = '';
  phoneNumber: string = '';
  formSubmitted: boolean = false;
  isSubmitting: boolean = false;
  termsAndConditions: boolean = false; // Initialize to false
  constructor(
    private readonly modalService: BsModalService,
    private router: Router,
    private account: AccountService,
    private toaster: ToastrService
  ) {}

  ngOnInit(): void {
    this.account.user$.pipe(filterNonNull()).subscribe((user) => {
      this.user = user;
    });
    this.loadCountries();
  }
  openVideo(template: any) {
    this.modalService.show(template, {
      class: 'modal-lg modal-dialog-centered',
    });
  }
  navigateToBecomePartner() {
    this.router.navigate(['/become-partner']);
  }
  navigateToSignIn() {
    this.router.navigate(['/account/choose-option']);
  }

  isFormValid(): boolean {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return (
      this.email.trim() !== '' &&
      emailPattern.test(this.email) &&
      this.firstName.trim() !== '' &&
      this.lastName.trim() !== '' &&
      this.companyName.trim() !== '' &&
      this.countryName.trim() !== '' &&
      this.termsAndConditions
    );
  }

  onSubmit() {
    this.formSubmitted = true;

    if (!this.termsAndConditions) {
      return;
    }
    // Stop if any required field is invalid
    if (
      !this.email.trim() ||
      !this.firstName.trim() ||
      !this.lastName.trim() ||
      !this.companyName.trim()
    ) {
      return;
    }

    if (this.isSubmitting) {
      return;
    }

    this.isSubmitting = true;

    const payload = {
      firstName: this.firstName,
      lastName: this.lastName,
      email: this.email,
      phoneNumber: this.phoneNumber,
      companyName: this.companyName,
      countryName: this.countryName,
      termsAndConditions: this.termsAndConditions,
    };

    this.account.requestDemo(payload).subscribe(
      () => {
        this.resetForm();
        this.toaster.success('Invitation sent', '', { timeOut: 15000 });
        this.closeModal();
      },
      (error) => {
        console.error('Error sending invitation:', error);
      },
      () => {
        this.isSubmitting = false;
      }
    );
  }



  resetForm() {
    this.firstName = '';
    this.lastName = '';
    this.email = '';
    this.phoneNumber = '';
    this.companyName = '';
    this.countryName = '';
    this.termsAndConditions = true;
    this.formSubmitted = false; // Reset form submission state
  }
  onLoad() {
    this.isLoaded = true;
  }

  closeModal() {
    // Close the modal using DOM manipulation
    const modalElement = document.querySelector('.modal'); // Adjust selector as needed
    if (modalElement) {
      modalElement.classList.remove('show');
      modalElement.setAttribute('aria-hidden', 'true');
      modalElement.setAttribute('style', 'display: none;');
    }

    // Reset modal-related states
    this.resetForm(); // Clear form fields and reset submission state
    this.isSubmitting = false; // Ensure the submitting state is reset
    this.formSubmitted = false; // Reset form validation state

    // Optionally, hide the modal using ngx-bootstrap modal service if applicable
    this.modalService.hide(); // Ensure the modal is hidden if using ngx-bootstrap
  }

  loadCountries(): void {
    this.account.getCountries().subscribe({
      next: (response: any) => {
        if (response && response.data) {
          this.countries = response.data.map((country: any) => ({
            id: country.id,
            name: country.name,
          }));
        }
      },
      error: (error) => {
        console.error('Error fetching countries:', error);
      },
      complete: () => {
        console.log('Country list loaded successfully.');
      },
    });
  }
  redirectTo(path: string, event: MouseEvent): void {
    event.stopPropagation(); // Prevent checkbox toggle
    this.router.navigate([path]);
    this.closeModal();
  }
}
