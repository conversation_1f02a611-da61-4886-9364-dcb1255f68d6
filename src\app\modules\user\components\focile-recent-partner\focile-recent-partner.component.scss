.fc-recent-partner-wrap {
    // background: url(../../../../../assets/images/up-arrow.svg) no-repeat calc((100% - 1110px) / 2) center;
    min-height: 170px;
    position: relative;
    background-size: 50px 50px;
    display: flex;
    align-items: center;
}

.blur-dot {
    /* Ellipse 24 */
    position: absolute;
    width: 500px;
    height: 500px;
    right: -300px;
    bottom: 0;
    background: #014681;
    opacity: 0.5;
    filter: blur(250px);
}

.recent-partner-logo{
    display: flex;
    flex-direction: row;;        
    justify-content: space-between;
    position: relative;


    &::before{
        content: ''; /* Creates a pseudo-element for the background icon */
        background: url('../../../../../assets/images/up-arrow.svg') no-repeat center center;
        background-size: 71px 150px; /* Adjust the size of the icon */
        position: absolute;
        top: 50%; /* Align vertically in the center */
        left: auto; /* Align horizontally to the start of .fc-container */
        transform: translateY(-50%); /* Adjust vertical alignment */
        width:71px; /* Width of the icon */
        height: 150px; /* Height of the icon */
        z-index: 0; /* Keep it behind the content */
        left: -120px;
        }

        img{
            height: 50px;
        }
}

@media(max-width:768px) {
    .fc-recent-partner-wrap{
        min-height: auto;
        padding-block: 1rem;
    }
    .recent-partner-logo {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
        align-items: center;
        text-align: center;
        column-gap: 1rem;
        row-gap: 2rem;

        img {
            width:auto;
            height: 25px;
        }
    }
}