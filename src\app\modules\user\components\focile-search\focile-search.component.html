<ng-container *ngIf="!isLoggedIn">
  <app-focile-banners></app-focile-banners>
  <app-focile-recent-partner></app-focile-recent-partner>
  <app-top-categories></app-top-categories>
  <app-easy-connection></app-easy-connection>
  <app-people-joined></app-people-joined>
  <app-key-features></app-key-features>
  <app-our-testimonail></app-our-testimonail>
</ng-container>
<!-- <app-bulk-invite></app-bulk-invite> -->
<app-our-blog *ngIf="!isLoggedIn"></app-our-blog>

<div
  class="fc-container landing-page"
  [ngClass]="{ 'landing-page': isLoggedIn }"
>
  <ng-container *ngIf="isLoggedIn">
    <div
    id="controls"
    class="text-center d-flex flex-wrap gap-3 justify-content-center my-3"
    *ngIf="(viewType$ | async) !== 'avconsultant'"
  >
    <button class="slider-btn prev-btn" (click)="slidePrev()"  [disabled]="!showPrevArrow">&#8249;</button>
    <div class="sub-categories-slider">
      <div
        class="sub-categories-row"
        *ngFor="let control of visibleControls"
      >
        <button
          class="sub-cate-btn"
          (click)="handleControl(control)"
          [ngClass]="{ active: control.isActive }"
        >
          <span>
            <img
              [src]="control.image"
              alt="{{ control.text }}"
              class="control-icon"
            />
          </span>
          {{ control.text }}
        </button>
      </div>
    </div>
    <button class="slider-btn next-btn" (click)="slideNext()"  [disabled]="!showNextArrow">&#8250;</button>
  </div>
    <ng-container>
      <div class="text-center hero-title-row">
        <p>
          We made it our mission to connect companies to sales and channel
          experts for all their needs
        </p>
        <span class="hero-title"> Explore & Connect </span>
      </div>
    </ng-container>
    <div
      class="mb-3"
      [ngClass]="{ 'mt-3': (viewType$ | async) === 'avconsultant' }"
    >
      <div
        class="fc-custom-search-bar"
        [ngClass]="{
          'avconsultant-active': (viewType$ | async) === 'avconsultant'
        }"
      >
        <span class="position-absolute search-icon">
          <!-- <i *ngIf="!searching" class="fa fa-search"></i> -->
          <i *ngIf="searching" class="fas fa-circle-notch fa-spin"></i>
        </span>
        <span class="filter-btn" (click)="toggleSearchFilters()" role="button">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M11 21V15H13V17H21V19H13V21H11ZM3 19V17H9V19H3ZM7 15V13H3V11H7V9H9V15H7ZM11 13V11H21V13H11ZM15 9V3H17V5H21V7H17V9H15ZM3 7V5H13V7H3Z"
              fill="#014681"
            />
          </svg>
        </span>
        <input
          type="search"
          class=""
          placeholder="Search focile"
          name="globleSearch"
          id="globleSearch"
          [formControl]="eCardSearch"
        />
        <ng-container>
          <div
            id="serchFilters"
            class="search-filter-container"
            *ngIf="showFilters"
          >
            <ng-container>
              <div class="w-190" *ngIf="(viewType$ | async) === 'avconsultant'">
                <div class="d-flex align-items-center">
                  <focile-dropdown
                    [placeholder]="'Consultant Type'"
                    class="w-100 rounded-3"
                    [items]="filters.companySystemList"
                    [isRequired]="false"
                    [(ngModel)]="filters.companySystemId"
                    name="productId"
                    [bindValue]="'idGuid'"
                    [clearable]="true"
                    (change)="
                      mainFilters.companySystemId = $event; applyFilters()
                    "
                  ></focile-dropdown>
                </div>
              </div>
              <div class="w-190">
                <div class="d-flex align-items-center">
                  <focile-dropdown
                    [placeholder]="'Product'"
                    class="w-100 rounded-3"
                    [items]="filters.productList"
                    [isRequired]="false"
                    [(ngModel)]="filters.productId"
                    name="productId"
                    [bindValue]="'idGuid'"
                    [clearable]="true"
                    (change)="mainFilters.productId = $event; applyFilters()"
                  ></focile-dropdown>
                </div>
              </div>
              <div class="w-190">
                <focile-dropdown
                  [placeholder]="'Service'"
                  class="w-100 rounded-3"
                  [isRequired]="false"
                  [items]="filters.serviceList"
                  [(ngModel)]="filters.serviceId"
                  [bindValue]="'idGuid'"
                  name="serviceId"
                  [clearable]="true"
                  (change)="mainFilters.serviceId = $event; applyFilters()"
                ></focile-dropdown>
              </div>
              <div class="w-190">
                <focile-dropdown
                  [placeholder]="'Industry'"
                  class="w-100 rounded-3"
                  [isRequired]="false"
                  [items]="filters.industryList"
                  [(ngModel)]="filters.industryId"
                  [bindValue]="'idGuid'"
                  name="industryId"
                  [clearable]="true"
                  (change)="mainFilters.industryId = $event; applyFilters()"
                ></focile-dropdown>
              </div>
              <div class="w-190">
                <focile-dropdown
                  [placeholder]="'Country'"
                  class="w-100 rounded-3"
                  [isRequired]="false"
                  [items]="filters.countryList"
                  [(ngModel)]="mainFilters.countryId"
                  name="modalCountryId"
                  [bindValue]="'id'"
                  [clearable]="true"
                  (change)="
                    mainFilters.countryId = $event;
                    handleChange($event);
                    applyFilters()
                  "
                ></focile-dropdown>
              </div>
              <div class="w-190">
                <focile-dropdown
                  [placeholder]="'State'"
                  class="w-100 rounded-3"
                  [bindValue]="'id'"
                  [items]="states"
                  (change)="mainFilters.stateId = $event; applyFilters()"
                  [isRequired]="false"
                  [clearable]="true"
                  [(ngModel)]="mainFilters.stateId"
                  name="modalStateId"
                ></focile-dropdown>
              </div>
              <div class="w-190">
                <focile-dropdown
                  [placeholder]="'Ratings'"
                  class="w-100 rounded-3"
                  [isRequired]="false"
                  [items]="ratingList"
                  [(ngModel)]="mainFilters.ratting"
                  name="ratings"
                  [clearable]="true"
                  (change)="mainFilters.ratting = $event; applyFilters()"
                  name="modalRatting"
                ></focile-dropdown>
              </div>
            </ng-container>

            <div class="search-btn"></div>
          </div>
        </ng-container>
      </div>
    </div>
  </ng-container>

  <ng-container *ngIf="isLoggedIn">
    <section>
      <div
        class="p-0"
        [ngClass]="{ 'card-container': companies.length && !companiesLoading }"
      >
        <ng-container *ngIf="!companiesLoading && companies?.length">
          <div *ngFor="let company of companies">
            <app-e-card [company]="company"></app-e-card>
          </div>
        </ng-container>
      </div>
      <div
        class="d-flex justify-content-center align-items-center w-100"
        style="min-height: 250px"
        *ngIf="companiesLoading"
      >
        <app-spinner></app-spinner>
      </div>
      <p
        class="alert alert-primary"
        *ngIf="!companiesLoading && !companies.length"
      >
        Oops, we couldn't find any results matching your search. Would you like
        to try a broader search or provide different filters?
      </p>
    </section>
    <div class="text-center mb-5 mt-5" *ngIf="companies.length">
      <button class="load-more-btn" (click)="browseMoreList()">
        <svg
          width="26"
          height="24"
          viewBox="0 0 26 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M11.8588 20.95C9.68478 20.7 7.88354 19.8208 6.45512 18.3125C5.02671 16.8042 4.3125 15.0333 4.3125 13C4.3125 11.9 4.54608 10.8458 5.01323 9.8375C5.48039 8.82917 6.14518 7.95 7.00762 7.2L8.54384 8.625C7.86108 9.19167 7.34451 9.85 6.99415 10.6C6.64378 11.35 6.4686 12.15 6.4686 13C6.4686 14.4667 6.97169 15.7625 7.97787 16.8875C8.98404 18.0125 10.2777 18.7 11.8588 18.95V20.95ZM14.0149 20.95V18.95C15.5781 18.6833 16.8673 17.9917 17.8824 16.875C18.8976 15.7583 19.4052 14.4667 19.4052 13C19.4052 11.3333 18.7763 9.91667 17.5186 8.75C16.2609 7.58333 14.7336 7 12.9369 7H12.856L14.0419 8.1L12.5326 9.5L8.75945 6L12.5326 2.5L14.0419 3.9L12.856 5H12.9369C15.3445 5 17.3838 5.775 19.0548 7.325C20.7258 8.875 21.5613 10.7667 21.5613 13C21.5613 15.0167 20.8471 16.7792 19.4187 18.2875C17.9902 19.7958 16.189 20.6833 14.0149 20.95Z"
            fill="black"
          />
        </svg>
        Load More
      </button>
    </div>
  </ng-container>
</div>
<ng-container *ngIf="isLoggedIn">
  <!-- <app-connected-by-solutions></app-connected-by-solutions>    -->
  <app-connected-solutions></app-connected-solutions>
  <div class="my-0 my-sm-5">
    <app-recent-joined-partners-testimonail></app-recent-joined-partners-testimonail>
  </div>

  <!-- <app-recently-joined-partners
        [items]="technologies"
        [user]="user"
      ></app-recently-joined-partners> -->
</ng-container>

<!-- <app-free-trial-promote></app-free-trial-promote> -->

<ng-template #signInPropmt>
  <div class="modal-header">
    <h4 class="modal-title pull-left">Are you focile member ?</h4>
    <button
      type="button"
      class="btn-close close pull-right"
      aria-label="Close"
      (click)="modalRef?.hide()"
    >
      <span aria-hidden="true" class="visually-hidden">&times;</span>
    </button>
  </div>

  <div class="modal-footer d-block text-center">
    <button routerLink="/account/login" class="btn btn-primary">
      Yes, Sign in Now
    </button>
    <button routerLink="/account/register" class="btn btn-primary">
      No, Sign up Now
    </button>
    <p>
      By clicking here you agree to accept
      <a routerLink="#/terms-and-condtions">Terms</a> and
      <a routerLink="#/terms-and-condtions">Privacy</a>
    </p>
  </div>
</ng-template>

<app-bulk-invite *ngIf="isLoggedIn"></app-bulk-invite>
<app-our-blog *ngIf="isLoggedIn"></app-our-blog>
