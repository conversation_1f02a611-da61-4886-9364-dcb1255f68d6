.focile-search {
  font-size: 48px;
  font-weight: 700;
  line-height: 56.25px;
}

// .hero-title {
//   font-style: normal;
//   font-weight: 700;
//   line-height: 3.5rem;
//   letter-spacing: -0.0625rem;
//   color: #0d152e;
// }

#search-icon {
  z-index: 99;
}

.hw-60 {
  height: 3.75rem;
  width: 3.75rem;
}

.focile-search-text {
  font-family: "Roboto Flex";
  font-style: normal;
  font-weight: 700;
  font-size: 48px;
  line-height: 56px;
  letter-spacing: -1px;
  color: #0d152e;
  margin-top: 1rem;
}

.search-icon {
  top: 0.75rem;
  left: 1.5rem;
}

.w-200 {
  width: 12.5rem;
}

.landing-page {
  margin-top: 55px;
}

.w-190 {
  width: 100% !important;
}

.our-purpose-container {
  // background-image: url('../../../../../assets/images/background-6556413_640.jpg');
  // background-size: cover;
  // background-position: center;
}
.search-filter-container {
  display: grid;
  grid-template-columns: repeat(3, minmax(31%, 1fr));
  gap: 2rem;
  padding: 2rem;
  padding-bottom: 0;
  border-top: 1px solid #f0f0f0;
}

@media (max-width: 1200px) {
  .search-filter-container {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 992px) {
  .search-filter-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .search-filter-container {
    grid-template-columns: repeat(2, 1fr);
    padding: 1rem;
    gap: 1rem;
  }
  #controls {
    div {
      // width:100%;
      button {
        width: 100%;
      }
    }
  }
}

@media (max-width: 576px) {
  .search-filter-container {
    grid-template-columns: repeat(1, 1fr);
  }
}

.card-container {
  display: grid;
  grid-template-columns: repeat(
    auto-fill,
    minmax(280px, 1fr)
  ); /* Limit to 1fr per column */
  gap: 32px;
  margin-top: 55px;
}

@media (max-width: 1200px) {
  .card-container {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 992px) {
  .card-container {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
}

@media (max-width: 576px) {
  .card-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1.5rem;
  }
}

/* New Stylesheet */

.sub-categories-row {
}

.sub-cate-btn {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 18px 28px;
  min-width: 270px;
  height: 84px;
  flex: none;
  order: 1;
  flex-grow: 0;
  font-weight: 500;
  background: #ffffff;
  border: 1px solid #eff0f7;
  box-shadow: 0px 5px 14px rgba(8, 15, 52, 0.04);
  border-radius: 20px;
  color: #6f6c90;
  font-size: 18px;
  white-space: nowrap;
  gap: 12px;
  &:hover {
    border-color: #014681;
    color: #014681;
    background: white;
  }
  &.active {
    border-color: #014681;
    color: #014681;
    background: white;
  }

  span {
    background: rgba(1, 69, 129, 0.1);
    width: 48px;
    height: 48px;
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 26px;
      height: 21px;
    }
  }
}

.hero-title-row {
  margin-top: 55px;
  p {
    text-align: center;
    font-weight: 500;
    color: black;
  }
  .hero-title {
    font-size: 2rem;
    color: black;
    font-weight: 600;
  }
}

.fc-custom-search-bar {
  position: relative;
  background: #ffffff;
  box-shadow: 0px 3px 14px rgba(74, 58, 255, 0.03),
    0px -2px 4px rgba(20, 20, 43, 0.02), 0px 12px 24px rgba(20, 20, 43, 0.04);
  border-radius: 26px;
  width: 100%;
  border: none;
  min-height: 80px;
  margin-top: 55px;

  input {
    width: 100%;
    padding: 0px 2rem;
    border: none;
    height: 80px;
    display: flex;
    align-items: center;
    font-size: 1.25rem;
    border-radius: 26px;

    &:focus {
      border: none;
      outline: none;
    }
  }

  &.avconsultant-active {
    .search-filter-container {
      padding-bottom: 2.5rem;
    }
  }
}

.filter-btn {
  position: absolute;
  width: 50px;
  height: 50px;
  right: 30px;
  top: 14px;
  background: #f3f1ff;
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.load-more-btn {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 16px 32px;
  gap: 16px;
  width: 223.76px;
  height: 68.77px;
  background: #014681;
  box-shadow: 0px 548px 219px rgba(0, 0, 0, 0.01),
    0px 308px 185px rgba(0, 0, 0, 0.04), 0px 137px 137px rgba(0, 0, 0, 0.06),
    0px 34px 75px rgba(0, 0, 0, 0.07), 0px 0px 0px rgba(0, 0, 0, 0.07);
  border-radius: 100px;
  border: none;
  justify-content: center;
  color: white;
  margin: 0px auto;
  font-size: 20px;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  svg path {
    fill: white;
  }

  &:hover {
    background: #0056b3;
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0px 8px 25px rgba(1, 70, 129, 0.3);

    svg path {
      fill: white;
    }
  }

  &:focus {
    outline: 2px solid #014681;
    outline-offset: 2px;
    svg path {
      fill: white;
    }
  }

  &:active {
    transform: translateY(0);

    svg path {
      fill: white;
    }
  }
}

::ng-deep body.no-scroll {
  overflow: hidden;
}

.fc-login-sign-up[_ngcontent-tbk-c74]
  .dropdown-item[_ngcontent-tbk-c74]
  .dropdown-item.active,
.dropdown-item:active {
  color: white !important;
}

.sub-categories-slider {
  display: flex;
  overflow: hidden;
  width: auto; /* Adjust width as needed */
  gap: 10px;
}

.sub-categories-row {
  max-width: 50%;
  transition: transform 0.3s ease-in-out;
}

.slider-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  align-self: center;
  background: #e0e0e0;
  width: 30px;
  height: 30px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: black;

  &:hover {
    background: #004681;
    color: white;
  }
  &:disabled {
    background: #ccc; /* Sets the background color to light gray when disabled */
    cursor: not-allowed;
    opacity: 0.3; /* Shows a "not allowed" cursor (⛔) when hovered */
  }
}

.prev-btn {
  margin-right: 10px;
}

.next-btn {
  margin-left: 10px;
}

@media (max-width: 768px) {
  .sub-cate-btn {
    padding: 0px 10px;
    font-size: 14px;
    min-width: auto;
    height: 40px;

    span {
      height: 24px;
      width: 24px;
      display: none;
    }
  }
  .sub-categories-row {
    max-width: 100%;
  }
  .sub-categories-slider {
    // width: calc(100% - 120px);
    justify-content: center;
  }

  .prev-btn {
    position: absolute;
    bottom: 5px;
    left: 10px;
    transform: translateX(-40%);
  }
  .next-btn {
    position: absolute;
    bottom: 5px;
    right: -10px;
    transform: translateX(-40%);
  }
  #controls {
    position: relative;
  }

  .fc-custom-search-bar input {
    padding-inline: 1rem;
    border-radius: 1rem;
  }
  .filter-btn {
    right: 10px;
    width: 30px;
    height: 30px;
    top: 10px;

    svg {
      width: 20px;
    }
  }
  .fc-custom-search-bar {
    margin-top: 2rem;
    min-height: 50px;
    border-radius: 12px;

    input {
      font-size: 1rem;
      height: 50px;
    }
  }

  .hero-title-row {
    .hero-title {
      font-size: 1.5rem;
    }
  }
}
