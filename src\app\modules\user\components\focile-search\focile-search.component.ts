import {
  Component,
  ElementRef,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  Renderer2,
  ViewChild,
} from '@angular/core';
import { Router } from '@angular/router';
import { BsModalRef } from 'ngx-bootstrap/modal';
import {
  Subject,
  Subscription,
  debounceTime,
  finalize,
  map,
  take,
  takeUntil,
} from 'rxjs';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { Ecard } from 'src/app/utils/e-card';
import { FormControl } from '@angular/forms';
import { UtilsService } from 'src/app/shared/services/utils.service';
import { HttpClient } from '@angular/common/http';
import { EcardFiltersService } from 'src/app/shared/services/ecard-filter.service';
import { filterNonNull } from 'src/app/shared/oprators/filter-null-values';

@Component({
  selector: 'app-focile-search',
  templateUrl: './focile-search.component.html',
  styleUrls: ['./focile-search.component.scss'],
})
export class FocileSearchComponent implements OnInit, OnDestroy {
  @ViewChild('signInPropmt') ngTemplate!: any;
  @ViewChild('cookies') cookiesNgTemplte!: any;
  modalRef?: BsModalRef;
  eCards: Array<number> = [];
  isLoggedIn = false;
  viewType$ = this.account.viewType$.asObservable();
  user: any = {};
  latitude = null;
  longitude = null;
  companies: Ecard[] = [];
  companiesLoading = false;
  searching = false;
  userType: number = 1;
  timeOutId: any;
  eCardSearch: FormControl = new FormControl('');
  cancelPreviousRequest$ = new Subject();
  typeList: any = [];
  locationList: any;
  roleList: any;
  viewTypeMap: any = {
    1: {
      text: 'Reseller',
      image: 'src/assets/svgs/team-fill.svg',
      controls: [
        {
          text: 'Resellers',
          isActive: true,
          id: '1856641a-b0cb-4c43-03df-08db8950bd8a',
          image: '../../../assets/images/reseller-icon.svg',
        },
        {
          text: 'System Integrators',
          isActive: false,
          id: '252b152a-94f9-4445-03e0-08db8950bd8a',
          image: '../../../assets/images/system-tab.svg',
        },
      ],
    },
    3: {
      text: 'Vendor',
      image: 'src/assets/svgs/vendor.svg',
      controls: [
        {
          text: 'Software',
          isActive: true,
          id: '23127e86-f999-419c-03e1-08db8950bd8a',
          image: '../../../assets/images/ac_unit.svg',
        },
        {
          text: 'Hardware',
          isActive: false,
          id: '3922ce71-4d8c-4731-ac8f-08db894ba761',
          image: '../../../assets/images/hardware.svg',
        },
        {
          text: 'Cloud',
          isActive: false,
          id: '10f4cb64-61c3-4557-3aea-08dc7f4a1997',
          type: 'solution',
          image: '../../../assets/images/cloud-icon.svg',
        },
        {
          text: 'AI / VR',
          isActive: false,
          id: '092b3dd3-556e-4f80-3aeb-08dc7f4a1997',
          type: 'solution',
          image: '../../../assets/images/vr-icon.svg',
        },
      ],
    },
    2: {
      text: 'Distributor',
      image: 'src/assets/svgs/distributor.svg',
      controls: [
        {
          text: 'Software',
          isActive: true,
          id: 'a1788d89-5856-4cbc-03e4-08db8950bd8a',
          image: '../../../assets/images/ac_unit.svg',
        },
        {
          text: 'Hardware',
          isActive: false,
          id: 'c20e07ee-3c78-4983-03e3-08db8950bd8a',
          image: '../../../assets/images/hardware.svg',
        },
        {
          text: 'Cloud',
          isActive: false,
          id: '10f4cb64-61c3-4557-3aea-08dc7f4a1997',
          type: 'solution',
          image: '../../../assets/images/cloud-icon.svg',
        },
        {
          text: 'AI / VR',
          isActive: false,
          id: '092b3dd3-556e-4f80-3aeb-08dc7f4a1997',
          type: 'solution',
          image: '../../../assets/images/vr-icon.svg',
        },
      ],
    },
    4: {
      text: 'AV Consultant',
      image: '../../../assets/svgs/distributor.svg',
      condition: false,
      controls: [
        {
          text: 'Software',
          isActive: true,
          id: 'a1788d89-5856-4cbc-03e4-08db8950bd8a',
          image: '../../../assets/images/ac_unit.svg',
        },
        {
          text: 'Hardware',
          isActive: false,
          id: 'c20e07ee-3c78-4983-03e3-08db8950bd8a',
          image: '../../../assets/images/hardware.svg',
        },
        {
          text: 'Cloud',
          isActive: false,
          id: '10f4cb64-61c3-4557-3aea-08dc7f4a1997',
          type: 'solution',
          image: '../../../assets/images/cloud-icon.svg',
        },
        {
          text: 'AI / VR',
          isActive: false,
          id: '092b3dd3-556e-4f80-3aeb-08dc7f4a1997',
          type: 'solution',
          image: '../../../assets/images/vr-icon.svg',
        },
      ],
    },
  };
  ratingList = [
    {
      id: 1,
      name: '1',
    },
    {
      id: 2,
      name: '2',
    },
    {
      id: 3,
      name: '3',
    },
    {
      id: 4,
      name: '4',
    },
    {
      id: 5,
      name: '5',
    },
  ];

  filters: any = {
    solutionList: [],
    serviceList: [],
    productList: [],
    industryList: [],
    countryList: [],
    stateList: [],
    loginUserId: null,
    isCertified: true,
    location: null,
    ratting: null,
    countryId: null,
    stateId: null,
    type: null,
    role: null,
    productId: null,
    servuceId: null,
    solutionId: null,
    industryId: null,
  };
  states = [];
  mainFilters: any = {
    productIds: null,
    serviceIds: null,
    solutionIds: null,
    industryIds: null,
    isCertified: true,
    role: null,
    countryId: null,
    searchQuery: '',
  };
  viewType = '';
  technologies: any[] = [];

  memeberSubTypes = [
    {
      id: 1,
      text: 'Resellers',
    },
  ];
  pageNumber = 1;
  disabledLoadMore = false;
  subscriptions: Subscription[] = [];
  constructor(
    public readonly account: AccountService,
    private readonly router: Router,
    private readonly http: HttpClient,
    private readonly renderer: Renderer2,
    public readonly utils: UtilsService,
    private readonly ecardFilter: EcardFiltersService
  ) {
    account.isLoggedIn$.subscribe((response) => {
      this.eCards = [];
      this.isLoggedIn = response;
      if (!this.isLoggedIn) {
        this.user = {};
      }
      for (let index = 0; index < (response ? 12 : 8); index++) {
        this.eCards.push(index);
      }
    });
  }

  ngOnInit(): void {
    this.eCardSearch.valueChanges
      .pipe(debounceTime(500))
      .subscribe((term: string) => {
        this.companies = [];
        this.mainFilters.searchQuery = term;
        this.getUsers();
      });
    const cookiesPrivacy = localStorage.getItem('cookies');
    this.account.cookiesPrivacy$.next(cookiesPrivacy);
    this.account.user$.pipe(filterNonNull()).subscribe((response) => {
      if (!response) return;
      this.user = response;
      this.viewType$.subscribe((response) => {
        this.eCardSearch.setValue(null, {
          emitEvent: false,
        });
        const userTypeMap: { [key: string]: number } = {
          reseller: 1,
          vendor: 3,
          distributor: 2,
          avconsultant: 4,
        };
        this.userType = userTypeMap[response];
        this.applyFilters();
      });
      this.getFilters();
    });

    this.router.events.subscribe(() => {
      this.isHomeRoute = this.router.url === '/home';
    });
    this.viewType$.subscribe((viewType) => {
      const userTypeMap: { [key: string]: number } = {
        reseller: 1,
        distributor: 2,
        vendor: 3,
        avconsultant: 4,
      };
      this.userType = userTypeMap[viewType] || 1; // Default to 1 if viewType is invalid
      this.updateVisibleControls();
    });
  }

  acceptCooikes(cookieTyoe: string) {
    localStorage.setItem('cookies', cookieTyoe);
    this.account.cookiesPrivacy$.next(cookieTyoe);
    this.modalRef?.hide();
  }

  getFilters() {
    const filterSubscription = this.ecardFilter.ecardFilters$.subscribe(
      (response: any) => {
        if (response?.data) {
          this.typeList = response.data.typeList;
          this.locationList = response.data.locationList;
          const {
            countryList,
            industryList,
            productList,
            serviceList,
            solutionList,
            companySystemList,
          } = response.data;
          this.filters.countryList = countryList;
          this.filters.industryList = industryList;
          this.filters.productList = productList;
          this.filters.companySystemList = companySystemList;
          this.filters.serviceList = serviceList;
          this.filters.solutionList = solutionList;
          this.roleList = response.data.roleList;
        }
      }
    );
    this.subscriptions.push(filterSubscription);
  }

  applyFilters() {
    this.pageNumber = 1;
    this.companies = [];
    this.getUsers();
  }

  getUsers() {
    if (!this.user.userId) return;
    const filteredParams = Object.fromEntries(
      Object.entries(this.mainFilters).filter(([_, value]) => value)
    );
    const subscription = this.http
      .get('ECard/V2/GetCardList', {
        params: {
          loginUserId: this.user.userId,
          expertType: this.userType,
          pageNumber: this.pageNumber,
          pageSize: 6,
          ...filteredParams,
        },
      })
      .pipe(
        map((response: any) => {
          return response.data;
        }),
        take(1)
      )
      .subscribe((response: any) => {
        this.disabledLoadMore = response.length < 6;
        this.companies = [...this.companies, ...response];
      });
    this.subscriptions.push(subscription);
  }

  searchEcards(term: string) {
    this.cancelPreviousRequest$.next(true);
    this.cancelPreviousRequest$.complete();
    this.companiesLoading = true;
    this.searching = true;
    this.companies = [];
    this.account
      .searchEcard(term, this.user?.userId)
      .pipe(
        takeUntil(this.cancelPreviousRequest$),
        finalize(() => {
          this.companiesLoading = false;
          this.searching = false;
        })
      )
      .subscribe((response: any) => {
        if (response.data?.length) {
          this.companies = [this.companies, ...response.data];
        }
      });
  }

  ngOnDestroy(): void {
    this.subscriptions.map((s) => s?.unsubscribe());
    clearTimeout(this.timeOutId);
    this.cancelPreviousRequest$.next(false);
    this.cancelPreviousRequest$.complete();
    this.cancelPreviousRequest$.unsubscribe();
  }

  handleChange(changedType?: string) {
    this.account.getStates(changedType).subscribe((response: any) => {
      this.states = response.data;
    });
  }

  browseMoreList() {
    if (this.isLoggedIn) {
      this.pageNumber++;
      this.getUsers();
    } else {
      this.router.navigate(['account']);
    }
  }

  handleControl(control: any) {
    this.viewTypeMap[this.userType].controls.map(
      (x: any) => (x.isActive = false)
    );
    control.isActive = true;
    if (control.type == 'solution') {
      this.mainFilters.solutionId = control.id;
    } else {
      this.mainFilters.organizationTypeId = control.id;
    }
    this.applyFilters();
  }

  showFilters = false; // Tracks visibility of the filters section

  toggleSearchFilters(): void {
    this.showFilters = !this.showFilters; // Toggle visibility
  }

  @ViewChild('section2') section2!: ElementRef;

  ngAfterViewInit(): void {
    if (!this.section2) {
      console.error('section2 is undefined');
      return;
    }

    const section2Element = this.section2.nativeElement;

    // Listen for touchstart and touchmove events
    this.renderer.listen(section2Element, 'touchstart', (event: TouchEvent) => {
      document.body.classList.add('no-scroll'); // Disable body scroll
    });

    this.renderer.listen(section2Element, 'touchmove', (event: TouchEvent) => {
      const scrollLeft = section2Element.scrollLeft;
      const scrollWidth = section2Element.scrollWidth;
      const clientWidth = section2Element.clientWidth;

      // Check if the user has reached the left or right edge
      if (scrollLeft <= 0) {
        console.log('Reached the left edge');
        alert('You have touched the left edge of Section 2');
        document.body.classList.remove('no-scroll'); // Resume body scroll
      } else if (scrollLeft + clientWidth >= scrollWidth) {
        console.log('Reached the right edge');
        alert('You have touched the right edge of Section 2');
        document.body.classList.remove('no-scroll'); // Resume body scroll
      }
    });

    this.renderer.listen(section2Element, 'touchend', () => {
      document.body.classList.remove('no-scroll'); // Enable body scroll on touch end
    });
  }

  isHomeRoute: boolean = false;

  navigate1(url: string) {
    window.location.href = '#' + url;
  }

  visibleControls: any[] = [];
  currentIndex: number = 0;
  showArrows: boolean = false; // Tracks whether to show arrows
  showPrevArrow: boolean = false;
  showNextArrow: boolean = false; // Tracks whether to show the next arrow

  updateVisibleControls(): void {
    const controls = this.viewTypeMap[this.userType]?.controls || [];
    this.visibleControls = controls.slice(
      this.currentIndex,
      this.currentIndex + 2
    );
    this.showArrows = controls.length > 1; // Show arrows only if there are more than two controls
    this.showPrevArrow = this.currentIndex > 0;
    this.showNextArrow = this.currentIndex + 2 < controls.length; // Show the next arrow only if there are more items to slide
  }

  slideNext(): void {
    const controls = this.viewTypeMap[this.userType]?.controls || [];
    if (this.currentIndex + 2 < controls.length) {
      this.currentIndex++;
      this.updateVisibleControls();
    }
  }

  slidePrev(): void {
    if (this.currentIndex > 0) {
      this.currentIndex--;
      this.updateVisibleControls();
    }
  }
}
