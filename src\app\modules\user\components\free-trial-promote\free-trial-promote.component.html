<section class="free-trial-container mb-5 container-fluid bg-default">
  <div class="row">
    <div class="col-md-12 text-center mb-4">
      <span style="font-weight: bolder" class="mb-4 mt-4 fs-48">
        Your Connection Matters!
      </span>
    </div>
    <div class="col-md-23">
      <div class="d-flex flex-column">
        <p class="mt-3 fw-bold" style="font-size: 20px">
          Get Connected and build a solid relationship with channel experts!
          <br />
        </p>
      </div>
      <span class="text-muted bold"> With your free account you will get:</span>
      <div class="d-flex flex-row bp-1 mb-3 align-items-center mt-3">
        <div class="point point-1">1</div>
        <span>Access to detailed view and search company brands </span>
      </div>
      <div class="d-flex flex-row bp-2 mb-3 align-items-center">
        <div class="point point-2">2</div>
        <span>Locate channel partners and experts in the area of choice</span>
      </div>
      <div class="d-flex flex-row bp-3 mb-3 align-items-center">
        <div class="point point-3">3</div>
        <span
          >View, easily Connect, and invite your business experts or
          users.</span
        >
      </div>
      <div class="d-flex flex-row bp-4 mb-3 align-items-center">
        <div class="point point-4">4</div>
        <span
          >Sync and Collaborate with your connections for sales inquiries
        </span>
      </div>
    </div>

    <div
      class="col-md-6 d-flex flex-column justify-content-center align-items-center d-none"
    >
      <!-- <img
        class="img"
        style="width: 53%"
        src="./assets/svgs/undraw_connecting_teams_re_hno7.svg"
      />
      <br /> -->

      <button
        *ngIf="isLoggedIn"
        [routerLink]="isLoggedIn ? '/connections' : '/account'"
        class="btn btn-outline-primary w-100"
      >
        {{ isLoggedIn ? "Learn more about connections" : "Sign up now" }}
      </button>
    </div>
  </div>
</section>
