import { Component, OnInit } from '@angular/core';
import { AccountService } from 'src/app/modules/account/services/account.service';

@Component({
  selector: 'app-free-trial-promote',
  templateUrl: './free-trial-promote.component.html',
  styleUrls: ['./free-trial-promote.component.scss'],
})
export class FreeTrialPromoteComponent implements OnInit {
  isLoggedIn = false;
  constructor(private account: AccountService) {}

  ngOnInit(): void {
    this.account.isLoggedIn$.subscribe((response) => {
      this.isLoggedIn = response;
    });
  }
}
