import { Component, OnInit } from '@angular/core';
import { finalize } from 'rxjs';
import { AccountService } from 'src/app/modules/account/services/account.service';

@Component({
  selector: 'app-frequently-asked-questions',
  templateUrl: './frequently-asked-questions.component.html',
  styleUrls: ['./frequently-asked-questions.component.scss']
})
export class FrequentlyAskedQuestionsComponent implements OnInit {
  accordionItems: { title: string; content: string }[] = [];
  loading = false;
  constructor(private accountService: AccountService) {}

  ngOnInit(): void {
    this.loading = true;
    this.accountService.getFrequentlyAskedQuestions().pipe(finalize(() => {
      this.loading = false;
    })).subscribe((response: any) => {
      if (response && response.data) {
        this.accordionItems = response.data.map((item: any) => ({
          title: item.question,
          content: item.answer
        }));
      }
    });
  }
}