import { Component } from '@angular/core';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-e-card-marketing',
  templateUrl: './e-card-marketing.component.html',
  styleUrls: ['./e-card-marketing.component.scss']
})
export class ECardMarketingComponent {
  isLoggedIn$: Observable<boolean>;

  constructor(private accountService: AccountService) {
    this.isLoggedIn$ = this.accountService.isLoggedIn$;
  }
}
