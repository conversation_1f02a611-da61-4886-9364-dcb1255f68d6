.fc-only-header {
    width: 100%;
    display: flex;
    flex-direction: column;

    label {
        color: #014681;
        min-width: 100%;
        height: 28px;
        font-style: normal;
        font-weight: 700;
        font-size: 23px;
        line-height: 120%;
        letter-spacing: 0.2em;
        text-transform: uppercase;
        color: #014681;
        flex: none;
        order: 0;
        flex-grow: 0;
        margin-bottom: 1rem;
    }

    h3 {
        max-width: 100%;
        font-style: normal;
        font-weight: 700;
        font-size: 44px;
        line-height: 120%;
        color: #191825;
        flex: none;
        order: 1;
        flex-grow: 0;
    }

    p {
        max-width: 882px;
        font-style: normal;
        font-weight: 300;
        font-size: 16px;
        line-height: 1.5;
        color: rgba(25, 24, 37, 0.5);
        flex: none;
        order: 1;
        flex-grow: 0;
        margin-bottom: 0px;
    }
}


.fc-key-difference{
    width: 100%;
    display: flex;
    flex-direction: row;
    gap: 62px;
    margin-top: 2rem;
    margin-bottom: 90px;

    .fc-list-of-key{
        width: 540px;
        display: flex;
        flex-direction: column;
    }

    .fc-key-details{
        display: flex;
        flex-direction: column;
        width: 540px;

        h4{
        font-weight: 700;
        font-size: 30px;
        line-height: 70px;
        color: #000000;
        }

        label{
            font-style: normal;
            font-weight: 400;
            font-size: 16px;
            line-height: 160%;
            /* or 186% */
            letter-spacing: -0.656526px;
            color: rgba(25, 24, 37, 0.5);
        }

        p{
            font-style: normal;
            font-weight: 400;
            font-size: 16px;
            line-height: 160%;
            color: rgba(25, 24, 37, 0.5);
            flex: none;
            flex-grow: 0;
        }

        iframe{
            border-radius: 6px;
            margin-block: 1.5rem;
        }
    }

    .tab-item{
        box-sizing: border-box;
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 32px;
        gap: 32px;
        width: 100%;
        height: 164px;
        background: #FFFFFF;        
        border: 1px solid transparent;
        border-radius: 32px;        
        flex: none;
        order: 1;
        align-self: stretch;
        flex-grow: 0;
        cursor: pointer;

        &:hover{
            border: 1px solid rgba(25, 24, 37, 0.1);
        }

        span{
            color: #191825;
            font-size: 23px;
            font-weight: 600;
            cursor: pointer;
        }
        label{
            color: rgb(25 24 37 / 50%);
            font-size: 16px;
            margin-bottom: 0px;
            font-weight: 400;
            cursor: pointer;
        }
        .fc-tab-name{
            display: flex;
            flex-direction: column;
            align-items: start;
            cursor: pointer;
        }

        &.active{
            border: 1px solid rgba(25, 24, 37, 0.1);
        }
    }
}

.fc-key-details-modal{
    padding: 2rem;

    p{
        line-height: 30px;
    }
}


.read-more-btn{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 119px;
    height: 49px;
    background: #014681;
    border-radius: 100px;
    flex: none;
    order: 1;
    flex-grow: 0;
    font-size: 14px;
    color: white;
    text-decoration: none;
    font-weight: bold;   
    border: none;
}

@media(max-width:768px){
    .fc-only-header{
        label{
            font-size:1rem;            
            margin-bottom: 0px;
        }
        h3{
            font-size: 1.25rem;            
        }
        p{
            font-size: 14px;
        }
    }

    .fc-key-difference{
        flex-direction: column;
        gap: 1.5rem;
        margin-bottom: 0px;

        .fc-list-of-key{
            width: 100%;
        }
        .tab-item{
            padding: 1rem;
            height: auto;
            gap: 1rem;
            border-radius: 1rem;
            img{
                width: 50px;
            }
            span{
                font-size: 1rem;
            }
            label{
                font-size: 14px;
            }
        }
        
    .fc-key-details{
        width: 100%;
        h4{
            font-size: 1.25rem;
            width: 100%;
            line-height: 1.5;
            margin-bottom: 0px;
        }
        label{
            font-size: 14px;
        }
        p{
            font-size: 14px;
        }
        iframe{
            width: 100%;
            height: 250px;
        }
    }
    }
    .m-key-features-bg{
        padding: 1rem;
        background-color: #f5f5f5;
        padding-block: 3rem;
        padding-bottom: 2rem;
    }

    .fc-list-of-key{
        gap: 1rem;        
    }
}