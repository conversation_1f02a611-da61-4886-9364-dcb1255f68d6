import { Component } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { BsModalService } from 'ngx-bootstrap/modal';


export interface TabData {
  id: string;
  title: string;
  imageSrc: string;
  heading: string;
  description: string;
  videoUrl: string;
  content: string;
}
@Component({
  selector: 'app-key-features',
  templateUrl: './key-features.component.html',
  styleUrls: ['./key-features.component.scss']
})
export class KeyFeaturesComponent {
 constructor(private readonly modalService: BsModalService, private router: Router,) {}

 openProcess(template: any) {
  document.documentElement.style.overflow = 'hidden';
  const modalRef = this.modalService.show(template, {
    class: 'modal-xl modal-dialog-centered',
  });
  modalRef.onHidden?.subscribe(() => {
    document.documentElement.style.overflow = '';
  });
}

eCard(eCardTemplate: any) {
  document.documentElement.style.overflow = 'hidden';
  const modalRef =  this.modalService.show(eCardTemplate, {
    class: 'modal-xl modal-dialog-centered',
  });
  modalRef.onHidden?.subscribe(() => {
    document.documentElement.style.overflow = '';
  });
}

confidenceDailog(confidenceTemplate: any) {
  document.documentElement.style.overflow = 'hidden';
  const modalRef =  this.modalService.show(confidenceTemplate, { 
    class: 'modal-xl modal-dialog-centered',
  });
  modalRef.onHidden?.subscribe(() => {
    document.documentElement.style.overflow = '';
  });
}

openAudience(audienceTemplate: any) {
  document.documentElement.style.overflow = 'hidden';
  const modalRef =  this.modalService.show(audienceTemplate, {
    class: 'modal-xl modal-dialog-centered',
  });
  modalRef.onHidden?.subscribe(() => {
    document.documentElement.style.overflow = '';
  });  
}
}
