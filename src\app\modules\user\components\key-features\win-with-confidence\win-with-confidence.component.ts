import { Component } from '@angular/core';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-win-with-confidence',
  templateUrl: './win-with-confidence.component.html',
  styleUrls: ['./win-with-confidence.component.scss']
})
export class WinWithConfidenceComponent {
  isLoggedIn$: Observable<boolean>;

  constructor(private accountService: AccountService) {
    this.isLoggedIn$ = this.accountService.isLoggedIn$;
  }
}
