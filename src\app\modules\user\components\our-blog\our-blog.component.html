<div class="fc-blog-wrap">
    <div class="fc-container">
        <div class="fc-blog-header">
            <label>Our Blog</label>
            <h4>Unlock valuable insights, enhance your knowledge, 
              and share your voice in our community.
              </h4>
        </div>        
        <div class="fc-blog-list">
          <!-- Loading state -->
          <div *ngIf="loading" class="fc-loading-state">
            <div class="fc-spinner"></div>
            <p>Loading latest blogs...</p>
          </div>

          <!-- Blog cards -->
          <div class="fc-blog-card" *ngFor="let blog of blogs" (click)="navigateToBlog(blog)">
            <img
              [src]="getBlogImage(blog)"
              [alt]="blog.title"
              (error)="onImageError($event)"
              loading="lazy">
            <div class="fc-overlay-text">
              <div class="post-date">Focile Inc • {{ formatDate(blog.publishedAt) }}</div>
              <div class="fc-related-blog">
                <span *ngFor="let tag of getTags(blog.tags)">{{ tag }}</span>
              </div>
              <div class="fc-related-name">
                {{ blog.title }}
                <span>
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M7 17L17 7M17 7H7M17 7V17" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </span>
              </div>
            </div>
          </div>

          <!-- Empty state -->
          <div *ngIf="!loading && blogs.length === 0" class="fc-empty-state">
            <p>No blogs available at the moment.</p>
          </div>
              
        </div>
        <div class="col-sm-12 pt-2 pt-sm-4 mt-4 mt-sm-5">
            <a class="more-blog-btn" routerLink="/blog-list" role="button">More Blog
                <svg width="34" height="35" viewBox="0 0 34 35" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_257_1630)">
                    <path d="M9.89219 17.7986H24.0343M24.0343 17.7986L16.9633 10.7275M24.0343 17.7986L16.9633 24.8697" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </g>
                    <defs>
                    <clipPath id="clip0_257_1630">
                    <rect width="24" height="24" fill="white" transform="translate(16.9633 0.828125) rotate(45)"/>
                    </clipPath>
                    </defs>
                    </svg>
            </a>
        </div>
    </div>
</div>