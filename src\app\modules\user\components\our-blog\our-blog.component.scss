.fc-blog-wrap {
  background-color: #f5f5f5;
  padding: 80px 0px;

  .fc-blog-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;

    label {
      font-family: "Poppins";
      font-style: normal;
      font-weight: 700;
      font-size: 23px;
      line-height: 120%;
      text-align: center;
      letter-spacing: 0.2em;
      text-transform: uppercase;
      color: #014681;
      flex: none;
      order: 0;
      align-self: stretch;
      flex-grow: 0;
    }

    h4 {
      width: 920px;
      font-family: "Poppins";
      font-style: normal;
      font-weight: 700;
      font-size: 44px;
      line-height: 120%;
      text-align: center;
      color: #191825;
      flex: none;
      order: 1;
      align-self: stretch;
      flex-grow: 0;
      margin: 0px auto;
    }
  }
}

.fc-blog-list {
  display: flex;
  flex-direction: row;
  gap: 30px;
  margin-top: 80px;
  justify-content: center;

  .fc-blog-card {
    position: relative;
    display: flex;
    height: 400px;
    overflow: hidden;
    border-radius: 20px;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    img {
      position: absolute;
      top: 0px;
      left: 0px;
      width: 100%;
      object-fit: cover;
    }

    .fc-overlay-text {
      position: relative;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        180deg,
        rgba(18, 18, 18, 0.0001) 0%,
        #121212 100%
      );
      padding-top: initial;
      padding-bottom: 0px;
      bottom: 0%;
      top: auto;
      display: flex;
      flex-direction: column;
      justify-content: end;
      padding: 1rem;
      color: white;
      gap: 1rem;

      .post-date {
        font-size: 14px;
        color: white;
        font-weight: 500;
      }

      .fc-related-blog {
        display: flex;
        flex-direction: row;
        gap: 10px;

        span {
          background: rgba(255, 255, 255, 0.28);
          border-radius: 19px;
          color: white;
          font-size: 14px;
          padding: 10px 1rem;
        }
      }
      .fc-related-name {
        width: 306px;
        font-style: normal;
        font-weight: 500;
        font-size: 25px;
        line-height: 35px;
        color: #ffffff;
      }
    }
  }

  // Loading state styles
  .fc-loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 2rem;
    width: 100%;

    .fc-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #014681;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    p {
      color: #666;
      font-size: 16px;
      margin: 0;
    }
  }

  // Empty state styles
  .fc-empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    width: 100%;

    p {
      color: #666;
      font-size: 16px;
      margin: 0;
      text-align: center;
    }
  }
}

// Spinner animation
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.more-blog-btn {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 16px 32px;
  gap: 16px;
  width: 223.76px;
  height: 68.77px;
  background: #014681;
  box-shadow: 0px 548px 219px rgba(0, 0, 0, 0.01),
    0px 308px 185px rgba(0, 0, 0, 0.04), 0px 137px 137px rgba(0, 0, 0, 0.06),
    0px 34px 75px rgba(0, 0, 0, 0.07), 0px 0px 0px rgba(0, 0, 0, 0.07);
  border-radius: 100px;
  border: none;
  justify-content: center;
  color: white;
  margin: 0px auto;
  font-size: 20px;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: #0056b3;
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0px 8px 25px rgba(1, 70, 129, 0.3);
  }

  &:focus {
    outline: 2px solid #014681;
    outline-offset: 2px;
  }

  &:active {
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .fc-blog-wrap {
    padding-block: 3rem;

    .fc-blog-header {
      label {
        font-size: 1rem;
      }

      h4 {
        width: 100%;
        font-size: 1.25rem;
        line-height: 1.5;
      }
    }
  }

  .fc-blog-list {
    margin-top: 2rem;
    flex-direction: column;
    gap: 1rem;

    .fc-blog-card {
      .fc-overlay-text {
        .fc-related-name {
          font-size: 1rem;
          line-height: 1.5;
          width: auto;
        }
        .fc-related-blog {
          border-radius: 1rem;
          padding: 5px 0rem;
        }
      }
    }
  }

  .fc-footer-logo {
    width: 100%;

    p {
      margin-bottom: 2rem;
    }
  }

  .more-blog-btn {
    width: auto;
    height: auto;
    padding: 1rem 2rem;
    font-size: 1rem;
  }
}
