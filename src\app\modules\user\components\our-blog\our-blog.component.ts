import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { finalize, catchError, of } from 'rxjs';

// Blog interface based on API response
interface Blog {
  id: string;
  title: string;
  subTitle: string;
  slug: string;
  content: string | null;
  imageUrl: string | null;
  tags: string;
  publishedAt: string;
  likeCount?: number;
  viewCount?: number;
  isLiked?: boolean;
}

@Component({
  selector: 'app-our-blog',
  templateUrl: './our-blog.component.html',
  styleUrls: ['./our-blog.component.scss']
})
export class OurBlogComponent implements OnInit {
  blogs: Blog[] = [];
  loading = false;
  defaultBlogImage = 'assets/images/e-card-default.svg'; // Default image path

  constructor(
    private accountService: AccountService,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.loadLatestBlogs();
  }

  loadLatestBlogs(): void {
    this.loading = true;
    this.accountService.getAllBlogs()
      .pipe(
        catchError((error) => {
          console.error('Error loading blogs:', error);
          // Return empty array instead of letting error propagate
          return of([]);
        }),
        finalize(() => this.loading = false)
      )
      .subscribe({
        next: (response: Blog[]) => {
          // Sort blogs by publishedAt date (latest first) and take only 3
          const sortedBlogs = (response || []).sort((a, b) =>
            new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime()
          );
          this.blogs = sortedBlogs.slice(0, 3);
          console.log('Latest 3 blogs loaded:', this.blogs);
        },
        error: (error) => {
          console.error('Unexpected error loading blogs:', error);
          this.blogs = [];
        }
      });
  }

  // Get blog image with fallback to default
  getBlogImage(blog: Blog): string {
    return blog.imageUrl || this.defaultBlogImage;
  }

  // Format date for display
  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  }

  // Parse tags from string to array
  getTags(tagsString: string): string[] {
    if (!tagsString) return [];
    return tagsString.split(',').map(tag => tag.trim());
  }

  // Handle image error
  onImageError(event: any): void {
    event.target.src = this.defaultBlogImage;
  }

  // Navigate to blog detail page
  navigateToBlog(blog: Blog): void {
    if (blog.slug) {
      this.router.navigate(['/blog-detail', blog.slug]);
    } else {
      // Fallback to ID if slug is not available
      this.router.navigate(['/blog-detail', blog.id]);
    }
  }

  // Format count for display (e.g., 1200 -> 1.2K)
  formatCount(count: number): string {
    if (count >= 1000000) {
      return (count / 1000000).toFixed(1) + 'M';
    } else if (count >= 1000) {
      return (count / 1000).toFixed(1) + 'K';
    }
    return count.toString();
  }
}
