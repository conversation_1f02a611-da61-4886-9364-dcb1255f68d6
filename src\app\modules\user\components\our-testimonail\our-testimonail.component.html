<div class="fc-testimonail-wrap">
  <div class="testimonial-header">
      <label>Our testimonial</label>
      <h4>Here what people say and reveal about our offered services</h4>
  </div>
  <div class="fc-recent-testimonial">        
      <owl-carousel-o [options]="carouselOptions" (translated)="onTranslated($event)">
          <ng-container *ngFor="let item of items; let i = index">
              <ng-template carouselSlide>
                  <div [ngClass]="{'active-slide': i === centerSlide, 'inactive-slide': i !== centerSlide}" class="slide">
                      <!-- Slide Content -->
                      <div class="fc-testimonial-card">
                          <div class="fc-text-column">
                              <img [src]="item.logo" alt="Company Logo">
                              <h5>{{ item.name }}</h5>
                              <label>{{ item.company }}</label>                
                              <p>{{ item.message }}</p>
                          </div>
                          <div class="fc-testimonal-avatar">
                              <img [src]="item.avatar" alt="Avatar">
                          </div>
                      </div>
                      <!-- End Slide Content -->
                  </div>
              </ng-template>
          </ng-container>
      </owl-carousel-o>    
  </div>
</div>