.fc-testimonail-wrap {
  background: url(../../../../../assets/images/connecting-pattern.svg) black no-repeat left 150px;
  min-height: 1029px;
  width: 100%;
  position: relative;
  padding-block: 80px;
  text-align: center;
  overflow-x: hidden;
  background-size: cover;

  label {
    width: 982px;
    height: 28px;
    font-style: normal;
    font-weight: 700;
    font-size: 23px;
    line-height: 120%;
    text-align: center;
    letter-spacing: 0.2em;
    text-transform: uppercase;
    color: #FFFFFF;
    flex: none;
    order: 0;
    flex-grow: 0;
    margin-bottom: 2rem;
  }

  h4 {
    width: 982px;
    font-style: normal;
    font-weight: 700;
    font-size: 44px;
    line-height: 120%;
    text-align: center;
    color: #FFFFFF;
    flex: none;
    order: 1;
    flex-grow: 0;
    margin: 0px auto;
  }

  &::after {
    position: absolute;
    width: 452px;
    height: 464px;
    left: 0px;
    top: 374px;
    background: #014681;
    filter: blur(262px);
    content: '';
  }

  &::before {
    position: absolute;
    width: 249px;
    height: 251px;
    right: 100px;
    top: 9px;
    background: #014681;
    filter: blur(262px);
    content: '';
  }



  ::ng-deep .owl-carousel.owl-drag .owl-item {
    transform: scale(0.8);
    /* Default scale for all slides */
    transition: transform 0.3s ease, opacity 0.3s ease;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0.6;
    /* Dim non-active slides */

    &.active.center {
      transform: scale(1);
      /* Highlight active slide */
      opacity: 1;
      z-index: 10;
      /* Bring to front */
    }
  }

  ::ng-deep .owl-theme .owl-nav {
    max-width: 1280px;
    margin: 0px auto;
    text-align: right;
    line-height: 60px;
    display: flex;
    justify-content: end;
  }

  .owl-nav button {
    background-color: #333;
    color: #fff;
    border: none;
    padding: 10px;
    border-radius: 50%;
    cursor: pointer;
  }

  .owl-nav button:hover {
    background-color: #555;
  }

  ::ng-deep {
    .owl-theme .owl-dots {
      max-width: 1244px;
      margin: 0px auto;
      text-align: left;
      margin-top: -66px;
    }

    .owl-theme .owl-nav [class*=owl-] {
      width: 100px;
      height: 100px;
      left: 232px;
      top: 0px;
      background: #FFFFFF;
      border-radius: 100px;
      transform: matrix(-1, 0, 0, 1, 0, 0);
      color: black;
      text-align: center;
      justify-content: center;
      display: flex;
      transform: inherit;

      &:hover {
        background-color: #014681;
        color: white;
      }

      img {
        max-width: 20px;
      }
    }
  }

  ::ng-deep .owl-theme .owl-dots .owl-dot span {
    width: 20px;
    height: 20px;
    background: #ffffff;
  }

  ::ng-deep .owl-theme .owl-dots .owl-dot.active span,
  ::ng-deep .owl-theme .owl-dots .owl-dot:hover span {
    background: #035890;
    width: 44px;
  }
}

.fc-testimonial-card {
  max-width: 920px;
  display: flex;
  flex-direction: row;
  background-color: white;
  z-index: 11;
  position: relative;
  margin: 3rem auto;
}

.fc-text-column {
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-content: start;
  padding: 2rem;
  width: 570px;
  text-align: left;

  img {
    max-width: 125px;
  }

  h5 {
    position: relative;
    width: max-content;
    height: 29px;
    font-style: normal;
    font-weight: 700;
    font-size: 24px;
    line-height: 120%;
    color: #000000;
    margin-top: 1.5rem;
    margin-bottom: 0.5rem;
  }

  label {
    min-width: 154px;
    height: 19px;
    font-style: normal;
    font-weight: 300;
    font-size: 16px;
    line-height: 120%;
    color: #000000;
    width: auto;
    text-align: left;
  }

  p {
    width: 473px;
    height: 203px;
    
    font-style: normal;
    font-weight: 400;
    font-size: 18px;
    line-height: 160%;
    color: rgba(25, 24, 37, 0.5);
    text-align: left;
    margin-bottom: 0px;
    overflow: auto;
  }
}

.fc-testimonal-avatar {
  width: 350px;

  img{
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

@media(max-width:768px){
  .fc-testimonail-wrap{
    padding-block: 3rem;
    min-height: auto;
    overflow-x: inherit;
    label{
      width: 100%;
      font-size: 1rem;
      margin-bottom: 1rem;
    }   
    h4{
      width: 100%;
      font-size: 1.25rem;
      line-height: 1.5;
      
    } 
    ::ng-deep .owl-theme .owl-nav{
      display: none;
    }
    ::ng-deep .owl-theme .owl-dots{
      max-width: 100%;
      margin: 0px auto;
      text-align: center;
      margin-top: 0px;

      span{
        width: 1rem;
        height: 1rem;
      }
    }
  }
  .testimonial-header{
    z-index: 11;
    position: relative;
    padding: 0px 1rem;
  }
  .fc-testimonial-card{
    width:90%;
    max-width: 90%;
    flex-direction: column;
    margin: 2rem auto;
    padding-top: 2rem;
    border-radius: 1rem;
  }
  .fc-text-column{
    width: 100%;
    order: 2;
    padding: 1rem;
    padding-top:2rem;
    align-items: center;
    h5{
      font-size: 1rem;
      margin-top: 1rem;
      margin-bottom: 0px;
    }
    label{
      font-size: 0.8rem;    
      text-align: center;  
    }
    p{
      width: 100%;
      height: auto;
      text-align: center;
      font-size: 14px;
    }
    img{
      max-width: 80px;
    }
  }
  .fc-testimonal-avatar{
    width: 100px;
    order: 1;
    height: 100px;
    margin: 0px auto;
    border-radius: 100px;
    overflow: hidden;
  }
}