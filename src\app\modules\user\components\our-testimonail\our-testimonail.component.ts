import { Component } from '@angular/core';
import { OwlOptions } from 'ngx-owl-carousel-o';

import {
  Testimonials,
  TestimonialsService,
} from 'src/app/shared/services/testimonials.service';

@Component({
  selector: 'app-our-testimonail',
  templateUrl: './our-testimonail.component.html',
  styleUrls: ['./our-testimonail.component.scss'],
})
export class OurTestimonailComponent {
  constructor(private readonly testimonial: TestimonialsService) {}
  items: any[] = [];
  centerSlide: number = 0;
  ngOnInit(): void {
    this.loadTestimonials();
  }
  loadTestimonials(): void {
    this.testimonial.testimonials$.subscribe((response: Testimonials[]) => {
      this.items = response.map((item: Testimonials) => ({
        logo: item.comapnyLogo || 'default-logo.png',
        name: item.name,
        company: item.comapnyName,
        message: item.message,
        avatar: item.userImage || 'default-avatar.png',
      }));
    });
  }

  carouselOptions: OwlOptions = {
    loop: true,
    margin: 10,
    center: true,
    dots: true,
    autoplay: true,
    nav: true,
    navText: [
      '<img src="../../../../../assets/images/arrow-left.svg" alt="Previous" class="custom-nav-arrow" />',
      '<img src="../../../../../assets/images/black-arrow.svg" alt="Next" class="custom-nav-arrow" />',
    ],
    autoplayTimeout: 10000,
    responsive: {
      0: { items: 1 },
      600: { items: 3 },
      1000: { items: 2 },
    },
  };

  onTranslated(event: any): void {
    const centerIndex =
      event.item.index - event.relatedTarget._clones.length / 2;
    this.centerSlide = (centerIndex + this.items.length) % this.items.length;
  }
}
