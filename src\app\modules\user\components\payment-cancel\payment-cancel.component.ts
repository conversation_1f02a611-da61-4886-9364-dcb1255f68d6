import { CommonModule } from '@angular/common';
import { Component, type OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { AccountService } from 'src/app/modules/account/services/account.service';

@Component({
  selector: 'app-payment-cancel',
  standalone: true,
  imports: [CommonModule,RouterModule],
  templateUrl: './payment-cancel.component.html',
})
export class PaymentCancelComponent implements OnInit {
  user: any;
  constructor(private account: AccountService) {}
  ngOnInit(): void {
    this.user = this.account.user;
  }
}
