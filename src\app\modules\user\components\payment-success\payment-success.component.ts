import { CommonModule } from '@angular/common';
import { Component, type OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { finalize } from 'rxjs';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { SubscriptionPlanService } from 'src/app/shared/services/subscription-plan.service';

@Component({
  selector: 'app-payment-success',
  templateUrl: './payment-success.component.html',
})
export class PaymentSuccessComponent implements OnInit {
  user: any;
  loading!: boolean;
  constructor(
    private account: AccountService,
    private sbscriptionPlanService: SubscriptionPlanService,
    private activateRoute: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.user = this.account.user;
    this.activateRoute.queryParams.subscribe((response: any) => {
      const { sessionId, p, u } = response;
      const item = {
        subscriptionPlanId: p,
        StripeCheckoutSessionId: sessionId,
        userId: u,
      };
      this.purchesePlan(item);
    });
  }

  purchesePlan(item: any) {
    this.loading = true;
    this.sbscriptionPlanService
      .addUserPlan(item)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((response: any) => {
        console.log(response);
      });
  }
}
