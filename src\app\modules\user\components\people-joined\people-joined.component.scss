.fc-people-join-wrap {
    margin-block: 90px;
    position: relative;
    flex-direction: row;
    display: flex;
}

.fc-people-count {
    max-width: 445px;
    display: flex;
    flex-direction: column;
    gap: 4rem;
    position: relative;
    margin-left: auto;

    .box-container {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 0px;
        /* Adjust as needed */
        position: relative;
    }

    .box {
        padding: 16px;
        /* Adjust padding as needed */
        text-align: center;
        display: flex;
        flex-direction: column;
        min-height: 165px;
        align-items: center;
        justify-content: center;

        strong {
            color: #02447D;
            font-size: 35px;
            line-height: 1.5;
            margin-bottom: 0;
        }

        label {
            color: #191825;
            font-weight: 400;
            font-size: 18px;
        }
    }

    .box-1 {
        border-top: 0px;
        border-right: 1px solid #E8E8EA;
    }

    .box-2 {
        border-top: 0;
        border-right: 0;
    }

    .box-3 {
        border-top: 1px solid #E8E8EA;
        border-right: 1px solid #E8E8EA;
    }

    .box-4 {
        border-top: 1px solid #E8E8EA;
        border-left: 0;
    }
}

.fc-count-of-joined {
    position: relative;
}

.connection-icon {
    width: 88px;
    height: 88px;
    background-color: #FACD49;
    border-radius: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: -49px;
    top: -59px;

}

.fc-only-header {
    width: 100%;
    display: flex;
    flex-direction: column;

    label {
        color: #014681;
        min-width: 100%;
        height: 28px;
        font-style: normal;
        font-weight: 700;
        font-size: 23px;
        line-height: 120%;
        letter-spacing: 0.2em;
        text-transform: uppercase;
        color: #014681;
        flex: none;
        order: 0;
        flex-grow: 0;
        margin-bottom: 1rem;
    }

    h3 {
        max-width: 100%;
        font-style: normal;
        font-weight: 700;
        font-size: 44px;
        line-height: 120%;
        color: #191825;
        flex: none;
        order: 1;
        flex-grow: 0;
    }

    p {
        max-width: 100%;
        font-style: normal;
        font-weight: 300;
        font-size: 18px;
        line-height: 1.5;
        color: rgba(25, 24, 37, 0.5);
        flex: none;
        order: 1;
        flex-grow: 0;
        margin-bottom: 0px;
        margin-top: 2rem;
    }
}


.create-avatar {
    width: 615px;
    position: relative;

    &::after {
        position: absolute;
        width: calc(50vw + 570px);
        height: 96px;
        left: -50vw;
        top: 65px;
        content: '';
        height: 550px;
        top: 150px;
        background: #FACD49;
        border-radius: 0px 500px 500px 0px;
    }

    img {
        z-index: 1;
        position: relative;
        left: -160px;
        bottom: 1.5rem;
    }

    .small-dot {
        /* Ellipse 15 */

        position: absolute;
        width: 40px;
        height: 40px;
        left: 0;
        top: 130px;
        /* Gradient */
        background: linear-gradient(280.84deg, #5D50C6 7.11%, #F85E9F 93.54%);
        filter: blur(2px);
        z-index: 1;
        border-radius:20px;

    }

    .medium-dot {
        /* Ellipse 16 */

        position: absolute;
        width: 40px;
        height: 40px;
        top: 225px;
        /* Yellow */
        background: #FACD49;
        filter: blur(2px);
        right: 10px;
        border-radius: 100px;
    }

    .big-dot {
        position: absolute;
        width: 96px;
        height: 96px;
        top: 40px;
        background: linear-gradient(280.84deg, #5D50C6 7.11%, #F85E9F 93.54%);
        filter: blur(6px);
        right: 80px;
        border-radius: 100px;
    }
    .x-small-dot{
    position: absolute;
    width: 32px;
    height: 32px;
    right:100px;
    top: 504px;
    /* Orange */
    background: #FF5722;
    filter: blur(2px);
    z-index: 1;
    border-radius: 16px;
    }

    .xx-small-dot{                
        position: absolute;
        width: 16px;             
        height: 16px;
        right:150px;
        top: 344px;
        background: #FF5722;
        filter: blur(2px);
        z-index: 1;
        border-radius: 8px;
    }
}


.small-dot, .medium-dot, .big-dot, .x-small-dot, .xx-small-dot {
    animation: bounce 2s infinite ease-in-out;
    position: absolute;
  }
  
  @keyframes bounce {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-10px);
    }
  }

@media(max-width:768px) {
    .fc-people-join-wrap{
        flex-direction: column;   
        margin-block: 0px;     
        padding-block: 2rem;

        h3{
            font-size: 1.25rem;
        }
    }
    .create-avatar{
        width: 100%;
        img{
            width: 100%;
            left: 0px;
        }
        .x-small-dot{
            z-index: 0;
        }
    }
    .create-avatar::after{
        width: 100%;
        height: 250px;
        left: -16px;
        transform: translateY(50%);
        top: 0px;
    }
    .fc-only-header{
        label{
            font-size: 1rem;
            margin-bottom: 0;
        }
        p{            
            font-size: 14px;
            margin-top: 0px;
        }
    }
    .fc-people-count{
        gap: 2rem;

        .box{
            min-height: 120px;
            label{
                font-size: 1rem;
            }
            strong{
                font-size: 1.5rem;
            }
        }
    }
}