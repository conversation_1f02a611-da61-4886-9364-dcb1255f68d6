import { Component, ElementRef, HostListener, OnInit } from '@angular/core';

@Component({
  selector: 'app-people-joined',
  templateUrl: './people-joined.component.html',
  styleUrls: ['./people-joined.component.scss']
})
export class PeopleJoinedComponent  implements OnInit{

  activeUsers = 1025; // Example values
  directLeads = 325;
  referrals = 760;
  eventActivities = 3250;

  counted = false;

  ngOnInit(): void {
    this.startCounter();
  }

  @HostListener('window:scroll', [])
  onScroll(): void {
    const counterElement = document.getElementById('counter');
    if (!counterElement || this.counted) return;

    const oTop = counterElement.offsetTop - window.innerHeight;
    if (window.scrollY > oTop) {
      this.animateCounters();
      this.counted = true;
    }
  }

  startCounter(): void {
    // Add any initialization logic if needed
  }

  animateCounters(): void {
    const counters = document.querySelectorAll('.count');
    counters.forEach((counter) => {
      const element = counter as HTMLElement;
      const countTo = parseInt(element.getAttribute('data-count') || '0', 10);
      let current = 0;
      const duration = 2000; // 2 seconds
      const increment = countTo / (duration / 16); // Approx. 60 FPS

      const updateCount = () => {
        current += increment;
        if (current >= countTo) {
          element.innerText = countTo.toString();
        } else {
          element.innerText = Math.floor(current).toString();
          requestAnimationFrame(updateCount);
        }
      };

      updateCount();
    });
  }

}
