import { Component, Renderer2 } from '@angular/core';
import { Router } from '@angular/router';
import { BsModalService } from 'ngx-bootstrap/modal';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { UsersService } from 'src/app/shared/services/user.service';
import { UtilsService } from 'src/app/shared/services/utils.service';

@Component({
  selector: 'app-platform',
  templateUrl: './platform.component.html',
  styleUrls: ['./platform.component.scss']
})
export class PlatformComponent {
  eCards: Array<number> = [];
  isLoggedIn = false;
  user: any = {};
  constructor(
    public readonly account: AccountService,
    private readonly users: UsersService,
    private readonly modalService: BsModalService,
    private readonly router: Router,
    private renderer: Renderer2,
    public utls: UtilsService,
  ) {
    account.isLoggedIn$.subscribe((response) => {
      this.eCards = [];
      this.isLoggedIn = response;
      if (!this.isLoggedIn) {
        this.user = {};
      }
      for (let index = 0; index < (response ? 12 : 8); index++) {
        this.eCards.push(index);
      }
    });
  }

}
