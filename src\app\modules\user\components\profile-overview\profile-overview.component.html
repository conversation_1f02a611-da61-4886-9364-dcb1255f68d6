<section *ngIf="!loading">
  <!-- <div class="profile-page">
    <div class="banner">
      <img [src]="selectedImage" />
      <div
        *ngIf="userState$.userType != companyType.EndUser"
        style="
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        "
        class="text-white"
      >
        <span
          role="button"
          (click)="navigateToCompany()"
          class="fs-1 d-flex flex justify-content-center align-items-center"
          tooltip="View {{ vm?.companyName }}"
          style="font-size: 1.5rem; font-weight: 700"
        >
          <i class="fa fa-building fs-3"></i> &nbsp; {{ vm?.companyName }}</span
        >
        <div>
          <p class="mt-2" style="font-weight: 500">
            <span *ngFor="let item of experties | slice : 0 : 3; let i = index">
              <span *ngIf="i !== 0"> {{ "||" }} </span> {{ item.name }}
            </span>
          </p>
        </div>
      </div>
      <div
        *ngIf="userState$.userType == companyType.EndUser"
        style="font-size: 1.5rem; font-weight: 700"
      >
        <i class="fa fa-building fs-3"></i> &nbsp;
        {{ endUser.organizationName }}
      </div>
      <div
        class="user-icon position-absolute"
        style="right: 0rem; bottom: 0rem"
      >
        <button (click)="uploadBanner()" class="btn btn-primary rounded-0">
          <i class="fa fa-pen"></i>
        </button>
        <input
          type="file"
          class="hide d-none"
          accept="image/jpeg, image/png"
          #bannerFile
          (change)="uploadBannerImage($event)"
        />
      </div>
    </div>
    <div class="profile-container">
      <div class="profile-picture">
        <img [src]="vm.profilePhoto" alt="Profile Picture" appImg />
        <span
          class="p-1 position-absolute d-flex justify-content-center"
          (click)="updateProfile()"
          role="button"
          tooltip="Change profile picture"
          style="bottom: 0px; width: 35px; right: 0px; background: white"
        >
          <i class="fa fa-pen"></i>
        </span>
        <input
          type="file"
          accept="image/png, image/jpeg"
          name="profile"
          class="d-none"
          id="profile"
          #profile
          (change)="uploadProfileImage($event)"
        />
      </div>
    </div>
  </div> -->
  <div>
    <app-end-user-overview
      *ngIf="userState$?.userType === companyType.EndUser && endUser"
      [endUser]="endUser"
    ></app-end-user-overview>
    <app-expert-overview
      *ngIf="userState$?.userType !== companyType.EndUser && expert"
      [expert]="expert"
    ></app-expert-overview>
  </div>
  <!--  -->
</section>

<div
  *ngIf="loading"
  class="text-center"
  style="margin-top: 15rem; height: 80vh"
>
  <app-spinner></app-spinner>
</div>
