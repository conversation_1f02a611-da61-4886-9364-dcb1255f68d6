import {
  AfterViewInit,
  Component,
  ElementRef,
  OnInit,
  ViewChild,
} from '@angular/core';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { UserResponseData } from '../end-user-overview/end-user-overview.component';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { finalize, forkJoin } from 'rxjs';
import { UtilsService } from 'src/app/shared/services/utils.service';
import { companyType, UserState } from 'src/app/shared/constant';
import { EndUserService } from 'src/app/shared/services/end-user.service';
import { ModalService } from 'src/app/shared/services/modal.service';
import { CropperComponent } from 'src/app/shared/modals/cropper/cropper.component';
import { filterNonNull } from 'src/app/shared/oprators/filter-null-values';

@Component({
  selector: 'app-profile-overview',
  templateUrl: './profile-overview.component.html',
  styleUrls: ['./profile-overview.component.scss'],
})
export class ProfileOverviewComponent implements OnInit, AfterViewInit {
  @ViewChild('profile') profile!: ElementRef;
  @ViewChild('bannerFile') bannerInput!: ElementRef;
  linkedCopied = false;
  vm: any = {};
  isCompany = false;
  isLoading = false;
  userComment = null;
  addingComment = false;
  userState$!: UserState;
  endUser!: UserResponseData;
  expert: any;
  selectedImage: any;
  companyId: string = '';
  experties: any[] = [];
  loading = false;
  userId: any = '';
  companyType = companyType;

  constructor(
    private account: AccountService,
    private router: Router,
    private toaster: ToastrService,
    private utils: UtilsService,
    private endUserService: EndUserService,
    private modalService: ModalService
  ) {}

  ngAfterViewInit(): void {
    this.toaster.info(
      'This is your profile once focile member is connected with you '
    );
  }

  ngOnInit(): void {
    this.account.user$.pipe(filterNonNull()).subscribe((response) => {
      if (response != null) {
        this.userState$ = response;
        if (response.userType == companyType.EndUser) {
          this.getEnduser();
        } else {
          this.isCompany = response.userType === companyType.Admin;
          this.getEndUserDetails();
        }
      }
    });
  }

  getEnduser() {
    this.endUserService.getEndUser().subscribe((response: any) => {
      if (response.data) {
        this.endUser = response.data;
        this.endUser.userId = this.userState$.userId;
        this.selectedImage = response.data.banner || './assets/svgs/bg.svg';
      }
    });
  }
  getEndUserDetails() {
    this.loading = true;

    forkJoin([
      this.account.getUserDetails(this.userState$.userId),
      this.account.getCompanyComments(this.userState$.userId),
    ])
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(([userDetailResponse, userCommentResponse]) => {
        let response: any = userDetailResponse as any;
        let comments: any = userCommentResponse as any;
        this.selectedImage = response.data.banner || './assets/svgs/bg.svg';
        this.companyId = response.data.expertDetail.companyId;
        this.userId = response.data.userId;
        this.vm.companyName = response.data.expertDetail.companyName;
        this.vm.profilePhoto = response.data.profilePhoto;

        this.experties = response.data?.expertiseList?.filter((x: any) =>
          response.data.expertDetail?.expertiseIds
            ?.split(',')
            ?.includes(x?.idGuid)
        );

        if (response.data.userType == `1`) {
          this.endUser = response.data;
        } else if (
          response.data.userType == `2` ||
          response.data.userType == `3`
        ) {
          this.expert = response.data;
          this.expert.comments = comments.data;
        }

        if (response.messageType) {
          this.router.navigate(['/home']);
          return this.toaster.error(response.message);
        }
        return;
      });
  }

  getExpertComments() {
    // this.account
    //   .getCompanyComments(this.userId)
    //   .subscribe((response: any) => {});
  }

  uploadBanner() {
    this.modalService.openModal('cropper', {
      class: 'modal-xl',
      initialState: {
        onCrop: (data: string) => {
          this.selectedImage = data;
          this.modalService.closeModal();
        },
      },
    });
    // this.bannerInput.nativeElement.click();
  }

  async uploadBannerImage($event: any) {
    const valid = await this.displaySelectedImage(
      $event.target.files[0],
      'banner'
    );
    if (!valid) return;
    const formData = new FormData();
    formData.append('UserId', this.userState$.userId);
    formData.append(
      'photo',
      $event.target.files[0],
      this.utils.generateRandomFileName()
    );
    this.account.uploadUserBanner(formData).subscribe((response) => {});
  }

  updateProfile() {
    this.profile.nativeElement.click();
  }

  async uploadProfileImage($event: any) {
    const valid = await this.displaySelectedImage($event.target.files[0]);
    if (!valid) return;
    const formData = new FormData();
    formData.append('UserId', this.userId);
    formData.append(
      'photo',
      $event.target.files[0],
      this.utils.generateRandomFileName()
    );
    this.account.uploadProfileImage(formData).subscribe((response) => {});
  }

  displaySelectedImage(file: File, fileType = 'profile') {
    return new Promise((resolve, reject) => {
      if (!file.type.startsWith('image/')) {
        this.toaster.error('Selected file is not an image.');
        return resolve(false);
      }
      const isProfile = fileType == 'profile';

      // Check the file size (in bytes)
      if (isProfile && file.size > this.utils.getProfileImageSize()) {
        this.toaster.error('Image size should be under 2MB.');
        return resolve(false);
      } else if (file.size > this.utils.getProfileImageSize()) {
        this.toaster.error('Image size should be under 5MB.');
        return resolve(false);
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const file = e.target?.result;
        this.account.profileImage$.next(this.vm.profilePhoto);
        let user: any = localStorage.getItem('user');
        if (user) {
          user = JSON.parse(user);
        }
        if (fileType === 'profile') {
          user.profilePhoto = file;
          this.vm.profilePhoto = file;
          this.account.profileImage$.next(user.profilePhoto);
        } else {
          user.banner = file;
          this.selectedImage = file;
        }
        localStorage.setItem('user', JSON.stringify(user));
      };
      reader.readAsDataURL(file);
      resolve(true);
    });
  }

  navigateToCompany() {
    this.router.navigate([`/details/${this.companyId}/company`]);
  }
}
