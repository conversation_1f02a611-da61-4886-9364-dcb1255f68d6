<div class="fc-container hide-text" #fcContainer>Welcome to</div>
<div class="fc-what-we-do-container" #fcSlideCategories1>
  <div class="fc-only-header">
    <div class="left-content">
      <h3>Recently Joined Partners</h3>
      <p>
        These are some comments from our customers who have recently joined us
        and enjoyed our services.
      </p>
    </div>
  </div>
  <div class="fc-easy-card-row" #fcSlideCategories>
    <owl-carousel-o [options]="carouselOptions" class="full-width">
      <ng-template carouselSlide *ngFor="let card of easyCards">
        <div class="fc-recently-card">
          <div class="fc-partner-name">
            <div class="fc-card-avatar">
              <figure>
                <!-- <img src="../../../../../assets/images/avatat-img.jpg">   -->
                <img [src]="card.image" alt="{{ card.title }}" />
              </figure>
            </div>
            <div class="fc-rating-start">
              <div class="d-flex flex-column">
                <label class="fs-18 text-black-100">{{ card.hero }}</label>
                <span class="fs-14 text-blue-100">{{ card.location }}</span>
              </div>
              <div>
                {{ card.stars}}
                <svg
                  width="16"
                  height="17"
                  viewBox="0 0 16 17"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M14.1892 6.16512L10.222 5.58856L8.44859 1.99325C8.40015 1.89481 8.32046 1.81512 8.22203 1.76669C7.97515 1.64481 7.67515 1.74637 7.55171 1.99325L5.77828 5.58856L1.81109 6.16512C1.70171 6.18075 1.60171 6.23231 1.52515 6.31044C1.43259 6.40557 1.38159 6.53356 1.38334 6.66628C1.3851 6.79901 1.43948 6.9256 1.53453 7.01825L4.40484 9.81669L3.72671 13.7682C3.71081 13.8602 3.72098 13.9547 3.75608 14.0411C3.79117 14.1276 3.84978 14.2025 3.92526 14.2573C4.00074 14.3121 4.09007 14.3447 4.18312 14.3513C4.27617 14.3579 4.36922 14.3384 4.45171 14.2948L8.00015 12.4292L11.5486 14.2948C11.6455 14.3464 11.758 14.3636 11.8658 14.3448C12.1377 14.2979 12.3205 14.0401 12.2736 13.7682L11.5955 9.81669L14.4658 7.01825C14.5439 6.94169 14.5955 6.84169 14.6111 6.73231C14.6533 6.45887 14.4627 6.20575 14.1892 6.16512Z"
                    fill="#FEA250"
                  />
                </svg>
              </div>
            </div>
          </div>
          <div class="text-black-100 fs-6">
            {{ card.description }}
          </div>
        </div>
      </ng-template>
    </owl-carousel-o>
  </div>
</div>
