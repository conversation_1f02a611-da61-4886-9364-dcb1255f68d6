import {
  Component,
  ElementRef,
  OnInit,
  Renderer2,
  ViewChild,
} from '@angular/core';
import { OwlOptions } from 'ngx-owl-carousel-o';

@Component({
  selector: 'app-recent-joined-partners-testimonail',
  templateUrl: './recent-joined-partners-testimonail.component.html',
  styleUrls: ['./recent-joined-partners-testimonail.component.scss'],
})
export class RecentJoinedPartnersTestimonailComponent {
  @ViewChild('fcSlideCategories') fcSlideCategories!: ElementRef;
  @ViewChild('fcSlideCategories1') fcSlideCategories1!: ElementRef;
  @ViewChild('fcSlideCategories2') fcSlideCategories2!: ElementRef;
  @ViewChild('fcContainer') fcContainer: ElementRef | undefined;
  marginLeft: number = 0;

  constructor(private renderer: Renderer2) {}

  //  ngOnInit(): void {
  //   setTimeout(() => {
  //     const owlItems = document.querySelectorAll('.owl-item');
  //     owlItems.forEach(item => {
  //       item.setAttribute('style', 'width: 515px !important; margin-right: 40px !important;');
  //     });
  //   }, 100);
  //  }
  carouselOptions: OwlOptions = {
    loop: false,
    margin: 50,
    center: false,
    dots: false,
    autoplay: false,
    nav: true,
    navText: [
      '<img src="../../../../../assets/images/arrow-left.svg" alt="Previous" class="custom-nav-arrow" />',
      '<img src="../../../../../assets/images/black-arrow.svg" alt="Next" class="custom-nav-arrow" />',
    ],
    // autoplayTimeout: 10000,
    responsive: {
      0: { items: 1 },
      600: { items: 3 },
      1000: { items: 3 },
    },
    // autoWidth: true,
  };

  easyCards = [
    {
      image: '../../../../../assets/images/client4.jpg',
      title: 'Your connection matters!',
      description: `Utilizing the platform is incredibly straightforward! I am genuinely impressed by its user-friendly and the quality of the connections I’ve made. Connections are reaching out to build a circle of connections and leverage my network installation services in QC. Great support from Focile! I recommend it`,
      hero: 'Dragan Jerbic',
      location: 'Cablatel, Canada',
      stars: '5.0',
    },
    {
      image: '../../../../../assets/images/client5.jpg',
      title: 'Stay ahead of the curve',
      description: `As an enthusiastic early adopter, I’m impressed by the user-friendly design and continuous innovation of the Focile platform. The quality of my connections has been exceptional, with partners across multiple provinces leveraging my role as a distributor in Ontario. I highly recommend the Focile platform for anyone looking to expand their business network!`,
      hero: 'Ryan Perks',
      location: 'TDL Gentek, Canada',
      stars: '5.0',
    },
    {
      image: '../../../../../assets/images/client6.jpg',
      title: 'Maintaining your connections',
      description: `I’m impressed by the platform’s simplicity! As an early adopter, I’ve made valuable connections, with partners and vendors eager to collaborate with me as a reseller and integrator in Ontario, Canada. This is a fantastic opportunity for mutual growth!`,
      hero: 'Roman Seges',
      location: 'Inline Comm, Canada',
      stars: '4.5',
    },
    {
      image: '../../../../../assets/images/client1.jpg',
      title: 'Enhancing the direct communication',
      description: ` Focile is a powerful platform that transforms professional contacts into great friendly connections. I'm exploring it to connect with key decision-makers and engage with end-user businesses. It offers a valuable opportunity for lead generation and building meaningful relationships.`,
      hero: 'Stratton Jones',
      location: 'VARS Tech, USA',
      stars: '5.0',
    },
    {
      image: '../../../../../assets/images/client2.png',
      title: 'Build a favorite network',
      description: `The platform is user-friendly and has enabled me to form invaluable connections with experts. The partners, especially my main contacts, are authentic and dependable. As an early adopter, Focile has significantly strengthened my connection network and created solid local references for future opportunities. Thank you!`,
      hero: 'Pasquale Fontone',
      location: 'Quebec, Canada',
      stars: '4.5',
    },
    {
      image: '../../../../../assets/images/client3.jpg',
      title: 'Build a favorite network',
      description: `This is an exceptional idea! Focile is an invaluable tool for identifying key decision-makers. The platform allows you to find ideal partners, engage with your network, and get connected by prospects to address sales inquiries effectively. Its intuitive interface lets you amplify your outreach and make a significant impact. Embrace it! `,
      hero: 'Keith Jones',
      location: 'AVINCI Tech, USA',
      stars: '5.0',
    },
    {
      image: '../../../../../assets/images/client7.jpg',
      title: 'Build a favorite network',
      description: `Focile is revolutionizing the business landscape with its innovative platform. As early adopters, we've experienced a game-changing ability to quickly connect with the right contacts and get engaged with local businesses, significantly enhancing our presence across Canada. We are truly grateful! `,
      hero: 'Fred Jeudine',
      location: 'Auvitech Integration Inc., Canada',
      stars: '4.5',
    },
  ];

  ngAfterViewChecked(): void {
    this.calculateMarginLeft();
  }

  calculateMarginLeft(): void {
    if (this.fcContainer && this.fcSlideCategories) {
      const rect = this.fcContainer.nativeElement.getBoundingClientRect();
      this.marginLeft = rect.left;
      this.renderer.setStyle(
        this.fcSlideCategories1.nativeElement,
        'margin-left',
        `${this.marginLeft}px`
      );
      const calculatedWidth = `calc(100vw - ${this.marginLeft}px)`;
      this.renderer.setStyle(
        this.fcSlideCategories.nativeElement,
        'width',
        calculatedWidth
      );
      const owlStage = document.querySelector('.owl-stage');
      if (owlStage) {
        this.renderer.setStyle(
          owlStage,
          'padding-right',
          `${this.marginLeft}px`
        );
      }
    }
  }
}
