<section class="recently-joined-parners mb-3">
  <div
    *ngIf="title"
    [class]="'fw-bold ' + titleSizeMap[fontSize]"
    [ngClass]="{ 'text-center': textCenter }"
  >
    {{ title }}
  </div>
  <div
    class="d-fle container-fluid justify-content-center"
    *ngIf="recentlyJoinedPartners$"
  >
    <div class="slider mt-5">
      <div
        class="slide-track mt-4"
        [ngClass]="{ animate: items?.length > 5, paused: scrolling }"
      >
        <div
          class="slide p-2 mx-3 text-center d-flex flex-column justify-content-center align-items-center"
          role="button"
          (click)="navigateToCompany(item)"
          *ngFor="let item of recentlyJoinedPartners$ | async"
        >
          <img
            [src]="item.companyImage"
            class="rounded-4"
            height="140"
            width="140"
            style="min-height: 120px;"
            [alt]="item?.company?.name"
            appImg
            viewType="company"
            (mouseenter)="pauseAnimation()"
            (mouseleave)="resumeAnimation()"
          />
        </div>
      </div>
    </div>
  </div>
  <div *ngIf="loading" class="w-100 text-center">
    <app-spinner></app-spinner>
  </div>
</section>
