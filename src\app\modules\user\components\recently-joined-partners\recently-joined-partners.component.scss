@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(calc(-250px * 7));
  }
}

.slider {
  background: white;
  height: 150px;
  margin: auto;
  overflow: hidden;
  position: relative;
  margin-top: 1.25rem;
}

.slider::before,
.slider::after {
  background: linear-gradient(to right, white 0%, rgba(255, 255, 255, 0) 100%);
  content: "";
  height: 150px;
  position: absolute;
  width: 200px;
  z-index: 2;
}

.slider::after {
  right: 0;
  top: 0;
  transform: rotateZ(180deg);
}

.slider::before {
  left: 0;
  top: 0;
}

.slider .slide-track {
  display: flex;
  width: calc(250px * 14);
}

.animate {
  animation: scroll 40s linear infinite;
}

.slider .slide {
  height: 100px;
  width: 250px;
}

.recently-joined-parners {
  margin-bottom: 4.25rem;
  gap: 1.25rem;
  min-height: 18.75rem;
}

.paused {
  animation-play-state: paused;
}
