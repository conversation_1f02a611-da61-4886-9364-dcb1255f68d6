import { Component, Input, OnInit, SimpleChanges } from '@angular/core';
import { Router } from '@angular/router';
import { finalize, map, tap } from 'rxjs';
import { AccountService } from 'src/app/modules/account/services/account.service';

@Component({
  selector: 'app-recently-joined-partners',
  templateUrl: './recently-joined-partners.component.html',
  styleUrls: ['./recently-joined-partners.component.scss'],
})
export class RecentlyJoinedPartnersComponent implements OnInit {
  @Input() title = 'Recently Joined Partners';
  @Input() fontSize = '48px';
  @Input() textCenter = true;
  @Input() items: any = [];
  @Input() user: any;
  isLoggedIn: any;
  loading = false;
  recentlyJoinedPartners$: any;
  scrolling = false;
  _titleClass = '';
  titleSizeMap: any = {
    '48px': 'fs-48',
    '18px': 'fs-18',
  };
  constructor(private router: Router, private account: AccountService) {}

  ngOnInit(): void {
    this.getTechnologies(this?.user?.userId);
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes.user.currentValue != changes.user.previousValue) {
      this.ngOnInit();
    }
  }

  getTechnologies(id: any) {
    this.loading = true;
    this.recentlyJoinedPartners$ = this.account
      .getRecentlyJoinedPartners(id)
      .pipe(
        finalize(() => (this.loading = false)),
        map((response: any) => {
          return response?.data || [];
        }),
        tap((response) => {
          this.items = response;
        })
      );
  }

  navigateToCompany(item: any) {
    this.user
      ? this.router.navigate([`/details/${item.company.id}/company`])
      : this.router.navigate(['/account']);
  }

  pauseAnimation() {
    this.scrolling = true;
  }

  resumeAnimation() {
    this.scrolling = false;
  }
}
