<div class="fc-container">
  <div class="breadcumb my-4">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item"><a routerLink="/">Home</a></li>
        <li class="breadcrumb-item active" aria-current="page">Pricing Plan</li>
      </ol>
    </nav>
  </div>
</div>
<div class="container-fluid d-none">
  <div class="row">
    <!-- <div class="col-md-12 text-center mb-5 fs-2 fw-bolder">
        Connect with your Channel Members
        <br />
        <h3>Access tailored for each member to leverage various features</h3>
      </div> -->
    <!-- <div class="col-md-12 text-center ">
        <tabset type="pills">
          <tab heading="Connect With Channel Members"></tab>
          <tab heading="Videos"> </tab>
        </tabset>
      </div> -->
    <div class="mt-3"></div>
    <div class="plans-container">
      <div *ngFor="let item of plans$ | async">
        <div class="PricingPlans_plan__IF4q9 borders">
          <h2 class="text-center">{{ item.name }}</h2>
          <div class="text-center">
            <div>$0</div>
            <div>
              Your essential gateway to convenience with simplified services
              and essential features.
            </div>
          </div>
          <div class="PricingPlans_planNotesContainer__23Bvs MuiBox-root mui-style-0">
            <div class="MuiTypography-root MuiTypography-bodySmall mui-style-q4ny8c">
              <div class="MuiStack-root mui-style-1cr5cwi"></div>
            </div>
          </div>
          <div class="PricingPlans_planCreditsCountsContainer__BQNrQ MuiBox-root mui-style-0">
            <div class="MuiTypography-root MuiTypography-body1 mui-style-c5w0ux">
              <div class="MuiStack-root mui-style-18zsr3k">
                <div>
                  <div class="PricingPlans_planCreditsCount__u1ZmL">
                    <div class="">Subscription include</div>
                  </div>
                  <p class="MuiTypography-root MuiTypography-bodySmall mui-style-16ty86f">
                    1 Brand recognition e-Card
                  </p>
                  <p>5 Experts connections</p>
                  <p>5 Mobile calls</p>
                </div>
                <div>
                  <div class="PricingPlans_planCreditsCount__u1ZmL">
                    <div class="">5 emails blast / year</div>
                  </div>
                </div>
                <div>
                  <div class="PricingPlans_planCreditsCount__u1ZmL">
                    <div class="">5 social media extensions / year</div>
                  </div>
                </div>
                <div>
                  <div class="PricingPlans_planCreditsCount__u1ZmL">
                    <div class="">20 invites / year</div>
                  </div>
                </div>
                <div>
                  <div class="PricingPlans_planCreditsCount__u1ZmL">
                    <div class="">1 Company video profile Link (*)</div>
                  </div>
                </div>
                <div>
                  <div class="PricingPlans_planCreditsCount__u1ZmL">
                    <div class="">1 Company elevator pitch Video (**)</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="PricingPlans_planFeaturesContainer__wM4O5 MuiBox-root mui-style-1yuhvjn">
            <div>
              <div class="MuiTypography-root MuiTypography-caption mui-style-105nme">
                &nbsp;
              </div>
              <ul class="PricingPlans_planFeaturesList__o54r6">
                <div class="MuiStack-root mui-style-18zsr3k">
                  <li>
                    <div class="MuiTypography-root MuiTypography-bodySmall mui-style-1k9lcz0">
                      <div class="PricingPlans_planFeature__HJTRm">
                        <div>
                          <i class="fas fa-check"></i>
                        </div>
                        <div>Advertise your company brand</div>
                      </div>
                    </div>
                  </li>
                  <li>
                    <div class="MuiTypography-root MuiTypography-bodySmall mui-style-1k9lcz0">
                      <div class="PricingPlans_planFeature__HJTRm">
                        <div>
                          <i class="fas fa-check"></i>
                        </div>
                        <div>Free account access</div>
                      </div>
                    </div>
                  </li>
                  <li>
                    <div class="MuiTypography-root MuiTypography-bodySmall mui-style-1k9lcz0">
                      <div class="PricingPlans_planFeature__HJTRm">
                        <div>
                          <i class="fas fa-check"></i>
                        </div>
                        <div class="text-content">Custom profile page</div>
                      </div>
                    </div>
                  </li>
                  <li>
                    <div class="MuiTypography-root MuiTypography-bodySmall mui-style-1k9lcz0">
                      <div class="PricingPlans_planFeature__HJTRm">
                        <div>
                          <i class="fas fa-check"></i>
                        </div>
                        <div class="text-content">Ad-free browsing</div>
                      </div>
                    </div>
                  </li>
                  <li>
                    <div class="MuiTypography-root MuiTypography-bodySmall mui-style-1k9lcz0">
                      <div class="PricingPlans_planFeature__HJTRm">
                        <div>
                          <i class="fas fa-check"></i>
                        </div>
                        <div class="text-content">Customer support</div>
                      </div>
                    </div>
                  </li>
                  <li>
                    <div class="MuiTypography-root MuiTypography-bodySmall mui-style-1k9lcz0">
                      <div class="PricingPlans_planFeature__HJTRm">
                        <div>
                          <i class="fas fa-check"></i>
                        </div>
                        <div class="text-content">
                          Access to connection features
                        </div>
                      </div>
                    </div>
                  </li>
                  <li>
                    <div class="MuiTypography-root MuiTypography-bodySmall mui-style-1k9lcz0">
                      <div class="PricingPlans_planFeature__HJTRm">
                        <div>
                          <i class="fas fa-check"></i>
                        </div>
                        <div class="text-content">
                          Access partners Website
                        </div>
                      </div>
                    </div>
                  </li>
                  <li>
                    <div class="MuiTypography-root MuiTypography-bodySmall mui-style-1k9lcz0">
                      <div class="PricingPlans_planFeature__HJTRm">
                        <div>
                          <i class="fas fa-check"></i>
                        </div>
                        <div class="text-content">Access Mobile partners</div>
                      </div>
                    </div>
                  </li>
                  <li>
                    <div class="MuiTypography-root MuiTypography-bodySmall mui-style-1k9lcz0">
                      <div class="PricingPlans_planFeature__HJTRm">
                        <div>
                          <i class="fas fa-check"></i>
                        </div>
                        <div class="text-content">
                          Chat direct with connections
                        </div>
                      </div>
                    </div>
                  </li>
                  <li>
                    <div class="MuiTypography-root MuiTypography-bodySmall mui-style-1k9lcz0">
                      <div class="PricingPlans_planFeature__HJTRm">
                        <div>
                          <i class="fas fa-check"></i>
                        </div>
                        <div class="text-content">
                          Unlimited partners research
                        </div>
                      </div>
                    </div>
                  </li>
                  <li>
                    <div class="MuiTypography-root MuiTypography-bodySmall mui-style-1k9lcz0">
                      <div class="PricingPlans_planFeature__HJTRm">
                        <div>
                          <i class="fas fa-check"></i>
                        </div>
                        <div class="text-content">Unlimited Video views</div>
                      </div>
                    </div>
                  </li>
                </div>
              </ul>
            </div>
            <button class="rounded-5 btn btn-primary w-100 mt-5">
              Purchase
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="container-fluid">
  <div class="fc-container">
    <div class="col-md-12 text-center fc-subscription-text">
      <h4 class="fs-2 fw-medium">Connect with your Channel Members</h4>
      <p>Access tailored for each member to leverage various features</p>
    </div>
    <!-- <div class="fc-price-categories">
      <div class="active"><span><svg width="24" height="24" viewBox="0 0 24 24" fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path
              d="M20.8776 14.25L13.9695 21.4C13.7762 21.6 13.5588 21.75 13.3173 21.85C13.0757 21.95 12.8342 22 12.5927 22C12.3511 22 12.1096 21.95 11.868 21.85C11.6265 21.75 11.4091 21.6 11.2159 21.4L2.68934 12.575C2.51221 12.3917 2.37533 12.1792 2.27872 11.9375C2.1821 11.6958 2.13379 11.4417 2.13379 11.175V4C2.13379 3.45 2.323 2.97917 2.70142 2.5875C3.07984 2.19583 3.53475 2 4.06614 2H10.9985C11.2561 2 11.5057 2.05417 11.7472 2.1625C11.9888 2.27083 12.1981 2.41667 12.3753 2.6L20.8776 11.425C21.0709 11.625 21.2118 11.85 21.3003 12.1C21.3889 12.35 21.4332 12.6 21.4332 12.85C21.4332 13.1 21.3889 13.3458 21.3003 13.5875C21.2118 13.8292 21.0709 14.05 20.8776 14.25ZM12.5927 20L19.5008 12.85L10.9743 4H4.06614V11.15L12.5927 20ZM6.48159 8C6.88416 8 7.22635 7.85417 7.50815 7.5625C7.78995 7.27083 7.93085 6.91667 7.93085 6.5C7.93085 6.08333 7.78995 5.72917 7.50815 5.4375C7.22635 5.14583 6.88416 5 6.48159 5C6.07901 5 5.73682 5.14583 5.45502 5.4375C5.17322 5.72917 5.03232 6.08333 5.03232 6.5C5.03232 6.91667 5.17322 7.27083 5.45502 7.5625C5.73682 7.85417 6.07901 8 6.48159 8Z"
              fill="#014681" />
          </svg>
        </span> Channel Member</div>
      <div><span>
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M4 20C3.45 20 2.97917 19.8042 2.5875 19.4125C2.19583 19.0208 2 18.55 2 18V6C2 5.45 2.19583 4.97917 2.5875 4.5875C2.97917 4.19583 3.45 4 4 4H16C16.55 4 17.0208 4.19583 17.4125 4.5875C17.8042 4.97917 18 5.45 18 6V10.5L22 6.5V17.5L18 13.5V18C18 18.55 17.8042 19.0208 17.4125 19.4125C17.0208 19.8042 16.55 20 16 20H4ZM4 18H16V6H4V18Z"
              fill="#A1BBD2" />
          </svg>
        </span> Video</div>
    </div> -->

    <!-- Loading state -->
    <div *ngIf="loadingPlans" class="text-center p-4">
      <div class="spinner-border" role="status">
        <span class="sr-only">Loading subscription plans...</span>
      </div>
    </div>

    <!-- Subscription Plans from API -->
    <div class="fc-plan-container" *ngIf="!loadingPlans && subscriptionPlans.length > 0">
      <div class="fc-plan-card" *ngFor="let plan of subscriptionPlans; let i = index">
        <div class="fc-plan-name-row">{{ plan.name }}</div>
        <div class="fc-plan-intruction">{{ plan.description }}</div>
        <div class="tc-plan-price">
          <span *ngIf="plan.price === 0">Free</span>
          <span *ngIf="plan.price > 0">${{ plan.price }}</span>
          <div class="tc-plan-price-text">
            <span><b>{{ plan.durationInDays }} days</b></span>
            <span class="early-phase-text" *ngIf="plan.discount > 0">{{ plan.discount }}% off</span>
            <span *ngIf="plan.userSubscriptionType">{{ plan.userSubscriptionType }}</span>
          </div>
        </div>
        <hr>
        <h6>Subscription includes</h6>
        <ul class="purchase-list">
          <li>{{ plan.invitationsLimit }} Invitation requests</li>
          <li>{{ plan.addExpertLimit }} Additional Expert members</li>
          <li>{{ plan.socialMediaLimit }} Social media extensions</li>
          <li>{{ plan.youTubeLinkLimit }} YouTube video hyperlinks</li>
          <li>{{ plan.connectionsRefsLimit }} Lead connections & referrals</li>
          <li *ngIf="plan.elevatorPitchesLimit > 0">{{ plan.elevatorPitchesLimit }} Elevator pitch videos</li>
          <li *ngIf="plan.connetionLimit > 0">{{ plan.connetionLimit }} Total connections</li>
        </ul>
        <!-- <h6>Features</h6>
        <ul class="purchase-list">
          <li>Brand recognition e-Card</li>
          <li>Contact access (phone & email)</li>
          <li>Expert service requests</li>
          <li *ngIf="plan.price > 0">Priority support</li>
          <li *ngIf="plan.price > 0">Advanced analytics</li>
        </ul> -->
        <button
          (click)="purchesePlan(plan)"
          class="get-started-btn"
          [disabled]="isPlanLoading(plan)">
          <span *ngIf="!isPlanLoading(plan)">
            {{ plan.price === 0 ? 'Get Started Free' : 'Subscribe Now' }}
          </span>
          <span *ngIf="isPlanLoading(plan)">
            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
            Processing...
          </span>
        </button>
      </div>
    </div>

    <!-- Fallback to original hardcoded plans if API fails -->
    <div class="fc-plan-container" *ngIf="!loadingPlans && subscriptionPlans.length === 0">
      <div class="fc-plan-card" *ngFor="let plan of plan;let i = index">
        <div class="fc-plan-name-row">{{ plan.planName }}</div>
        <div class="fc-plan-intruction">{{ plan.instruction }}</div>
        <div class="tc-plan-price">
          {{ plan.price }}
          <div class="tc-plan-price-text">
            <span><b>{{ plan.billingCycle }}</b></span>
            <span class="early-phase-text">{{ plan.earlyPhase }}</span>
            {{ plan.priceText }}
            </div>
          <!-- <h4 *ngIf="i > 0">Coming Soon</h4> -->
          <!-- <h4 *ngIf="i <= 0">Free</h4> -->
        </div>
        <hr>
        <h6>Subscription include</h6>
        <ul>
          <li *ngFor="let item of plan.subscriptionIncludes">{{ item }}</li>
        </ul>
        <h6>Purchase</h6>
        <ul class="purchase-list">
          <li *ngFor="let benefit of plan.purchaseBenefits">{{ benefit }}</li>
        </ul>
        <button (click)="purchesePlan(plan)" class="get-started-btn">{{plan.buttonText}}</button>
      </div>
    </div>

    <div class="faq-related-to pb-0 pb-sm-4 mb-5">
      <h3>FAQs</h3>
      <div class="accordion" id="accordionExample">
        <div *ngFor="let item of accordionItems; let i = index" class="accordion-item">
          <h2 class="accordion-header" id="heading{{ i }}">
            <button 
              class="accordion-button {{ i === 0 ? '' : 'collapsed' }}" 
              type="button" 
              data-bs-toggle="collapse" 
              [attr.data-bs-target]="'#collapse' + i" 
              [attr.aria-expanded]="i === 0 ? 'true' : 'false'" 
              [attr.aria-controls]="'collapse' + i">
              {{ item.question }}
            </button>
          </h2>
          <div 
            id="collapse{{ i }}" 
            class="accordion-collapse collapse {{ i === 0 ? 'show' : '' }}" 
            [attr.aria-labelledby]="'heading' + i" 
            data-bs-parent="#accordionExample">
            <div class="accordion-body">
              {{ item.answer }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>