.plans-container {
  display: grid;
  -moz-column-gap: 2rem;
  column-gap: 2rem;
  row-gap: 3rem;
  grid-template-columns: repeat(4, 1fr);
}

.PricingPlans_plan__IF4q9 {
  --border-color: var(--color-gray-15);
  border: 1px solid lightgray;
  border-top: 15px solid #014681;
  // --border-color-top: var(--color-blue-50);
  // --border-width-base: 1px;
  // --border-width-top-base: 10px;
  position: relative;
  position: relative;
  padding: 3em 1.5em 1em;
  // border: var(--border-width-base) solid var($primary);
  // border-top: var(--border-width-top-base) solid var(--border-color-top);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
}

svg {
  height: 12px;
  width: 12px;
}

// Loading states
.spinner-border {
  width: 1rem;
  height: 1rem;
  border-width: 0.125em;
}

.spinner-border-sm {
  width: 0.875rem;
  height: 0.875rem;
  border-width: 0.1em;
}

// Button loading state
.get-started-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

// Plan card improvements
.fc-plan-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

// // Price styling improvements
// .tc-plan-price {
//   font-size: 3rem;
//   font-weight: 700;
//   color: #014681;
//   margin: 1.5rem 0;
//   text-align: center;
//   line-height: 1.2;

//   span {
//     display: block;
//     font-size: 3rem;
//     font-weight: 700;
//     color: #014681;
//     text-shadow: 0 2px 4px rgba(1, 70, 129, 0.1);
//   }

//   // Free plan styling
//   span:contains("Free") {
//     color: #28a745;
//     font-size: 2.5rem;
//   }
// }

// New Price styling with separate elements
.tc-plan-price {
  margin: 1.5rem 0;
  text-align: center;

  .price-main {
    font-size: 4rem;
    font-weight: 800;
    color: #014681;
    line-height: 1;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(1, 70, 129, 0.1);

    span {
      display: block;
      font-size: inherit;
      font-weight: inherit;
      color: inherit;
    }
  }

  .billing-cycle {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0rem;

    span {
      display: block;
      font-size: inherit;
      font-weight: inherit;
      color: inherit;
    }
  }
}

// .tc-plan-price-text {
//   font-size: 0.875rem;
//   color: #666;
//   font-weight: normal;
//   margin-top: 0.5rem;
//   text-align: center;

//   span {
//     display: inline-block;
//     margin: 0 0.25rem;
//     font-size: 0.875rem;
//     font-weight: normal;
//     color: #666;
//     text-shadow: none;
//   }

//   b {
//     font-weight: 600;
//     color: #333;
//   }
// }

// .early-phase-text {
//   color: #28a745;
//   font-weight: 600;
//   background: rgba(40, 167, 69, 0.1);
//   padding: 0.25rem 0.5rem;
//   border-radius: 4px;
//   font-size: 0.75rem;
//   text-transform: uppercase;
//   letter-spacing: 0.5px;
// }

// Additional price highlighting
// .fc-plan-card .tc-plan-price {
//   background: linear-gradient(
//     135deg,
//     rgba(1, 70, 129, 0.05) 0%,
//     rgba(1, 70, 129, 0.1) 100%
//   );
//   border: 2px solid rgba(1, 70, 129, 0.1);
//   border-radius: 12px;
//   padding: 1.5rem 1rem;
//   margin: 1.5rem 0;
//   position: relative;

//   &::before {
//     content: "";
//     position: absolute;
//     top: -2px;
//     left: -2px;
//     right: -2px;
//     bottom: -2px;
//     background: linear-gradient(135deg, #014681, #0066cc);
//     border-radius: 14px;
//     z-index: -1;
//     opacity: 0;
//     transition: opacity 0.3s ease;
//   }

//   &:hover::before {
//     opacity: 0.1;
//   }
// }

// Free plan special styling
// .fc-plan-card:first-child .tc-plan-price {
//   background: linear-gradient(
//     135deg,
//     rgba(40, 167, 69, 0.05) 0%,
//     rgba(40, 167, 69, 0.1) 100%
//   );
//   border-color: rgba(40, 167, 69, 0.2);

//   span {
//     color: #28a745 !important;
//   }

//   &::before {
//     background: linear-gradient(135deg, #28a745, #20c997);
//   }
// }

.PricingPlans_planCreditsCountsContainer__BQNrQ,
.PricingPlans_planNotesContainer__23Bvs {
  padding-bottom: 1rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid lightgray;
  text-align: center;
}

.MuiStack-root {
  display: flex;
  flex-direction: column;
}

.PricingPlans_planFeaturesList__o54r6 {
  list-style-type: none;
  padding: 0;
  margin: 0.1em 0 0;
}

.PricingPlans_planFeature__HJTRm {
  display: flex;
  gap: 0.5em;
}

.PricingPlans_planFeature__HJTRm > div:nth-child(2) {
  text-decoration: underline;
  text-decoration-style: dashed;
  text-underline-offset: 4px;
}

.tab-container {
  .nav-pills {
    justify-content: center !important;
  }
}

/* New Design Stylesheet */

.breadcumb {
  li {
    a {
      text-decoration: none;
    }

    &.breadcrumb-item + .breadcrumb-item::before {
      content: "-";
    }
  }
}
.fc-subscription-text {
  margin-block: 5rem;
  h3 {
  }

  p {
    font-size: 24px;
    color: rgba(0, 0, 0, 0.5);
    font-weight: 300;
  }
}

.fc-price-categories {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #d9dbe9;
  svg {
    width: 20px;
    height: 20px;

    path {
      fill: #a0a3bd;
    }
  }

  div {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    min-width: 300px;
    gap: 1rem;
    padding-bottom: 1rem;
    color: #a0a3bd;
    border-bottom: 1px solid transparent;
    font-weight: 500;
    cursor: pointer;
    span {
      min-width: 46.37px;
      min-height: 48px;
      left: 0px;
      top: 0px;
      background: rgba(1, 69, 129, 0.1);
      border-radius: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &:hover {
      border-bottom: 1px solid #014681;
      color: #014681;

      svg {
        path {
          fill: #014681;
        }
      }
    }

    &.active {
      border-bottom: 1px solid #014681;
      color: #014681;

      svg {
        path {
          fill: #014681;
        }
      }
    }
  }

  &::before {
    background: url(../../../../../assets/images/dot-pattern.svg) no-repeat left
      top;
    width: 183px;
    height: 176px;
    content: "";
    left: -35px;
    position: absolute;
    opacity: 0.1;
    top: 400px;
  }
}

.fc-plan-container {
  margin-top: 60px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.fc-plan-card {
  box-sizing: border-box;
  width: calc(33.33% - 10px);
  min-height: 770px;
  background: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 18px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  cursor: pointer;
  z-index: 11;
  position: relative;

  .fc-plan-name-row {
    width: max-content;
    height: 42px;
    background: #101825;
    border-radius: 15px;
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin: 0px auto;
  }

  .fc-plan-intruction {
    width: 100%;
    min-height: 90px;
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 25px;
    color: #807e8d;
    margin-top: 1rem;
    text-align: center;
  }

  .tc-plan-price-text {
    color: rgba(0, 0, 0, 0.5);
    font-weight: 400;
    min-height: 27px;
    display: flex;
    flex-direction: column;
    text-align: center;
    gap: 5px;
    font-size: 1rem;

    .early-phase-text {
      color: #ffa500;
    }
  }

  .tc-plan-price {
    width: auto;
    font-style: normal;
    font-weight: 700;
    font-size: 45px;
    line-height: 1.5;
    color: #12151f;
    margin-block: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0rem;
    min-height: auto;

    // span {
    //   font-size: 1rem;
    //   font-weight: normal;
    //   color: rgba(0, 0, 0, 0.6);
    // }
  }

  h6 {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    &:before {
      content: "";
      position: relative;
      display: flex;
      width: 15px;
      height: 1px;
      background-color: rgba(0, 0, 0, 0.3);
      margin-right: 1rem;
    }
  }

  ul {
    list-style: none;
    padding-left: 0px;
    gap: 5px;
    display: flex;
    flex-direction: column;
    li {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.8);
    }

    &.purchase-list {
      li {
        display: flex;
        align-items: center;
        &:before {
          content: "";
          position: relative;
          display: flex;
          width: 16px;
          height: 12px;
          background: url(../../../../../assets/images/right-icon.svg) no-repeat
            left center;
          margin-right: 1rem;
        }
      }
    }
  }

  hr {
    color: rgba(0, 0, 0, 0.2);
    margin-top: 0px;
  }

  .get-started-btn {
    box-sizing: border-box;
    width: 100%;
    height: 43px;
    background: #ffffff;
    border: 1px solid #014681;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    color: #014681;
    margin-top: auto;
  }

  &:hover {
    filter: drop-shadow(0px 4px 14px rgba(0, 0, 0, 0.1));

    .get-started-btn {
      background-color: #014681;
      color: white;
    }
  }
}

.faq-related-to {
  h3 {
    text-align: center;
    font-size: 48px;
    margin-top: 100px;
    margin-bottom: 60px;
  }
}

@media (max-width: 768px) {
  .fc-subscription-text {
    margin-block: 2rem;
    h4 {
      font-size: 1.25rem !important;
    }
    p {
      font-size: 14px;
    }
  }
  .fc-plan-container {
    flex-direction: column;
    gap: 1rem;
    margin-top: 2rem;
  }
  .fc-plan-card {
    width: 100%;
    height: auto;

    .tc-plan-price {
      margin-block: 1rem;
      min-height: auto;
      h4 {
        font-size: 1.25rem !important;
      }
    }
    .fc-plan-intruction {
      min-height: auto;
    }
  }
  .fc-price-categories {
    gap: 1rem;
    div {
      min-width: auto;

      span {
        min-width: 24px;
        min-height: 24px;

        svg {
          width: 14px;
        }
      }
    }
  }
  .fc-price-categories::before {
    z-index: -1;
  }

  .container-fluid {
    padding: 0px !important;
  }
  .faq-related-to {
    h3 {
      font-size: 1.5rem;
      margin-top: 3rem;
      margin-bottom: 1rem;
    }
  }
}

// Simple API integration styles - minimal impact on existing design

.fc-section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #014681;
  margin-bottom: 1rem;
}

.fc-section-subtitle {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 3rem;
}

.fc-loading-state {
  .fc-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #014681;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
  }

  p {
    color: #666;
    font-size: 18px;
  }
}

.fc-error-state {
  h3 {
    color: #dc3545;
    margin-bottom: 1rem;
  }

  p {
    color: #666;
    margin-bottom: 2rem;
  }
}

.fc-plan-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  height: 100%;
  position: relative;
  transition: all 0.3s ease;
  border: 2px solid transparent;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  }

  &.fc-popular {
    border-color: #014681;
    transform: scale(1.05);

    &:hover {
      transform: scale(1.05) translateY(-5px);
    }
  }
}

.fc-popular-badge {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #014681, #0056b3);
  color: white;
  padding: 0.5rem 1.5rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(1, 70, 129, 0.3);
}

.fc-plan-header {
  text-align: center;
  margin-bottom: 2rem;
}

.fc-plan-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 50%;
  margin-bottom: 1rem;
}

.fc-plan-name {
  font-size: 1.8rem;
  font-weight: 700;
  color: #014681;
  margin-bottom: 0.5rem;
}

.fc-plan-description {
  color: #666;
  font-size: 1rem;
  line-height: 1.5;
}

.fc-plan-pricing {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #eee;
}

.fc-price-container {
  margin-bottom: 0.5rem;
}

.fc-price {
  font-size: 3rem;
  font-weight: 700;
  color: #014681;
}

.fc-price-period {
  font-size: 1rem;
  color: #666;
  margin-left: 0.5rem;
}

.fc-discount-info {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
}

.fc-original-price {
  text-decoration: line-through;
  color: #999;
  font-size: 1.1rem;
}

.fc-discount-amount {
  background: #28a745;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
}

.fc-plan-features {
  margin-bottom: 2rem;

  h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
  }
}

.fc-features-list {
  list-style: none;
  padding: 0;
  margin: 0;

  li {
    display: flex;
    align-items: center;
    padding: 0.5rem 0;
    color: #555;

    i {
      color: #28a745;
      margin-right: 0.75rem;
      font-size: 0.875rem;
    }
  }
}

.fc-plan-action {
  text-align: center;
}

.fc-plan-btn {
  width: 100%;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 25px;
  transition: all 0.3s ease;

  &.btn-primary {
    background: linear-gradient(135deg, #014681, #0056b3);
    border: none;

    &:hover {
      background: linear-gradient(135deg, #0056b3, #004494);
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(1, 70, 129, 0.3);
    }
  }

  &.btn-outline-primary {
    border: 2px solid #014681;
    color: #014681;

    &:hover {
      background: #014681;
      color: white;
      transform: translateY(-2px);
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .fc-section-title {
    font-size: 2rem;
  }

  .fc-plan-card {
    margin-bottom: 2rem;

    &.fc-popular {
      transform: none;

      &:hover {
        transform: translateY(-5px);
      }
    }
  }

  .fc-price {
    font-size: 2.5rem;
  }
}
