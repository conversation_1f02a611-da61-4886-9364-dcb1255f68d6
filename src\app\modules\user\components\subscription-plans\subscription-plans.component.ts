import { Component, OnInit } from '@angular/core';
import { finalize, map } from 'rxjs';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { SubscriptionPlanService } from 'src/app/shared/services/subscription-plan.service';
import { environment } from 'src/environments/environment';
import { Stripe, loadStripe } from '@stripe/stripe-js';
import { button } from 'aws-amplify';
import { filterNonNull } from 'src/app/shared/oprators/filter-null-values';

// Interface for API response
interface SubscriptionPlan {
  subscriptionId: string;
  name: string;
  description: string;
  imageUrl: string;
  durationInDays: number;
  price: number;
  discount: number;
  userSubscriptionType: string;
  invitationsLimit: number;
  addExpertLimit: number;
  socialMediaLimit: number;
  youTubeLinkLimit: number;
  connectionsRefsLimit: number;
  elevatorPitchesLimit: number;
  connetionLimit: number;
}

@Component({
  selector: 'app-subscription-plans',
  templateUrl: './subscription-plans.component.html',
  styleUrls: ['./subscription-plans.component.scss'],
})
export class SubscriptionPlansComponent implements OnInit {
  plans$: any;
  user: any = {};
  loading = false;
  subscriptionPlans: SubscriptionPlan[] = [];
  loadingPlans = false;
  plans = [
    {
      id: '1aafecfc-cfb4-45d7-3099-08dc4dbdc737',
      name: 'Golden',
      connectionLimit: 50,
      description:
        'The Golden Subscription Plan offers users exclusive access to premium features and content, designed to enhance their experience on the platform. Subscribers enjoy benefits such as ad-free browsing, priority customer support, and early access to new features or updates. With the Golden Subscription Plan, users can elevate their engagement and unlock additional perks tailored to meet their needs and preferences.',
      imageUrl: '',
      duration: 30,
      price: 999,
      discount: 199,
      features: [
        'Limited E-Card Search',
        'Limited Connections',
        'Standard Profile Page',
        'Profile Experts per company',
        'Promotions and Videos per Channel Profile Page',
        'Analytics and Reporting',
        'Customized Profile Page with more videos and tabs for events and training',
      ],
    },
    {
      id: 'd0075a6b-bcbe-46d0-309a-08dc4dbdc737',
      name: 'Platinum',
      connectionLimit: 100,
      description:
        'The Platinum Plan offers an elite level of service tailored for discerning individuals seeking premium features and unparalleled benefits. Designed to exceed expectations, this plan encompasses a comprehensive array of exclusive offerings aimed at enhancing convenience, luxury, and peace of mind.',
      imageUrl: '',
      duration: 30,
      price: 499,
      discount: 0,
      features: [
        'Unlimited E-Card Search',
        'Unlimited Connections',
        'Enhanced Profile Page',
        'Priority Customer Support',
        'Early Access to Features',
        'Exclusive Promotions',
        'Advanced Analytics and Reporting',
      ],
    },
    {
      id: '0eba96ce-5a42-4f52-efbd-08dc5060afff',
      name: 'Silver',
      connectionLimit: 50,
      description:
        'Experience elevated service with our Silver Plan. Enjoy premium features tailored for your needs, including personalized assistance, exclusive discounts, and peace of mind, all within a budget-friendly package.',
      imageUrl: '',
      duration: 1,
      price: 299,
      discount: 0,
      features: [
        'Basic E-Card Search',
        'Basic Connections',
        'Profile Page',
        'Customer Support',
        'Discounts',
        'Analytics',
      ],
    },
    {
      id: 'd88a5701-ae39-4bb7-efbe-08dc5060afff',
      name: 'Basic',
      connectionLimit: 99,
      description:
        'Introducing our Basic Plan: your essential gateway to convenience. With streamlined services and essential features, this plan offers practical solutions for everyday needs, ensuring affordability without compromising quality. Simplify your life with our Basic Plan today.',
      imageUrl: '',
      duration: 1,
      price: 149,
      discount: 29,
      features: [
        'E-Card Search',
        'Connections',
        'Basic Profile Page',
        'Basic Customer Support',
      ],
    },
  ];
  private stripePromise?: Promise<Stripe | null>;
  constructor(
    private subscriptionPlan: SubscriptionPlanService,
    private accountService: AccountService
  ) { }

  ngOnInit(): void {
    this.accountService.user$.pipe(filterNonNull()).subscribe((response) => {
      this.user = response;
    });
    this.plans$ = this.subscriptionPlan
      .getPlans()
      .pipe(map((response: any) => response?.data || []));

    this.loadSubscriptionPlans();
    this.loadFrequentlyAskedQuestions();
  }

  loadSubscriptionPlans(): void {
    this.loadingPlans = true;
    this.accountService.getSubscriptionPlans()
      .pipe(finalize(() => this.loadingPlans = false))
      .subscribe({
        next: (response: SubscriptionPlan[]) => {
          this.subscriptionPlans = response || [];
        },
        error: (error) => {
          console.error('Error loading subscription plans:', error);
          this.subscriptionPlans = [];
        }
      });
  }

  purchesePlan(plan: SubscriptionPlan | any) {
    // Handle both API plans and hardcoded plans
    const planId = plan.subscriptionId || plan.buttonId;

    if (!planId || !this.user?.userId) {
      console.error('Missing subscription ID or user ID');
      return;
    }

    // Add loading state to the specific plan
    if (plan.subscriptionId) {
      // API plan
      const planIndex = this.subscriptionPlans.findIndex(p => p.subscriptionId === plan.subscriptionId);
      if (planIndex !== -1) {
        this.subscriptionPlans[planIndex] = { ...this.subscriptionPlans[planIndex], loading: true } as any;
      }
    } else {
      // Hardcoded plan
      plan.loading = true;
    }

    this.subscriptionPlan
      .createPayment(planId, this.user.userId)
      .pipe(finalize(() => {
        // Remove loading state
        if (plan.subscriptionId) {
          const planIndex = this.subscriptionPlans.findIndex(p => p.subscriptionId === plan.subscriptionId);
          if (planIndex !== -1) {
            const { loading, ...planWithoutLoading } = this.subscriptionPlans[planIndex] as any;
            this.subscriptionPlans[planIndex] = planWithoutLoading;
          }
        } else {
          plan.loading = false;
        }
      }))
      .subscribe(async (response: any) => {
        if (response.item2) {
          this.stripePromise = loadStripe(environment.stripe);
          const stripe = await this.stripePromise;
          stripe?.redirectToCheckout({ sessionId: response.item2 });
        }
      });
  }

  // Helper method to check if a plan is loading
  isPlanLoading(plan: SubscriptionPlan): boolean {
    return (plan as any)?.loading || false;
  }

  plan = [
    {
      planName: 'Basic',
      instruction:
        'Your essential gateway to convenience with simplified services and essential features.',
      price: 'Free',
      billingCycle: '',
      earlyPhase: '',
      priceText: '',
      subscriptionIncludes: [
        '01  Brand recognition e-Card',
        '05  Channel/Experts connections',
        '02  Additional Expert members',
        '05  Lead connections & referrals',
        '05  Contact phone numbers access',
        '05  Email contact access',
        '05  Request a service from Expert',
        '20  Invitation requests',
        '05  Social media extensions',
        '01  Company video hyperlink (YouTube) *',
        '00  Company elevator pitch Video **',
      ],
      purchaseBenefits: [
        'Advertise your company brand',
        'Free account access',
        'Custom profile page',
        'Direct connection by prospects',
        'Ad-free browsing',
        'Request a direct service',
        'Customer support',
        'Access to connection features',
        'Access partners Website',
        'Access mobile numbers',
        'Chat direct with connections',
        'Unlimited partners research',
        'Unlimited Video views',
      ],
      buttonText: 'Active Plan',
    },
    {
      planName: 'Standard',
      instruction:
        'Exclusive access to other featured partner contents, get connected by businesses and prospects.',
      price: '$18',
      billingCycle: 'Monthly/Channel Expert.',
      earlyPhase: 'Early adopter’s phase',
      priceText: 'Pricing subject to slight change',
      subscriptionIncludes: [
        '01 Brand recognition e-Card',
        '30 Channel/Exp connections/Month',
        '05 Additional Expert members',
        '30 Lead connections & referrals (i)',
        '30 Contact phone numbers access',
        '30 Email contact access',
        '30 Request a service from Expert',
        '50 Invitation requests',
        '05 Social media extensions',
        '05 Company video hyperlink (YouTube) *',
        '01 Company elevator pitch Video Link **',
      ],
      purchaseBenefits: [
        'Advertise your company brand',
        'Free account access',
        'Custom profile page',
        'Direct connection by prospects',
        'Ad-free browsing',
        'Request a direct service',
        'Customer support',
        'Access to connection features',
        'Access partners Website',
        'Access mobile numbers',
        'Chat direct with connections',
        'Unlimited partners research',
        'Unlimited Video views',
      ],
      buttonText: 'Get started now ',
      // buttonId: '782C67C2-6EAD-41C3-8235-6FC98EAEBF73',
    },
    {
      planName: 'Standard Plus',
      instruction:
        'Elite level tailored for extra-active members. Solutions with various quality connections, get connected by businesses and prospects.',
      price: '$29',
      billingCycle: 'Monthly/Channel Expert.',
      earlyPhase: 'Early adopter’s phase',
      priceText: 'Pricing subject to slight change',
      subscriptionIncludes: [
        '01 Brand recognition e-Card',
        '50 Channel/Exp connections/Month',
        '10 Additional Expert members',
        '50 Lead connections & referrals (i)',
        '50 Contact phone numbers access',
        '50 Email contact access',
        '50 Request a service from Expert',
        '75 Invitation requests',
        '05 Social media extensions',
        '10 Company video hyperlink (YouTube) *',
        '01 Company elevator pitch Video Link **',
      ],
      purchaseBenefits: [
        'Advertise your company brand',
        'Free account access',
        'Custom profile page',
        'Direct connection by prospects',
        'Ad-free browsing',
        'Request a direct service',
        'Customer support',
        'Access to connection features',
        'Access partners Website',
        'Access mobile numbers',
        'Chat direct with connections',
        'Unlimited partners research',
        'Unlimited Video views',
      ],
      buttonText: 'Get started now ',
      // buttonId: '56C3B3DA-63B7-465E-86BE-74E214DACBDA',
    },
  ];

  //   accordionItems = [
  //     { title: 'Which Vercel plan is right for me?', content: `Lorem Ipsum is simply dummy text of the printing and typesetting industry.
  // Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type
  // and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised.
  // ` },
  //     { title: 'Do you offer custom invoicing?', content: 'This is the content for item 2.' },
  //     { title: 'What are the limits for each plan?', content: 'This is the content for item 3.' },
  //   ];

  accordionItems: any[] = [];
  private loadFrequentlyAskedQuestions(): void {
    this.accountService
      .getFrequentlyAskedQuestionsByType(2)
      .subscribe((response) => {
        this.accordionItems = response?.data || [];
      });
  }
}
