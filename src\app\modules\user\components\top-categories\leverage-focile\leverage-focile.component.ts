import { Component } from '@angular/core';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-leverage-focile',
  templateUrl: './leverage-focile.component.html',
  styleUrls: ['./leverage-focile.component.scss']
})
export class LeverageFocileComponent {
  isLoggedIn$: Observable<boolean>;

  constructor(private accountService: AccountService) {
    this.isLoggedIn$ = this.accountService.isLoggedIn$;
  }
}
