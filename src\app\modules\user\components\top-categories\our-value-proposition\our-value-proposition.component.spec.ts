import { ComponentFixture, TestBed } from '@angular/core/testing';

import { OurValuePropositionComponent } from './our-value-proposition.component';

describe('OurValuePropositionComponent', () => {
  let component: OurValuePropositionComponent;
  let fixture: ComponentFixture<OurValuePropositionComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ OurValuePropositionComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(OurValuePropositionComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
