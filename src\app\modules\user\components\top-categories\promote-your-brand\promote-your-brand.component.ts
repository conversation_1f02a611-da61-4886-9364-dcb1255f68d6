import { Component } from '@angular/core';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-promote-your-brand',
  templateUrl: './promote-your-brand.component.html',
  styleUrls: ['./promote-your-brand.component.scss']
})
export class PromoteYourBrandComponent {
  isLoggedIn$: Observable<boolean>;

  constructor(private accountService: AccountService) {
    this.isLoggedIn$ = this.accountService.isLoggedIn$;
  }
}
