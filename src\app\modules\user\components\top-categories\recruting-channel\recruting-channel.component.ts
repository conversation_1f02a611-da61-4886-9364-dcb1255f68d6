import { Component } from '@angular/core';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-recruting-channel',
  templateUrl: './recruting-channel.component.html',
  styleUrls: ['./recruting-channel.component.scss']
})
export class RecrutingChannelComponent {
  isLoggedIn$: Observable<boolean>;

  constructor(private accountService: AccountService) {
    this.isLoggedIn$ = this.accountService.isLoggedIn$;
  }
}
