
.fc-what-we-do-wrap{
    display: flex;
    flex-direction: row;
    gap: 32px;
    margin-block: 4rem;
    align-items: center;
    position: relative;
    min-height:525px;

    .fc-our-top-value{
        width: 470px;
        display: flex;
        flex-direction: column;         
        flex-shrink: 0;
        label{
            font-size: 23px;
            color:#014681;
            text-transform: uppercase;
            font-weight: 700;
            letter-spacing: 2px;
            margin-bottom: 1rem;
        }

        h2{            
            width: 470px;
            height: 106px;
            font-style: normal;
            font-weight: 700;
            font-size: 44px;
            line-height: 120%;
            color: #191825;
            flex: none;
            order: 1;
            align-self: stretch;
            flex-grow: 0;
            margin-bottom: 2rem;
        }

        p{            
            
            font-style: normal;
            font-weight: 400;
            font-size: 16px;
            line-height: 160%;
            color: rgba(25, 24, 37, 0.5);
            flex: none;
            order: 1;
            align-self: stretch;
            flex-grow: 0;
            margin-bottom: 0;
        }
    }
}

.fc-slide-categories{
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    position:relative;
    // left: calc(470px + 32px);
    right: 0;
    flex-wrap: nowrap;    
    width:100vw;
    gap: 2rem;
    padding: 2rem;    
    // scroll-behavior: smooth;    
    // scroll-snap-type: x mandatory;
    overflow-x: auto;
    transition: all 0.3s ease-in-out;
    scrollbar-width: thin;
    cursor: pointer;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.fc-slide-categories::-webkit-scrollbar {
  display: none; /* Chrome, Safari, and Edge */
}

.categories-card{
    width: 350px;
    display: flex;
    flex-direction: column;
    padding: 2rem;
    align-items: center;
    justify-content: center;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 451px;
    background: #FFFFFF;
    box-shadow:0px 11px 29px rgba(0, 0, 0, 0.1), 0px 0px 0px rgba(0, 0, 0, 0.1);
    border-radius: 32px;
    flex: none;
    order: 1;
    align-self: stretch;
    flex-grow: 0;
    transition: 0.3s;
    transition: transform 0.3s;    
    scroll-snap-align: start;
    flex: 0 0 auto;

    span{
        margin-block:64px;
    }

    h4{
       color: #191825; 
       margin-bottom: 2rem;
       font-size: 28px;
       font-weight: bold;
    }

    p{
            
            font-style: normal;
            font-weight: 400;
            font-size: 16px;
            line-height: 160%;
            text-align: center;
            color: rgba(25, 24, 37, 0.5);
    }

    &:hover{
        background-color: transparent;
        box-shadow: none;
    }
}

.fc-what-we-do-container {
    position: relative;
    transition: position 0.3s;
  }
  
  .fc-what-we-do-container.sticky {
    position: fixed;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    z-index: 10;
    background: white;
  }
  .hide-text{
    visibility: hidden;
  }

  .h-content{
    width: 100%;
    display: flex;
    flex-direction: row;
    gap: 2rem;
  }

  @media(max-width:768px) {
    .fc-what-we-do-wrap{
        min-height: auto;
        margin-block: 0rem;
        flex-direction: column;
        gap: 1rem;

        .fc-our-top-value{
            width: 100%;

            
            label{
                font-size: 1rem;
                height: 28px;
                margin-bottom: 0px;
            }

            h2{
                font-size:1.25rem;
                height: auto;         
                margin-bottom: 1rem;       
                width: 100%;
            }

            p{
                font-size: 14px;
            }
        }
    }
    .fc-what-we-do-container{
        padding-inline: 1rem;
    }
    .fc-slide-categories{
        gap: 1rem;
        padding:1rem 1rem 2rem;
    }
    .categories-card{
        height: auto;
        padding: 1rem; 
        border-radius: 1rem;
        h4{
            font-size: 1.25rem;
            margin-bottom: 1rem;
        }
        span{
            margin-block:2rem;
        }
        p{
            font-size: 14px;
        }
    }    
  }