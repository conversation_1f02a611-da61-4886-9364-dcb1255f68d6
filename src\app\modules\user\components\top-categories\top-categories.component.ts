import { Component, ElementRef, HostListener, OnInit, Renderer2, ViewChild } from '@angular/core';

@Component({
  selector: 'app-top-categories',
  templateUrl: './top-categories.component.html',
  styleUrls: ['./top-categories.component.scss']
})
export class TopCategoriesComponent {
  @ViewChild('fcSlideCategories') fcSlideCategories!: ElementRef;
  @ViewChild('fcSlideCategories1') fcSlideCategories1!: ElementRef;
  @ViewChild('fcContainer') fcContainer: ElementRef | undefined;
  marginLeft: number = 0;

  constructor(private renderer: Renderer2) {}

  categories = [
    { image: 'assets/images/recruting-partner.png', alt: 'Recruiting channel partners', title: 'Recruiting channel partners', description: 'What looked like a small patch of purple grass, above five feet.' },
    { image: 'assets/images/leverage-focile.svg', alt: 'Leverage Focile', title: 'Leverage Focile', description: 'Square, was moving across the sand in their direction.' },
    { image: 'assets/images/create-profile.jpg', alt: 'Create profile', title: 'Create detailed profile', description: 'What looked like a small patch of purple grass, above five feet.' },
    { image: 'assets/images/direction-.svg', alt: 'Direct connection', title: 'Direct connection', description: 'What looked like a small patch of purple grass, above five feet.' },
    { image: 'assets/images/our-value.svg', alt: 'Our value proposition', title: 'Our value proposition', description: 'What looked like a small patch of purple grass, above five feet.' },
    { image: 'assets/images/invite-connection.svg', alt: 'Invite connections', title: 'Invite connections', description: 'What looked like a small patch of purple grass, above five feet.' },
    
  ];

  ngAfterViewChecked(): void {
    this.calculateMarginLeft();
  }

  calculateMarginLeft(): void {
    if (this.fcContainer && this.fcSlideCategories) {
      const rect = this.fcContainer.nativeElement.getBoundingClientRect();
      this.marginLeft = rect.left;
      this.renderer.setStyle(this.fcSlideCategories1.nativeElement, 'margin-left', `${this.marginLeft}px`);
    }
  }

  onScroll(event: Event): void { 
    const wheelEvent = event as WheelEvent; // Cast Event to WheelEvent
    const target = event.currentTarget as HTMLElement;

    if (target) {
        const maxScrollLeft = target.scrollWidth - target.clientWidth;

        // Allow default vertical scrolling if at the start or end of horizontal scroll
        if (
            (wheelEvent.deltaY < 0 && target.scrollLeft === 0) || // Scrolling up and at the start
            (wheelEvent.deltaY > 0 && target.scrollLeft >= maxScrollLeft) // Scrolling down and at the end
        ) {
            return; // Allow vertical body scrolling
        }

        // Scroll horizontally
        target.scrollLeft += wheelEvent.deltaY;
        event.preventDefault(); // Prevent vertical scrolling
    }
}
}
