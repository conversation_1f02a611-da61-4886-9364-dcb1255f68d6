.fc-content-card{
    align-items: center;
  
  
    img{
      max-width: 90%;
    }
  
    label{
      color: black;
    }
  
    h5{
      margin-bottom: 1rem;
    }
  
    p{
       line-height: 30px;
       color: black;
    }
  
    .read-more-btn{    
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 21px 46px;  
    width:max-content;
    height: 52px;
    background: #000000;
    border-radius: 200px;
    color: white;
    text-decoration: none;
    }
  }
  
  .fc-full-container {
    background: #12141D;
    position: relative;
    min-height: auto;
    overflow: hidden;
    padding-block: 100px;
    display: flex;
    align-items: end;
  
    >div {
      z-index: 1;
      position: relative;
    }
  
    &::after {
      position: absolute;
      width: 447px;
      height: 443px;
      right: 0px;
      top: 3px;
      background: #014681;
      filter: blur(262px);
      content: '';
    }
  
    &::before {
      position: absolute;
      width: 464px;
      height: 468px;
      left: 0px;
      top: 742px;
      background: #014681;
      filter: blur(262px);
      content: '';
    }  
  }

  .fc-hdr-content {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    text-align: center;
  
    color: white;
  
    .text-transfer-upparcase {
      text-transform: uppercase;
      font-size: 25px;
    }
  
    .fc-brand-txt {
      font-size: 40px;
      font-weight: 600;
      margin-bottom: 0;
      line-height: 1.5;
    }
  
    p {
      color: #FFFFFF;
      line-height: 30px;
      text-align: center;
      font-weight: 300;
      font-size: 18px;
      margin-top: 1rem;
    }
  }
  
  .fc-vision-section-box{
    margin-top: 2rem;
    p{
        font-size: 1rem;
        color: white;
        line-height:30px;
    }
  }
  .breadcumb{
    z-index: 11;
    position: relative;
    li{
      a{
        text-decoration: none;
      }
  
      &.breadcrumb-item + .breadcrumb-item::before{
        content:'-';
      }
    }
  }

  .fc-vision-section-box{
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 47px;
    margin-top: 88px;
  
    .text-card{     
      position:relative;
      width: 100%;  
      height: auto;
      background: linear-gradient(180deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.125) 68.53%);
      backdrop-filter: blur(16px);
      border-radius: 16px;   
      padding:2rem;
      color: white;
  
      label{
        font-size: 24px;
        font-weight: 600;
      }
  
      p{
        font-size: 16px;
        line-height: 30px;
        font-weight: 300;
        margin-top: 1rem;
  
        b{
          color: #FFA500;
          font-weight: 600;
        }
      }
    }
  }

  .fc-hdr-content {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    text-align: center;
  
    color: white;
  
    .text-transfer-upparcase {
      text-transform: uppercase;
      font-size: 25px;
    }
  
    .fc-brand-txt {
      font-size: 40px;
      font-weight: 600;
      margin-bottom: 0;
      line-height: 1.5;
    }
  
    p {
      color: #FFFFFF;
      line-height: 30px;
      text-align: center;
      font-weight: 300;
      font-size: 18px;
      margin-top: 1rem;
    }
  }

  .fc-content-card{
    align-items: center;
  
  
    img{
      max-width: 90%;
    }
  
    label{
      color: black;
    }
  
    h5{
      margin-bottom: 1rem;
    }
  
    p{
       line-height: 30px;
       color: black;
    }
  
    .read-more-btn{    
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 21px 46px;  
    width:max-content;
    height: 52px;
    background: #000000;
    border-radius: 200px;
    color: white;
    text-decoration: none;
    }
  }