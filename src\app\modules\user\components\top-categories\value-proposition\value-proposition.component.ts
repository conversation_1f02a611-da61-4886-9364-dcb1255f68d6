import { Component } from '@angular/core';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-value-proposition',
  templateUrl: './value-proposition.component.html',
  styleUrls: ['./value-proposition.component.scss']
})
export class ValuePropositionComponent {
  isLoggedIn$: Observable<boolean>;

  constructor(private accountService: AccountService) {
    this.isLoggedIn$ = this.accountService.isLoggedIn$;
  }
}
