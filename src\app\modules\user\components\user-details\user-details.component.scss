.banner-wrapper {
  position: relative;
  height: 20rem;
  // background: url("../../../../../assets/svgs/bg.svg") center center no-repeat;
  background-size: cover;
}

.person-info-wrapper {
  margin-top: 5rem;
}

.chip {
  border-radius: 16px;
  -webkit-font-smoothing: antialiased;
  margin-inline: 4px;
  margin-top: 4px;
  margin-bottom: 4px;
  padding-inline: 12px;
  padding-top: 7px;
  padding-bottom: 7px;
  display: inline-flex;
  position: relative;
  align-items: center;
  height: 24px;
  font-size: 12px;
  cursor: pointer;
  overflow: hidden;
  vertical-align: middle;
  box-sizing: border-box;
  min-width: 4.375rem;
}

.search-icon {
  top: 0.75rem;
  left: 1.5rem;
}

.min-h-300 {
  min-height: 200px;
}

.meta-data-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.meta-details {
  display: flex;
  flex-wrap: wrap;
}

.social-links-grid {
  display: grid;
  grid-template-columns: 100px auto;
  gap: 10px;
}

@media (max-width: 768px) {
  .meta-data-grid {
    grid-template-columns: 1fr;
  }
  .social-links-grid {
    grid-template-columns: 1fr;
  }
  .meta-details {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem !important;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.pulse-animation {
  cursor: pointer;
  animation: pulse 1.5s infinite;
  color: white;
}





.fc-expert-overview-wrapper {
  background-color: #F4F4F4;
}

.fc-full-common-banner {
  width: 100%;
  height: 370px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.rating-view {
  min-width: 56px;
  height: 28px;
  background: #D6FFB7;
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000000;
  gap: 5px;
  font-size: 12px;
  padding: 0px 1rem;
}

.connect-btn {
  min-width: 88px;
  height: 28px;
  background: #014681;
  border-radius: 50px;
  color: white;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  padding-inline: 1rem;

  button{
    background-color: transparent;
    border: none;
  }
}

.share-btn {
  width: 28px;
  height: 28.45px;
  background: #FACD49;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 14px;
}

.follow-btn {
  min-width: 77px;
  height: 28px;
  background: #FF7777;
  border-radius: 50px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  padding-inline: 1rem;
  a{
    text-decoration: none;
    color: white;
  }
}

.chat-btn{
  width: 28px;
  height: 28.45px;
  background: #00ACFF;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 14px;
  border: none;
}

.fc-top-personal-detail {
  width: 100%;
  min-height: 155px;
  background: #FFFFFF;
  box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.1);
  border-radius: 15px;
  z-index: 11;
  position: relative;
  padding: 25px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: -80px;
}

.fc-address-detail {
  display: flex;
  flex-direction: row;
  gap: 49px;
  align-items: center;

  .as-user {
    min-width: 77px;
    height: 28px;
    background: #CCDCFF;
    border-radius: 50px;
    color: black;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
  }

  .fc-text-bar {
    display: flex;
    flex-direction: column;

    label {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.50);
    }

    span {
      color: black;
      font-size: 14px;
    }

    a{
      text-decoration: none;
    }
  }
}

.top-line {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.fc-user-avtar {
  display: flex;
  flex-direction: row;
  gap: 1rem;
  align-items: center;
}

.user-img {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  border: 1px solid #03589140;
  overflow: hidden;
  img{
    width: 100%;
    height: 100%;
    object-fit: cover;
    padding: 0px;
    border-radius: 30px;
  }
}

.fc-user-name {
  display: flex;
  flex-direction: column;

  b {
    color: #024781;
    font-weight: 500;
  }

  label {
    color: rgba(0, 0, 0, 0.5);
    font-size: 16px;
    width: max-content;
  }
  p{
    font-size: 14px;
    margin-bottom: 0px;
  }
}

.fc-socile-profile {
  display: flex;
  flex-direction: row;
  gap: 1rem;
}

.website-link {
  a {
    color: #E435FF;
  }
}


.fc-proile-tab-section {
  display: flex;
  flex-direction: row;
  gap: 30px;
  margin-top: 1.5rem;
  justify-content: space-between;

  .fc-left-bar {
    width: 255px;
    gap: 1.5rem;
    display: flex;
    flex-direction: column;

    a{
      text-decoration: none;
      color: #212529;
    }
  }

  .fc-center-bar {
    width: 580px;
    gap: 1.5rem;
    display: flex;
    flex-direction: column;
  }

  .fc-right-bar {
    width: 255px;
    gap: 1.5rem;
    display: flex;
    flex-direction: column;
  }
}

.fc-card {
  box-sizing: border-box;
  width: 100%;
  min-height: auto;
  background: #FFFFFF;
  border: 1px solid rgba(140, 140, 140, 0.2);
  border-radius: 18px;
  padding: 1rem;
}


.list-of-connection {
  display: flex;
  flex-direction: row;
  gap: 10px;
  padding-inline: 1rem;

  .ref-profile {
    width: 35px;
    height: 35px;
    border-radius: 20px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .refer-name {
    width: calc(100% - 100px);
    display: flex;
    flex-direction: column;

    label {
      font-size: 14px;
      font-weight: 500;
      color: #212121;
      cursor: pointer;
    }

    .refer-post {
      color: rgba(0, 0, 0, 0.5);
      font-size: 12px;

      b {
        font-weight: 500;
        color: #212121;
      }
    }
  }
}

.fc-connection-card {
  display: flex;
  flex-direction: column;

  h4 {
    font-size: 14px;
    color: black;
    font-weight: 500;
    width: 100%;
    padding-inline: 1rem;
    margin-bottom: 20px;
  }
}

.fc-connect-user {
  display: flex;
  flex-direction: row;
  gap: 1rem;
  align-items: start;
  justify-content: end;
  margin-left: auto;
  flex: 1;

  .add-connection-btn {
    background-color: transparent;
    border: none;
    padding: 0px;
  }

  .online {
    width: 10px;
    height: 10px;
    background: #00ACFF;
    border-radius: 10px;
    margin-top: 8px;
  }
}

.see-all-bar {
  color: #014681;
  font-size: 12px;
  width: 100%;
  height: 45px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background-color: transparent;


  &:before {
    border-top: 1px solid rgba(140, 140, 140, 0.20);
    content: "";
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
  }
  button{
    background-color: transparent;
    border: none;
  }
}

.fc-my-expertise {
  .heading-label {
    width: 100%;
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid #F4F4F4;
    padding-bottom: 1rem;

    label {
      text-transform: uppercase;
      color: #181818;
      font-size: 14px;
      font-weight: 500;
    }

    p {
      color: rgba(24, 24, 24, 0.60);
      font-size: 12px;
      margin-bottom: 0px;
    }
  }

  .fc-expertise-tag {
    display: flex;
    flex-direction: row;
    flex-direction: column;

    ul {
      display: flex;
      gap: 10px;
      list-style: none;
      padding-left: 0px;
      flex-direction: row;
      flex-wrap: wrap;
      margin-top: 1rem;
    }

    li {
      background-color: #E9F0F8;
      border-radius: 5px;
      color: black;
      font-size: 12px;
      padding: 2px 5px;
    }
  }
}

.category-navbar-item {
  display: flex;
  flex-direction: row;
  border-bottom: 1px solid #D9DBE9;
  align-items: center;
  justify-content: space-between;
  list-style: none;
  padding-left: 0px;
  margin-bottom: 0px;

  li {
    width: auto;
    display: flex;
    align-items: start;
    justify-content: center;
    min-height: 40px;
    border-bottom: 2px solid transparent;


    a {
      text-decoration: none;
      font-size: 16px;
      font-weight: 400;
      display: flex;
      flex-direction: row;
      gap: 10px;
      align-items: center;
      color: #A0A3BD;
      white-space: nowrap;
    }

    span {
      min-width: 26px;
      min-height: 26px;
      left: 0px;
      top: 0px;
      background: rgba(1, 69, 129, 0.1);
      border-radius: 14px;
      display: flex;
      align-items: center;
      justify-content: center;

      svg {
        width: 14px;
      }
    }

    &.active {
      border-color: #014681;

      a {
        color: #014681;
      }
    }
  }
}

.about-us-card {
  label {
    font-weight: 500;
    font-size: 14px;
    margin-bottom: 10px;
  }

  p {
    font-size: 14px;
    color: #4A4A4A;
    line-height: 1.5;
  }
}

.fc-self-intro {
  iframe {
    border-radius: 18px;
  }
}

.category-navbar-item {
  display: flex;
  list-style: none;
  padding: 0;
}

.nav-item {
  padding: 10px 0pzx;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
}

.nav-item.active {
  border-bottom: 2px solid #014681;
  font-weight: bold;
}

.tab-content {}

.user-given-review {
  border-bottom: 1px solid rgba(213, 213, 213, 0.5);
  margin-bottom: 1rem;
  
  .review-section{
    label{
      color: #858585;
      font-weight: 500;
      font-size: 14px;
    }

    .separate-rate{
      display: flex;
      line-height: 1;

      ::ng-deep{
        span{
          font-size: 1.5rem;
          color: #E7B66B;
        }
      }
    }

    p{
      font-size: 14px;
      color: #0D0C22;
      line-height: 1.5;
    }
    
  .user-name{
    font-size: 15px;
    color: #0D0C22;
    font-weight: 500;
  }
  }

  &:last-child{
    border-bottom: 0px;
  }
}

.review-section {
  display: flex;
  flex-direction: column;
  gap: 10px;

  p {
    font-size: 14px;
    color: #0D0C22;
  }
}

.user-review-avtar {
  display: flex;
  flex-direction: row;
  gap:10px;
  align-items: center;

  .img-avatar {
    width: 36px;
    height: 36px;
    object-fit: cover;
    border-radius:20px;
    overflow: hidden;
  }

}
.title{
  font-size: 14px; 
  color: #858585;
  font-weight:500;
}
.fc-profile-avatar {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;

  .avtar-img {
    width: 30px;
    height: 30px;
    border-radius: 15px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      padding: 0px;
    }
  }

  .fc-profile-name {
    display: flex;
    flex-direction: column;
    width: calc(100% - 40px);

    label {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 2px;
    }

    span {
      color: rgba(0, 0, 0, 0.5);
      font-size: 12px;
    }
  }
}

.text-up {
  display: flex;
  flex-direction: row;
  gap: 5px;
  font-size: 12px;

  span {
    width: 25px;
    text-align: center;
  }

  label {
    flex: 1
  }
}


.my-follow {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
  gap: 8px;

  label {
    font-size: 14px;
    color: #000000;
  }

  span {
    font-size: 14px;
    color: #014681;
    font-weight: 600;
  }
}

.fs-social-follow {
  border-top: 1px solid rgba(140, 140, 140, 0.20);
  padding-top: 1rem;
  padding-bottom: 0;
  gap: 1rem;
  display: flex;
  flex-direction: row;
}



.form-control {
  box-sizing: border-box;
  margin: 0 auto;
  height: 56px;
  background: #F6F6F6;
  border: 1px solid rgba(246, 246, 245, 0.4);
  border-radius: 12px;
  flex: none;
  // order: 1;
  align-self: stretch;
  flex-grow: 0;
  padding: 1rem;

  &::placeholder {
    color: rgba(102, 102, 102, 0.6);
  }
}

.form-label {
  font-weight: 400;
  font-size: 1rem;
  color: #666666;
}

.request-btn {
  min-width: 150px;
  height: 50px;
  background: #014681;
  box-shadow: 0px 548px 219px rgba(0, 0, 0, 0.01), 0px 308px 185px rgba(0, 0, 0, 0.04), 0px 137px 137px rgba(0, 0, 0, 0.06), 0px 34px 75px rgba(0, 0, 0, 0.07), 0px 0px 0px rgba(0, 0, 0, 0.07);
  border-radius: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  border: none;
  font-size: 16px;
  gap: 0.5rem;
}

.fc-post-user {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;

  .user-img {
    width: 30px;
    height: 30px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      padding: 0px;
    }
  }

  span {
    color: rgba(0, 0, 0, 0.45);
  }
}

.cmnt-user-avatar {
  width: 25px;
  height: 25px;
  position: absolute;
  left: 7px;
  top: 7px;
}

.fc-write-comment-post {
  display: flex;
  position: relative;

  input {
    width: 100%;
    background-color: #F6F6F6;
    height: 40px;
    border-radius: 20px;
    padding-left: 3rem;
    border: none;
    padding-right: 1rem;
    font-size: 14px;

    &::placeholder {
      color: rgba(0, 0, 0, 0.36);
    }
  }
}

.videp-preview-link {
  gap: 1rem;
  display: flex;
  flex-direction: column;

  a {
    font-size: 14px;
    color: black;
  }
}

.fc-post-comments {
  display: flex;
  flex-direction: row;
  gap: 10px;
  margin-block: 1.2rem;

  button {
    border: none;
    background: none;
    position: relative;
  }

  .c-badge {
    background-color: #00ACFF;
    color: white;
    font-size: 14px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: -9px;
    right: -9px;
    padding: 2px 7px;
    font-size: 11px;
    border: 2px solid white;
    border-radius: 15px;
  }
}

.rating-b {
  ::ng-deep span {
    font-size: 2rem;
  }
}

.fc-give-ratings {
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-content: start;

  textarea {
    width: 100%;
    background-color: #F6F6F6;
    min-height: 150px;
    border-radius: 8px;
    padding: 1rem;
    border: none;
    padding-right: 1rem;
    font-size: 14px;
    color: black;
  }

  .submit-btn {
    width: max-content;
    height: 50px;
    background: #014681;
    box-shadow: 0px 548px 219px rgba(0, 0, 0, 0.01), 0px 308px 185px rgba(0, 0, 0, 0.04), 0px 137px 137px rgba(0, 0, 0, 0.06), 0px 34px 75px rgba(0, 0, 0, 0.07), 0px 0px 0px rgba(0, 0, 0, 0.07);
    border-radius: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    border: none;
    font-size: 16px;
    gap: 0.5rem;
    padding: 2px 1rem;
    min-width: 150px;
    margin-left: auto;
    margin-top: 1.2rem;
  }
}

.fc-give-star {
  margin-top: 5px;
  margin-bottom: 10px;
}

.shorting-dropdown {
  // margin: 1.5rem 0px;

  .ng-select {
    // padding: 5px 1rem;
    // border-radius: 5px;
    font-size: 14px;
    // background: url(../../../../../assets/svgs/arrow-drown.svg)#FFFFFF no-repeat calc(100% - 15px) center;
    // background-repeat: no-repeat;
    // background-size: 14px 14px;
    // appearance: none;
    // padding-right: 2.5rem;
    // text-align: left;
    // border: none;
    border: none;
    min-width: 250px;
    margin-left: auto;
    margin-right: 0px;
  }
}

.fc-company-review-section {
  display: flex;
  flex-direction: column;

  .fc--review-left {
    display: flex;
    flex-direction: column;

    h5 {
      color: black;
      font-weight: 600;
      font-size: 2.5rem;
      margin-bottom: 0px;
    }
    .rating-start{
      ::ng-deep{
        color: #E7B66B;
      }
    }
  }

  .fc-div--review {
    display: flex;
    gap: 1rem;
    justify-content: space-between;
  }

  .total-review {
    color: #858585;
    font-size: 14px;    
  }
}

.fc--percentage-right{
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: column;
  flex: 1;
 
  .star-label{
    font-size: 14px;
    font-weight: 500;
    color: #0D0C22;
    min-width:60px;
  }

  .progress-bar{
    width:280px;
    background-color: #f2f6fb;
    height: 5px;
    border-radius: 5px;
    position: relative;
    margin-right: auto;

    .orange-bar{
      width: 100%;
      background-color:#E7B66B;
      width: 70%;    
      height:5px;
      left: 0px;
      position: absolute;  
    }
  }
  .each-count{
    font-size: 14px;
    font-weight:500;
    width: auto;
    display: flex;
    justify-content:end;
  }

  .fc-pro-line{
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.company-offer-card {
  overflow: hidden;
 
  .fc-company-offer-hdr {
    font-size: 14px;
    color: black;
    font-weight: 600;
    width: 100%;
    padding-inline: 1rem;
  }

  .service-count {
    margin-left: auto;
    margin-right: 0px;
    width: auto;
    flex: 1;
    position: absolute;
    left: auto;
    right: 59px;
    background: rgba(255, 25, 48, 0.5);
    width: 11px;
    height: 11px;
    padding: 9px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 400;
    font-size: 10px;
  }

  .accordion-button:not(.collapsed) {
    background-color: transparent;
    box-shadow: none;
    .service-count {
      background-color: #024781;
    }
  }

  .accordion-button:focus {
    box-shadow: none;
    border: none;
  }
  .accordion-button{
    font-weight: 500;
  }

  .accordion-item {
    border-left: 0px;
    border-right: 0px;
  }

  .accordion-item:first-of-type .accordion-button,
  .accordion-item:first-of-type {
    border-radius: 0px;
  }
  .accordion-item:last-of-type{
    border-bottom: 0px;
  }
  .accordion-body {
    color: rgba(0, 0, 0, 0.50);
    font-size: 15px;
    padding-top: 0px;
  }
}

::ng-deep{
  .spinner-border{
    width: 1rem;
    height: 1rem;    
    color: white !important;
  }

  app-spinner{
    display: flex;
  }
}

/* Expert Chat Section */

.fc-expert-chat-row{
  width: 100%;
}
.fc-chat-icon{
 display: flex;
 align-items: center;
 justify-content: space-between;
 width: 100%;
 border-bottom: 1px solid #F4F4F4;
 padding-bottom: 10px;

 span{
  font-size:12px;
  gap:10px;
  display: flex;
  align-items: center;
  text-transform: uppercase;
  color: #181818;
 }
}

.fc-saparete-chat-card {
  .messages {
    flex-grow: 1;
    overflow-y: auto;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    height: 246px;
    
    .message{
      max-width: 70%;
      margin-bottom: 15px;
      padding: 5px 10px;
      border-radius: 8px;
      display: flex;
      position: relative;
      cursor: pointer;

    &.incoming {
      background-color: #e0e0e0;
      align-self: flex-start;
      border-radius: 20px;
      font-size: 12px;

      &:before {
        content: '';
        width: 10px;
        height: 10px;
        border-radius: 5px;
        background-color: #e0e0e0;
        position: absolute;
        bottom: -5px;
        left: -10px;
      }
    }

    &.outgoing {
      background-color: var(--bs-primary);
      color: white;
      align-self: flex-end;
      border-radius: 20px;
      font-size: 12px;


      &:before {
        content: '';
        width: 10px;
        height: 10px;
        border-radius: 5px;
        background-color: var(--bs-primary);
        position: absolute;
        bottom: -5px;
        right: -10px;
      }

      .time-stemp {
        right: 12px;
        left: auto;
      }
    }
  }

    .time-stemp {
      position: absolute;
      width: auto;
      font-size: 12px !important;
      white-space: nowrap;
      bottom: -20px;
      left: 12px;
      color: #707070;
    }
  }
}

.fc-chat-footer{
  display: flex;
  align-items: center;
  position: relative;

  input{
   background-color:#EFF6FC;
   border-radius:50px; 
   border:none;
   font-size: 12px;
   height: auto;
   padding: 5px;
   padding-left: 1rem;
   padding-right: 2.5rem;
  }
  
}

.related-chat{
  position: absolute;
  right: 12px;
  display: flex;
  align-items: center;
  top: 10px;

  button{
    border: none;
    color: white;
    border-radius: 15px;
    font-size:12px;
    padding: 5px 20px 5px 10px; 
    background: none;
    border: none;
  }
}
.chat-disable {
  pointer-events: none;
  opacity: 0.5;
}

.chat-enable {
  pointer-events: auto;
  opacity: 1;
}

.back-profile{
  display: flex;
  flex-direction: row;

  .company-profile{
    display: flex;
    flex-direction: column;
    width:40px;
    height:40px;
    overflow: hidden;
    border-radius: 20px;

    img{
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 20px;
    }
  }

  .company-name{
    width: calc(100% - 40px);
    display: flex;
    flex-direction: column;
  }
}

.m-back-btn {
  display: none;
  position: absolute;
  left: 1rem;
  background: #ffffff33;
  top: 1rem;
  padding: 5px 1rem;
  border-radius: 1rem;
  color: white;
}

@media(max-width:768px){
  .fc-full-common-banner{
    height: 170px;
    position: relative;
  }
  .fc-user-avtar{
    flex-direction: row;
    align-items: center;
  }
  .fc-address-detail{
    flex-wrap: wrap;
    gap: 1rem;
  }
  .top-line{
    flex-direction: column;
    gap: 1rem;
  }
  .fc-user-name{
    width: calc(100% - 70px);
  }
  .fc-proile-tab-section{
    flex-direction: column;
    margin-top: 2rem;
    gap: 1rem;

    .fc-left-bar{
      flex-direction: column;
      width: 100%;
      gap: 1rem;
      order: 3;
    }
    .fc-center-bar{
      width: 100%;
      order: 1;
      gap: 1rem;
    }
    .fc-right-bar{
      width: 100%;
      order: 2;
      gap: 1rem;
    }
    .fc-div--review{
      flex-direction: column;
    }
  }
  .category-navbar-item{
      overflow-x: scroll;     /* Enable horizontal scroll */
      -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
      scrollbar-width: none;  /* Firefox */
      gap: 1rem;

      li a{
        font-size: 14px;
      }
    }    
    .category-navbar-item::-webkit-scrollbar {
      display: none; /* Chrome, Safari, and Edge */
    }
    .fc-top-personal-detail{
      padding: 1rem;
    }
    .shorting-dropdown{
      flex-direction: column !important;
    }
    .form-label{
      font-size: 14px;
    }
    .form-control{
      font-size: 14px;
    }
    .fc-socile-profile{
      flex-wrap: wrap;
    }
    .fc-chat-footer{
      form{
        width: 100%;
      }
    }
    .m-back-btn{
      display: flex;
      position: absolute;
      left: 1rem;
      top: 1rem;
    }

    .fc-self-intro{
      label{
        font-size: 14px !important;
      }
    }
}