import { Location } from '@angular/common';
import { Component, NgZone, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { finalize, map, catchError, take } from 'rxjs/operators';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { ExpertService } from 'src/app/modules/admin/services/expert.service';
import {
  DEFAULT_CONNCTION_REQUEST_LIMIT,
  DropMessageType,
  EMPTY_GUID,
  FollowStatus,
  socialMediaIds,
  socialMediaImages,
} from 'src/app/shared/constant';
import { ChatService } from 'src/app/shared/services/chat.service';
import { HubService } from 'src/app/shared/services/hub.service';
import { UtilsService } from 'src/app/shared/services/utils.service';
import { PROFILE_TYPE } from 'src/app/shared/constant';
import { ModalService } from 'src/app/shared/services/modal.service';
import { HttpClient } from '@angular/common/http';
import { CompanyService } from 'src/app/shared/services/company.service';
import { of } from 'rxjs';
import { FormBuilder, FormGroup } from '@angular/forms';
@Component({
  selector: 'app-user-details',
  templateUrl: './user-details.component.html',
  styleUrls: ['./user-details.component.scss'],
})
export class UserDetailsComponent implements OnInit, OnDestroy {
  loggedInUserId: string = '';
  vm: any = {};
  @ViewChild('pop') expertisePopover!: any;
  isExpert = false;
  isLoading = false;
  userState$: any = {};
  userCommentModel = null;
  isLinkedCopied = false;
  connectLoading = false;
  followLoading = false;
  addingComment = false;
  ratingModel = null;
  maxRating = 5;
  selectedBannerImage = '';
  isCompany = false;
  searchExpert = '';
  FollowStatus = FollowStatus;
  followStatus$ = this.hubService.getFollowStatus();
  videosLoading = false;
  elevatorPitches: any = [];
  socialMediaIds = socialMediaIds;
  socialMediaLinks: any = [];
  socialMediaTypes = socialMediaImages;
  followingLoading = false;
  experts$!: any[];
  expertsLoading = false;
  ratingData: any = {};
  expertise = [];
  isOwnProfile: boolean = false;

  onlineUsers: any[] = [];
  onlineUsersCopy: any[] = [];
  receiverUser: any = null;
  message: string = '';
  messagesList: any[] = [];
  messagesLoading: boolean = false;
  isMobileView: boolean = false;
  isSendingMessage: boolean = false;

  hideConnectButton = false;
  messageText: any;

  expertId: string = '';
  isFavorite: boolean = false;
  loading: boolean = false;
  profileImage!: string;

  requestServiceForm!: FormGroup;
  constructor(
    private activedRoute: ActivatedRoute,
    private toasterService: ToastrService,
    private account: AccountService,
    private modalService: ModalService,
    private utils: UtilsService,
    private router: Router,
    private location: Location,
    private chatService: ChatService,
    private expertService: ExpertService,
    private hubService: HubService,
    private http: HttpClient,
    private companyService: CompanyService,
    private ngZone: NgZone,
    private route: ActivatedRoute,
    private formBuilder: FormBuilder
  ) { }

  ngOnInit(): void {
    this.setRequestServiceForm();
    this.account.isLoggedIn$.subscribe((response) => {
      if (!response) {
        this.router.navigate(['/']);
      }
    });
    this.account.profileImage$.subscribe((profileImage: string) => {
      this.profileImage = profileImage;
    });
    this.activedRoute.params.subscribe((response: any) => {
      const { type, id } = response;
      this.elevatorPitches = [];
      if (type && id) {
        this.checkFollowStatus();
        this.account.user$.pipe(take(1)).subscribe((user) => {
          this.loggedInUserId = user?.userId; // Retrieve the logged-in user's ID
          this.isOwnProfile = this.vm?.admin?.id === this.loggedInUserId; // Compare with the profile's admin ID
          this.userState$ = user;
          this.vm.userId = id;
          this.hideConnectButton =
            `${user.userId}`.toLowerCase().toString() ==
            `${id}`.toLowerCase().toString();
          let payload = {
            loginUserId: user?.userId as string,
            userId: EMPTY_GUID,
            comapanyid: id,
          };
          if (type === PROFILE_TYPE.company) {
            this.isCompany = true;
            this.getExperts(id);
            return this.getCompanyDetails(payload);
          } else if (type === PROFILE_TYPE.expert) {
            this.isCompany = false;
            // using userId getting expert details
            this.getSocialMediaLinks(this.vm.userId);
            payload.userId = id;
            this.getPitches(payload.userId);
            return this.getUserDetails(payload);
          }
        });
      } else {
        return this.handleError(
          'Opps the resource you are looking is not available.'
        );
      }
      return;
    });

    this.expertId = this.route.snapshot.paramMap.get('id') || '';
  }

  ngOnDestroy(): void {
    this.isCompany = false;
    this.elevatorPitches = [];
  }

  getRatingData(userId: string): void {
    this.account.getCompanyRatingData(userId).subscribe((response: any) => {
      this.ratingData = response.data;
    });
  }

  getPercentage(count: number): number {
    const total = this.ratingData.totalRatings || 1; // Avoid division by zero
    return (count / total) * 100;
  }

  getPitches(id: any) {
    this.videosLoading = true;
    this.account
      .getUsersApprovedElevatorPitches(id)
      .pipe(finalize(() => (this.videosLoading = false)))
      .subscribe((response: any) => {
        this.elevatorPitches = response.data;
      });
  }

  setRequestServiceForm() {
    this.requestServiceForm = this.formBuilder.group({
      firstName: null,
      lastName: null,
      phoneNumber: null,
      email: null,
      companyName: null,
      zipCode: null,
      serviceType: 'Sales Inquiries',
      receiverUser: this.userState$.email,
    });
  }

  handleRequestServiceSubmit() {
    const formData = new FormData();
    Object.keys(this.requestServiceForm.value).forEach((key) => {
      formData.append(key, this.requestServiceForm.value[key]);
    });
    formData.append('receiveEmail', this.vm.userId);

    this.http
      .post('bulkinvite/RequestService', formData)
      .subscribe((response) => { });
  }

  getExperts(companyId: any) {
    this.expertsLoading = true;
    this.companyService
      .getCompanyExperts(companyId)
      .pipe(
        map((response: any) => response.data),
        finalize(() => (this.expertsLoading = false))
      )
      .subscribe((response) => {
        this.experts$ = response;
      });
  }

  checkFollowStatus() {
    this.followStatus$.subscribe((response: any) => {
      this.vm.isRequestRecieved = true;
      this.vm.isUserConnected = response;
    });
  }

  getCompanyDetails(payload: any) {
    this.isLoading = true;
    this.account
      .getCompanyProfile(payload)
      .pipe(
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe((companyDetail: any) => {
        if (companyDetail.messageType) {
          return this.handleError(
            'An error occurred while fetching company details.'
          );
        }
        const { data } = companyDetail;
        this.getRatingData(companyDetail.data.userId);
        this.updateViewModel(data, true);
        this.loadUserComments();
      });
  }

  handleError(message: string) {
    this.toasterService.error(message);
    this.router.navigate(['/']);
  }

  updateViewModel(data: any, isCompany = false) {
    this.vm = data;
    const primaryAddress = data.admin.expertDetail.userContacts.find(
      (x: any) => x.isPrimary
    );
    this.vm.address = `${primaryAddress?.cityName}`;
    this.isExpert = data.userType == 3;
    this.vm.about = this.isCompany ? data.about : data.aboutMe;
    this.vm.experties = data.experts.length
      ? data.experts[0]['expertise']?.slice(0, 3)
      : [];
    this.isCompany = isCompany;
    if (!this.isCompany) {
      this.vm.admin.isRequestReceived = this.vm.isRequestReceived;
      this.vm.admin.isRequestSent = this.vm.isRequestSent;
      this.vm.admin.isConnected = this.vm.isConnected;
    }
    if (this.vm?.admin.expertDetail.elevatorPitchVideo) {
      this.vm.admin.expertDetail.elevatorPitchVideo = this.utils.getId(
        this.vm.admin.expertDetail.elevatorPitchVideo
      );
    }

    if (data?.admin?.expertDetail?.userPosts?.length && isCompany) {
      this.elevatorPitches = data?.admin?.expertDetail?.userPosts.map(
        (x: any) => {
          x.url = x.path;
          return x;
        }
      );
    }
    if (!isCompany) {
      this.selectedBannerImage = data.banner || './assets/svgs/bg.svg';

      this.vm.profilePhoto = data.profilePhoto || './assets/svgs/focile.svg';
      this.vm.firstMessage = data.strengthAndAbility;
      this.vm.secondMessage = data.messageToAudience;
    } else {
      this.selectedBannerImage = data.companyBanner || './assets/svgs/bg.svg';
      this.vm.profilePhoto = data.companyImage || './assets/svgs/focile.svg';
      this.vm.firstMessage = data.companyValueProposition;
      this.vm.secondMessage = data.companyMessageToAudience;
    }

    if (this.isCompany) {
      const links = [
        { link: 'facebookLink', svg: './assets/svgs/facebook.svg' },
        { link: 'youtubeLink', svg: './assets/svgs/youtube.svg' },
        { link: 'twitterLink', svg: './assets/svgs/twitter.svg' },
        { link: 'instagramLink', svg: './assets/svgs/instagram.svg' },
        { link: 'linkedInLink', svg: './assets/svgs/linkedin.svg' },
        { link: 'pinterestLink', svg: './assets/svgs/pintrest.svg' },
      ];
      links.forEach((link) => {
        if (this.vm[link.link]) {
          this.socialMediaLinks.push({
            url: this.vm[link.link],
            imageUrl: link.svg,
          });
        }
      });
    }

    if (data.conversationId) {
      this.getMessages(data.conversationId);
    }
  }

  getMessages(conversationId: string) {
    this.messagesLoading = true;
    this.http
      .get(`Chat/GetConvertionMessages?convertionId=${conversationId}`)
      .pipe(
        map((messages: any) => this.assignSender(messages.data)),
        // tap((messages) => this.updateReceiverMessages(messages)),
        finalize(() => (this.messagesLoading = false)),
        catchError((error) => {
          console.error('Failed to load messages:', error);
          return of([]);
        })
      )
      .subscribe((response: any) => {
        this.messagesList = response || [];
        this.updateNewMessage();
      });
  }

  sendPrivateMessage() {
    const message = JSON.parse(JSON.stringify(this.messageText));
    this.messageText = '';
    this.isSendingMessage = true;
    this.chatService.sendMessageAPI(
      this.vm.userId,
      message,
      this.vm.conversationId,
      this.userState$.userId
    );
    this.ngZone.run(() => {
      this.chatService.messageId$.asObservable().subscribe((id) => {
        this.isSendingMessage = false;
        this.messagesList.push({
          message,
          sender: this.userState$.userId,
          isSender: true,
          reciverId: this.vm.userId,
          createdAt: new Date(),
          id,
        });
        this.scrollToBottom();
      });
    });
  }

  updateNewMessage() {
    this.ngZone.run(() => {
      this.chatService.onNewMessage$
        .asObservable()
        .subscribe((response: any) => {
          this.messagesList.push({
            message: response.message,
            sender: this.userState$.userId,
            isSender: false,
            reciverId: this.vm.userId,
            createdAt: response.createdAt,
          });
          this.scrollToBottom();
        });
    });
  }

  scrollToBottom(): void {
    setTimeout(() => {
      const element: any = document.getElementById('scrollContainer');
      if (element) {
        element.scrollTop = element?.scrollHeight;
      }
    }, 1);
  }

  private assignSender(messages: any[]): any[] {
    return messages.map((message) => ({
      ...message,
      isSender:
        message.sender.toLowerCase() === this.userState$.userId.toLowerCase(),
    }));
  }

  getUserDetails(payload: any) {
    this.isLoading = true;
    this.account
      .getProfile(payload)
      .pipe(
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe((apiResponse: any) => {
        const { data, message, messageType } = apiResponse;
        if (messageType === 1) {
          this.toasterService.error(message);
          return this.router.navigate(['/home']);
        }
        this.getRatingData(data.userId);
        this.updateViewModel(data);
        return this.loadUserComments();
      });
  }

  loadUserComments() {
    this.account
      .getCompanyComments(this.vm.userId)
      .subscribe((response: any) => {
        this.vm.comments = response.data;
      });
  }

  gotoLink(link: string) {
    window.open('//' + link, '_blank');
  }

  // addToFavorite() {
  //   if (!this.vm.isFavorite) return this.toggleFavorite();
  //   this.modalService.openModal('confirmation', {
  //     initialState: {
  //       message: 'Are you sure you want remove from favorite ?',
  //       firstButtonText: 'Yes, remove',
  //       onConfirm: () => {
  //         this.toggleFavorite();
  //       },
  //     },
  //   });
  // }

  cancel(): void {
    this.modalService.closeModal();
  }

  toggleFavorite() {
    this.vm.isFavorite = !this.vm.isFavorite;
    const payload = {
      loginUserId: this.userState$.userId,
      expertId: this.vm.userId,
      isAdd: this.vm.isFavorite,
    };
    this.followLoading = true;
    this.account
      .addToFavorite(payload)
      .pipe(
        finalize(() => {
          this.followLoading = false;
          this.cancel();
        })
      )
      .subscribe((response: any) => {
        !response.messageType
          ? this.toasterService.success(response.message)
          : this.toasterService.error(response.message);
      });
  }

  connectUser(): any {
    if (
      `${this.userState$.userId}`.toLowerCase().toString() ==
      `${this.vm.userId}`.toLowerCase().toString()
    ) {
      return this.toasterService.info('You cannot send request to this admin');
    }
    const payload = {
      userId: this.vm.userId,
      followerUserId: this.userState$.aspNetUserId,
      loginUserId: this.userState$.userId,
    };
    this.connectLoading = true;
    this.account
      .connectExpert(payload)
      .pipe(finalize(() => (this.connectLoading = false)))
      .subscribe((response: any) => {
        if (response.messageType == DropMessageType.Success) {
          this.chatService.sendNotification(
            this.vm.userId,
            response.data.text,
            this.userState$.userId,
            response.data.id
          );
          this.toasterService.success(response.message);
          this.vm.isConnected = FollowStatus.InProgress;
          this.vm.admin.isRequestSent = true;
        } else if (
          response.messageType == DropMessageType.ConnectionLimitExceed
        ) {
          this.modalService.openModal('connection-limit-exceed', {
            class: 'modal-sm',
            initialState: {
              message: response.message,
            },
          });
        } else {
          this.toasterService.error(response.message);
        }
        if (
          response.messageType &&
          response.messageType == DEFAULT_CONNCTION_REQUEST_LIMIT
        ) {
          this.modalService.openModal('connection-limit-exceed');
        }
      });
  }

  postComment() {
    const payload = {
      id: EMPTY_GUID,
      userId: this.vm.userId,
      addCommentUserId: this.userState$.userId,
      parentCommentId: null,
      commentText: this.userCommentModel,
      commentDate: new Date(),
      Comment: 1,
      rating: this.ratingModel,
    };
    this.addingComment = true;
    this.account
      .postComment(payload)
      .pipe(finalize(() => (this.addingComment = false)))
      .subscribe((commentResponse) => {
        if (commentResponse) {
          this.toasterService.success('Comment Added successfully');
          (this.vm.comments as Array<any>).unshift({
            commentText: this.userCommentModel,
            commentDate: new Date(),
            addComentUserName: this.userState$.userName,
            addComentUserProfileImage: this.userState$.profilePhoto,
            userName: this.userState$.userName,
            userProfileImage: this.userState$.profilePhoto,
            childComments: [],
            ratting: this.ratingModel,
          });
          this.ratingModel = null;
          this.userCommentModel = null;
        }
      });
  }

  copyURL() {
    let url = document.location.href;

    navigator.clipboard.writeText(url).then(
      (response) => {
        this.isLinkedCopied = true;
        setTimeout(() => {
          this.isLinkedCopied = false;
        }, 2000);
      },
      (e) => { }
    );
  }

  navigateTo(url: string) {
    window.location.href = url;
  }

  async sendRequest() {
    this.hubService.changeFollowStatus(this.vm.userId, FollowStatus.InProgress);
  }

  // TODO API GIVES ERROR SOMETHING WENT WRONG
  onRatingChanged($event: any) {
    // const payload = {
    //   id: this.utils.getEmptyGuid(),
    //   userId: this.vm.userId, // page id
    //   userCommentId: null, // logged in
    //   expertUserId: this.vm.userId, // page id
    //   rating: $event,
    //   createdAt: new Date(),
    //   updatedAt: null,
    //   ratingType: 2,
    // };
    // this.account.addCompanyRating(payload).subscribe((response: any) => {
    //   if (response.messageType) {
    //     return this.toasterService.error('', response.message);
    //   } else {
    //     return this.toasterService.success('', response.message);
    //   }
    // });
  }

  navigateBack() {
    this.location.back();
  }

  viewExperts() {
    this.modalService.openModal('view-expert', {
      initialState: {
        experts: this.experts$,
        title: `${this.vm.companyName}'s Experts`,
      },
      class: 'modal-xl',
    });
  }

  approve() {
    this.expertService
      .SetConnectionApproval(this.vm.userId, this.userState$.userId)
      .subscribe((response: any) => {
        if (response.data?.item1) {
          this.vm.admin.isConnected = FollowStatus.Connected;
          this.toasterService.success(response.data.item2);
        }
      });
  }

  isPlaying: boolean = false;
  togglePlayPause(id: any) {
    const video = document.getElementById(id) as HTMLVideoElement;
    if (video.paused) {
      video.play();
      this.isPlaying = true;
    } else {
      video.pause();
      this.isPlaying = false;
    }
  }

  navigateToCompany() {
    this.router.navigate([`/details/${this.vm.companyId}/company`]);
  }

  getSocialMediaLinks(id: any) {
    this.account.getSocialMedia(id).subscribe((response: any) => {
      if (response.data?.length) {
        response.data.forEach((x: any) => {
          if (x.url) {
            x.imageUrl = this.socialMediaTypes[x.socialConnectionTypeId];
            this.socialMediaLinks.push(x);
          }
        });
        this.socialMediaLinks = response.data;
      }
    });
  }

  followUser() {
    this.followingLoading = true;
    this.http
      .post('follower/follow', {
        userId: this.vm.userId,
        followerUserId: this.userState$.userId,
      })
      .pipe(finalize(() => (this.followingLoading = false)))
      .subscribe((response: any) => {
        if (response.data) {
          this.vm.followers += 1;
          this.vm.isFollowing = true;
          this.chatService.sendFollowNotification(
            this.vm.userId,
            response.data.text,
            this.userState$.userId
          );
        }
      });
  }

  unfollowUser() {
    this.followingLoading = true;
    this.http
      .post('follower/unfollow', {
        userId: this.vm.userId,
        followerUserId: this.userState$.userId,
      })
      .pipe(finalize(() => (this.followingLoading = false)))
      .subscribe((response) => {
        if (this.vm.followers > 0) {
          this.vm.followers -= 1;
          this.vm.isFollowing = false;
        }
      });
  }

  onImageError() {
    this.selectedBannerImage = '../../../../../assets/svgs/bg.svg';
  }

  navigateToChat() {
    if (this.vm.admin.isConnected !== FollowStatus.Connected) {
      this.toasterService.info(
        `You are not connected yet with ${this.vm?.admin?.firstName}. Upon approval, you will have access to the chat and other features.`
      );
    } else {
      this.router.navigate(['/my-account/chat']);
    }
  }

  activeTab: string = 'aboutus';

  setActiveTab(tab: string) {
    this.activeTab = tab;
  }

  focusExpertise() {
    document.getElementById('expertise')?.focus();
  }

  showAll: boolean = false;

  toggleShowAll(): void {
    this.showAll = !this.showAll;
  }

  addToFavorite(): void {
    if (!this.vm.isFavorite) {
      this.account.getAddFavorite(this.expertId).subscribe({
        next: (res) => {
          this.vm.isFavorite = true;
          this.toasterService.success('Added to favorites');
        },
        error: (err) => {
          console.error('Add favorite failed', err);
          this.toasterService.error('Failed to add to favorites');
        },
      });
    } else {
      this.account.getRemoveFavorite(this.expertId).subscribe({
        next: (res) => {
          this.vm.isFavorite = false;
          this.toasterService.success('Removed from favorites');
        },
        error: (err) => {
          console.error('Remove favorite failed', err);
          this.toasterService.error('Failed to remove from favorites');
        },
      });
    }
  }
  goBack(): void {
    this.location.back(); // Navigates to the previous page in the browser history
  }

  getExpertiseString(): string {
    if (!this.vm.expertise || !Array.isArray(this.vm.expertise)) {
      return '';
    }
    return this.vm.expertise.map((item: any) => item.name).join(' | ');
  }
  // addToFavorite(): void {
  //   const payload = {
  //     loginUserId: this.userState$.userId,
  //     expertId: this.expertId,
  //   };

  //   if (!this.vm.isFavorite) {
  //     this.account.getAddFavorite(payload).subscribe({
  //       next: (res) => {
  //         this.vm.isFavorite = true;
  //         this.toasterService.success('Added to favorites');
  //       },
  //       error: (err) => {
  //         console.error('Add favorite failed', err);
  //         this.toasterService.error('Failed to add to favorites');
  //       },
  //     });
  //   } else {
  //     this.account.getRemoveFavorite(payload).subscribe({
  //       next: (res) => {
  //         this.vm.isFavorite = false;
  //         this.toasterService.success('Removed from favorites');
  //       },
  //       error: (err) => {
  //         console.error('Remove favorite failed', err);
  //         this.toasterService.error('Failed to remove from favorites');
  //       },
  //     });
  //   }
  // }
}
