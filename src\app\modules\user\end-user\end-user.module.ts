import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgSelectModule } from '@ng-select/ng-select';
import { CarouselModule } from 'ngx-bootstrap/carousel';
import { PopoverModule } from 'ngx-bootstrap/popover';
import { RatingModule } from 'ngx-bootstrap/rating';
import { LayoutModule } from 'src/app/layout/layout.module';
import { ExpertConnectCardComponent } from 'src/app/shared/components/expert-connect-card/expert-connect-card.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { CopyrightsPolicyComponent } from '../../account/copyrights-policy/copyrights-policy.component';
import { AboutUsComponent } from '../components/about-us/about-us.component';
import { BecomePartnerComponent } from '../components/become-partner/become-partner.component';
import { CodeOfConductComponent } from '../components/code-of-conduct/code-of-conduct.component';
import { ConnectedBySolutionsComponent } from '../components/connected-by-solutions/connected-by-solutions.component';
import { ConnectionsComponent } from '../components/connections/connections.component';
import { ContactUsComponent } from '../components/contact-us/contact-us.component';
import { CookiePolicyComponent } from '../components/cookie-policy/cookie-policy.component';
import { ECardsBySolutionComponent } from '../components/e-cards-by-solution/e-cards-by-solution.component';
import { ExpertLocatorComponent } from '../components/expert-locator/expert-locator.component';
import { FocileSearchComponent } from '../components/focile-search/focile-search.component';
import { FreeTrialPromoteComponent } from '../components/free-trial-promote/free-trial-promote.component';
import { LegalPolicyComponent } from '../components/legal-policy/legal-policy.component';
import { MediaUsageComponent } from '../components/media-usage/media-usage.component';
import { OurCompanyComponent } from '../components/our-company/our-company.component';
import { PartnershipComponent } from '../components/partnership/partnership.component';
import { PrivayPolicyComponent } from '../components/privay-policy/privay-policy.component';
import { ProfileOverviewComponent } from '../components/profile-overview/profile-overview.component';
import { RecentlyJoinedPartnersComponent } from '../components/recently-joined-partners/recently-joined-partners.component';
import { SubscriptionPlansComponent } from '../components/subscription-plans/subscription-plans.component';
import { TermsAndConditionsComponent } from '../components/terms-and-conditions/terms-and-conditions.component';
import { TrademarkPolicyComponent } from '../components/trademark-policy/trademark-policy.component';
import { UserAccountsInfoComponent } from '../components/user-accounts-info/user-accounts-info.component';
import { UserDetailsComponent } from '../components/user-details/user-details.component';
import { EndUserShellComponent } from './end-user-shell/end-user-shell.component';
import { ViewExpertsComponent } from 'src/app/shared/modals/view-experts/view-experts.component';
import { PaymentSuccessComponent } from '../components/payment-success/payment-success.component';
import { PaymentCancelComponent } from '../components/payment-cancel/payment-cancel.component';
import { TabsModule } from 'ngx-bootstrap/tabs';
import { AccessibilityComponent } from '../components/accessibility/accessibility.component';
import { ApiComponent } from '../components/api/api.component';
import { AgreementsComponent } from '../components/agreements/agreements.component';
import { AngularCropperjsModule } from 'angular-cropperjs';
import { BlogListComponent } from '../components/blog-list/blog-list.component';
import { BlogDetailComponent } from '../components/blog-detail/blog-detail.component';
import { OurCultureComponent } from '../components/about-us/our-culture/our-culture.component';
import { OurValuesComponent } from '../components/about-us/our-values/our-values.component';
import { AboutFocileComponent } from '../components/about-us/about-focile/about-focile.component';
import { OurCommitmentComponent } from '../components/about-us/our-commitment/our-commitment.component';
import { BulkInviteComponent } from 'src/app/shared/components/bulk-invite/bulk-invite.component';
import { RecrutingChannelComponent } from '../components/top-categories/recruting-channel/recruting-channel.component';
import { LeverageFocileComponent } from '../components/top-categories/leverage-focile/leverage-focile.component';
import { CreatedProfileComponent } from '../components/top-categories/created-profile/created-profile.component';
import { DirectConnectionComponent } from '../components/top-categories/direct-connection/direct-connection.component';
import { ProcessComponent } from '../components/key-features/process/process.component';
import { ECardMarketingComponent } from '../components/key-features/e-card-marketing/e-card-marketing.component';
import { TargetAudienceComponent } from '../components/key-features/target-audience/target-audience.component';
import { WinWithConfidenceComponent } from '../components/key-features/win-with-confidence/win-with-confidence.component';
import { JoinHelpCardComponent } from 'src/app/shared/components/join-help-card/join-help-card.component';
import { ConnectedSolutionsComponent } from '../components/connected-solutions/connected-solutions.component';
import { PlatformComponent } from '../components/platform/platform.component';
import { FrequentlyAskedQuestionsComponent } from '../components/frequently-asked-questions/frequently-asked-questions.component';
import { PromoteYourBrandComponent } from '../components/top-categories/promote-your-brand/promote-your-brand.component';
import { ValuePropositionComponent } from '../components/top-categories/value-proposition/value-proposition.component';
import { AdminEcardPortalComponent } from '../components/admin-ecard-portal/admin-ecard-portal.component';
import { PartnerEngagementComponent } from '../components/top-categories/partner-engagement/partner-engagement.component';
import { RecentJoinedPartnersTestimonailComponent } from '../components/recent-joined-partners-testimonail/recent-joined-partners-testimonail.component';
import { OurBlogComponent } from '../components/our-blog/our-blog.component';
import { EndUserOverviewExpertComponent } from '../components/end-user-overview-expert/end-user-overview-expert.component';
@NgModule({
  declarations: [
    EndUserShellComponent,
    ContactUsComponent,
    BecomePartnerComponent,
    UserDetailsComponent,
    ExpertConnectCardComponent,
    SubscriptionPlansComponent,
    RecentlyJoinedPartnersComponent,
    ConnectedBySolutionsComponent,
    FreeTrialPromoteComponent,
    ECardsBySolutionComponent,
    ViewExpertsComponent,
    AgreementsComponent,
    TermsAndConditionsComponent,
    AccessibilityComponent,
    ApiComponent,
    CodeOfConductComponent,
    BulkInviteComponent,
    JoinHelpCardComponent,
    ConnectedSolutionsComponent,
    RecentJoinedPartnersTestimonailComponent,
    OurBlogComponent,
    EndUserOverviewExpertComponent,
    PaymentSuccessComponent
  ],
  imports: [
    CommonModule,
    LayoutModule,
    SharedModule,
    PopoverModule,
    FormsModule,
    ReactiveFormsModule,
    NgSelectModule,
    RatingModule.forRoot(),
    CarouselModule.forRoot(),
    PaymentCancelComponent,
    TabsModule.forRoot(),
    AngularCropperjsModule,
    RouterModule,
    PaymentCancelComponent,
    RouterModule.forChild([
      {
        path: '',
        component: EndUserShellComponent,
        children: [
          {
            path: '',
            pathMatch: 'full',
            redirectTo: 'home',
          },
          {
            path: 'home',
            component: FocileSearchComponent,
          },
          {
            path: 'contact-us',
            component: ContactUsComponent,
          },
          {
            path: 'become-partner',
            component: BecomePartnerComponent,
          },
          {
            path: 'privacy-policy',
            component: PrivayPolicyComponent,
          },
          {
            path: 'terms-and-condtions',
            component: TermsAndConditionsComponent,
          },
          {
            path: 'details/:id/:type',
            component: UserDetailsComponent,
          },
          {
            path: 'about-us',
            component: AboutUsComponent,
          },
          {
            path: 'expert-locator',
            component: ExpertLocatorComponent,
          },
          {
            path: 'subscription-plans',
            component: SubscriptionPlansComponent,
          },
          {
            path: 'profile-overview',
            component: ProfileOverviewComponent,
          },
          {
            path: 'our-company',
            component: OurCompanyComponent,
          },
          {
            path: 'partnership',
            component: PartnershipComponent,
          },
          {
            path: 'user-accounts-info',
            component: UserAccountsInfoComponent,
          },
          {
            path: 'connections',
            component: ConnectionsComponent,
          },
          {
            path: 'trademark-policy',
            component: TrademarkPolicyComponent,
          },
          {
            path: 'media-usage',
            component: MediaUsageComponent,
          },
          {
            path: 'cookies-policy',
            component: CookiePolicyComponent,
          },
          {
            path: 'legal-policy',
            component: LegalPolicyComponent,
          },
          {
            path: 'privacy-policy',
            component: PrivayPolicyComponent,
          },
          {
            path: 'code-of-conduct',
            component: CodeOfConductComponent,
          },
          {
            path: 'copyrights',
            component: CopyrightsPolicyComponent,
          },
          {
            path: 'accessibility',
            component: AccessibilityComponent,
          },
          {
            path: 'agreements',
            component: AgreementsComponent,
          },
          {
            path: 'api',
            component: ApiComponent,
          },
          {
            path: 'solution/:id',
            component: ECardsBySolutionComponent,
          },
          {
            path: 'payment-success',
            component: PaymentSuccessComponent,
          },
          {
            path: 'payment-cancel',

            component: PaymentCancelComponent,
          },
          {
            path: 'blog-list',
            component: BlogListComponent,
            data: { breadcrumb: 'blog-list' }
          },
          {
            path: 'blog-detail/:slug',
            component: BlogDetailComponent,
            data: { breadcrumb: 'blog-detail' }
          },
          {
            path: 'about-us/our-culture',
            component: OurCultureComponent,
            data: { breadcrumb: 'About-us' }
          },
          {
            path: 'about-us/our-vaules',
            component: OurValuesComponent,
            data: { breadcrumb: 'About-us' }
          },
          {
            path: 'about-us/our-focile',
            component: AboutFocileComponent,
            data: { breadcrumb: 'About-us' }
          },
          {
            path: 'about-us/our-commitment',
            component: OurCommitmentComponent,
            data: { breadcrumb: 'About-us' }
          },
          {
            path: 'simplified-process',
            component: RecrutingChannelComponent,
          },
          {
            path: 'leverage-focile',
            component: LeverageFocileComponent
          },
          {
            path: 'create-detailed-profile',
            component: CreatedProfileComponent
          },
          {
            path: 'direct-connection',
            component: DirectConnectionComponent
          },
          {
            path: 'promote-your-brand',
            component: PromoteYourBrandComponent
          },
          {
            path: 'value-proposition',
            component: ValuePropositionComponent
          },
          {
            path: 'key-feature/process',
            component: ProcessComponent,
            data: { breadcrumb: 'key-feature' }
          },
          {
            path: 'key-feature/e-card-marketing',
            component: ECardMarketingComponent,
            data: { breadcrumb: 'e-card' }
          },
          {
            path: 'key-feature/target-audience',
            component: TargetAudienceComponent,
            data: { breadcrumb: 'target-audience' }
          },
          {
            path: 'key-feature/win-with-confidence',
            component: WinWithConfidenceComponent,
            data: { breadcrumb: 'win-with-confidence' }
          },
          {
            path: 'platform',
            component: PlatformComponent
          },
          {
            path: 'faq',
            component: FrequentlyAskedQuestionsComponent
          },
          {
            path: 'admin-ecard-portal-access',
            component: AdminEcardPortalComponent,
          },
          {
            path: 'partner-engagement',
            component: PartnerEngagementComponent,
          },
          {
            path: 'enduser-details/:id',
            component: EndUserOverviewExpertComponent,
          },
          // {
          //   path: '**',
          //   redirectTo: 'home',
          // }
        ],
      },
    ]),
  ],
  exports: [
    NgSelectModule,
    RecentlyJoinedPartnersComponent,
    ConnectedBySolutionsComponent,
    FreeTrialPromoteComponent,
    ViewExpertsComponent,
    BulkInviteComponent,
    JoinHelpCardComponent,
    ConnectedSolutionsComponent,
    RecentJoinedPartnersTestimonailComponent,
    OurBlogComponent
  ],
})
export class EndUserModule { }