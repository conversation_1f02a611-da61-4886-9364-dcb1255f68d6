import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'phoneFormat',
})
export class PhoneFormatPipe implements PipeTransform {
  transform(value: string): string {
    if (!value) return value;
    // Remove any non-digit characters from the input
    const digitsOnly = value.replace(/\D/g, '');

    // Split the digits into groups of 3, 3, and 4
    const groups = digitsOnly.match(/(\d{3})(\d{3})(\d{4})/);
    if (groups?.length) {
      // Join the groups with spaces and return the formatted phone number
      return groups.slice(1).join('-');
    } else {
      const numericValue =
        typeof value === 'string' ? parseInt(value, 10) : value;
      if (!isNaN(numericValue)) {
        // Convert the number to a string and format it in groups of 3 using regular expressions
        return numericValue.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '-');
      }
      // If the input is empty or invalid, return it as is
      return value;
    }
  }
}
