<div class="fc-blog-card">
    <img [src]="blog.image" alt="Blog Image" />
    <div class="fc-overlay-text">
      <div class="post-date">{{ blog.author }} • {{ blog.date }}</div>
      <div class="fc-related-blog">
        <span *ngFor="let tag of blog.tags">{{ tag }}</span>
      </div>
      <div class="fc-related-name">
        {{ blog.title }}
        <span>
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M7 17L17 7M17 7H7M17 7V17"
              stroke="white"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </span>
      </div>
    </div>
  </div>
  