<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
      <li
        *ngFor="let item of breadcrumbItems; let isLast = last"
        class="breadcrumb-item"
        [ngClass]="{ 'active': isLast }"
        [attr.aria-current]="isLast ? 'page' : null">
        <!-- <a *ngIf="!isLast; else lastItem" [routerLink]="item.url">{{ item.label }}</a> -->
        <ng-template #lastItem>{{ item.label }}</ng-template>
      </li>
    </ol>
  </nav>    