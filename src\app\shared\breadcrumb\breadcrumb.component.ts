import { Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { filter, map } from 'rxjs/operators';

@Component({
  selector: 'app-breadcrumb',
  templateUrl: './breadcrumb.component.html',
  styleUrls: ['./breadcrumb.component.scss']
})
export class BreadcrumbComponent {
  @Input() breadcrumbItems: { label: string; url: string }[] = [];

  constructor(private router: Router, private activatedRoute: ActivatedRoute) {}

  ngOnInit(): void {
    if (this.breadcrumbItems.length === 0) {
      this.generateBreadcrumbs();
    }
  }

  private generateBreadcrumbs(): void {
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        map(() => this.activatedRoute),
        map((route) => {
          const breadcrumbs = [];
          while (route.firstChild) {
            route = route.firstChild;
            if (route.snapshot.data['breadcrumb']) {
              breadcrumbs.push({
                label: route.snapshot.data['breadcrumb'],
                url: route.snapshot.url.join('/'),
              });
            }
          }
          return breadcrumbs;
        })
      )
      .subscribe((breadcrumbs) => {
        this.breadcrumbItems = breadcrumbs;
      });
  }
}
