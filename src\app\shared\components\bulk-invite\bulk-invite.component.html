<!-- <div class="my-5">
  <div class="position-relative rounded-5">
    <img
      (load)="onLoad()"
      src="./assets/images/bulk-invite-banner.jpg"
      class="w-100 banner-image"
    />
    <div
      class="invite-container bg-primary p-0 pt-2 px-3 rounded-5 shadow-lg py-4"
      *ngIf="isLoaded"
    >
      <h3 class="text-white text-center">
        Invite colleagues, Experts and Partners. <br />Make wise connections!
      </h3>
      <p class="text-white m-0 text-center">
        Make Network Connections for Better Solution Selections.
      </p>
      <p class="text-white text-center">
        Extend your choices and leverage your connections!
      </p>
      <div class="row mb-3">
        <div class="col-md-6">
          <input
            id="bulkInviteFirstName"
            class=""
            placeholder="First Name"
            [(ngModel)]="firstName"
          />
        </div>
        <div class="col-md-6">
          <input
            id="bulkInviteLastName"
            class=""
            placeholder="Last Name"
            [(ngModel)]="lastName"
          />
        </div>
      </div>
      <div class="position-relative">
        <input
          id="bulkInviteEmail"
          class=""
          [(ngModel)]="emails"
          placeholder="Enter Email"
        />
        <button
          class="invite-button btn position-absolute rounded-5"
          [disabled]="inviting || !user"
          (click)="inviteUsers()"
        >
          <span>{{ !inviting ? "Invite" : "Inviting" }}</span>
        </button>
        <p *ngIf="invitationSuccess" class="text-white">
          Invitation Sent Successfully.
        </p>
        <p *ngIf="!user" class="text-white mt-3 ms-2">
          Please log in to your account to send an invitation.
        </p>
      </div>
    </div>
  </div>
</div> -->


<div class="invite-favorites-wrap">
  <div class="invite-section">
    <div class="dot-matrik"></div>
    <div class="invite-header">
      <label>Invite to Favorites</label>
      <h4>Invite colleagues, Experts and Partners.Make wise connections!</h4>
    </div>
    <div class="special-text">
      Make Network Connections for Better Solution Selections.
Extend your choices and leverage your connections!  
    </div>
    <div class="fc-form-fill">
      <div class="fc-form-control">
        <span>
          <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M12 12.5C10.35 12.5 8.9375 11.9125 7.7625 10.7375C6.5875 9.5625 6 8.15 6 6.5C6 4.85 6.5875 3.4375 7.7625 2.2625C8.9375 1.0875 10.35 0.5 12 0.5C13.65 0.5 15.0625 1.0875 16.2375 2.2625C17.4125 3.4375 18 4.85 18 6.5C18 8.15 17.4125 9.5625 16.2375 10.7375C15.0625 11.9125 13.65 12.5 12 12.5ZM0 21.5V20.3C0 19.45 0.21875 18.6687 0.65625 17.9562C1.09375 17.2437 1.675 16.7 2.4 16.325C3.95 15.55 5.525 14.9687 7.125 14.5812C8.725 14.1937 10.35 14 12 14C13.65 14 15.275 14.1937 16.875 14.5812C18.475 14.9687 20.05 15.55 21.6 16.325C22.325 16.7 22.9062 17.2437 23.3438 17.9562C23.7812 18.6687 24 19.45 24 20.3V21.5C24 22.325 23.7063 23.0313 23.1188 23.6188C22.5313 24.2063 21.825 24.5 21 24.5H3C2.175 24.5 1.46875 24.2063 0.88125 23.6188C0.29375 23.0313 0 22.325 0 21.5Z"
              fill="#53525C" />
          </svg>                        
        </span>
        <input
        id="bulkInviteFirstName"
        class=""
        placeholder="First Name"
        [(ngModel)]="firstName"
      />
      </div>

      <div class="fc-form-control">
        <span>
          <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M12 12.5C10.35 12.5 8.9375 11.9125 7.7625 10.7375C6.5875 9.5625 6 8.15 6 6.5C6 4.85 6.5875 3.4375 7.7625 2.2625C8.9375 1.0875 10.35 0.5 12 0.5C13.65 0.5 15.0625 1.0875 16.2375 2.2625C17.4125 3.4375 18 4.85 18 6.5C18 8.15 17.4125 9.5625 16.2375 10.7375C15.0625 11.9125 13.65 12.5 12 12.5ZM0 21.5V20.3C0 19.45 0.21875 18.6687 0.65625 17.9562C1.09375 17.2437 1.675 16.7 2.4 16.325C3.95 15.55 5.525 14.9687 7.125 14.5812C8.725 14.1937 10.35 14 12 14C13.65 14 15.275 14.1937 16.875 14.5812C18.475 14.9687 20.05 15.55 21.6 16.325C22.325 16.7 22.9062 17.2437 23.3438 17.9562C23.7812 18.6687 24 19.45 24 20.3V21.5C24 22.325 23.7063 23.0313 23.1188 23.6188C22.5313 24.2063 21.825 24.5 21 24.5H3C2.175 24.5 1.46875 24.2063 0.88125 23.6188C0.29375 23.0313 0 22.325 0 21.5Z"
              fill="#53525C" />
          </svg>                        
        </span>
        <input
        id="bulkInviteLastName"
        class=""
        placeholder="Last Name"
        [(ngModel)]="lastName"
      />
      </div>

      <div class="fc-form-control">
        <span>
          <svg width="28" height="25" viewBox="0 0 28 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd"
              d="M20.586 0.5C22.374 0.5 24.094 1.20667 25.3594 2.47467C26.626 3.74 27.334 5.44667 27.334 7.23333V17.7667C27.334 21.4867 24.3074 24.5 20.586 24.5H7.41403C3.69269 24.5 0.667358 21.4867 0.667358 17.7667V7.23333C0.667358 3.51333 3.67936 0.5 7.41403 0.5H20.586ZM22.7074 9.22L22.814 9.11333C23.1327 8.72667 23.1327 8.16667 22.7994 7.78C22.614 7.58133 22.3594 7.46 22.094 7.43333C21.814 7.41867 21.5474 7.51333 21.346 7.7L15.334 12.5C14.5607 13.1413 13.4527 13.1413 12.6674 12.5L6.66736 7.7C6.25269 7.39333 5.67936 7.43333 5.33403 7.79333C4.97403 8.15333 4.93403 8.72667 5.23936 9.12667L5.41403 9.3L11.4807 14.0333C12.2274 14.62 13.1327 14.94 14.0807 14.94C15.026 14.94 15.9474 14.62 16.6927 14.0333L22.7074 9.22Z"
              fill="#191825" fill-opacity="0.75" />
          </svg>                              
        </span>
        <input
        id="bulkInviteEmail"
        class=""
        [(ngModel)]="emails"
        placeholder="Enter Work Email"
      />
      </div>

      <div class="fc-form-control">
        <span>
          <svg width="28" height="25" viewBox="0 0 28 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M23.8116 13.4093C22.024 14.7499 19.8418 15.6806 17.4667 16.1398C17.2268 17.8386 15.7641 19.1499 14 19.1499C12.2359 19.1499 10.7732 17.8386 10.5333 16.1398C8.15823 15.6806 5.97598 14.7499 4.18835 13.4093C1.48745 11.3837 0 8.6684 0 5.76367V20.6655C0 22.8931 1.8124 24.7054 4.04004 24.7054H23.96C26.1876 24.7054 28 22.8931 28 20.6655V5.76367C28 8.6684 26.5126 11.3837 23.8116 13.4093Z"
              fill="#191825" fill-opacity="0.75" />
            <path
              d="M10.4981 14.4555V12.9655C10.4981 12.5124 10.8654 12.1452 11.3184 12.1452H16.6816C17.1346 12.1452 17.5019 12.5124 17.5019 12.9655V14.4555C22.6167 13.3453 26.3594 9.86872 26.3594 5.76249V4.94221H19.7627V3.31214C19.7627 1.48581 18.2768 0 16.4504 0H11.5496C9.72327 0 8.23736 1.48581 8.23736 3.31214V4.94221H1.64062V5.76249C1.64062 9.86872 5.38333 13.3453 10.4981 14.4555ZM9.87793 3.31214C9.87793 2.39041 10.6279 1.64057 11.5496 1.64057H16.4504C17.3721 1.64057 18.122 2.39041 18.122 3.31214V4.94221H9.87787V3.31214H9.87793Z"
              fill="#191825" fill-opacity="0.75" />
            <path
              d="M15.8611 13.7861H12.1385V15.6474C12.1385 16.6736 12.9736 17.5086 13.9998 17.5086C15.0261 17.5086 15.8611 16.6736 15.8611 15.6474V13.7861Z"
              fill="#191825" fill-opacity="0.75" />
          </svg>
                                                       
        </span>
        <input
        id="bulkInviteCompanyName"
        class=""
        placeholder="Company Name"
        [(ngModel)]="comanyName"
      />
      </div>
    </div>

    <div class="d-flex flex-row align-items-center flex-column">
      <button
      class="invite-button"
      [disabled]="!isFormValid() || inviting || !user"
      (click)="inviteUsers()"
    >
      <span>{{ !inviting ? "Ask To Join" : "Inviting" }}</span>
    </button>
    <p *ngIf="invitationSuccess" class="text-black">
      Invitation Sent Successfully.
    </p>
    <p *ngIf="!user" class="text-black mt-3 ms-2">
      Please log in to your account to send an invitation.
    </p>
    </div>
  </div>
</div>
