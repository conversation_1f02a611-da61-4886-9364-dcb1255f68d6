#buldInvite::placeholder {
  color: white;
}
.banner-image {
  display: block;
}

.invite-container {
  position: absolute;
  right: 50px;
  top: 35px;
  height: fit-content;
  width: 550px;
}

.invite-input {
  height: 63px;
  background: transparent;
  border: white 1px solid;
  color: white;
  padding: 13px 17px;
}

.invite-input::placeholder {
  color: white;
}

.invite-button {
  border: white 1px solid;
  background: white;
  color: var(--bs-color-primary);
  height: 63px;
  width: 100px;
  right: 0;

  &:disabled {
    opacity: 0.7;
  }
}

@media (max-width: 768px) {
  .banner-image {
    display: none;
  }

  .invite-container {
    position: static;
    width: 90%;
    margin: 0 auto;
    top: 20px;
    // height: 386px;
  }

  .invite-input {
    height: 50px;
    padding: 10px 15px;
  }

  .invite-button {
    height: 50px;
    width: 80px;
  }
}

@media (max-width: 576px) {
  .invite-container {
    position: static;
    width: 100%;
    top: 10px;
    padding: 10px;
    // height: 355px;
  }

  .invite-input {
    height: 40px;
    padding: 8px 10px;
  }

  .invite-button {
    height: 40px;
    width: 70px;
  }
}

/* Invite to NewDesign */

.invite-favorites-wrap {
  width: 100%;
  background: url(../../../../assets/images/half-cirle.svg) no-repeat 100% 100%;
  padding-block: 131px;
}
.invite-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 128px 64px 64px;
  gap: 47px;
  width: 1110px;
  min-height: 957px;
  background: #fef7e2;
  border-radius: 32px;
  margin: 0px auto;
  position: relative;

  .dot-matrik {
    position: absolute;
    width: 190px;
    height: 177px;
    top: -51px;
    left: -51px;
    z-index: -1;
    background: url(../../../../assets/images/dot-pattern.svg) no-repeat center
      center;
  }
}
.invite-header {
  label {
    width: 982px;
    height: 28px;
    font-style: normal;
    font-weight: 700;
    font-size: 23px;
    line-height: 120%;
    text-align: center;
    letter-spacing: 0.2em;
    text-transform: uppercase;
    color: #014681;
    flex: none;
    order: 0;
    flex-grow: 0;
    margin-bottom: 2rem;
  }

  h4 {
    width: 982px;
    font-style: normal;
    font-weight: 700;
    font-size: 44px;
    line-height: 120%;
    text-align: center;
    color: #191825;
    flex: none;
    order: 1;
    flex-grow: 0;
    margin: 0px auto;
  }
}
.special-text {
  width: 559px;
  font-style: normal;
  font-weight: 300;
  font-size: 20px;
  line-height: 180%;
  text-align: center;
  color: #000000;
  flex: none;
  order: 0;
  flex-grow: 0;
}

.fc-form-fill {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 64px;
  row-gap: 40px;

  .fc-form-control {
    width: 100%;
    position: relative;

    input {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 0px 32px 0px 90px;
      gap: 16px;
      width: 405px;
      height: 117px;
      background: #ffffff;
      border-radius: 32px;
      flex: none;
      order: 0;
      flex-grow: 0;
      border: none;
      font-size: 23px;
      color: #191825;
      font-weight: 600;

      &::placeholder {
        color: rgb(25 24 37 / 75%);
      }
      &:hover {
        border: none;
        outline: none;
      }
    }

    span {
      position: absolute;
      left: 32px;
      top: 46px;
    }
  }
}
.invite-button {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 16px;
  width: 256px;
  height: 104px;
  background: #014681;
  border-radius: 100px;
  flex: none;
  order: 3;
  flex-grow: 0;
  justify-content: center;
  font-size: 23px;
  color: white;
  border: none;
  font-weight: 600;
}

#invitationSentModal {
  align-items: center;

  &.show {
    display: flex !important;
    opacity: 1;
    align-items: center;
  }
}

#toast-container {
  z-index: 1111 !important;

  &.toast-message {
    z-index: 1111 !important;
  }
}

@media (max-width: 768px) {
  .invite-section {
    width: 90%;
    padding: 2rem 1rem;
    min-height: auto;
    gap: 1rem;
    border-radius: 1rem;
  }
  .invite-header {
    label {
      width: 100%;
      height: auto;
      margin-bottom: 1rem;
      font-size: 1rem;
    }
    h4 {
      width: 100%;
      font-size: 1.25rem;
      line-height: 1.5;
    }
  }
  .fc-form-fill {
    grid-template-columns: repeat(1, 1fr);
    gap: 1rem;
    row-gap: 1.5rem;
    width: 100%;

    .fc-form-control {
      input {
        width: 100%;
        padding: 1rem;
        font-size: 1rem;
        font-weight: 500;
        border-radius: 0.5rem;
        height: auto;
        padding-left: 3rem;
      }

      span {
        left: 12px;
        top: 15px;
        svg {
          width: 25px;
          height: auto;
        }
      }
    }
  }
  .special-text {
    width: 100%;
    font-size: 14px;
  }
  .invite-favorites-wrap {
    padding-block: 43px;
    margin-top: 3rem;
  }
  .invite-button {
    padding: 1rem 2rem;
    border-radius: 50px;
    height: auto;
    width: auto;
    font-size: 1.2rem;
    line-height: 1.5;
  }

  .invite-section {
    .dot-matrik {
      top: -38px;
      left: -23px;
    }
  }
}
