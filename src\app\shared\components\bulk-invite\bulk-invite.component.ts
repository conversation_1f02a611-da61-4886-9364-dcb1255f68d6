import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { finalize } from 'rxjs';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { HttpClient } from '@angular/common/http';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { filterNonNull } from '../../oprators/filter-null-values';
import { DropMessageType } from '../../constant';
import { ModalService } from '../../services/modal.service';

@Component({
  selector: 'app-bulk-invite',
  templateUrl: './bulk-invite.component.html',
  styleUrls: ['./bulk-invite.component.scss'],
})
export class BulkInviteComponent implements OnInit {
  emails = '';
  isLoaded = false;
  inviting = false;
  invitationSuccess = false;
  user: any;
  firstName = '';
  lastName = '';
  comanyName = '';
  modalRef?: BsModalRef;
  constructor(
    private account: AccountService,
    private http: HttpClient,
    private readonly toaster: ToastrService,
    private modalService: ModalService
  ) {}
  ngOnInit(): void {
    this.account.user$.pipe(filterNonNull()).subscribe((user) => {
      this.user = user;
    });
  }
  inviteUsers() {
    this.inviting = true;
    const payload: BulkInviteType = {
      email: this.emails,
      receiverName: `${this.firstName} ${this.lastName}`,
      senderName: this.user.userName.split(' ')[0],
      senderLastName: this.user.userName.split(' ')[1],
      senderCompanyName: this.user.comanyName,
      receiverCompany: this.comanyName,
      receiverEmail: this.emails,
      receiverLastName: this.lastName,
      senderEmail: this.user.email,
    };
    this.http
      .post('User/SendInvitation', payload)
      .pipe(finalize(() => (this.inviting = false)))
      .subscribe((response: any) => {
        if (response.data) {
          this.emails = '';
          this.firstName = '';
          this.lastName = '';
          this.comanyName = '';
          this.toaster.success(response.message);
        } else if (
          response.messageType == DropMessageType.ConnectionLimitExceed
        ) {
          this.modalService.openModal('connection-limit-exceed', {
            class: 'modal-sm',
            initialState: {
              message: response.message,
            },
          });
        }
      });
  }
  isFormValid(): boolean {
    return (
      this.emails.trim() !== '' &&
      this.firstName.trim() !== '' &&
      this.lastName.trim() !== '' &&
      this.comanyName.trim() !== ''
    );
  }
  onLoad() {
    this.isLoaded = true;
  }
}

export interface BulkInviteType {
  id?: string;
  createdAt?: Date;
  createdBy?: string;
  updatedAt?: Date;
  updatedBy?: string;
  deletedAt?: Date;
  deletedBy?: string;
  body?: string;
  subject?: string;
  cc?: string;
  userId?: string;
  senderLastName?: string;
  senderEmail?: string;
  dateSent?: Date;
  receiverLastName?: string;
  receiverCompany?: string;
  receiverEmail?: string;
  dateReceived?: Date;
  creditPoints?: number;
  registered?: boolean;
  email?: string;
  receiverName?: string;
  senderName?: string;
  senderCompanyName?: string;
}
