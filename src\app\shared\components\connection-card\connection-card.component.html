<div class="col-sm-4 fc-favorite-list">
  <div class="fc-favorite-card" (click)="gotoDetails()" role="button">
    <span
      class="fc-favorite-i"
      *ngIf="isLoggedIn$"
      (click)="addToFavorite(); $event.stopPropagation()"
      [ngClass]="{ 'active': company.isFavorite }"
      style="cursor:pointer"
      [tooltip]="company.isFavorite ? 'Remove from favorites' : 'Add to favorites'"
    >
      <i
        class="fa-heart"
        [ngClass]="{ 'fa': company.isFavorite, 'far': !company.isFavorite }"
        style="color: #ffffff; font-size: 13px;"
      ></i>
    </span>
    <figure class="fc-favorite-card__figure">
      <!-- <img [src]="company['companyImage'] ? company['companyImage'] : 'assets/images/user-avatar.svg'" alt="{{ company.companyName }}" alt="" /> -->

      <img [src]="company['companyImage'] ? company['companyImage'] : 'assets/images/user-avatar.svg'"
        alt="Company Profile Photo" onerror="this.src='assets/images/user-avatar.svg'" />

    </figure>
    <h6 class="text-truncate-2-lines">{{ company.companyName }}</h6>
    <div class="expertise-list">
      <span *ngFor="let e of company.expertise | slice : 0 : 3; let i = index">
        <div (click)="gotoDetails()">
          <span *ngIf="i !== 0" (click)="gotoDetails()"> || </span>{{ e.name }}
        </div>
      </span>
    </div>
    <div class="d-flex flex-column gap-2 w-100">
      <div class="fc-call-btn">
        <span class="call-icon">
          <svg _ngcontent-rao-c17="" width="13" height="13" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path _ngcontent-rao-c17="" d="M4 20C3.45 20 2.97917 19.8042 2.5875 19.4125C2.19583 19.0208 2 18.55 2 18V6C2 5.45 2.19583 4.97917 2.5875 4.5875C2.97917 4.19583 3.45 4 4 4H20C20.55 4 21.0208 4.19583 21.4125 4.5875C21.8042 4.97917 22 5.45 22 6V18C22 18.55 21.8042 19.0208 21.4125 19.4125C21.0208 19.8042 20.55 20 20 20H4ZM12 13L4 8V18H20V8L12 13ZM12 11L20 6H4L12 11ZM4 8V6V18V8Z" fill="white"></path></svg>
        </span>
        <span class="company-email">{{company.email}}</span>
      </div>
      <div class="fc-call-btn">
        <span class="call-icon">
          <svg width="10" height="10" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M11.7752 12.3332C10.4249 12.3332 9.09076 12.0388 7.77286 11.4501C6.45496 10.8613 5.25588 10.0268 4.17564 8.9466C3.09539 7.86635 2.2609 6.66728 1.67216 5.34937C1.08343 4.03147 0.789062 2.69737 0.789062 1.34706C0.789062 1.15262 0.853877 0.990578 0.983507 0.860948C1.11314 0.731319 1.27517 0.666504 1.46962 0.666504H4.09462C4.24585 0.666504 4.38088 0.717816 4.49971 0.820439C4.61854 0.923063 4.68875 1.04459 4.71036 1.18502L5.13166 3.45354C5.15326 3.62638 5.14786 3.77221 5.11545 3.89104C5.08304 4.00987 5.02363 4.11249 4.93721 4.19891L3.36545 5.78687C3.5815 6.18657 3.83806 6.57275 4.13513 6.94544C4.4322 7.31812 4.75897 7.67731 5.11545 8.02299C5.45033 8.35786 5.80141 8.66843 6.16869 8.9547C6.53598 9.24096 6.92486 9.50292 7.33536 9.74058L8.85851 8.21743C8.95573 8.12021 9.08266 8.04729 9.23929 7.99868C9.39593 7.95007 9.54986 7.93657 9.7011 7.95817L11.9372 8.41187C12.0884 8.45508 12.2127 8.5334 12.3099 8.64683C12.4071 8.76025 12.4557 8.88718 12.4557 9.02761V11.6526C12.4557 11.8471 12.3909 12.0091 12.2613 12.1387C12.1317 12.2684 11.9696 12.3332 11.7752 12.3332ZM2.74971 4.55539L3.81915 3.48595L3.54369 1.9628H2.10156C2.15557 2.4057 2.23119 2.8432 2.32841 3.2753C2.42564 3.7074 2.56607 4.1341 2.74971 4.55539ZM8.55064 10.3563C8.97193 10.54 9.40133 10.6858 9.83883 10.7938C10.2763 10.9018 10.7165 10.9721 11.1594 11.0045V9.57854L9.63628 9.27067L8.55064 10.3563Z"
              fill="white" />
          </svg>
        </span>
        {{company.workMobileNumer}}
      </div>
    </div>
  </div>
</div>
<ng-template #removeFav>
  <div class="modal-header">
    Are sure sure you want to remove {{ company.companyType }} from you
    favorites ?
  </div>
  <div class="modal-footer d-block text-center">
    <button
      [disabled]="removingFav"
      (click)="removeFavorite()"
      class="btn btn-primary"
    >
      <span *ngIf="!removingFav">Yes</span>
      <app-spinner *ngIf="removingFav"></app-spinner>
    </button>
    <button class="btn btn-primary">No</button>
  </div>
</ng-template>
