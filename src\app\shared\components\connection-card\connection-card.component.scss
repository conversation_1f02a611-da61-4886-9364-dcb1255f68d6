.fc-favorite-list {
  gap: 1rem;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: start;
  padding: 1rem;
  width: 255.24px;
  min-height: 100%;
  background: #f6f6f6;
  border: 1px solid rgb(0 0 0 / 7%);
  //   box-shadow: 0px -1px 10px rgba(0, 0, 0, 0.05);
  border-radius: 20px;
  text-align: left;
  position: relative;
  transition: all 0.3s ease-in-out;
  cursor: pointer;

  &:hover {
    transform: translateY(-5px);
  }
  .fc-favorite-card {
    display: flex;
    flex-direction: column;
    align-items: start;
    justify-content: start;
    height: 100%;
    gap: 1rem;
    width: 100%;
  }

  figure {
    width: 56px;
    height: 56px;
    overflow: hidden;
    border-radius: 30px;
    margin-bottom: 0rem;
    border: 1px solid #8101601f;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  h6 {
    font-size: 18px;
    color: black;
    font-weight: 500;
    margin-bottom: 0rem;
    height: 50px;
  }
  p {
    margin-bottom: 0px;
    color: #666666;
    font-size: 14px;
  }
  .fc-call-btn {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
    color: black;
    font-size: 13px;
    font-weight: 400;
    margin-top: auto;

    a {
      width: 100%;
      display: flex;
      flex-direction: row;
      text-decoration: none;
    }
    .call-icon {
      width: 20px;
      height: 20px;
      background-color: #035891;
      border-radius: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .fc-favorite-i {
    position: absolute;
    right: 1rem;
    top: 1rem;
    background-color: rgb(1 70 129);
    width: 30px;
    height: 30px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .expertise-name-list {
    display: flex;
    span {
      color: #d1d1d1;
      font-size: 12px;
      display: flex;
      flex-direction: row;
      gap: 5px;
    }
  }

  &:hover {
    background: rgba(171, 173, 196, 0.4);
    border: 1px solid rgba(140, 140, 140, 0.1);
    box-shadow: 0px -1px 10px rgba(0, 0, 0, 0.05);
    border-radius: 20px;
  }

  .expertise-list {
    font-size: 14px;
    cursor: pointer;
    display: table;
    word-wrap: break-word;
    word-break: break-all;
    gap: 5px;
    flex-wrap: wrap;
    color: rgba(0, 0, 0, 0.5);
    height: 62px;

    span {
      display: inline-flex;
      margin-right: 5px;
    }
  }
  .company-email {
    width: calc(100% - 30px);
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.4;
    max-height: calc(1.4em * 2);
  }
}

@media (max-width: 768px) {
  .fc-favorite-list {
    width: 100%;
  }
}
// Text truncation utility class for 2 lines
.text-truncate-2-lines {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  max-height: calc(1.4em * 2); // 2 lines with line-height 1.4
}
