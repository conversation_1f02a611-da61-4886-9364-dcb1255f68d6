import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { finalize } from 'rxjs';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { Ecard } from 'src/app/utils/e-card';
import { APP_LOGO } from '../../constant';
import { ModalService } from '../../services/modal.service';
import { ConfirmationComponent } from '../../modals/confirmation/confirmation.component';
import { UtilsService } from '../../services/utils.service';
import { BsModalService } from 'ngx-bootstrap/modal';
import { YtVideoComponent } from '../yt-video/yt-video.component';

@Component({
  selector: 'app-connection-card',
  templateUrl: './connection-card.component.html',
  styleUrls: ['./connection-card.component.scss'],
})
export class ConnectionCardComponent implements OnInit {
  isLoggedIn$: any;
  user: any;
  @Input() company!: Ecard;
  @Input() isExpert = false;
  @Output() onRemoved = new EventEmitter();
  @Output() locationClick = new EventEmitter();
  @ViewChild('removeFav') removeFav: any;
  removingFav = false;
  canCall = true;
  APP_LOGO = APP_LOGO;
  cookiesPrivacy: any;

  constructor(
    private account: AccountService,
    private router: Router,
    private toastrService: ToastrService,
    private modalService: ModalService,
    private utils: UtilsService,
    private readonly modalService1: BsModalService
  ) {}
  ngOnInit(): void {
    this.account.isLoggedIn$.subscribe((response) => {
      this.isLoggedIn$ = response;
    });
    this.account.user$.subscribe((response) => {
      this.user = response;
    });

    if (!this.company.workMobileNumer) {
      this.company.workMobileNumer = 'XXXXXXXXXX';
    }

    if (this.company.elevatorPitchVideo) {
      this.company.elevatorPitchVideo = this.utils.getId(
        this.company.elevatorPitchVideo
      );
    } else {
      this.company.elevatorPitchVideo = null;
    }
  }

  gotoDetails() {
    const url = `/details/${
      this.isExpert ? this.company.id : this.company.companyId
    }/${this.isExpert ? 'expert' : 'company'}`;
    !this.isLoggedIn$
      ? this.router.navigate(['/account'])
      : this.router.navigate([url]);
  }

  addToFavorite() {
    if (this.company.isFavorite) {
      this.modalService.openModal('confirmation', {
        class: 'modal-dialog-centered',
        initialState: {
          message:
            'Are you sure you want you remove this profile from favorite?',
          firstButtonText: `Yes, remove`,
          onConfirm: (success: any) => {
            this.removeFavorite();
          },
        },
      });
    } else {
      this.company.isFavorite = !this.company.isFavorite;
      this.toggelFavorite(true);
    }
  }

  toggelFavorite(state: any) {
    const payload = {
      loginUserId: this.user.userId,
      expertId: this.company.userId,
      isAdd: state,
    };
    this.removeFav = true;
    this.company.isFavorite = state;
    this.account
      .addToFavorite(payload)
      .pipe(finalize(() => (this.removeFav = false)))
      .subscribe((response: any) => {
        if (!response.messageType) {
          this.toastrService.success(`Added Successfully`);
        } else {
          this.toastrService.error(response.message);
        }
      });
  }

  removeFavorite() {
    const payload = {
      loginUserId: this.user.userId,
      expertId: this.company.userId,
      isAdd: false,
    };
    this.company.isFavorite = false;
    this.removingFav = true;
    this.account
      .addToFavorite(payload)
      .pipe(finalize(() => (this.removingFav = false)))
      .subscribe((response: any) => {
        if (!response.messageType) {
          this.toastrService.success(`Removed from favorite list`);
          this.onRemoved.emit(true);
          this.modalService.closeModal();
        } else {
          this.toastrService.error(response.message);
        }
      });
  }

  emitLocation() {
    const latlog = this.company?.latLong?.split(',');
    if (latlog.length) {
      this.locationClick.emit(latlog);
    }
  }

  isPlaying: boolean = false;
  togglePlayPause(id: any) {
    const video = document.getElementById(id) as HTMLVideoElement;
    if (video.paused) {
      video.play();
      this.isPlaying = true;
    } else {
      video.pause();
      this.isPlaying = false;
    }
  }

  openVideo() {
    this.modalService1.show(YtVideoComponent, {
      initialState: {
        link: this.company.elevatorPitchVideo,
      },
      class: 'modal-lg modal-dialog-centered',
    });
  }
}
