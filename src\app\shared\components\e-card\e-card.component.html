<div class="e-new-card-box">
  <div class="e-new-card-video">
    <img
      [src]="
        company.companyBanner
      "
      alt="E-Card Banner"
      appImg viewType="eCard"/>
  </div>
  <div class="e-card-expert">
    <div class="e-card-expert-list" *ngIf="company?.services?.length">
      <div class="e-card-expert-item dropdown" role="button">
        <div data-bs-toggle="dropdown" aria-expanded="false">
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M8 16C5.74545 16 3.84848 15.5788 2.30909 14.7364C0.769697 13.8939 0 12.8606 0 11.6364C0 10.8 0.387879 10.0333 1.16364 9.33636C1.93939 8.63939 3.00606 8.10909 4.36364 7.74545V9.23636C3.47879 9.51515 2.77273 9.87273 2.24545 10.3091C1.71818 10.7455 1.45455 11.1879 1.45455 11.6364C1.45455 12.4121 2.10909 13.0909 3.41818 13.6727C4.72727 14.2545 6.25455 14.5455 8 14.5455C9.74545 14.5455 11.2727 14.2545 12.5818 13.6727C13.8909 13.0909 14.5455 12.4121 14.5455 11.6364C14.5455 11.1879 14.2818 10.7455 13.7545 10.3091C13.2273 9.87273 12.5212 9.51515 11.6364 9.23636V7.74545C12.9939 8.10909 14.0606 8.63939 14.8364 9.33636C15.6121 10.0333 16 10.8 16 11.6364C16 12.8606 15.2303 13.8939 13.6909 14.7364C12.1515 15.5788 10.2545 16 8 16ZM5.81818 13.0909V5.09091H2.18182V3.63636H13.8182V5.09091H10.1818V13.0909H8.72727V9.45455H7.27273V13.0909H5.81818ZM8 2.90909C7.6 2.90909 7.25758 2.76667 6.97273 2.48182C6.68788 2.19697 6.54545 1.85455 6.54545 1.45455C6.54545 1.05455 6.68788 0.712121 6.97273 0.427273C7.25758 0.142424 7.6 0 8 0C8.4 0 8.74242 0.142424 9.02727 0.427273C9.31212 0.712121 9.45455 1.05455 9.45455 1.45455C9.45455 1.85455 9.31212 2.19697 9.02727 2.48182C8.74242 2.76667 8.4 2.90909 8 2.90909Z"
              fill="white"
            />
          </svg>
          <svg
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M3.96967 6.21967C4.26256 5.92678 4.73744 5.92678 5.03033 6.21967L9.375 10.5643L13.7197 6.21967C14.0126 5.92678 14.4874 5.92678 14.7803 6.21967C15.0732 6.51256 15.0732 6.98744 14.7803 7.28033L9.90533 12.1553C9.76468 12.296 9.57391 12.375 9.375 12.375C9.17609 12.375 8.98532 12.296 8.84467 12.1553L3.96967 7.28033C3.67678 6.98744 3.67678 6.51256 3.96967 6.21967Z"
              fill="white"
            />
          </svg>
        </div>
        <ul class="dropdown-menu custom-scroll">
          <li *ngFor="let experty of company.services">
            <a class="dropdown-item" href="#">{{ experty }}</a>
          </li>
        </ul>
      </div>
    </div>
    <div class="ms-auto">
      <div
        *ngIf="company?.expertProfiles.length"
        class="card-footer bg-transparent border-0 d-flex fs-14"
      >
        <div class="avatars">
          <a
            *ngFor="let item of company?.expertProfiles | slice : 0 : 5"
            class="avatars__item"
            ><img class="avatar" [src]="item" appImg viewType="profile_rounded"
          /></a>
          <a class="ps-2 nav-link fw-regular text-white">
            <span> {{ company.expertCount || 0 }} Expert(s) </span>
          </a>
        </div>
      </div>
    </div>
  </div>
  <div class="play-video-icon" (click)="openVideo()">
    <svg
      width="60"
      height="60"
      viewBox="0 0 60 60"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="60" height="60" rx="30" fill="black" fill-opacity="0.7" />
      <path d="M21 19V41L39 30L21 19Z" fill="white" />
    </svg>
  </div>
  <div class="e-card-detail">
    <div class="e-card-connect-row">
      <span *ngIf="isLoggedIn$">
        <focile-chip
          (onClick)="addToFavorite()"
          [type]="'danger'"
          [ngClass]="{ blue: company.isFavorite, black: !company.isFavorite }"
        >
          <i
            class="fa-heart"
            [ngClass]="{ fa: company.isFavorite, far: !company.isFavorite }"
          ></i>
        </focile-chip>
      </span>
      <a
        [tooltip]="'Call Now: ' + company.workMobileNumer"
        href="tel:+{{ company.workMobileNumer }}"
      >
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M19.95 21C17.8667 21 15.8083 20.5458 13.775 19.6375C11.7417 18.7292 9.89167 17.4417 8.225 15.775C6.55833 14.1083 5.27083 12.2583 4.3625 10.225C3.45417 8.19167 3 6.13333 3 4.05C3 3.75 3.1 3.5 3.3 3.3C3.5 3.1 3.75 3 4.05 3H8.1C8.33333 3 8.54167 3.07917 8.725 3.2375C8.90833 3.39583 9.01667 3.58333 9.05 3.8L9.7 7.3C9.73333 7.56667 9.725 7.79167 9.675 7.975C9.625 8.15833 9.53333 8.31667 9.4 8.45L6.975 10.9C7.30833 11.5167 7.70417 12.1125 8.1625 12.6875C8.62083 13.2625 9.125 13.8167 9.675 14.35C10.1917 14.8667 10.7333 15.3458 11.3 15.7875C11.8667 16.2292 12.4667 16.6333 13.1 17L15.45 14.65C15.6 14.5 15.7958 14.3875 16.0375 14.3125C16.2792 14.2375 16.5167 14.2167 16.75 14.25L20.2 14.95C20.4333 15.0167 20.625 15.1375 20.775 15.3125C20.925 15.4875 21 15.6833 21 15.9V19.95C21 20.25 20.9 20.5 20.7 20.7C20.5 20.9 20.25 21 19.95 21ZM6.025 9L7.675 7.35L7.25 5H5.025C5.10833 5.68333 5.225 6.35833 5.375 7.025C5.525 7.69167 5.74167 8.35 6.025 9ZM14.975 17.95C15.625 18.2333 16.2875 18.4583 16.9625 18.625C17.6375 18.7917 18.3167 18.9 19 18.95V16.75L16.65 16.275L14.975 17.95Z"
            fill="white"
          />
        </svg>
      </a>
      <a
        [tooltip]="'Email : ' + company.email"
        [href]="'mailto:' + company.email"
      >
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M4 20C3.45 20 2.97917 19.8042 2.5875 19.4125C2.19583 19.0208 2 18.55 2 18V6C2 5.45 2.19583 4.97917 2.5875 4.5875C2.97917 4.19583 3.45 4 4 4H20C20.55 4 21.0208 4.19583 21.4125 4.5875C21.8042 4.97917 22 5.45 22 6V18C22 18.55 21.8042 19.0208 21.4125 19.4125C21.0208 19.8042 20.55 20 20 20H4ZM12 13L4 8V18H20V8L12 13ZM12 11L20 6H4L12 11ZM4 8V6V18V8Z"
            fill="white"
          />
        </svg>
      </a>
      <span [tooltip]="company.address">
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12 12C12.55 12 13.0208 11.8042 13.4125 11.4125C13.8042 11.0208 14 10.55 14 10C14 9.45 13.8042 8.97917 13.4125 8.5875C13.0208 8.19583 12.55 8 12 8C11.45 8 10.9792 8.19583 10.5875 8.5875C10.1958 8.97917 10 9.45 10 10C10 10.55 10.1958 11.0208 10.5875 11.4125C10.9792 11.8042 11.45 12 12 12ZM12 19.35C14.0333 17.4833 15.5417 15.7875 16.525 14.2625C17.5083 12.7375 18 11.3833 18 10.2C18 8.38333 17.4208 6.89583 16.2625 5.7375C15.1042 4.57917 13.6833 4 12 4C10.3167 4 8.89583 4.57917 7.7375 5.7375C6.57917 6.89583 6 8.38333 6 10.2C6 11.3833 6.49167 12.7375 7.475 14.2625C8.45833 15.7875 9.96667 17.4833 12 19.35ZM12 22C9.31667 19.7167 7.3125 17.5958 5.9875 15.6375C4.6625 13.6792 4 11.8667 4 10.2C4 7.7 4.80417 5.70833 6.4125 4.225C8.02083 2.74167 9.88333 2 12 2C14.1167 2 15.9792 2.74167 17.5875 4.225C19.1958 5.70833 20 7.7 20 10.2C20 11.8667 19.3375 13.6792 18.0125 15.6375C16.6875 17.5958 14.6833 19.7167 12 22Z"
            fill="white"
          />
        </svg>
      </span>
      <span class="business-cate" [tooltip]="company.companyType">
        {{ company.companyType }}
      </span>
    </div>
    <div class="e-card-name">
      <div
        class="e-card-logo"
        (click)="gotoDetails()"
        role="button"
        [tooltip]="company.name"
      >
        <img [src]="company['companyImage']" alt="" appImg/>
      </div>
      <div class="e-company-categories">
        <label (click)="gotoDetails()" role="button">{{ company.name }}</label>
        <span role="button" class="text-truncate-2-lines">
          <div
            *ngFor="let experty of company.expertise; let i = index"
            class="expertise-name-list"
          >
            <div (click)="gotoDetails()">
                <span class="fs-12"> {{ experty }} </span>
              <span *ngIf="experty.length > 10"></span>
            </div>
            <span
              class="seperator"
              *ngIf="i < company.expertise.length - 1 && i < 1"
              >|</span
            >
          </div>
        </span>
      </div>
    </div>
  </div>
  <div class="overlay-bg"></div>
</div>

<ng-template #removeFav>
  <div class="modal-header">
    Are sure sure you want to remove {{ company.companyType }} from you
    favorites ?
  </div>
  <div class="modal-footer d-block text-center">
    <button
      [disabled]="removingFav"
      (click)="removeFavorite()"
      class="btn btn-primary"
    >
      <span *ngIf="!removingFav">Yes</span>
      <app-spinner *ngIf="removingFav"></app-spinner>
    </button>
    <button class="btn btn-primary">No</button>
  </div>
</ng-template>
