// .card {
//   height: 273px;
// }

.avatars {
  display: flex;
  list-style-type: none;
  margin: auto;
  padding: 0px;
  flex-direction: row;
  &__item {
    background-color: #f3f3f3;
    border: 2px solid #1f2532;
    border-radius: 100%;
    color: #ffffff;
    display: block;
    font-family: sans-serif;
    font-size: 12px;
    font-weight: 100;
    height: 25px;
    width: 25px;
    text-align: center;
    transition: margin 0.1s ease-in-out;
    overflow: hidden;
    margin-left: -10px;
    &:first-child {
      z-index: 5;
    }
    &:nth-child(2) {
      z-index: 4;
    }
    &:nth-child(3) {
      z-index: 3;
    }
    &:nth-child(4) {
      z-index: 2;
    }
    &:nth-child(5) {
      z-index: 1;
    }
    &:last-child {
      z-index: 0;
    }
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

.flex-item {
  height: 5.125rem;
  height: 3.5rem;
}

.flex-item-text {
  font-weight: 500;
}

.mw-84 {
  min-width: 4.5rem;
}

.video-container {
  position: relative;
  width: 100%;
}

.video {
  width: 100%;
  height: 138px;
}

.video-controls {
  top: 0;
  left: 0;
  right: 0;
  transition: all 0.5s ease-in-out;
  opacity: 0;
}

.video-controls:hover {
  background-color: rgba($color: #000000, $alpha: 0.3);
  opacity: 1;
}

.e-new-card-box {
  display: flex;
  flex-direction: column;
  min-height: 491px;
  background-color: #f9f9f9;
  border-radius: 12px;
  overflow: hidden;
  width: 100%;
  position: relative;
  .avatars {
    align-items: center;
    .avatars__item {
      border-color: white;
      width: 35px;
      height: 35px;
    }
  }
}

.e-card-box {
  min-height: 491px;
  background-color: #f9f9f9;
  border-radius: 12px;
  position: relative;
  width: 100%;
}

.e-card-expert {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  position: relative;
  padding: 1rem;
  z-index: 11;
}
.overlay-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0px;
  top: 0px;
  background: linear-gradient(
    180deg,
    rgba(18, 18, 18, 0.0001) 0%,
    #**********%
  );
}

::ng-deep .ytp-chrome-top .ytp-impression-link {
  display: none !important;
}

.e-new-card-video {
  img {
    width: 100%;
    height: 491px;
    position: absolute;
    top: 0px;
    left: 0px;
    object-fit: cover;
  }
}
.e-card-logo {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  border: 1px solid transparent;
  overflow: hidden;

  img {
    width: 100%;
    object-fit: cover;
    height: 100%;
  }
}

.e-company-categories {
  width: calc(100% - 50px);
  display: flex;
  flex-direction: column;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  label {
    color: white;
    font-weight: medium;
  }
  span {
    color: #d1d1d1;
    font-size: 12px;
    display: flex;
    flex-direction: row;
    gap: 5px;
    white-space: nowrap;
  }
  .seperator {
    margin-left: 5px;
  }
  .expertise-name-list {
    display: flex;
  }
}

.e-card-detail {
  width: 100%;
  display: flex;
  z-index: 1;
  flex-direction: column;
  padding: 1rem;
  margin-top: auto;

  .e-card-name {
    display: flex;
    flex-direction: row;
    gap: 10px;
    align-items: center;
    padding-top: 1rem;
  }
}
.e-card-connect-row {
  border-bottom: 1px solid black;
}

.e-card-connect-row {
  display: flex;
  flex-direction: row;
  gap: 20px;
  align-items: center;
  padding-bottom: 1rem;

  span {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 10px;
    gap: 5px;
    width: 40px;
    height: 40px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50px;
    flex: none;
    order: 0;
    flex-grow: 0;
    cursor: pointer;

    &:hover {
      background-color: #035891;
    }
  }

  a {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 10px;
    gap: 5px;
    width: 40px;
    height: 40px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50px;
    flex: none;
    order: 0;
    flex-grow: 0;
    cursor: pointer;

    &:hover {
      background-color: #035891;
    }
  }
}

.business-cate {
  margin-left: auto;
  color: white;
  font-weight: 600;
}

.play-video-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: auto;
  z-index: 1;
  cursor: pointer;
}

.e-card-expert-item {
  background-color: #035891;
  border-radius: 20px;
  min-width: 57px;
  height: 31px;
  background: rgba(0, 110, 255, 0.5);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  > div {
    gap: 5px;
    display: flex;
    align-items: center;
    height: 100%;
  }
}

.e-card-expert-list {
  .dropdown-menu {
    min-height: auto;
    overflow-y: auto;
    width: 150px;
    background: #000000e0;
    color: white;
    overflow-x: hidden;
    max-height: 200px;
    box-shadow: 0px 0px 10px rgb(255 255 255 / 15%);

    /* Custom scrollbar styles for WebKit browsers */
    ::-webkit-scrollbar {
      width: 8px;
    }

    ::-webkit-scrollbar-track {
      background: #ffffffa4;
    }

    ::-webkit-scrollbar-thumb {
      background: black;
      border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb:hover {
      background: #024a7c;
    }

    /* Custom scrollbar styles for Firefox */
    scrollbar-width: thin;
    scrollbar-color: black #ffffffa4;

    a {
      font-size: 12px;
      color: white;

      &:hover {
        background-color: transparent;
        color: black;
      }

      &:focus {
        background-color: transparent;
      }
    }
  }
}

.black {
  ::ng-deep .chip-danger {
    background-color: black;

    &:hover {
      background-color: #024a7c;
    }
  }
}

.tooltip {
  font-size: 12px;
}

@media (max-width: 768px) {
  .e-new-card-box {
    min-height: 350px;
  }
}

.text-truncate-2-lines {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  max-height: calc(1.4em * 2); // 2 lines with line-height 1.4
}
