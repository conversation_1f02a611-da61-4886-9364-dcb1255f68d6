import {
  Component,
  EventEmitter,
  Input,
  Output,
  type OnInit,
  CUSTOM_ELEMENTS_SCHEMA,
} from '@angular/core';
import { EditorModule } from '@tinymce/tinymce-angular';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-editor',
  standalone: true,
  imports: [EditorModule, HttpClientModule, FormsModule],
  templateUrl: './editor.component.html',
  styleUrls: ['./editor.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class TinyMCEditorComponent implements OnInit {
  @Output() onChange = new EventEmitter<string>();
  @Input() content: string = '';
  init: any = {
    plugins: 'lists link image table code help wordcount',
  };
  constructor(
    private http: HttpClient // Ensure you have HttpClientModule imported in your app module
  ) { }
  ngOnInit(): void {
    this.init = {
      height: 400,
      menubar: true,
      plugins: [
        'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
        'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
        'insertdatetime', 'media', 'table', 'help', 'wordcount', 'emoticons',
        'codesample', 'quickbars', 'powerpaste', 'textcolor', 'colorpicker'
      ],
      toolbar: 'undo redo | blocks | bold italic forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help | fullscreen | code',
      toolbar_mode: 'sliding',
      quickbars_selection_toolbar: 'bold italic | quicklink h2 h3 blockquote quickimage quicktable',
      quickbars_insert_toolbar: 'quickimage quicktable',
      contextmenu: 'link image table',
      skin: 'oxide',
      content_css: 'default',
      branding: false,
      promotion: false,
      resize: true,
      elementpath: false,
      statusbar: true,
      paste_data_images: true,
      images_upload_url: 'https://localhost:44312/api/Blog/UploadBlogImage',
      automatic_uploads: true,
      file_picker_types: 'image',
      setup: (editor: any) => {
        editor.on('Change', () => {
          const content = editor.getContent();
          this.handleOnChange(content);
        });
        editor.on('KeyUp', () => {
          const content = editor.getContent();
          this.handleOnChange(content);
        });
      },
    };
  }

  handleOnChange($event: string) {
    this.onChange.emit($event);
  }
}
