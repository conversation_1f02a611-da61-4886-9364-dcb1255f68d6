<!-- <div
  role="button"
  class="expert-connect-card-container border d-flex gap-4 p-2"
>
  <div (click)="navigate()">
    <img
      style="height: 36px; width: 36px; border-radius: 50px"
      [src]="expert.profileImage || './assets/svgs/focile.svg'"
      appImg
    />
  </div>
  <div (click)="navigate()">
    <span class="fs-14 fw-bold">
      {{ expert.userName | titlecase }}
    </span>
    <p class="m-0 fs-12 text-muted text-truncate" style="max-width: 150px">
      <i
        [tooltip]="expert.userName + '\'s Role '"
        class="fas fa-user-tag text-primary"
      ></i>
      {{ expert.roleName }} at {{ expert.companyName }}
    </p>
    <p class="fs-12 text-muted">
      <i class="fab fa-buromobelexperte text-primary"></i>
      <span *ngFor="let item of expertise | slice: 0: 3; let i = index">
        <span *ngIf="i > 0"> | </span>     
      </span>
    </p>
  </div>
  <div class="d-flex justify-content-end pe-3 pt-2" style="flex: 1">
    <ng-container *ngIf="expert.connectionStatus">
      <span class="fs-12" style="min-width: max-content">{{
        expert.connectionStatus
      }}</span>
    </ng-container>

    <ng-container *ngIf="expert.connectionState == FollowStatus.NA">
      <button
        class="btn btn-outline-primary btn-sm p-0 fs-12"
        style="height: 24px; width: 84px; border-radius: 100px"
        role="button"
        [ngClass]="{ 'btn-primary text-white': expert.isFollowing }"
        (click)="connectToExpert($event)"
      >
        Connect
      </button>
    </ng-container>
  </div>
</div> -->


<div class="list-of-connection">
  <div class="ref-profile" (click)="navigate()" role="button">    
      <img        
        [src]="expert.profilePhoto || './assets/svgs/focile.svg'"
        appImg
      />
  </div>
  <div class="refer-name" (click)="navigate()" role="button">
    <label role="button"> {{ expert.userName || (expert.firstName + ' ' + expert.lastName) | titlecase }} </label>
    <span class="fs-10" role="button">{{expert.roleName}}</span>
    <div class="refer-post">  
      <span class="single-expertise" *ngFor="let item of expertise | slice: 0: 3; let i = index">
        <span *ngIf="i > 0"> | </span> {{ item.name }}
      </span>
    </div>
  </div>
  <div class="fc-connect-user"> 
    <!-- <ng-container *ngIf="expert.connectionStatus">
      <span class="fs-12" style="min-width: max-content">{{
        expert.connectionStatus
      }}</span>
    </ng-container> -->
    <ng-container *ngIf="expert.connectionState == FollowStatus.NA">
      <button
        class="add-connection-btn"        
        role="button"
        [ngClass]="{ 'btn-primary text-white': expert.isFollowing }"
        (click)="connectToExpert($event)"
      >
      <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8.06664 13.167H9.73987V9.83366H13.0863V8.16699H9.73987V4.83366H8.06664V8.16699H4.72018V9.83366H8.06664V13.167ZM8.90325 17.3337C7.74593 17.3337 6.65834 17.1149 5.64046 16.6774C4.62258 16.2399 3.73716 15.6462 2.98421 14.8962C2.23125 14.1462 1.63517 13.2642 1.19594 12.2503C0.756721 11.2364 0.537109 10.1531 0.537109 9.00033C0.537109 7.84755 0.756721 6.76421 1.19594 5.75033C1.63517 4.73644 2.23125 3.85449 2.98421 3.10449C3.73716 2.35449 4.62258 1.76074 5.64046 1.32324C6.65834 0.885742 7.74593 0.666992 8.90325 0.666992C10.0606 0.666992 11.1482 0.885742 12.166 1.32324C13.1839 1.76074 14.0693 2.35449 14.8223 3.10449C15.5752 3.85449 16.1713 4.73644 16.6106 5.75033C17.0498 6.76421 17.2694 7.84755 17.2694 9.00033C17.2694 10.1531 17.0498 11.2364 16.6106 12.2503C16.1713 13.2642 15.5752 14.1462 14.8223 14.8962C14.0693 15.6462 13.1839 16.2399 12.166 16.6774C11.1482 17.1149 10.0606 17.3337 8.90325 17.3337ZM8.90325 15.667C10.7717 15.667 12.3543 15.0212 13.651 13.7295C14.9478 12.4378 15.5962 10.8614 15.5962 9.00033C15.5962 7.13921 14.9478 5.56283 13.651 4.27116C12.3543 2.97949 10.7717 2.33366 8.90325 2.33366C7.03481 2.33366 5.45222 2.97949 4.15547 4.27116C2.85871 5.56283 2.21034 7.13921 2.21034 9.00033C2.21034 10.8614 2.85871 12.4378 4.15547 13.7295C5.45222 15.0212 7.03481 15.667 8.90325 15.667Z" fill="#014681"/>
        </svg> 
      </button>
    </ng-container>

    <span *ngIf="expert.connectionStatus">
      <svg width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M14.7158 2.4857C11.4017 -0.828717 6.00882 -0.828417 2.69469 2.4857C-0.619583 5.79997 -0.619583 11.1927 2.69469 14.5068C4.54859 16.361 7.18125 17.2557 9.78093 16.9268L12.7153 19.0941C12.8135 19.1667 12.9314 19.2046 13.0509 19.2046C13.1011 19.2046 13.1514 19.198 13.2007 19.1845C13.3678 19.1384 13.5046 19.0184 13.5717 18.8586L14.4087 16.8673C14.5296 16.5796 14.3944 16.2485 14.1068 16.1275C13.819 16.0069 13.4877 16.1418 13.3671 16.4294L12.8086 17.7583L10.2665 15.8808C10.1452 15.7912 9.99252 15.7529 9.84406 15.7771C7.52273 16.1374 5.1499 15.3642 3.49357 13.7079C0.619931 10.8343 0.619931 6.15836 3.49357 3.28457C6.36707 0.411078 11.0428 0.410796 13.9169 3.28457C15.7432 5.11112 16.4739 7.70375 15.8716 10.2199C15.8701 10.2261 15.8687 10.2322 15.8675 10.2384C15.783 10.5864 15.6728 10.9299 15.5429 11.2521L14.2231 14.3926C14.1021 14.6803 14.2373 15.0114 14.525 15.1324C14.8126 15.2531 15.1439 15.1181 15.2647 14.8305L16.5877 11.6824C16.7421 11.2996 16.8698 10.9009 16.9615 10.5203C16.9699 10.4937 16.9762 10.4669 16.9806 10.44C17.6565 7.55222 16.8117 4.58164 14.7158 2.4857Z" fill="#00ACFF"/>
        <path d="M12.2865 5.8501H5.48127C5.2316 5.8501 5.0293 6.05041 5.0293 6.30008C5.0293 6.54974 5.2316 6.75006 5.48127 6.75006H12.2865C12.5362 6.75006 12.7385 6.54974 12.7385 6.30008C12.7385 6.05041 12.5362 5.8501 12.2865 5.8501Z" fill="#00ACFF"/>
        <path d="M12.7385 8.88699C12.7385 8.63733 12.5362 8.43701 12.2865 8.43701H5.48127C5.2316 8.43701 5.0293 8.63733 5.0293 8.88699C5.0293 9.13666 5.2316 9.33698 5.48127 9.33698H12.2865C12.5362 9.33698 12.7385 9.13666 12.7385 8.88699Z" fill="#00ACFF"/>
        <path d="M5.48127 11.0249C5.2316 11.0249 5.0293 11.2252 5.0293 11.4749C5.0293 11.7245 5.2316 11.9249 5.48127 11.9249H9.37153C9.6212 11.9249 9.8235 11.7245 9.8235 11.4749C9.8235 11.2252 9.6212 11.0249 9.37153 11.0249H5.48127Z" fill="#00ACFF"/>
        </svg>                    
    </span>
    <span class="varified-connection" *ngIf="expert.connectionStatus">
      <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8.31336 12.4374C8.22288 12.4379 8.13319 12.4206 8.04942 12.3864C7.96566 12.3521 7.88947 12.3017 7.82523 12.238L5.07523 9.48801C4.94578 9.35855 4.87305 9.18297 4.87305 8.99988C4.87305 8.8168 4.94578 8.64122 5.07523 8.51176C5.20469 8.3823 5.38028 8.30957 5.56336 8.30957C5.74644 8.30957 5.92203 8.3823 6.05148 8.51176L8.31336 10.7805L12.6377 6.44926C12.7672 6.3198 12.9428 6.24707 13.1259 6.24707C13.3089 6.24707 13.4845 6.3198 13.614 6.44926C13.7434 6.57872 13.8162 6.7543 13.8162 6.93738C13.8162 7.12047 13.7434 7.29605 13.614 7.42551L8.80148 12.238C8.73724 12.3017 8.66106 12.3521 8.57729 12.3864C8.49353 12.4206 8.40384 12.4379 8.31336 12.4374Z" fill="#8A9A5B"/>
        <path d="M9 17.9375C7.23233 17.9375 5.50436 17.4133 4.0346 16.4313C2.56483 15.4492 1.41929 14.0534 0.742831 12.4202C0.0663725 10.7871 -0.11062 8.99009 0.234236 7.25638C0.579091 5.52268 1.43031 3.93017 2.68024 2.68024C3.93017 1.43031 5.52268 0.579091 7.25638 0.234236C8.99009 -0.11062 10.7871 0.0663725 12.4202 0.742831C14.0534 1.41929 15.4492 2.56483 16.4313 4.0346C17.4133 5.50436 17.9375 7.23233 17.9375 9C17.9375 11.3704 16.9959 13.6437 15.3198 15.3198C13.6437 16.9959 11.3704 17.9375 9 17.9375ZM9 1.4375C7.50428 1.4375 6.04215 1.88104 4.7985 2.71201C3.55486 3.54299 2.58555 4.72409 2.01316 6.10596C1.44078 7.48783 1.29101 9.00839 1.58282 10.4754C1.87462 11.9424 2.59487 13.2899 3.65251 14.3475C4.71014 15.4051 6.05765 16.1254 7.52463 16.4172C8.99162 16.709 10.5122 16.5592 11.894 15.9868C13.2759 15.4145 14.457 14.4452 15.288 13.2015C16.119 11.9579 16.5625 10.4957 16.5625 9C16.5625 6.9943 15.7657 5.07075 14.3475 3.65251C12.9293 2.23427 11.0057 1.4375 9 1.4375Z" fill="#8A9A5B"/>
        </svg>                    
    </span>
  </div>
</div>