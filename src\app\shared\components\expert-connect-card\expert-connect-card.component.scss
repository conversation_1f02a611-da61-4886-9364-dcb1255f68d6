.expert-connect-card-container {

  transition: all .3s ease-in-out;
  &:hover {
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
    border-radius: .5rem;
  }
}
.fc-connect-user {
  display: flex;
  flex-direction: row;
  gap: 1rem;
  align-items: start;
  justify-content: end;
  margin-left: auto;
  flex: 1;

  .add-connection-btn {
    background-color: transparent;
    border: none;
    padding: 0px;
  }

  .online {
    width: 10px;
    height: 10px;
    background: #00ACFF;
    border-radius: 10px;
    margin-top: 8px;
  }
}
.list-of-connection {
  display: flex;
  flex-direction: row;
  gap: 10px;
  padding-inline: 1rem;
  margin-bottom: 1rem;

  .ref-profile {
    width: 35px;
    height: 35px;
    border-radius: 20px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .refer-name {
    width: calc(100% - 100px);
    display: flex;
    flex-direction: column;

    label {
      font-size: 14px;
      font-weight: 500;
      color: #212121;
      cursor: pointer;
    }

    .refer-post {
      color: rgba(0, 0, 0, 0.5);
      font-size: 12px;

      b {
        font-weight: 500;
        color: #212121;
      }
    }
  }
}