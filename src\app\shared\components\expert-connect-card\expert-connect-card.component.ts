import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { DropMessageType, FollowStatus } from '../../constant';
import { filterNonNull } from '../../oprators/filter-null-values';

@Component({
  selector: 'app-expert-connect-card',
  templateUrl: './expert-connect-card.component.html',
  styleUrls: ['./expert-connect-card.component.scss'],
})
export class ExpertConnectCardComponent implements OnInit {
  @Input() expert: any;
  @Input() expertise: any[] = [];
  userState$: any;
  FollowStatus = FollowStatus; // Expose the enum to the template
  constructor(
    private readonly router: Router,
    private readonly account: AccountService,
    private readonly toasterService: ToastrService
  ) {}

  ngOnInit(): void {
    this.account.user$.pipe(filterNonNull()).subscribe((response) => {
      if (response) {
        this.userState$ = response;
      }
    });
    this.expert.connectionStatus = this.getStatusString(
      this.expert.connectionState || 0
    );
    if (this.expert?.experties?.length) {
      this.expert.experties = this.expert.experties.slice(5);
    }
    console.log(this.expert, 'expert');
  }

  navigate() {
    this.router.navigate([`/details/${this.expert.userId}/expert`]);
  }

  connectToExpert(buttonClickEvent: any) {
    buttonClickEvent.stopPropagation();
    const loginUserId = this.userState$.userId;
    const companyAdminId = this.expert.userId;
    const payload = {
      userId: companyAdminId,
      followerUserId: loginUserId,
      loginUserId: loginUserId,
    };
    this.account.connectExpert(payload).subscribe((response: any) => {
      if (response.messageType == DropMessageType.Success) {
        this.expert.connectionStatus = this.getStatusString(
          FollowStatus.InProgress
        );
        this.expert.connectionState = FollowStatus.InProgress;
      } else if (response.messageType == DropMessageType.Info) {
        this.toasterService.info(response.message);
      } else {
        this.toasterService.error(response.message);
      }
    });
  }

  getStatusString(status: number): string {
    switch (status) {
      case FollowStatus.NA:
        return '';
      case FollowStatus.InProgress:
        return 'In Progress';
      case FollowStatus.Reject:
        return 'Rejected';
      case FollowStatus.Connected:
        return 'Connected';
      case FollowStatus.Requestwithdraw:
        return 'Request Withdrawn';
      default:
        return 'Unknown Status';
    }
  }
}
