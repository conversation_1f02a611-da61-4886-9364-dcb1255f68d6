<!-- <ng-template #services>
  <div>
    <span
      *ngFor="let service of vm.services"
      class="badge me-1 rounded-pill bg-primary"
    >
      {{ service }}
    </span>
  </div>
</ng-template>

<ng-template #Products>
  <div>
    <span
      *ngFor="let product of vm.products"
      class="badge me-1 rounded-pill bg-primary"
    >
      {{ product }}
    </span>
  </div>
</ng-template>

<ng-template #Solutions>
  <div>
    <span
      *ngFor="let solution of vm.solutions"
      class="badge me-1 rounded-pill bg-primary"
    >
      {{ solution }}
    </span>
  </div>
</ng-template>

<ng-template #expertise>
  <div>
    <span
      *ngFor="let experty of vm.expertise"
      class="badge me-1 rounded-pill bg-primary"
    >
      {{ experty }}
    </span>
  </div>
</ng-template>

<ng-template #industries>
  <div>
    <span
      *ngFor="let industry of vm.industries"
      class="badge me-1 rounded-pill bg-primary"
    >
      {{ industry }}
    </span>
  </div>
</ng-template>
<div class="row mt-4">
  <div class="col-md-12">
    <h3 class="fw-bold fs-18">
      {{ title }}
    </h3>
  </div>
  <div
    class="col text-center"
    role="button"
    container="body"
    triggers="mouseover:mouseleave"
    [popoverTitle]="'Service Provided By ' + authorName | titlecase"
    [popover]="expertise"
    [placement]="'bottom'"
    #pop="bs-popover"
  >
    <h6 class="fw-bold border p-2 border-2 rounded">
      <span class="fa-14"> Expertise </span><i id="expertise" tabindex="0"></i>
    </h6>
  </div>
  <div
    class="col text-center"
    role="button"
    container="body"
    triggers="mouseover:mouseleave"
    [popoverTitle]="'Expertise Provided By ' + authorName | titlecase"
    [popover]="services"
    [placement]="'bottom'"
  >
    <h6 class="fw-bold border p-2 border-2 rounded">
      <span class="fa-14"> Services </span
      ><i class="ms-2 fa fa-info-circle text-primary"></i>
    </h6>
  </div>
  <div
    triggers="mouseover:mouseleave"
    role="button"
    container="body"
    [popoverTitle]="'Products Provided By ' + authorName | titlecase"
    [popover]="Products"
    [placement]="'bottom'"
    class="col text-center"
  >
    <h6 class="fw-bold border p-2 border-2 rounded">
      <span class="fa-14"> Products </span
      ><i class="ms-2 fa fa-info-circle text-primary"></i>
    </h6>
  </div>
  <div
    triggers="mouseover:mouseleave"
    role="button"
    container="body"
    [popoverTitle]="'Solutions Provided By ' + authorName | titlecase"
    [popover]="Solutions"
    [placement]="'bottom'"
    class="col text-center"
  >
    <h6 class="fw-bold border p-2 border-2 rounded">
      <span class="fa-14"> Solutions </span
      ><i class="ms-2 fa fa-info-circle text-primary"></i>
    </h6>
  </div>
  <div
    triggers="mouseover:mouseleave"
    role="button"
    container="body"
    [popoverTitle]="'Industry type By ' + authorName | titlecase"
    [popover]="industries"
    [placement]="'bottom'"
    class="col text-center"
  >
    <h6 class="fw-bold border p-2 border-2 rounded">
      <span class="fa-14"> Industry </span
      ><i class="ms-2 fa fa-info-circle text-primary"></i>
    </h6>
  </div>
</div> -->




<div class="fc-cmpy-offering">
  <!-- Debug information -->
  <!-- <div *ngIf="isEndUser" style="background: #f0f0f0; padding: 10px; margin: 10px 0; border: 1px solid #ccc;">
    <strong>Debug Info:</strong><br>
    isEndUser: {{ isEndUser }}<br>
    endUserData: {{ endUserData | json }}<br>
    vm: {{ vm | json }}<br>
    authorName: {{ authorName }}
  </div> -->

  <div class="accordion" id="accordionExample">
    <div class="accordion-item">
      <h2 class="accordion-header" id="headingOne">
        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
          Expertise 
          <div class="service-count">{{ vm.expertise?.length }}</div>
        </button>
      </h2>
      <div id="collapseOne" class="accordion-collapse collapse" aria-labelledby="headingOne" data-bs-parent="#accordionExample">
        <div class="accordion-body">
            <div>
              <span
                *ngFor="let experty of vm.expertise"
                class="badge me-1 rounded-pill bg-primary"
              >
                {{ experty }}
              </span>
            </div>
        </div>
      </div>
    </div>
    <div class="accordion-item">
      <h2 class="accordion-header" id="headingTwo">
        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
          Services <div class="service-count">{{ vm.services?.length }}</div>
        </button>
      </h2>
      <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#accordionExample">
        <div class="accordion-body">
          <div>
            <span
              *ngFor="let service of vm.services"
              class="badge me-1 rounded-pill bg-primary"
            >
              {{ service }}
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="accordion-item">
      <h2 class="accordion-header" id="headingThree">
        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
          Products <div class="service-count">{{ vm.products?.length }}</div>
        </button>
      </h2>
      <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#accordionExample">
        <div class="accordion-body">
          <div>
            <span
              *ngFor="let product of vm.products"
              class="badge me-1 rounded-pill bg-primary"
            >
              {{ product }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <div class="accordion-item">
      <h2 class="accordion-header" id="headingFour">
        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFour" aria-expanded="false" aria-controls="collapseFour">
          Solution <div class="service-count">{{vm.solutions?.length }}</div>
        </button>
      </h2>
      <div id="collapseFour" class="accordion-collapse collapse" aria-labelledby="headingFour" data-bs-parent="#accordionExample">
        <div class="accordion-body">
          <div>
            <span
              *ngFor="let solution of vm.solutions"
              class="badge me-1 rounded-pill bg-primary"
            >
              {{ solution }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <div class="accordion-item">
      <h2 class="accordion-header" id="headingFive">
        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFive" aria-expanded="false" aria-controls="collapseFive">
          Industry <div class="service-count">{{ vm.industries?.length }}</div>
        </button>
      </h2>
      <div id="collapseFive" class="accordion-collapse collapse" aria-labelledby="headingFive" data-bs-parent="#accordionExample">
        <div class="accordion-body">
          <div>
            <span
              *ngFor="let industry of vm.industries"
              class="badge me-1 rounded-pill bg-primary"
            >
              {{ industry }}
            </span>
          </div>
        </div>
      </div>
    </div>

  </div>


</div>