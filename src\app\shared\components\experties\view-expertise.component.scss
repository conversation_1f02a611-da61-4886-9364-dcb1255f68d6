
    .service-count {
      margin-left: auto;
      margin-right: 0px;
      width: auto;
      flex: 1;
      position: absolute;
      left: auto;
      right: 59px;
      background: rgba(255, 25, 48, 0.5);
      width: 11px;
      height: 11px;
      padding: 11px;
      border-radius: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 400;
      font-size: 10px;
    }
  
    .accordion-button:not(.collapsed) {
      background-color: transparent;
      box-shadow: none;
      .service-count {
        background-color: #024781;
      }
    }
  
    .accordion-button:focus {
      box-shadow: none;
      border: none;
    }
    .accordion-button{
      font-weight: 500;
    }
  
    .accordion-item {
      border-left: 0px;
      border-right: 0px;
      button{
        padding-inline: 1rem;
        font-size: 14px;
      }
    }
  
    .accordion-item:first-of-type .accordion-button,
    .accordion-item:first-of-type {
      border-radius: 0px;
    }
    .accordion-item:last-of-type{
      border-bottom: 0px;
    }
    .accordion-body {
      color: rgba(0, 0, 0, 0.50);
      font-size: 15px;
      padding-top: 0px;

      span{
        font-weight: 400;
      }
    }
    // .accordion-button::after{
    //     background-size: 15px 15px;
    // }
