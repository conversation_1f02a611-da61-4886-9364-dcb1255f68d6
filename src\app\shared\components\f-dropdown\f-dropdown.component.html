<ng-select
  [items]="items"
  [id]="id"
  [name]="name || id"
  [loading]="loading"
  [bindLabel]="bindLabel"
  [bindValue]="bindValue"
  [(ngModel)]="selecteValue"
  (ngModelChange)="onValueChange($event)"
  (blur)="handleBlueEvent($event)"
  [placeholder]="placeholder"
  [required]="isRequired"
  [disabled]="isDisabled || !items?.length || loading"
  [multiple]="multiple"
  [class]="'h-48'"
  [addTagText]="addTagText"
  [addTag]="addTag"
  [closeOnSelect]="!multiple"
  [clearable]="clearable"
  [readonly]="readonly"
  [disabled]="isDisabled"
>
  <ng-container *ngIf="hideMoreDetails">
    <ng-container *ngIf="multiple">
      <ng-template
        ng-optgroup-tmp
        let-item="item"
        let-item$="item$"
        let-index="index"
      >
        <input
          id="item-{{ index }}"
          type="checkbox"
          [ngModel]="item$.selected"
        />
        {{ item.gender | uppercase }}
      </ng-template>
      <ng-template
        ng-option-tmp
        let-item="item"
        let-item$="item$"
        let-index="index"
      >
        <input
          id="item-{{ index }}"
          type="checkbox"
          [ngModel]="item$.selected"
        />
        {{ item.name }}
      </ng-template>
    </ng-container>
    <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
      <div
        class="ng-value"
        *ngFor="let item of items | slice : 0 : showOptions"
      >
        <span class="ng-value-label"> {{ getLabel(item) }}</span>
        <span
          class="ng-value-icon right"
          (click)="clear(item)"
          aria-hidden="true"
          >×</span
        >
      </div>
      <div class="ng-value" *ngIf="items.length > 1">
        <span class="ng-value-label">{{ items.length - 1 }} more...</span>
      </div>
    </ng-template>
  </ng-container>
</ng-select>
