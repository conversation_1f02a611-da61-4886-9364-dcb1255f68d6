.focused .ng-select-container {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
  width: 100% !important;
}

.h-48 {
  height: 3rem;
  border-radius: .5rem;
}

.ng-select {
  box-sizing: border-box;
  height: 56px;
  border: 0px solid rgba(102, 102, 102, 0.35);
  border-radius: 12px;
  /* Inside auto layout */
  flex: none;
  order: 1;
  align-self: stretch;
  flex-grow: 0;
}

.ng-select-container {
  height: 56px;
  border-radius: 10px;
}

::ng-deep .ng-select.ng-select-single .ng-select-container {
  box-sizing: border-box;
  height: 56px;
  border: 1px solid rgba(102, 102, 102, 0.35);
  border-radius: 12px;
  /* Inside auto layout */
  flex: none;
  order: 1;
  align-self: stretch;
  flex-grow: 0;
}


::ng-deep .ng-select .ng-arrow-wrapper {
  padding-right: 10px;
  width: 30px;
}