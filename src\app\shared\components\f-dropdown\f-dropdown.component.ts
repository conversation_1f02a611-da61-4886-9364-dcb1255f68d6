import {
  Component,
  EventEmitter,
  Input,
  Output,
  forwardRef,
} from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'focile-dropdown, app-dropdown',
  templateUrl: './f-dropdown.component.html',
  styleUrls: ['./f-dropdown.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FDropdownComponent),
      multi: true,
    },
  ],
})
export class FDropdownComponent implements ControlValueAccessor {
  @Input() selecteValue = null;
  @Input() loading: any = false;
  @Input() items: any[] = [];
  @Input() bindValue = 'idGuid';
  @Input() bindLabel = 'name';
  @Input() placeholder = 'Select';
  @Input() isRequired = true;
  @Input() isDisabled = false;
  @Input() multiple = false;
  @Input() elementClass = null;
  @Output() change = new EventEmitter();
  @Input() addTagText: string = 'Click to add item';
  @Input() addTag: any;
  @Input() id = '';
  @Input() name = '';
  @Input() hideMoreDetails = true;
  @Input() clearable!: boolean;
  @Input() readonly = false;
  @Input() showOptions = 1;
  @Input() container = 'body';

  value!: string | null;
  onChange!: (value: string) => void;
  onTouched!: () => void;

  onValueChange($event: string | null) {
    this.value = $event;
    this.change.emit($event);
    if (this.onChange) {
      this.onChange($event as string);
    }
  }

  writeValue(value: any): void {
    this.value = value;
    this.selecteValue = value;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  handleBlueEvent($event: any) {
    if (this.onTouched) {
      this.onTouched();
    }
  }
  setDisabledState?(isDisabled: boolean): void {
    this.isDisabled = isDisabled;
  }

  getLabel(item: any) {
    return item[this.bindLabel];
  }
}
