<div class="accordion" id="accordionExample">
    <div *ngFor="let item of items; let i = index" class="accordion-item">
      <h2 class="accordion-header" id="heading{{ i }}">
        <button 
          class="accordion-button {{ i === 0 ? '' : 'collapsed' }}" 
          type="button" 
          data-bs-toggle="collapse" 
          [attr.data-bs-target]="'#collapse' + i" 
          [attr.aria-expanded]="i === 0 ? 'true' : 'false'" 
          [attr.aria-controls]="'collapse' + i">
          {{ item.title }}
        </button>
      </h2>
      <div 
        id="collapse{{ i }}" 
        class="accordion-collapse collapse {{ i === 0 ? 'show' : '' }}" 
        [attr.aria-labelledby]="'heading' + i" 
        data-bs-parent="#accordionExample">
        <div class="accordion-body">
          {{ item.content }}
        </div>
      </div>
    </div>
  </div>
  