<button
  [disabled]="disabled || loading"
  [ngClass]="{
    disabled: disabled || loading,
    'fw-bold': isBold,
    'btn-primary': btnType === 'primary',
    'btn-danger': btnType === 'danger',
    'btn-warning': btnType === 'warning',
  }"
  (click)="handleClick()"
  [class]="'btn ' + btnClass"
  [type]="type"
  [id]="id"
  [name]="name || id"
>
  <ng-container *ngIf="loading">
    <div class="spinner-border text-secondary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </ng-container>
  <ng-content *ngIf="!loading"></ng-content>
</button>
