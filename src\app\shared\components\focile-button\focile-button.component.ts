import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'focile-button',
  templateUrl: './focile-button.component.html',
  styleUrls: ['./focile-button.component.scss'],
})
export class FocileButtonComponent {
  @Input() btnType: 'primary' | 'seconday' | 'danger' | 'warning' | null =
    'primary';
  @Input() btnClass = '';
  @Input() type = 'button';
  @Input() loading = false;
  @Input() disabled = false;
  @Input() isBold = true;
  @Input() name: any = null;
  @Input() id: any = null;
  @Output() onClick = new EventEmitter();

  constructor() {
    const randomValue = Math.random().toString();
    this.id = randomValue;
    this.name = randomValue;
  }

  handleClick() {
    this.onClick.emit();
  }
}
