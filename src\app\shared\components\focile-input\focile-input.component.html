<span class="position-relative">
  <input
    [type]="type"
    [class]="'form-control ' + elementClass"
    [id]="id"
    [placeholder]="placeholder"
    [(ngModel)]="value"
    (ngModelChange)="onChange($event)"
    (blur)="onTouched($event)"
    [disabled]="disabled"
    [autocomplete]="autocomplete"
  />
  <!-- <span
    (click)="handleIconClick()"
    class="position-absolute"
    style="top: 38px; right: 22px"
  >
    <i class="fa fa-{{ iconName }}"></i>
  </span> -->
</span>
