import {
  Component,
  EventEmitter,
  Input,
  Output,
  forwardRef,
} from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

const FOCILE_INPUT_PROVIDER = {
  provide: NG_VALUE_ACCESSOR,
  useExisting: forwardRef(() => FocileInputComponent),
  multi: true,
};

@Component({
  selector: 'app-focile-input',
  templateUrl: './focile-input.component.html',
  styleUrls: ['./focile-input.component.scss'],
  providers: [FOCILE_INPUT_PROVIDER],
})
export class FocileInputComponent implements ControlValueAccessor {
  @Input() disabled = false;
  @Input() type = 'text';
  @Input() id = 'text';
  @Input() name = 'text';
  @Input() placeholder = 'Type here';
  @Input() iconName = '';
  @Input() elementClass: any;
  @Input() autocomplete = 'off';
  @Output() onIconClick = new EventEmitter();
  value: string | undefined;
  onChange: any = () => {};
  onTouched: any = () => {};

  writeValue(value: string): void {
    this.value = value;
  }

  registerOnChange(fn: (value: string) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  handleIconClick() {
    this.onIconClick.emit();
  }
}
