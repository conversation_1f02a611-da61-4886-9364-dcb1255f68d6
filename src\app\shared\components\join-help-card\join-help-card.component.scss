.fc-join-help-card{
    label{
      font-size: 18px; 
    }
  
    p{
      font-size: 32px;
      line-height: 1.5;
      font-weight: 600;
      color: #000000;
    }
  }
  
  .join-now-btn{
    background-color:#000000;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 21px 46px;
    gap: 10px;
    width: max-content;
    height: 52px;
    border-radius: 200px;
    text-align: center;
    color: white;
    text-decoration: none;
    font-size: 18px;
    margin-top: 2rem;
  }

  .invite-favorites-wrap {
    width: 100%;
    background: url(../../../../assets/images/object-light.png) no-repeat 100% 100%;
    padding-block: 131px;
  }
  
  .invite-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 128px 64px 64px;
    gap: 47px;
    width: 1110px;
    min-height: 597px;
    background: #F2F2F2;
    border-radius: 15px;
    margin: 0px auto;
    position: relative;
  
    .dot-matrik {
      position: absolute;
      width: 190px;
      height: 177px;
      top: -35px;
      left: -43px;
      z-index: -1;
      background: url(../../../../assets/images/light-dot-pattern.svg) no-repeat center center;
    }
  }
  

  @media(max-width:768px){
    .invite-section{
      max-width:100%;
      padding: 2rem 1rem;
      border-radius: 0px;
      min-height: auto;

     P{
      font-size: 1.25rem;
      text-align: center;
     } 
     label{
      width: 100%;
      text-align: center;
     }
     .join-now-btn{
      margin: 2rem auto 0px;
      font-size: 1rem;
      padding: 1rem 2rem;
     }
    }
    .invite-favorites-wrap{
      padding: 0px;
      margin-top: 5rem;
    }
  }