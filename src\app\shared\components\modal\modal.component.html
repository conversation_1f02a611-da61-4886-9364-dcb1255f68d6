<div class="modal-header">
  <h4 class="modal-title pull-left">{{ title }}</h4>
  <button
    *ngIf="canClose"
    type="button"
    class="btn-close close pull-right"
    aria-label="Close"
    (click)="close()"
  >
    <span aria-hidden="true" class="close-btn">&times;</span>
  </button>
</div>
<div class="modal-body">
  <ng-container *ngIf="loading">
    <div class="row text-center col-md-12">
      <app-spinner></app-spinner>
    </div>
  </ng-container>
  <ng-container *ngIf="!loading">
    <ng-container *ngTemplateOutlet="templateRef"></ng-container>
  </ng-container>
</div>
<div class="modal-footer">
  <button
    class="btn btn-primary"
    [disabled]="firstButtonDisabled || loading || firstButtonLoading"
    (click)="handleFirstButtonClick($event)"
    *ngIf="firstButtonText"
    [ngClass]="{ 'text-white': firstButtonLoading }"
  >
    <span *ngIf="!firstButtonLoading">{{ firstButtonText }}</span>
    <span *ngIf="firstButtonLoading">
      <app-spinner></app-spinner>
    </span>
  </button>
  <button
    class="btn btn-primary"
    [disabled]="secondButtonDisabled || loading || secondButtonLoading"
    (click)="handleSecondButtonClick($event)"
    *ngIf="secondButtonText"
    [ngClass]="{ 'text-white': secondButtonLoading }"
  >
    <span *ngIf="!secondButtonLoading">{{ secondButtonText }}</span>
    <span *ngIf="secondButtonLoading">
      <app-spinner color="white"></app-spinner>
    </span>
  </button>
  <button
    *ngIf="closeButtonText"
    type="button"
    class="btn btn-outline-dark"
    (click)="close()"
    [disabled]="loading"
  >
    {{ closeButtonText }}
  </button>
</div>
