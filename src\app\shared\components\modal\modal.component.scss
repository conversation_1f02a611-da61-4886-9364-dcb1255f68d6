// Modal component specific styling
.modal-header {
  background: linear-gradient(135deg, #014681 0%, #0056b3 100%);
  color: white;
  border-bottom: none;
  padding: 1.5rem;
  border-radius: 12px 12px 0 0;

  .modal-title {
    font-weight: 600;
    font-size: 1.25rem;
    margin: 0;
  }

  .btn-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    opacity: 0.8;
    transition: opacity 0.3s ease;
    line-height: 1;

    &:hover {
      opacity: 1;
      transform: scale(1.1);
    }
  }
}

.modal-body {
  padding: 2rem;
  background: white;
  // Smooth content animation
  animation: modalBodySlideIn 0.4s ease-out 0.2s both;
}

.modal-footer {
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  padding: 1rem 2rem;
  border-radius: 0 0 12px 12px;

  .btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;

    &.btn-primary {
      background: linear-gradient(135deg, #014681 0%, #0056b3 100%);
      border: none;
      box-shadow: 0 4px 12px rgba(1, 70, 129, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(1, 70, 129, 0.4);
      }

      &:disabled {
        opacity: 0.6;
        transform: none;
        box-shadow: none;
      }
    }

    &.btn-outline-dark {
      border: 2px solid #6c757d;
      color: #6c757d;
      background: white;

      &:hover {
        background: #6c757d;
        color: white;
        transform: translateY(-2px);
      }
    }
  }
}

// Loading spinner animation
.text-center {
  app-spinner {
    animation: spinnerFadeIn 0.3s ease-out;
  }
}

// Keyframe animations
@keyframes modalBodySlideIn {
  from {
    opacity: 0;
    transform: translateY(15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spinnerFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
