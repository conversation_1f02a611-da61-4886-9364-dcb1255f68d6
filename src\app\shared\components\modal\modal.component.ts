import { Component, EventEmitter, Input, Output } from '@angular/core';
import { ModalService } from '../../services/modal.service';

@Component({
  selector: 'app-modal',
  templateUrl: './modal.component.html',
  styleUrls: ['./modal.component.scss'],
})
export class ModalComponent {
  @Input() title = '';
  @Input() firstButtonText!: string | null;
  @Input() firstButtonDisabled = false;
  @Input() secondButtonText!: string | null;
  @Input() secondButtonDisabled = false;
  @Input() closeButtonText: string | null = 'Cancel';
  @Input() templateRef: any;
  @Input() canClose = true;
  @Input() loading = false;
  @Input() firstButtonLoading = false;
  @Input() secondButtonLoading  = false;

  @Output() onFirstButtonClick = new EventEmitter();
  @Output() onSecondButtonClick = new EventEmitter();
  constructor(private modalService: ModalService) {}

  close() {
    this.modalService.closeModal();
  }

  handleFirstButtonClick(event: any) {
    this.onFirstButtonClick.emit(this.modalService.closeModal);
  }

  handleSecondButtonClick(event: any) {
    this.onSecondButtonClick.emit(this.modalService.closeModal);
  }
}
