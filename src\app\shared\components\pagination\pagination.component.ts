import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
} from '@angular/core';

@Component({
  selector: 'app-pagination',
  template: `
    <div class="d-flex justify-content-end p-3">
      <pagination
        [totalItems]="data.length"
        (pageChanged)="pageChanged($event)"
        previousText="&lsaquo;"
        nextText="&rsaquo;"
        firstText="&laquo;"
        lastText="&raquo;"
      >
      </pagination>
    </div>
  `,
  styleUrls: ['./pagination.component.css'],
})
export class PaginationComponent {
  @Input() data: any[] = [];
  @Output() onPageChange = new EventEmitter();

  pageChanged($event: any) {
    const previousPage = $event.page;
    const startIndex = (previousPage - 1) * $event.itemsPerPage;
    const endIndex = startIndex + $event.itemsPerPage;
    const data = this.data.slice(startIndex, endIndex);
    this.onPageChange.emit({ ...$event, data });
  }
}
