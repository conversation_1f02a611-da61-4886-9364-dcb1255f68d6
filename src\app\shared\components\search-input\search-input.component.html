<form>
  <div class="sidebar-search" [class.hideInput]="!showInput">
    <input
      #searchInput
      type="text"
      aria-label="Search in docs"
      name="search"
      placeholder="Type here"
      class="form-control"
      autocomplete="off"
      [(ngModel)]="search"
      (ngModelChange)="handleSearchChange($event)"
    />
    <ul
      class="list-group position-absolute w-100"
      style="z-index: 2; max-height: 15.625rem; overflow-y: auto"
    >
      <li
        role="button"
        class="list-group-item"
        (click)="onSelectOption(item)"
        *ngFor="let item of list"
      >
        {{ item[bindValue] || item }}
      </li>
    </ul>
  </div>
</form>
