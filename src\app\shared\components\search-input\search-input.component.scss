
.sidebar-search {
  position: relative;
  min-width: 66.67%;

  &.icon .input-container::after, .icon .input-container::after {
    content: " ";
    position: absolute;
    top: 50%;
    left: 15px;
    width: 20px;
    height: 20px;
    // background-image: url('../images/icons/search.svg');
    background-repeat: no-repeat;
    background-position: center center;
    transform: translateY(-50%);
  }

  input::placeholder {
    // color: $light-grey
  }

  .hideInput {
    display: none;
  }

  .input-container {
    width: 100%;
    position: relative;
  }

  input {
    outline-offset: -2px;
    -webkit-appearance: none;
    margin: 0;
    display: block;
    width: 100%;
    // padding: 10px 15px 10px 45px;
    font-size: 1rem;
    line-height: 1.25;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, .15);
    border-radius: .25rem;
    transition: border-color ease-in-out .15s,
    box-shadow ease-in-out .15s;
    position: relative;

    &:focus-visible {
      outline: none;
    }
  }

  .sidebar-list {
    position: absolute;;
    &.menu {
      width: 100%;
      // background-color: $main-bg-color;
      color: white;
      top: 54px;
      border-radius: 0 0 2px 2px;
      align-items: flex-start;
    }

    li {
      margin-top: 0;
      padding: 10px 15px;
    }
  }
}
