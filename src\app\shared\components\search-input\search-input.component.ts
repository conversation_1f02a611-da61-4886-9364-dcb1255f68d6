import { DOCUMENT } from '@angular/common';
import {
  Component,
  EventEmitter,
  Inject,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  forwardRef,
} from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
const FOCILE_INPUT_PROVIDER = {
  provide: NG_VALUE_ACCESSOR,
  useExisting: forwardRef(() => SearchInputComponent),
  multi: true,
};
@Component({
  selector: 'app-search-input',
  templateUrl: './search-input.component.html',
  styleUrls: ['./search-input.component.scss'],
  providers: [FOCILE_INPUT_PROVIDER],
})
export class SearchInputComponent
  implements OnInit, OnChanges, ControlValueAccessor
{
  @Input() showInput = true;
  @Input() list: Array<any> = [];
  @Input() bindValue = 'name';
  @Input() value = null;
  @Input() inputStyles: string | any = null;
  @Output() onSearchChange = new EventEmitter();
  @Output() onOptionSelect = new EventEmitter();
  @Input() onTouched: any = () => {};
  @Input() onChange: any = () => {};

  isShown = false;
  routes: Array<string> = [];
  search = null;
  ngOnInit(): void {
    this.handleSearchChange();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (
      changes.value?.currentValue &&
      changes.value?.currentValue !== changes.value?.previousValue
    ) {
      this.search = changes.value?.currentValue;
    }
  }

  handleSearchChange(result = null) {
    this.onSearchChange.emit(result);
  }

  onSelectOption(item: any) {
    this.onOptionSelect.emit(item);
  }

  writeValue(obj: any): void {}
  registerOnChange(fn: any): void {
    this.onChange = fn;
    this.onSearchChange.emit(this.value);
  }
  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }
  setDisabledState?(isDisabled: boolean): void {}
}
