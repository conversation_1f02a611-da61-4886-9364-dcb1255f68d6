// Service Activities Component Styles

// Create Post Button
.create-post-section {
  .create-post-btn {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

// Activity Actions
.activity-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  .edit-post-btn {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    transition: all 0.2s;

    &:hover {
      background-color: #014681;
      color: white;
    }
  }

  .dropdown-toggle {
    padding: 0.25rem 0.5rem;
    border: none;
    background: none;
    color: #666;

    &:hover {
      color: #333;
    }
  }

  .dropdown-menu {
    min-width: 120px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    .dropdown-item {
      display: flex;
      align-items: center;
      padding: 0.5rem 0.75rem;
      font-size: 0.875rem;
      transition: background-color 0.2s;

      &:hover {
        background-color: #f8f9fa;
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 2rem;

  .empty-state-content {
    h2 {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      color: #333;
    }

    p {
      color: #666;
      margin-bottom: 0;
    }
  }
}

.fc-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;

  .fc-share-thought {
    .fc-post-user {
      display: flex;
      align-items: center;
      gap: 0.75rem;

      .user-img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .d-flex.flex-column {
        label {
          font-weight: 600;
          margin-bottom: 0.25rem;
        }

        span {
          color: #666;
          font-size: 0.875rem;
        }
      }
    }

    .videp-preview-link {
      margin: 1rem 0;
      display: flex;
      flex-direction: column;

      a {
        color: #014681;
        text-decoration: none;
        word-break: break-all;

        &:hover {
          text-decoration: underline;
        }
      }

      img {
        max-width: 100%;
        height: auto;
        border-radius: 4px;
        margin-top: 0.5rem;
      }
    }

    .fc-post-comments {
      display: flex;
      gap: 1rem;
      margin-top: 1rem;
      padding-top: 1rem;
      border-top: 1px solid #eee;

      button {
        background: none;
        border: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 4px;
        transition: all 0.3s ease;

        &:hover {
          background-color: #f8f9fa;
          transform: translateY(-1px);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
        }

        svg {
          width: 18px;
          height: 18px;
        }

        .c-badge {
          background: #014681;
          color: white;
          border-radius: 12px;
          padding: 0.125rem 0.5rem;
          font-size: 0.75rem;
          min-width: 20px;
          text-align: center;
          transition: all 0.2s ease;
        }

        // Like button specific styles
        &.like-button {
          position: relative;

          .heart-icon {
            transition: all 0.3s ease;
          }

          &.liked {
            .heart-icon {
              animation: heartBeat 0.6s ease-in-out;
              transform: scale(1.1);
            }

            .c-badge {
              background: #ff0000;
              animation: bounceIn 0.4s ease-out;
            }
          }

          &.loading {
            opacity: 0.7;

            .spinner-border-sm {
              width: 1rem;
              height: 1rem;
              border-width: 0.1em;
              color: #014681;
            }
          }

          &:hover:not(:disabled) {
            .heart-icon {
              transform: scale(1.1);
            }
          }
        }
      }

      // Error message styles
      .alert-sm {
        padding: 0.375rem 0.75rem;
        font-size: 0.75rem;
        border-radius: 4px;
        border: none;
        background-color: #f8d7da;
        color: #721c24;
        animation: slideDown 0.3s ease-out;
      }
    }

    .fc-write-comment-post {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      margin-top: 1rem;
      padding-top: 1rem;
      border-top: 1px solid #eee;

      .cmnt-user-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        overflow: hidden;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      input {
        flex: 1;
        border: 1px solid #ddd;
        border-radius: 20px;
        padding: 0.5rem 1rem;
        font-size: 0.875rem;

        &:focus {
          outline: none;
          border-color: #014681;
        }
      }
    }

    .activity-comments {
      .single-comment {
        margin-bottom: 1rem;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 8px;

        .avatar {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          overflow: hidden;
          margin-right: 0.75rem;
          flex-shrink: 0;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .commenter-data {
          .d-flex.justify-content-between {
            font-size: 0.875rem;
            margin-bottom: 0.5rem;

            strong {
              font-weight: 600;
              color: #333;
            }

            .comment-date {
              font-weight: normal;
              color: #666;
              font-size: 0.75rem;
            }
          }

          .comment-content {
            font-size: 0.875rem;
            line-height: 1.4;
            color: #555;
          }
        }
      }

      .view-comments-toggle {
        text-align: center;

        .view-all-comments-link,
        .view-less-comments-link {
          color: #014681;
          font-size: 0.875rem;
          font-weight: 500;
          text-decoration: none;
          cursor: pointer;
          transition: color 0.2s ease;

          &:hover {
            color: #0056b3;
            text-decoration: underline;
          }
        }
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .fc-card {
    .fc-share-thought {
      .fc-post-comments {
        flex-wrap: wrap;
        gap: 0.5rem;

        button {
          font-size: 0.75rem;

          svg {
            width: 16px;
            height: 16px;
          }
        }
      }
    }
  }
}

.dropdown-toggle::after {
  display: none;
}

// Animations for like functionality
@keyframes heartBeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideDown {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
