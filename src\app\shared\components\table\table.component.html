<div class="table-responsive">
  <table class="table table-centered table-nowrap mb-0 rounded">
    <thead>
      <tr>
        <th *ngFor="let header of headers">{{ header.lable }}</th>
        <th>{{ ActionColumnName }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let row of rowData">
        <td *ngFor="let header of headers">{{ row[header.field] }}</td>
        <td *ngIf="actionTemplate">
          <ng-container
            *ngTemplateOutlet="actionTemplate; context: { row: row }"
          ></ng-container>
        </td>
      </tr>
    </tbody>
  </table>
</div>
