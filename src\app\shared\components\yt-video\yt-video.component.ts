import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { UtilsService } from '../../services/utils.service';

@Component({
  selector: 'app-yt-video',
  templateUrl: './yt-video.component.html',
})
export class YtVideoComponent implements OnInit, OnDestroy {
  @Input() link: any = '';

  constructor(private urils: UtilsService) {}

  ngOnDestroy(): void {
    this.link = null;
  }

  ngOnInit(): void {
    this.link = this.urils.getId(this.link);
  }
}
