export const EMPTY_GUID = '00000000-0000-0000-0000-000000000000';

export const APP_LOGO = './assets/svgs/focile.svg';
export const DEFAULT_CONNCTION_REQUEST_LIMIT = 25;
export const Regex = {
  personName: /^(?!\s+$)[A-Za-z\s]{2,50}$/,
  strongPassword:
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#^])[A-Za-z\d@$!%*?&#^]{12,16}$/,
  websiteUrl: /^(https?:\/\/)?www\.[a-zA-Z0-9-]+(\.[a-zA-Z]{2,})+$/,
  youtubeChannelLink:
    /^https:\/\/(www\.)?youtube\.com\/(channel\/|c\/|user\/)?[a-zA-Z0-9_-]{1,}$/,
  twitterLink: /^https:\/\/(www\.)?twitter\.com\/[A-Za-z0-9_]{1,15}$/,
  facebookLink: /^https:\/\/(www\.)?facebook\.com\/[A-Za-z0-9\.]{5,}$/,
  pinteresetLink: /^https:\/\/(www\.)?pinterest\.com\/[A-Za-z0-9_\/-]+$/,
  instagramLink: /^https:\/\/(www\.)?instagram\.com\/[A-Za-z0-9_.]{1,30}$/,
  linkedInLink: /^https:\/\/(www\.)?linkedin\.com\/(in|company)\/[A-Za-z0-9_-]+\/?$/,
  youtubeLink:
    /^(https?:\/\/)?(www\.)?(youtube\.com\/(watch\?v=|embed\/|v\/|shorts\/)|youtu\.be\/)[A-Za-z0-9_-]{11}(\S*)?$/,
  socialMediaUrlRegex:
    /^(https?:\/\/)?(www\.)?(linkedin\.com|facebook\.com|twitter\.com|instagram\.com|pinterest\.com|youtube\.com)\/[a-zA-Z0-9(\.\?)?]/,
};

export const MemberTypes = {
  1: `Reseller`,
  2: `Vendor`,
  3: 'Distributor',
};

export const MemberType = {
  Reseller: 'Reseller',
  Vendor: 'Vendor',
  Distributor: 'Distributer',
};

export enum UserProfileStatusEnum {
  Pending = 0,
  Approved = 1,
  Rejected = 2,
}

export enum FollowStatus {
  NA = 0,
  InProgress = 1,
  Reject = 2,
  Connected = 3,
  Requestwithdraw = 4,
}

export enum DropMessageType {
  Success = 0,
  Error = 1,
  Warning = 2,
  Info = 3,
  None = 4,
  ConnectionLimitExceed = 5,
}

export const PROFILE_TYPE = {
  company: 'company',
  expert: 'expert',
  user: 'user',
};

export const socialMediaIds = {
  Pintrest: 'FB3855D4-1440-4685-8AB2-224402A1B63E',
  Youtube: '6E9390E3-601C-4F1E-8F8A-2782936E9C8E',
  Twitter: '008E2CB6-C2DD-4A7F-AB91-295487B5B505',
  Instagram: '1BB3EB25-74B8-465B-AD21-4706868FD905',
  Facebook: '93E2743E-0E2B-449E-A39D-D777012251DB',
  Linkedin: '8BC6FE61-AA5F-4FD9-B058-FC69AF9A7717',
};

export const socialMediaImages: Record<string, string> = {
  'fb3855d4-1440-4685-8ab2-224402a1b63e': './assets/svgs/pintrest.svg',
  '6e9390e3-601c-4f1e-8f8a-2782936e9c8e': './assets/svgs/youtube.svg',
  '008e2cb6-c2dd-4a7f-ab91-295487b5b505': './assets/svgs/twitter.svg',
  '1bb3eb25-74b8-465b-ad21-4706868fd905': './assets/svgs/instagram.svg',
  '93e2743e-0e2b-449e-a39d-d777012251db': './assets/svgs/facebook.svg',
  '8bc6fe61-aa5f-4fd9-b058-fc69af9a7717': './assets/svgs/linkedin.svg',
};

export const companyType = {
  EndUser: 1,
  ExpertUser: 2,
  Admin: 3,
};

export const memberType = {
  reseller: 1,
  distributer: 2,
  vendor: 3,
  consultant: 4,
};

export enum companyTypes {
  EndUser = 1,
  ExpertUser = 2,
  Admin = 3,
}

export const companyTypeDisplay: {
  [key in companyTypes]: string;
} = {
  [companyTypes.Admin]: 'Admin',
  [companyTypes.EndUser]: 'EndUser',
  [companyTypes.ExpertUser]: 'ExpertUser',
};

export enum ApprovePostStatusEnum {
  Pendding = 1,
  Approved = 2,
  Reject = 3,
}

export const ApprovePostStatusEnumDisplay: {
  [key in ApprovePostStatusEnum]: string;
} = {
  [ApprovePostStatusEnum.Pendding]: 'Pendding',
  [ApprovePostStatusEnum.Approved]: 'Approved',
  [ApprovePostStatusEnum.Reject]: 'Reject',
};

export type UserState = {
  userName: string;
  userId: string;
  aspNetId: string;
  userType: number | string | null;
  comanyName: string;
  adminId?: string;
};

export type ApiResponse = {
  message: string;
  data: any;
  messageType: number;
};

export enum ApprovePostTypeEnum {
  ElevatorPitchVideo = 1,
  CompanyElevatorPitchVideo = 2,
}

export const ApprovePostTypeDisplayNames: {
  [key in ApprovePostTypeEnum]: string;
} = {
  [ApprovePostTypeEnum.ElevatorPitchVideo]: 'Elevator Pitch Video',
  [ApprovePostTypeEnum.CompanyElevatorPitchVideo]: 'Elevator Pitch Video',
};

export const ApprovePostTypeDescriptions: {
  [key in ApprovePostTypeEnum]: string;
} = {
  [ApprovePostTypeEnum.ElevatorPitchVideo]: 'Expert Elevator Pitch Video',
  [ApprovePostTypeEnum.CompanyElevatorPitchVideo]:
    'Company Elevator Pitch Video',
};

export enum ProfileStatusEnum {
  AlreadyApproved = '1',
  Approved = '2',
  Rejected = '2',
}
