import { AfterViewInit, Directive, OnInit } from '@angular/core';

@Directive({
  selector: '[appFootercardtitle]',
})
export class FootercardtitleDirective implements AfterViewInit {
  ngAfterViewInit(): void {
    this.ngOnInit();
  }
  ngOnInit(): void {
    const h4Elements = document.querySelectorAll('h4');

    h4Elements.forEach(function (h4) {
      // Get the text content of the h4 element
      let text: any = h4.textContent;

      // Remove numbers and periods
      text = text.replace(/(\d+\.*)+/g, '');

      // Trim any leading or trailing whitespace
      text = text.trim();

      // remove all periods
      text = text.replace(/[\d.]+/g, '');

      // Convert the text to lowercase
      text = text.toLowerCase();

      let modifiedText = text.charAt(0).toUpperCase() + text.slice(1);

      // Set the modified text back to the h4 element
      h4.textContent = modifiedText;
    });
  }
}

function formatDocument() {
  // Select all h4 elements in the document
  const h4Elements = document.querySelectorAll('h4');

  h4Elements.forEach(function (h4) {
    // Get the text content of the h4 element
    let text: any = h4.textContent;

    // Remove numbers and periods
    text = text.replace(/(\d+\.*)+/g, '');

    // Trim any leading or trailing whitespace
    text = text.trim();

    // Convert the text to lowercase
    text = text.toLowerCase();

    // Capitalize the first letter of each word
    let modifiedText = text.replace(/\b\w/g, function (char: any) {
      return char.toUpperCase();
    });

    // Set the modified text back to the h4 element
    h4.textContent = modifiedText;
  });
}
