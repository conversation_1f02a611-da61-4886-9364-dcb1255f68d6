import { Directive, ElementRef, HostListener, Input } from '@angular/core';

@Directive({
  selector: '[appImg]',
})
export class ImgDirective {
  @Input() type: 'blue' | 'white' = 'blue';
  @Input() viewType: 'profile' | 'banner' | 'company' | 'profile_rounded' | 'eCard' | 'white-logo' = 'profile';
  constructor(private el: ElementRef) {}

  @HostListener('error') onError() {
    const imgElement: HTMLImageElement = this.el.nativeElement;
    const imageMap: Record<typeof this.viewType,string> = {
      banner: './assets/svgs/bg.svg',
      company: `./assets/images/company_logo.png`,
      'white-logo': `./assets/images/focile_logo_white.svg`,
      profile: `./assets/images/user-avatar.svg`,
      profile_rounded: `./assets/images/user-avatar.svg`,
      eCard: `./assets/images/e-card-default.svg`,
    }
    imgElement.src = imageMap[this.viewType];
    imgElement.style.borderRadius = '0% ';
  }
}
