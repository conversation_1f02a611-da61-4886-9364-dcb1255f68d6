<ng-container [formGroup]="registrationForm">
  <ng-container>
    <div class="row pt-0 add-expert-dailog">
      <div class="col-md-6">
        <div class="mb-3">
          <label for="name" class="form-label"
            >First Name <span class="text-danger">*</span></label
          >
          <app-focile-input
            [type]="'text'"
            [id]="'id'"
            [name]="'name'"
            [disabled]="false"
            formControlName="firstname"
            [elementClass]="
              registrationForm.get('firstname')?.touched &&
              (registrationForm.get('firstname')?.errors?.required ||
                registrationForm.get('firstname')?.errors?.pattern)
                ? 'is-invalid'
                : null
            "
          ></app-focile-input>
          <ng-container *ngIf="registrationForm.get('firstname')?.touched">
            <span
              *ngIf="registrationForm.get('firstname')?.errors?.required"
              class="text-danger"
            >
              First Name is required.
            </span>
            <span
              *ngIf="registrationForm.get('firstname')?.errors?.pattern"
              class="text-danger"
            >
              First Name should contains only letters.
            </span>
          </ng-container>
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="lastname" class="form-label"
            >Last Name <span class="text-danger">*</span></label
          >
          <app-focile-input
            [type]="'text'"
            [id]="'lastname'"
            [name]="'lastname'"
            [disabled]="false"
            formControlName="lastname"
            [elementClass]="
              registrationForm.get('lastname')?.touched &&
              (registrationForm.get('lastname')?.errors?.required ||
                registrationForm.get('lastname')?.errors?.pattern)
                ? 'is-invalid'
                : null
            "
          ></app-focile-input>
          <ng-container *ngIf="registrationForm.get('lastname')?.touched">
            <span
              *ngIf="registrationForm.get('lastname')?.errors?.required"
              class="text-danger"
            >
              Last Name is required.
            </span>
            <span
              *ngIf="registrationForm.get('lastname')?.errors?.pattern"
              class="text-danger"
            >
              Last Name should contains only letters.
            </span>
          </ng-container>
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="mobile" class="form-label"
            >Mobile No. <span class="text-danger">*</span></label
          >
          <span class="d-flex gap-3">
            <focile-dropdown
              [items]="countries$ | async"
              formControlName="workMobileNumberCountryCode"
              [bindValue]="'description'"
              [bindLabel]="'description'"
              [loading]="countriesLoading"
              [clearable]="false"
            ></focile-dropdown>
            <div class="w-100">
              <app-focile-input
                [type]="'text'"
                [id]="'mobileNo'"
                [name]="'mobileNo'"
                [disabled]="false"
                formControlName="mobileNo"
                [elementClass]="
                  (registrationForm.get('mobileNo')?.touched &&
                    registrationForm.get('mobileNo')?.errors?.required) ||
                  (registrationForm.get('mobileNo')?.touched &&
                    registrationForm.get('mobileNo')?.errors?.pattern)
                    ? 'is-invalid'
                    : null
                "
              ></app-focile-input>
            </div>
          </span>
          <span
            *ngIf="
              registrationForm.get('mobileNo')?.touched &&
              registrationForm.get('mobileNo')?.errors?.required
            "
            class="text-danger"
          >
            Mobile No is required.
          </span>
          <span
            *ngIf="
              registrationForm.get('mobileNo')?.touched &&
              registrationForm.get('mobileNo')?.errors?.pattern
            "
            class="text-danger"
          >
            Mobile No is not valid.
          </span>
        </div>
      </div>
      <div class="col-md-6">
        <div>
          <label for="email" class="form-label"
            >Email <span class="text-danger">*</span></label
          >
          <app-focile-input
            [type]="'email'"
            [id]="'email'"
            [name]="'email'"
            [disabled]="false"
            [iconName]="'envelope'"
            formControlName="email"
            [elementClass]="
              (registrationForm.get('email')?.touched &&
                registrationForm.get('email')?.errors?.required) ||
              (registrationForm.get('email')?.touched &&
                registrationForm.get('email')?.errors?.email)
                ? 'is-invalid'
                : null
            "
          ></app-focile-input>
        </div>
        <span
          *ngIf="
            registrationForm.get('email')?.touched &&
            registrationForm.get('email')?.errors?.required
          "
          class="text-danger"
        >
          Email is required.
        </span>
        <span
          *ngIf="
            registrationForm.get('email')?.touched &&
            registrationForm.get('email')?.errors?.email
          "
          class="text-danger"
        >
          Email is not valid.
        </span>
      </div>
      <div class="col-md-12">
        <div class="mb-3">
          <label for="role" class="form-label"
            >Role <span class="text-danger">*</span></label
          >
          <app-dropdown
            formControlName="roleId"
            [bindValue]="'idGuid'"
            [items]="roles$ | async"
            [clearable]="false"
          ></app-dropdown>
        </div>
      </div>
    </div>
  </ng-container>
</ng-container>