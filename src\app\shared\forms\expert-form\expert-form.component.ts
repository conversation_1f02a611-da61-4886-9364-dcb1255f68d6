import { HttpClient } from '@angular/common/http';
import { Component, type OnInit } from '@angular/core';
import { ControlContainer, FormGroup } from '@angular/forms';
import { map, catchError, of, finalize, Observable } from 'rxjs';
import { AccountService } from 'src/app/modules/account/services/account.service';

@Component({
  selector: 'app-expert-form',
  templateUrl: './expert-form.component.html',
})
export class ExpertFormComponent implements OnInit {
  registrationForm!: FormGroup;
  countriesLoading!: boolean;
  statesLoading!: boolean;
  countries$!: Observable<any>;
  roles$ = this.http
    .get('Role/GetAll')
    .pipe(map((response: any) => response.data));
  states$: any;
  cities$: any;
  constructor(
    private controlContainer: ControlContainer,
    private account: AccountService,
    private http: HttpClient
  ) {}

  ngOnInit(): void {
    this.registrationForm = this.controlContainer.control?.get(
      'expert'
    ) as FormGroup;
    this.getCountries();
  }

  getCountries() {
    this.countriesLoading = true;
    this.countries$ = this.account.getCountries().pipe(
      map((response: any) => response.data),
      catchError((error) => {
        return of([]);
      }),
      finalize(() => {
        this.countriesLoading = false;
      })
    );
  }

  getStates(countryId: number) {
    this.statesLoading = true;
    this.states$ = this.account.getStates(countryId).pipe(
      map((response: any) => response.data),
      catchError((error) => {
        return of([]);
      }),
      finalize(() => {
        this.statesLoading = false;
      })
    );
  }

  getCtiies(stateId: number) {
    this.countriesLoading = true;
    this.cities$ = this.account.getCities(stateId).pipe(
      map((response: any) => response.data),
      catchError((error) => {
        return of([]);
      }),
      finalize(() => {
        this.countriesLoading = false;
      })
    );
  }
}
