<ng-container [formGroup]="linksForm">
  <div class="col-md-12 social-network-wrap">    
        <div class="form-group">
          <label for="linkedIn">Linked In</label>
          <input
            class="form-control"
            placeholder="Type here"
            formControlName="8BC6FE61-AA5F-4FD9-B058-FC69AF9A7717"
            type="text"
          />
          <div
            *ngIf="
              linksForm.get('linkedInLink')?.invalid &&
              (linksForm.get('linkedInLink')?.dirty ||
                linksForm.get('linkedInLink')?.touched)
            "
            class="text-danger"
          >
            <ng-container
              [ngTemplateOutlet]="urlValidaiton"
              [ngTemplateOutletContext]="{
                name: 'LinkedIn',
                control: '93E2743E-0E2B-449E-A39D-D777012251DB'
              }"
            ></ng-container>
          </div>
        </div>      
      <div class="form-group">
        <label for="facebookLink">Facebook</label>
        <input
          class="form-control"
          placeholder="Type here"
          formControlName="93E2743E-0E2B-449E-A39D-D777012251DB"
          type="text"
        />
        <ng-container
          [ngTemplateOutlet]="urlValidaiton"
          [ngTemplateOutletContext]="{
            name: 'Facebook',
            control: 'facebookLink'
          }"
        ></ng-container>
      </div>
      <div class="form-group">
        <label for="twitterLink">Twitter</label>
        <input
          class="form-control"
          placeholder="Type here"
          formControlName="008E2CB6-C2DD-4A7F-AB91-295487B5B505"
          type="text"
        />
        <ng-container
          [ngTemplateOutlet]="urlValidaiton"
          [ngTemplateOutletContext]="{
            name: 'Twitter',
            control: 'twitterLink'
          }"
        ></ng-container>
      </div>
      <div class="form-group">
        <label for="instagramLink">Instagram</label>
        <input
          class="form-control"
          placeholder="Type here"
          formControlName="1BB3EB25-74B8-465B-AD21-4706868FD905"
          type="text"
        />
        <ng-container
          [ngTemplateOutlet]="urlValidaiton"
          [ngTemplateOutletContext]="{
            name: 'Instagram',
            control: 'instagramLink'
          }"
        ></ng-container>
      </div>
      <div class="form-group">
        <label for="Pinterest">Pinterest</label>
        <input
          class="form-control"
          placeholder="Type here"
          formControlName="FB3855D4-1440-4685-8AB2-224402A1B63E"
          type="text"
        />
        <ng-container
          [ngTemplateOutlet]="urlValidaiton"
          [ngTemplateOutletContext]="{
            name: 'Pinterest',
            control: 'pinteresetLink'
          }"
        ></ng-container>
      </div>
      <div class="form-group">
        <label for="Youtube">Youtube</label>
        <input
          class="form-control"
          placeholder="Type here"
          formControlName="6E9390E3-601C-4F1E-8F8A-2782936E9C8E"
          type="text"
        />
        <ng-container
          [ngTemplateOutlet]="urlValidaiton"
          [ngTemplateOutletContext]="{
            name: 'Youtube',
            control: 'youtubeLink'
          }"
        ></ng-container>
      </div>    
  </div>
</ng-container>
<ng-template #urlValidaiton let-control="control" let-name="name">
  <div
    *ngIf="
      linksForm.get(control)?.invalid &&
      (linksForm.get(control)?.dirty || linksForm.get(control)?.touched)
    "
    class="text-danger"
  >
    <div *ngIf="linksForm.get(control)?.errors?.['pattern']">
      Please enter a valid {{ name }} URL.
    </div>
  </div>
</ng-template>
