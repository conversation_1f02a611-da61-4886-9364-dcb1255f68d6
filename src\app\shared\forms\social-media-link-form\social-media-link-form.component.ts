import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, type OnInit } from '@angular/core';
import {
  ControlContainer,
  FormGroup,
  ReactiveFormsModule,
} from '@angular/forms';
import { socialMediaIds } from '../../constant';

@Component({
  selector: 'app-social-media-link-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './social-media-link-form.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SocialMediaLinkFormComponent implements OnInit {
  linksForm!: FormGroup;
  socialConnectionType = socialMediaIds;
  constructor(private controlContinaer: ControlContainer) {}

  ngOnInit(): void {
    this.linksForm = this.controlContinaer.control?.get(
      'sociallinks'
    ) as FormGroup;
  }
}
