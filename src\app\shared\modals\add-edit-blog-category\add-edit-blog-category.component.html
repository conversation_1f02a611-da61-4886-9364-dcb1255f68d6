<app-modal [title]="'Blog Category'" [firstButtonText]="'Save'" [templateRef]="templateRef"
  [firstButtonDisabled]="false" (onFirstButtonClick)="handleOnSubmit()" (onSecondButtonClick)="handleCancel()">
</app-modal>

<ng-template #templateRef>
  <form id="blogCategoryForm" [formGroup]="form">
    <div class="row">
      <div class="col-md-6">
        <div class="mb-3">
          <label for="name" class="form-label">Name</label>
          <input type="text" formControlName="name" class="form-control" id="name" name="name" required />
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="slug" class="form-label">Slug</label>
          <input type="text" formControlName="slug" class="form-control" id="slug" name="slug" required />
        </div>
      </div>
      <div class="col-md-12">
        <div class="mb-3">
          <label for="description" class="form-label">Description</label>
          <textarea class="form-control" id="description" name="description" formControlName="description"
            rows="3"></textarea>
        </div>
      </div>
      <div class="col-md-12">
        <div class="form-check mb-3">
          <input type="checkbox" formControlName="isActive" class="form-check-input" id="isActive" name="isActive" />
          <label class="form-check-label" for="isActive">Is Active</label>
        </div>
      </div>
    </div>


    <div class="row">
      <div class="col-md-12">
        <table class="table">
          <thead>
            <tr>
              <th scope="col">#</th>
              <th scope="col">Name</th>
              <th scope="col">Slug</th>
              <th scope="col">Description</th>
              <th scope="col">Active</th>
              <th scope="col">Action</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of categorise;let i = index">
              <th scope="row">
                {{ i + 1}}
              </th>
              <td>{{item.name}}</td>
              <td>{{item.slug}}</td>
              <td>{{item.description}}</td>
              <td>{{item.isActive ? 'Yes' : 'No'}}</td>
              <td><button (click)="handleEdit(item)" class="btn btn-outline-primary btn-sm">Edit</button></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

  </form>
</ng-template>