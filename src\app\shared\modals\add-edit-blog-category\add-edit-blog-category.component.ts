import { HttpClient } from '@angular/common/http';
import { Component, type OnInit } from '@angular/core';
import {
  Form<PERSON>uilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';

@Component({
  selector: 'app-add-edit-blog-category',
  templateUrl: './add-edit-blog-category.component.html',
  styleUrls: ['./add-edit-blog-category.component.scss'],
})
export class AddEditBlogCategoryComponent implements OnInit {
  form!: FormGroup;

  categorise: any[] = [];

  constructor(private fb: FormBuilder, private http: HttpClient) {}

  handleOnSubmit() {
    const id = this.form.get('id')?.value;
    if (id) {
      this.http
        .put('Blog/UpdateCategory', this.form.value)
        .subscribe((response: any) => {
          if (response) {
            this.handleCancel();
            const index = this.categorise.findIndex((x) => x.id == response.id);
            this.categorise.splice(index, 1, response);
          }
        });
      return;
    }
    this.http
      .post('Blog/AddCategory', this.form.value)
      .subscribe((response) => {
        if (response) {
          this.categorise.unshift(response);
          this.form.reset();
        }
      });
  }

  getAllCategorise() {
    this.http.get('blog/getallcategories').subscribe((response: any) => {
      this.categorise = response;
    });
  }

  ngOnInit(): void {
    this.getAllCategorise();
    this.form = this.fb.group({
      name: [null, Validators.required],
      slug: [null, Validators.required],
      description: [null],
      isActive: [true],
    });

    // Auto-generate slug
    this.form.get('name')?.valueChanges.subscribe((value: string) => {
      if (!value) return;
      const slug = value
        .toLowerCase()
        .replace(/\s+/g, '-')
        .replace(/[^a-z0-9\-]/g, '');
      this.form.get('slug')?.setValue(slug, { emitEvent: false });
    });
  }

  handleEdit(item: any) {
    this.form.addControl('id', new FormControl(item.id));
    this.form.patchValue({
      name: item.name,
      slug: item.slug,
      description: item.description,
      isActive: item.isActive,
    });
  }

  handleCancel() {
    this.form.removeControl('id');
    this.form.reset();
  }
}
