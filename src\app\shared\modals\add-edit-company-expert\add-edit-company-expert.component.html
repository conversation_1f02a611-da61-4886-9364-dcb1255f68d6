<app-modal
  [title]="editMode ? 'Update expert basic profile' : 'Add New Expert'"
  [firstButtonText]="editMode ? 'Update' : 'Submit to Expert'"
  [templateRef]="templateRef"
  (onFirstButtonClick)="editMode ? updateExpert() : saveExpert()"
  [firstButtonLoading]="saving"
>
</app-modal>

<ng-template #templateRef>
  <ng-container [formGroup]="expertForm">
    <ng-container>
      <div class="row pt-0">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="name" class="form-label"
              >First Name <span class="text-danger">*</span></label
            >
            <app-focile-input
              [type]="'text'"
              [id]="'id'"
              [name]="'name'"
              [disabled]="false"
              formControlName="firstName"
              [elementClass]="
                expertForm.get('firstName')?.touched &&
                (expertForm.get('firstName')?.errors?.required ||
                  expertForm.get('firstName')?.errors?.pattern)
                  ? 'is-invalid'
                  : null
              "
            ></app-focile-input>
            <ng-container *ngIf="expertForm.get('firstName')?.touched">
              <span
                *ngIf="expertForm.get('firstName')?.errors?.required"
                class="text-danger"
              >
                First Name is required.
              </span>
              <span
                *ngIf="expertForm.get('firstName')?.errors?.pattern"
                class="text-danger"
              >
                First Name should contains only letters.
              </span>
            </ng-container>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="lastName" class="form-label"
              >Last Name <span class="text-danger">*</span></label
            >
            <app-focile-input
              [type]="'text'"
              [id]="'lastName'"
              [name]="'lastName'"
              [disabled]="false"
              formControlName="lastName"
              [elementClass]="
                expertForm.get('lastName')?.touched &&
                (expertForm.get('lastName')?.errors?.required ||
                  expertForm.get('lastName')?.errors?.pattern)
                  ? 'is-invalid'
                  : null
              "
            ></app-focile-input>
            <ng-container *ngIf="expertForm.get('lastName')?.touched">
              <span
                *ngIf="expertForm.get('lastName')?.errors?.required"
                class="text-danger"
              >
                Last Name is required.
              </span>
              <span
                *ngIf="expertForm.get('lastName')?.errors?.pattern"
                class="text-danger"
              >
                Last Name should contains only letters.
              </span>
            </ng-container>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="mobile" class="form-label fw-bold"
              >Work Phone No. <span class="text-danger">*</span></label
            >
            <span class="d-flex gap-2">
              <focile-dropdown
                [items]="countryList"
                formControlName="countryCode"
                [bindValue]="'description'"
                [bindLabel]="'description'"
                [clearable]="false"
              ></focile-dropdown>
              <div class="w-100">
                <app-focile-input
                  [type]="'text'"
                  [id]="'phoneNumber'"
                  [name]="'phoneNumber'"
                  [disabled]="false"
                  formControlName="phoneNumber"
                  [elementClass]="
                    (expertForm.get('phoneNumber')?.touched &&
                      expertForm.get('phoneNumber')?.errors?.required) ||
                    (expertForm.get('phoneNumber')?.touched &&
                      expertForm.get('phoneNumber')?.errors?.pattern)
                      ? 'is-invalid'
                      : null
                  "
                ></app-focile-input>
              </div>
            </span>
            <span
              *ngIf="
                expertForm.get('phoneNumber')?.touched &&
                expertForm.get('phoneNumber')?.errors?.required
              "
              class="text-danger"
            >
              Mobile No is required.
            </span>
            <span
              *ngIf="
                expertForm.get('phoneNumber')?.touched &&
                expertForm.get('phoneNumber')?.errors?.pattern
              "
              class="text-danger"
            >
              Mobile No is not valid.
            </span>
          </div>
        </div>
        <div class="col-md-6">
          <div>
            <label for="email" class="form-label fw-bold"
              >Email <span class="text-danger">*</span></label
            > 
            <app-focile-input
              [type]="'email'"
              [id]="'email'"
              [name]="'email'"
              [disabled]="false"
              [iconName]="'envelope'"
              formControlName="email"
              [disabled]="editMode"
              [elementClass]="
                (expertForm.get('email')?.touched &&
                  expertForm.get('email')?.errors?.required) ||
                (expertForm.get('email')?.touched &&
                  expertForm.get('email')?.errors?.email)
                  ? 'is-invalid'
                  : null
              "
            ></app-focile-input>
          </div>
          <span
            *ngIf="
              expertForm.get('email')?.touched &&
              expertForm.get('email')?.errors?.required
            "
            class="text-danger"
          >
            Email is required.
          </span>
          <span
            *ngIf="
              expertForm.get('email')?.touched &&
              expertForm.get('email')?.errors?.email
            "
            class="text-danger"
          >
            Email is not valid.
          </span>
        </div>
        <div class="col-md-12">
          <div class="mb-3">
            <label for="role" class="form-label"
              >Role <span class="text-danger">*</span></label
            >
            <app-dropdown
              formControlName="roleId"
              [bindValue]="'id'"
              [items]="roles$ | async"
              [clearable]="false"
            ></app-dropdown>
          </div>
        </div>
        <div class="col-md-12">
          <app-helper-text message="This expert profile is added by the admin and will be send to the expert to confim and access and complete the profile in fill for better search criteria by End-Users."></app-helper-text>
        </div>
      </div>
    </ng-container>
  </ng-container>
</ng-template>
