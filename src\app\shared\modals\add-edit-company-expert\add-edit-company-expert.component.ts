import { HttpClient } from '@angular/common/http';
import { Component, Input, type OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { finalize, map, tap } from 'rxjs';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { DropMessageType } from '../../constant';

@Component({
  selector: 'app-add-edit-company-expert',
  templateUrl: './add-edit-company-expert.component.html',
  styleUrls: ['./add-edit-company-expert.component.scss'],
})
export class AddEditCompanyExpertComponent implements OnInit {
  @Input() companyId!: string;
  @Input() expert!: any;
  @Input() onAdd: any;
  @Input() onEdit: any;
  expertForm!: FormGroup;
  countryList = [];
  states = [];
  cityList = [];
  roleList: any[] = [];
  savingExpert = false;
  editMode = false;
  roles$ = this.http.get('Role/GetAll').pipe(
    map((response: any) => response.data),
    tap((response) => (this.roleList = response))
  );

  saving = false;
  constructor(
    private formBuilder: FormBuilder,
    private readonly accountService: AccountService,
    private readonly http: HttpClient
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.getContries();
    if (this.expert) {
      this.editMode = true;
    }
  }

  initForm() {
    this.expertForm = this.formBuilder.group({
      firstName: [this?.expert?.firstName || null, [Validators.required]],
      lastName: [this?.expert?.lastName || null, [Validators.required]],
      phoneNumber: [
        this?.expert?.phoneNumber || null,
        [Validators.required, Validators.pattern(/^\d{6,13}$/)],
      ],
      email: [
        { value: this?.expert?.email || null, disabled: this.editMode },
        [Validators.required, Validators.email],
      ],
      countryCode: [
        this?.expert?.mobileCountryCode || null,
        [Validators.required],
      ],
      roleId: [this.expert?.roleId || null, [Validators.required]],
    });
  }

  getContries() {
    this.accountService
      .getCountries()
      .pipe(map((response: any) => response.data))
      .subscribe((response) => {
        this.countryList = response;
      });
  }

  saveExpert() {
    this.saving = true;
    const roleName: any = this.roleList.find(
      (x: any) => x.id == this.expertForm.value.roleId
    )?.name;
    const payload = {
      companyId: this.companyId,
      ...this.expertForm.value,
      roleName,
      isInvited: true,
    };

    this.http
      .post(`user/AddExpertByCompanyAdmin`, payload)
      .pipe(finalize(() => (this.saving = false)))
      .subscribe((response: any) => {
        if (response.messageType == DropMessageType.Success) {
          payload.id = response.data.id;
          this.onAdd(payload);
        } else if (response.messageType == DropMessageType.Error) {
        }
      });
  }

  updateExpert() {
    this.saving = true;
    const roleName: any = this.roleList.find(
      (x: any) => x.id == this.expertForm.value.roleId
    )?.name;
    const payload = {
      companyId: this.companyId,
      ...this.expert,
      ...this.expertForm.value,
      id: this.expert.id,
      roleName,
    };

    this.http
      .post(`user/EditExpertByCompanyAdmin`, payload)
      .pipe(finalize(() => (this.saving = false)))
      .subscribe((response: any) => {
        if (response.messageType == DropMessageType.Success) {
          this.onEdit(payload);
        } else if (response.messageType == DropMessageType.Error) {
        }
      });
  }
}
