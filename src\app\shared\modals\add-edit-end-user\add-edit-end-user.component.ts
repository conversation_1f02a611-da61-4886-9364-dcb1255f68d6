import { Component, Input, type OnInit } from '@angular/core';
import { SharedModule } from '../../shared.module';
import { CommonModule } from '@angular/common';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'app-add-edit-end-user',
  standalone: true,
  imports: [SharedModule, CommonModule, ReactiveFormsModule],
  templateUrl: './add-edit-end-user.component.html',
})
export class AddEditEndUserComponent implements OnInit {
  @Input() endUser: any;

  endUserForm!: FormGroup;
  ngOnInit(): void {
    console.log(this.endUser);
  }
}
