<app-modal
  [title]="'Add FAQ'"
  [firstButtonText]="'Save'"
  [templateRef]="templateRef"
  (onFirstButtonClick)="saveFaq()"
  [loading]="false"
>
</app-modal>

<ng-template #templateRef>
  <form>
    <div class="row">
      <div class="col-md-12 mb-3">
        <div class="form-group">
          <label for="planName">Question <code>*</code></label>
          <input
            class="form-control"
            id="planName"
            required
          />
          <!-- <ng-container
            *ngIf="
              planForm.get('name')?.errors?.required &&
              planForm.get('name')?.touched
            "
            [ngTemplateOutlet]="errorTemplate"
            [ngTemplateOutletContext]="{ field: 'Name' }"
          ></ng-container> -->
        </div>
      </div>
      <div class="col-md-12 mb-3">
        <div class="form-group">
          <label for="description">Answer <code>*</code></label>
          <textarea
            class="form-control"
            id="description"
          ></textarea>
          <!-- <ng-container
            *ngIf="
              planForm.get('description')?.errors?.required &&
              planForm.get('description')?.touched
            "
            [ngTemplateOutlet]="errorTemplate"
            [ngTemplateOutletContext]="{ field: 'Description' }"
          ></ng-container> -->
        </div>
      </div>
    </div>
    <span>Note: Required field <code>*</code></span>
  </form>
</ng-template>

<ng-template #errorTemplate let-field="field">
  <span class="text-danger"> {{ field }} is required. </span>
</ng-template>
