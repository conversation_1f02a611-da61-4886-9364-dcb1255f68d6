<app-modal
  [title]="'Create New Plan'"
  [firstButtonText]="'Save'"
  [templateRef]="templateRef"
  [firstButtonDisabled]="planForm.invalid"
  (onFirstButtonClick)="submit()"
>
</app-modal>

<ng-template #templateRef>
  <form [formGroup]="planForm">
    <div class="row">
      <div class="col-md-12 mb-3">
        <div class="form-group">
          <label for="planName">Name <code>*</code></label>
          <input
            class="form-control"
            formControlName="name"
            id="planName"
            required
          />
          <ng-container
            *ngIf="
              planForm.get('name')?.errors?.required &&
              planForm.get('name')?.touched
            "
            [ngTemplateOutlet]="errorTemplate"
            [ngTemplateOutletContext]="{ field: 'Name' }"
          ></ng-container>
        </div>
      </div>
      <div class="col-md-12 mb-3">
        <div class="form-group">
          <label for="description">Description <code>*</code></label>
          <textarea
            class="form-control"
            formControlName="description"
            id="description"
          ></textarea>
          <ng-container
            *ngIf="
              planForm.get('description')?.errors?.required &&
              planForm.get('description')?.touched
            "
            [ngTemplateOutlet]="errorTemplate"
            [ngTemplateOutletContext]="{ field: 'Description' }"
          ></ng-container>
        </div>
      </div>
      <div class="col-md-6 mb-3">
        <div class="form-group">
          <label for="connectionLimit">Connection Limit <code>*</code></label>
          <input
            class="form-control"
            formControlName="connectionLimit"
            id="connectionLimit"
            type="number"
          />
          <ng-container
            *ngIf="
              planForm.get('connectionLimit')?.errors?.required &&
              planForm.get('connectionLimit')?.touched
            "
            [ngTemplateOutlet]="errorTemplate"
            [ngTemplateOutletContext]="{ field: 'Connection Limit' }"
          ></ng-container>
        </div>
      </div>
      <div class="col-md-6 mb-3">
        <div class="form-group">
          <label for="duration">Duration in days <code>*</code></label>
          <input
            class="form-control"
            formControlName="duration"
            id="duration"
            type="number"
          />
          <ng-container
            *ngIf="
              planForm.get('duration')?.errors?.required &&
              planForm.get('duration')?.touched
            "
            [ngTemplateOutlet]="errorTemplate"
            [ngTemplateOutletContext]="{ field: 'Duration' }"
          ></ng-container>
        </div>
      </div>
      <div class="col-md-6 mb-3">
        <div class="form-group">
          <label for="price">Price <code>*</code></label>
          <input
            class="form-control"
            formControlName="price"
            id="price"
            type="number"
            min="1"
          />
          <ng-container
            *ngIf="
              planForm.get('price')?.errors?.required &&
              planForm.get('price')?.touched
            "
            [ngTemplateOutlet]="errorTemplate"
            [ngTemplateOutletContext]="{ field: 'Price' }"
          ></ng-container>
        </div>
      </div>
      <div class="col-md-6 mb-3">
        <div class="form-group">
          <label for="discount">Discount <code>*</code></label>
          <input
            class="form-control"
            formControlName="discount"
            id="discount"
            type="number"
            min="0"
          />
        </div>
      </div>
    </div>
    <span>Note: Required field <code>*</code></span>
  </form>
</ng-template>

<ng-template #errorTemplate let-field="field">
  <span class="text-danger"> {{ field }} is required. </span>
</ng-template>
