import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnInit,
} from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { SubscriptionPlanService } from '../../services/subscription-plan.service';
import { ModalService } from '../../services/modal.service';
import { ToastrService } from 'ngx-toastr';
import { EMPTY_GUID } from '../../constant';

@Component({
  selector: 'app-add-plan',
  styleUrls: ['./add-plan.component.css'],
  templateUrl: './add-plan.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AddPlanComponent implements OnInit {
  @Input() plan: any = {};
  @Input() success: any;
  planForm!: FormGroup;
  editMode = false;
  constructor(
    private readonly formBuilder: FormBuilder,
    private readonly subscriptionPlanService: SubscriptionPlanService,
    private readonly modalService: ModalService,
    private readonly toaster: ToastrService
  ) {}

  ngOnInit(): void {
    this.initForm();
  }

  initForm() {
    if (this.plan) {
      this.editMode = true;
    }
    this.planForm = this.formBuilder.group({
      name: new FormControl(this.plan?.name || null, Validators.required),
      description: new FormControl(
        this.plan?.description || null,
        Validators.required
      ),
      connectionLimit: new FormControl(
        this?.plan?.connectionLimit || null,
        Validators.required
      ),
      imageUrl: '',
      id: this?.plan?.id ?? EMPTY_GUID,
      userId: EMPTY_GUID,
      duration: new FormControl(
        this.plan?.duration || null,
        Validators.required
      ),
      price: new FormControl(this.plan?.price || 1, Validators.required),
      discount: new FormControl(this.plan?.discount || 0),
    });
  }

  submit() {
    const value = this.planForm.value;
    const formData = new FormData();
    Object.keys(value).forEach((key) => {
      formData.append(key, value[key]);
    });
    this.subscriptionPlanService.addPlan(formData).subscribe((response) => {
      if (response.messageType) {
        this.toaster.error(response.message);
      } else {
        this.success();
        this.modalService.closeModal();
      }
    });
  }
}
