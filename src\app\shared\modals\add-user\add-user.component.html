<app-modal
  [title]="getModalTitle()"
  [firstButtonText]="getModalTitle()"
  [templateRef]="templateRef"
  (onFirstButtonClick)="handleSave($event)"
  [firstButtonDisabled]="userForm.invalid"
>
</app-modal>

<ng-template #templateRef>
  <form [formGroup]="userForm">
    <div class="row">
      <div class="col-md-6">
        <div class="mb-3">
          <label for="firstName" class="form-label">First Name</label>
          <input
            type="text"
            class="form-control"
            id="firstName"
            placeholder="Enter a first name"
            formControlName="firstName"
            [ngClass]="{
              'is-invalid':
                userForm.get('firstName')?.touched &&
                (userForm.get('firstName')?.errors?.required ||
                  userForm.get('firstName')?.errors?.pattern)
            }"
          />
          <ng-container *ngIf="userForm.get('firstName')?.touched">
            <span
              *ngIf="userForm.get('firstName')?.errors?.required"
              class="text-danger"
            >
              First Name is required.
            </span>
            <span
              *ngIf="userForm.get('firstName')?.errors?.pattern"
              class="text-danger"
            >
              First Name should contains only letters.
            </span>
          </ng-container>
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="lastName" class="form-label">Last Name</label>
          <input
            type="text"
            class="form-control"
            id="lastName"
            placeholder="Enter a last name"
            formControlName="lastName"
            [ngClass]="{
              'is-invalid':
                userForm.get('lastName')?.touched &&
                (userForm.get('lastName')?.errors?.required ||
                  userForm.get('lastName')?.errors?.pattern)
            }"
          />
          <ng-container *ngIf="userForm.get('lastName')?.touched">
            <span
              *ngIf="userForm.get('lastName')?.errors?.required"
              class="text-danger"
            >
              Last Name is required.
            </span>
            <span
              *ngIf="userForm.get('lastName')?.errors?.pattern"
              class="text-danger"
            >
              Last Name should contains only letters.
            </span>
          </ng-container>
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="email" class="form-label">Email Address</label>
          <app-focile-input
            [type]="'email'"
            [id]="'email'"
            [name]="'email'"
            [disabled]="false"
            [iconName]="'envelope'"
            formControlName="email"
            [elementClass]="
              (userForm.get('email')?.touched &&
                userForm.get('email')?.errors?.required) ||
              (userForm.get('email')?.touched &&
                userForm.get('email')?.errors?.email)
                ? 'is-invalid'
                : null
            "
          ></app-focile-input>
          <span
            *ngIf="
              userForm.get('email')?.touched &&
              userForm.get('email')?.errors?.required
            "
            class="text-danger"
          >
            Email is required.
          </span>
          <span
            *ngIf="
              userForm.get('email')?.touched &&
              userForm.get('email')?.errors?.email
            "
            class="text-danger"
          >
            Email is not valid.
          </span>
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="phoneNumber" class="form-label">Phone Number</label>
          <span class="d-flex">
            <select
              formControlName="countryCode"
              class="form-control shadow-none w-25"
              value="91"
            >
              <option value="+1">+ 1</option>
              <option value="+302">+ 302</option>
            </select>
            <div class="w-100">
              <app-focile-input
                [type]="'text'"
                [id]="'phoneNumber'"
                [name]="'phoneNumber'"
                [disabled]="false"
                formControlName="phoneNumber"
                [elementClass]="
                  (userForm.get('phoneNumber')?.touched &&
                    userForm.get('phoneNumber')?.errors?.required) ||
                  (userForm.get('phoneNumber')?.touched &&
                    userForm.get('phoneNumber')?.errors?.pattern)
                    ? 'is-invalid'
                    : null
                "
              ></app-focile-input>
              <span
                *ngIf="
                  userForm.get('phoneNumber')?.touched &&
                  userForm.get('phoneNumber')?.errors?.required
                "
                class="text-danger"
              >
                Phone Number is required.
              </span>
              <span
                *ngIf="
                  userForm.get('phoneNumber')?.touched &&
                  userForm.get('phoneNumber')?.errors?.pattern
                "
                class="text-danger"
              >
                Phone Number is not valid.
              </span>
            </div>
          </span>
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="companyWebsite" class="form-label">Company Website</label>
          <app-search-input
            [list]="companyList"
            [value]="userForm.get('companyWebsite')?.value"
            [bindValue]="'description'"
            (onSearchChange)="handleSearchChange($event)"
            (onOptionSelect)="handleOptionSelection($event)"
            formControlName="companyWebsite"
            [inputStyles]="
              userForm.get('companyWebsite')?.invalid &&
              userForm.get('companyWebsite')?.touched
                ? 'is-invalid'
                : ''
            "
          ></app-search-input>
          <span
            class="invalid-feedback"
            *ngIf="userForm.get('companyWebsite')?.invalid"
          >
            Organization is required.
          </span>
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="companyName" class="form-label">Company Name</label>
          <input
            type="text"
            class="form-control"
            id="companyName"
            placeholder="Enter Company Site"
            formControlName="companyName"
          />
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="companyType" class="form-label">Channel Partner</label>
          <focile-dropdown
            [items]="experts"
            formControlName="expertId"
            [bindValue]="'id'"
            [disabled]="true"
            (change)="handleExpertChange($event)"
            [loading]="dataLoading"
            [clearable]="false"
          ></focile-dropdown>
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="companySystem" class="form-label">Company Partner Type</label>
          <focile-dropdown
            [items]="companySystems"
            [placeholder]="'Select'"
            id="companySystemIds"
            formControlName="companySystemIds"
            [loading]="isCompanySystem"
            [multiple]="true"
            [clearable]="false"
          ></focile-dropdown>
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="solution" class="form-label">Solution</label>
          <focile-dropdown
            [items]="solutionItems"
            [placeholder]="'Select'"
            [multiple]="true"
            id="solution"
            formControlName="solutionIds"
            [disabled]="userForm.disabled"
            [loading]="dataLoading"
          ></focile-dropdown>
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="role" class="form-label">Role</label>
          <focile-dropdown
            [items]="roleItems"
            [placeholder]="'Select'"
            id="roles"
            formControlName="roleId"
            [clearable]="false"
            [disabled]="userForm.disabled"
            [loading]="dataLoading"
          ></focile-dropdown>
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="productIds" class="form-label">Product</label>

          <focile-dropdown
            [items]="productItems"
            [multiple]="true"
            [placeholder]="'Select'"
            id="products"
            [clearable]="false"
            formControlName="productIds"
            [disabled]="userForm.disabled"
            [loading]="dataLoading"
          ></focile-dropdown>
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="services" class="form-label">Services</label>
          <focile-dropdown
            [items]="servicesItems"
            [multiple]="true"
            [clearable]="false"
            [placeholder]="'Select'"
            id="services"
            formControlName="serviceIds"
            [disabled]="userForm.disabled"
            [loading]="dataLoading"
          ></focile-dropdown>
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="industry" class="form-label">Industry</label>

          <focile-dropdown
            [items]="industryItems"
            [placeholder]="'Select'"
            [multiple]="true"
            [clearable]="false"
            id="industries"
            formControlName="industryIds"
            [disabled]="userForm.disabled"
            [loading]="dataLoading"
          ></focile-dropdown>
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="companySize" class="form-label">Company Size</label>
          <focile-dropdown
            [items]="companySize"
            [placeholder]="'Select'"
            id="companySize"
            bindValue="id"
            [clearable]="false"
            formControlName="companySize"
            [disabled]="userForm.disabled"
            [loading]="dataLoading"
          ></focile-dropdown>
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="technologies" class="form-label">Technologies</label>

          <focile-dropdown
            [items]="technologyItems"
            [placeholder]="'Select'"
            id="technologies"
            formControlName="technologyIds"
            [disabled]="userForm.disabled"
            [loading]="dataLoading"
            [multiple]="true"
            [clearable]="false"
          ></focile-dropdown>
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="experties" class="form-label">Expertise</label>
          <focile-dropdown
            [items]="expertiseItems"
            [placeholder]="'Select'"
            id="expertise"
            formControlName="expertiseIds"
            [disabled]="userForm.disabled"
            [loading]="dataLoading"
            [multiple]="true"
            [clearable]="false"
          ></focile-dropdown>
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="level" class="form-label">Level</label>

          <focile-dropdown
            [items]="subscriptionLevelItems"
            [placeholder]="'Select'"
            id="subsciptionLevelId"
            [bindValue]="'id'"
            formControlName="subsciptionLevelId"
            [disabled]="userForm.disabled"
            [loading]="dataLoading"
            [clearable]="false"
          ></focile-dropdown>
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="workMobileNumber" class="form-label"
            >Work Mobile number</label
          >
          <span class="d-flex">
            <select
              formControlName="workMobileCountryCode"
              class="form-control shadow-no ne w-25"
              value="91"
            >
              <option value="+1">+ 1</option>
              <option value="+302">+ 302</option>
            </select>
            <div class="w-100">
              <app-focile-input
                [type]="'text'"
                [id]="'workMobileNumer'"
                [name]="'workMobileNumer'"
                [disabled]="false"
                formControlName="workMobileNumer"
                [elementClass]="
                  (userForm.get('workMobileNumer')?.touched &&
                    userForm.get('workMobileNumer')?.errors?.required) ||
                  (userForm.get('workMobileNumer')?.touched &&
                    userForm.get('workMobileNumer')?.errors?.pattern)
                    ? 'is-invalid'
                    : null
                "
              ></app-focile-input>
            </div>
          </span>
        </div>
      </div>
      <div class="col-md-12">
        <div class="mb-3">
          <label for="Address" class="form-label">Address</label>
          <input
            type="text"
            class="form-control"
            id="Address"
            placeholder="Enter Address"
            formControlName="address"
          />
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="country" class="form-label">Country</label>

          <focile-dropdown
            [items]="countryItems"
            [loading]="dataLoading"
            [placeholder]="'Select Country'"
            formControlName="country"
            id="country"
            [bindValue]="'id'"
            (change)="handleChange('country')"
            [disabled]="userForm.disabled"
            [clearable]="false"
          ></focile-dropdown>
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="state" class="form-label">State</label>

          <focile-dropdown
            [items]="stateitems"
            [loading]="statesLoading"
            [placeholder]="'Select State'"
            formControlName="state"
            id="state"
            [bindValue]="'id'"
            (change)="handleStateChange($event)"
            [disabled]="userForm.disabled"
          ></focile-dropdown>
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="city" class="form-label">City</label>
          <focile-dropdown
            [items]="cityItems"
            [loading]="dataLoading"
            [placeholder]="'Select City'"
            formControlName="city"
            [bindValue]="'id'"
            id="city"
          ></focile-dropdown>
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="zipcode" class="form-label">Zipcode</label>
          <input
            type="text"
            class="form-control"
            id="zipcode"
            placeholder="Enter a zipcode"
            formControlName="zipCode"
          />
        </div>
      </div>
    </div>
  </form>
</ng-template>
