import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { finalize, forkJoin, tap } from 'rxjs';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { UtilsService } from '../../services/utils.service';
import { ToastrService } from 'ngx-toastr';
import { ModalService } from '../../services/modal.service';
import { Regex } from '../../constant';

@Component({
  selector: 'app-add-user',
  templateUrl: './add-user.component.html',
  styleUrls: ['./add-user.component.scss'],
})
export class AddUserComponent implements OnInit {
  @Input() memberType = '';
  userForm!: FormGroup;
  companyList: any[] = [];
  showInstallationType!: boolean;
  dataLoading!: boolean;
  companySystems: any;
  installationList: any;
  isCompanySystem!: boolean;
  experts = [];
  solutionItems = [];
  roleItems = [];
  productItems = [];
  servicesItems = [];
  industryItems = [];
  companySize = [];
  technologyItems = [];
  expertiseItems = [];
  subscriptionLevelItems = [];
  countryItems = [];
  stateitems = [];
  cityItems = [];
  statesLoading!: boolean;
  organizationType: any;
  workMobileCountryCode = '+1';
  Regx = Regex;
  constructor(
    private formBuilder: FormBuilder,
    private readonly account: AccountService,
    private readonly utils: UtilsService,
    private readonly toaster: ToastrService,
    private readonly modalService: ModalService
  ) {}

  ngOnInit(): void {
    this.getRegistraionData();
    this.ininUserForm();
  }

  getModalTitle() {
    let modalTitle = '';
    if (this.memberType === 'reseller') {
      modalTitle = 'Add Reseller'
    } else if (this.memberType === 'vendor') {
      modalTitle = 'Add Vendor'
    } else {
      modalTitle = 'Add Distributor'
    }
    return modalTitle;
  }

  handleSave(event: any) {
    const payload = {
      ...this.userForm.value,
      companyId: this.userForm.value.companyId || this.utils.getEmptyGuid(),
      workMobileNumer: `${this.userForm.value.workMobileCountryCode}_${this.userForm.value.workMobileNumer}_true`,
    };

    Object.keys(payload).forEach((key: any) => {
      if (Array.isArray(payload[key])) {
        payload[key] = payload[key].toString();
      }
    });

    this.account.registerUser(payload).subscribe((response: any) => {
      if (response.data) {
        this.toaster.success(response.message);
        this.modalService.closeModal();
      } else {
        this.toaster.success(response.message);
      }
    });
  }

  ininUserForm() {
    this.userForm = this.formBuilder.group({
      firstName: [
        null,
        Validators.required,
        Validators.pattern(this.Regx.personName),
      ],
      address: [null, Validators.required],
      city: [null, Validators.required],
      companyId: [null, Validators.required],
      companyName: [null, Validators.required],
      companySize: [null, Validators.required],
      companySystemIds: [null, Validators.required],
      companyWebsite: [null, Validators.required],
      country: [null, Validators.required],
      countryCode: ['+1', Validators.required],
      workMobileCountryCode: ['+1', Validators.required],
      email: [null, Validators.required, Validators.email],
      expertId: [null, Validators.required],
      expertiseIds: [null, Validators.required],
      industryIds: [null, Validators.required],
      isActive: true,
      isCompleted: true,
      isVerified: false,
      lastName: [
        null,
        Validators.required,
        Validators.pattern(this.Regx.personName),
      ],
      latLong: [''],
      organizationName: [null],
      organizationType: [this.utils.getEmptyGuid(), Validators.required],
      password: ['Test@1234567'],
      phoneNumber: [null, Validators.required],
      productIds: [null, Validators.required],
      roleId: [null, Validators.required],
      serviceIds: [null, Validators.required],
      solutionIds: [null, Validators.required],
      state: [null, Validators.required],
      subsciptionLevelId: [null, Validators.required],
      technologyIds: [null, Validators.required],
      userType: ['2', Validators.required],
      workMobileNumer: [null, Validators.required],
      zipCode: [null, Validators.required],
    });
  }

  handleSearchChange(searchInputValue: string) {
    this.userForm.get('companyWebsite')?.setValue(searchInputValue);
    if (searchInputValue) {
      this.account
        .searchCompanyWebsite(searchInputValue)
        .subscribe((response: any) => {
          if (response['data']) {
            this.companyList = response.data;
          }
        });
    }
  }

  getRegistraionData(userType: string = '2') {
    this.dataLoading = true;
    this.account
      .getRegistrationData(userType)
      .pipe(finalize(() => (this.dataLoading = false)))
      .subscribe(
        (response: any) => {
          this.productItems = response.data.productList;
          this.experts = response.data.expertList;
          this.industryItems = response.data.industryList;
          this.productItems = response.data.productList;
          this.servicesItems = response.data.servicesList;
          this.solutionItems = response.data.solutionList;
          this.companySize = response.data.companySizeList;
          this.technologyItems = response.data.technologyList;
          this.roleItems = response.data.roleList;
          this.countryItems = response.data.countryList;
          this.expertiseItems = response.data.expertiseList;
          this.subscriptionLevelItems = response.data.subsciptionLevelList;
          this.organizationType = response.data.organizationTypeList;
        },
        (e) => {}
      );
  }

  handleOptionSelection(searchInputValue: any) {
    this.userForm.get('companyWebsite')?.setValue(searchInputValue.description);

    this.getCompanyDetails(searchInputValue.idGuid, true);
    this.companyList = [];
  }

  getCompanyDetails($event: string, disableExpertForm: boolean) {
    if (!$event) return;

    const isUserDefiend = this.companyList.find((x: any) => x === $event);
    const isExistingCompanySelected = this.companyList.find(
      (x: any) => x.idGuid === $event
    )?.idGuid;
    if (isExistingCompanySelected) {
      this.userForm.get('companyId')?.setValue(isExistingCompanySelected);
    }
    if (isUserDefiend) return;

    this.dataLoading = true;
    this.account
      .getCompanyDetails($event)
      .pipe(
        tap((response: any) => {
          if (response?.data?.comapnyTypeId) {
            this.handleExpertChange(
              response?.data?.comapnyTypeId,
              disableExpertForm
            );
          }
        }),
        finalize(() => (this.dataLoading = false))
      )
      .subscribe((response: any) => {
        if (response.data) {
          const companyDetails = response.data;
          this.userForm.patchValue({
            companyName: companyDetails.companyName,
            companySize: companyDetails.companySize,
            comapnyTypeId: companyDetails.comapnyTypeId,
            expertId: companyDetails.comapnyTypeId,
            expertiseIds: companyDetails.expertiseIds?.split(','),
            industryIds: companyDetails.industryIds?.split(','),
            productIds: companyDetails.productIds?.split(','),
            serviceIds: companyDetails.serviceIds?.split(','),
            solutionIds: companyDetails.solutionIds?.split(','),
            technologyIds: companyDetails.technologyIds?.split(','),
            subsciptionLevelId: companyDetails.subsciptionLevelId,
            companySystemIds: companyDetails.companySystemIds?.split(','),
          });
          if (companyDetails?.expertId) {
            this.handleExpertChange(companyDetails.expertId, disableExpertForm);
          }
        } else {
        }
      });
  }

  handleExpertChange(companyType: any, disbleForm = false) {
    this.userForm.get('companySystemIds')?.setValue([]);
    this.userForm.get('companySystemIds')?.markAllAsTouched();
    if (companyType === 4) {
      this.userForm
        .get('typeOfInstallationIds')
        ?.setValidators([Validators.required]);
    } else {
      this.userForm.get('typeOfInstallationIds')?.setValidators([]);
    }

    this.showInstallationType = companyType === 4;

    const promises = [this.account.getTypeOfExpert(companyType)];
    if (this.showInstallationType) {
      promises.push(this.account.getInstallationList());
    }

    forkJoin(promises)
      .pipe(finalize(() => (this.dataLoading = false)))
      .subscribe(([typeofExpertList, installationList]) => {
        if (typeofExpertList) {
          const typeOfExpertList: any = typeofExpertList;
          this.companySystems = typeOfExpertList.data;
        }
        if (installationList) {
          const companyTypeList: any = installationList;
          this.installationList = companyTypeList.data;
        }
      });

    this.isCompanySystem = true;
    this.account
      .getTypeOfExpert(companyType)
      .pipe(finalize(() => (this.isCompanySystem = false)))
      .subscribe((response: any) => {
        this.companySystems = response.data;
        if (disbleForm) {
          // this.userForm.disable();
          this.userForm.get('roles')?.enable();
        }
      });
  }

  handleChange(changedType?: string) {
    this.account.getStates(changedType).subscribe((response: any) => {
      this.stateitems = response.data;
      this.userForm.get('state')?.setValue(null);
      this.userForm.get('state')?.markAsUntouched();
      this.userForm.get('city')?.setValue(null);
      this.userForm.get('city')?.markAsTouched();
    });
  }
  handleStateChange($event: any) {
    this.account.getCities($event).subscribe((response: any) => {
      this.cityItems = response.data;
      this.userForm.get('city')?.setValue(null);
      this.userForm.get('city')?.markAsTouched();
    });
  }
}
