import { ChangeDetectionStrategy, Component, Input } from '@angular/core';

@Component({
  selector: 'app-confirmation',
  template: `
    <app-modal
      [title]="title"
      [firstButtonText]="firstButtonText"
      [templateRef]="templateRef"
      (onFirstButtonClick)="onConfirm($event)"
    >
    </app-modal>

    <ng-template #templateRef>
      <div>{{ message }}</div>
    </ng-template>
  `,
  styleUrls: ['./confirmation.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ConfirmationComponent {
  @Input() title: string = 'Confirmation';
  @Input() message: string = '';
  @Input() firstButtonText: string = 'Yes, remove';
  @Input() onConfirm!: Function;
}
