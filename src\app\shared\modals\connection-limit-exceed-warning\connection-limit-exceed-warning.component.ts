import { Component, Input } from '@angular/core';
import { Router } from '@angular/router';
import { ModalService } from '../../services/modal.service';

@Component({
  selector: 'app-connection-limit-exceed-warning',
  templateUrl: './connection-limit-exceed-warning.component.html',
})
export class ConnectionLimitExceedWarningComponent {
  @Input() message: string = '';
  constructor(private router: Router, private modalSerice: ModalService) {}

  navigateToPlan() {
    this.closeModal();
    this.router.navigate(['subscription-plans']);
  }

  openFollowRequest() {
    this.closeModal();
    setTimeout(() => {
      this.modalSerice.openModal('view-follow-requests', {
        class: 'modal-lg',
      });
    }, 100);
  }

  closeModal() {
    this.modalSerice.closeModal();
  }
}
