<div class="create-edit-post-modal">
  <div class="modal-header">
    <h5 class="modal-title">{{ isEditMode ? 'Edit Post' : 'Create New Post' }}</h5>
    <button type="button" class="btn-close" (click)="closeModal()" aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>

  <div class="modal-body">
    <form [formGroup]="postForm" (ngSubmit)="onSubmit()">
      <!-- User Info Section -->
      <div class="user-info-section mb-3">
        <div class="d-flex align-items-center">
          <div class="user-avatar">
            <img [src]="userProfilePhoto" alt="User Avatar" class="rounded-circle">
          </div>
          <div class="user-details ms-3">
            <h6 class="mb-0">{{ userName }}</h6>
            <small class="text-muted">{{ isEditMode ? 'Editing post' : 'Creating new post' }}</small>
          </div>
        </div>
      </div>

      <!-- TinyMCE Editor -->
      <div class="form-group mb-3">
        <label class="form-label">What's on your mind? *</label>
        <app-editor
          [content]="postForm.get('content')?.value || ''"
          (onChange)="onEditorChange($event)">
        </app-editor>
        <div class="text-danger mt-2" *ngIf="postForm.get('content')?.invalid && postForm.get('content')?.touched">
          <small *ngIf="postForm.get('content')?.errors?.['required']">Content is required</small>
        </div>
      </div>
    </form>
  </div>

  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="closeModal()">
      Cancel
    </button>
    <button 
      type="submit" 
      class="btn btn-primary" 
      (click)="onSubmit()"
      [disabled]="postForm.invalid || isSubmitting">
      <span *ngIf="isSubmitting" class="spinner-border spinner-border-sm me-2" role="status"></span>
      {{ isSubmitting ? 'Saving...' : (isEditMode ? 'Update Post' : 'Create Post') }}
    </button>
  </div>
</div> 