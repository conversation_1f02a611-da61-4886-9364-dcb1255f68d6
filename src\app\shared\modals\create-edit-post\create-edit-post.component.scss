.create-edit-post-modal {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);

  .modal-header {
    padding: 1.5rem 1.5rem 1rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .modal-title {
      font-size: 1.25rem;
      font-weight: 600;
      margin: 0;
      color: #333;
    }

    .btn-close {
      background: none;
      border: none;
      font-size: 1.5rem;
      color: #666;
      cursor: pointer;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: background-color 0.2s;

      &:hover {
        background-color: #f8f9fa;
        color: #333;
      }
    }
  }

  .modal-body {
    padding: 1.5rem;

    .user-info-section {
      .user-avatar {
        width: 48px;
        height: 48px;
        overflow: hidden;
        border-radius: 50%;
        border: 2px solid #e9ecef;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .user-details {
        h6 {
          font-weight: 600;
          color: #333;
        }

        small {
          font-size: 0.875rem;
        }
      }
    }

    .form-group {
      .form-label {
        font-weight: 600;
        color: #333;
        margin-bottom: 0.5rem;
      }

      .form-control {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 0.75rem;
        font-size: 0.875rem;
        transition: border-color 0.2s, box-shadow 0.2s;

        &:focus {
          border-color: #014681;
          box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
          outline: none;
        }

        &.is-invalid {
          border-color: #dc3545;
        }
      }

      textarea.form-control {
        resize: vertical;
        min-height: 120px;
      }

      .invalid-feedback {
        font-size: 0.875rem;
        color: #dc3545;
        margin-top: 0.25rem;
      }

      // TinyMCE Editor Styles
      ::ng-deep .tox-tinymce {
        border: 2px solid #e9ecef !important;
        border-radius: 8px !important;
        overflow: hidden;

        &.tox-tinymce--focus {
          border-color: #014681 !important;
          box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
        }
      }

      ::ng-deep .tox-toolbar-overlord {
        background: #f8f9fa !important;
        border-bottom: 1px solid #e9ecef !important;
      }

      ::ng-deep .tox-edit-area {
        background: white !important;
      }

      ::ng-deep .tox-statusbar {
        background: #f8f9fa !important;
        border-top: 1px solid #e9ecef !important;
      }
    }

    .image-upload-section {
      .upload-area {
        border: 2px dashed #dee2e6;
        border-radius: 8px;
        padding: 2rem;
        text-align: center;
        transition: border-color 0.2s, background-color 0.2s;
        cursor: pointer;

        &:hover {
          border-color: #014681;
          background-color: #f8f9fa;
        }

        .file-input {
          display: none;
        }

        .upload-label {
          cursor: pointer;
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 0.5rem;

          .upload-icon {
            width: 48px;
            height: 48px;
            color: #6c757d;
            margin-bottom: 0.5rem;
          }

          span {
            color: #6c757d;
            font-size: 0.875rem;
          }
        }
      }

      .image-preview {
        position: relative;
        border-radius: 8px;
        overflow: hidden;
        border: 2px solid #e9ecef;

        .preview-image {
          width: 100%;
          height: 200px;
          object-fit: cover;
        }

        .remove-image-btn {
          position: absolute;
          top: 0.5rem;
          right: 0.5rem;
          background: rgba(0, 0, 0, 0.7);
          color: white;
          border: none;
          border-radius: 50%;
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: background-color 0.2s;

          &:hover {
            background: rgba(0, 0, 0, 0.9);
          }
        }
      }
    }
  }

  .modal-footer {
    padding: 1rem 1.5rem 1.5rem;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;

    .btn {
      padding: 0.5rem 1.5rem;
      border-radius: 6px;
      font-weight: 500;
      transition: all 0.2s;

      &.btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
        color: white;

        &:hover {
          background-color: #5a6268;
          border-color: #545b62;
        }
      }

      &.btn-primary {
        background-color: #014681;
        border-color: #014681;
        color: white;

        &:hover:not(:disabled) {
          background-color: #0056b3;
          border-color: #0056b3;
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .create-edit-post-modal {
    margin: 1rem;
    max-width: none;

    .modal-header {
      padding: 1rem;
    }

    .modal-body {
      padding: 1rem;
    }

    .modal-footer {
      padding: 1rem;
      flex-direction: column;

      .btn {
        width: 100%;
      }
    }
  }
}
