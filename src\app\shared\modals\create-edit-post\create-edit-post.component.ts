import { Component, Input, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { ToastrService } from 'ngx-toastr';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { Subject } from 'rxjs';


@Component({
    selector: 'app-create-edit-post',
    templateUrl: './create-edit-post.component.html',
    styleUrls: ['./create-edit-post.component.scss']
})
export class CreateEditPostComponent implements OnInit {
    @Input() isEditMode: boolean = false;
    @Input() postData: any = null;
    @Input() userId: string = '';
    @Input() userProfilePhoto: string = '';
    @Input() userName: string = '';

    postForm!: FormGroup;
    isSubmitting = false;
    onClose = new Subject<any>();

    constructor(
        private formBuilder: Form<PERSON>uilder,
        private accountService: AccountService,
        private toastr: ToastrService,
        private bsModalRef: BsModalRef
    ) { }

    ngOnInit(): void {
        console.log('CreateEditPostComponent initialized with:', {
            isEditMode: this.isEditMode,
            postData: this.postData,
            userId: this.userId,
            userProfilePhoto: this.userProfilePhoto,
            userName: this.userName
        });

        this.initForm();
        if (this.isEditMode && this.postData) {
            this.populateForm();
        }
    }

    initForm(): void {
        this.postForm = this.formBuilder.group({
            content: ['', [Validators.required, Validators.minLength(1)]]
        });
    }

    populateForm(): void {
        this.postForm.patchValue({
            content: this.postData.content || ''
        });
    }

    onEditorChange(content: string): void {
        this.postForm.patchValue({ content });
    }

    get formControls() {
        return this.postForm.controls;
    }

    async onSubmit(): Promise<void> {
        if (this.postForm.invalid) {
            this.toastr.error('Please fill in all required fields');
            return;
        }

        this.isSubmitting = true;

        try {
            const formData = this.postForm.value;
            const payload = {
                userId: this.userId,
                content: formData.content,
                link: '',
                imageUrl: ''
            };

            if (this.isEditMode) {
                // Edit existing post
                await this.editPost(payload);
            } else {
                // Create new post
                await this.createPost(payload);
            }
        } catch (error) {
            console.error('Error submitting post:', error);
            this.toastr.error('Failed to submit post. Please try again.');
        } finally {
            this.isSubmitting = false;
        }
    }

    private async createPost(payload: any): Promise<void> {
        return new Promise((resolve, reject) => {
            this.accountService.createPost(payload).subscribe({
                next: (response) => {
                    this.toastr.success('Post created successfully!');
                    this.onClose.next({ success: true });
                    this.bsModalRef.hide();
                    resolve();
                },
                error: (error) => {
                    console.error('Create post error:', error);
                    this.toastr.error('Failed to create post');
                    reject(error);
                }
            });
        });
    }

    private async editPost(payload: any): Promise<void> {
        return new Promise((resolve, reject) => {
            this.accountService.editPost(this.postData.id, payload).subscribe({
                next: (response) => {
                    this.toastr.success('Post updated successfully!');
                    this.onClose.next({ success: true });
                    this.bsModalRef.hide();
                    resolve();
                },
                error: (error) => {
                    console.error('Edit post error:', error);
                    this.toastr.error('Failed to update post');
                    reject(error);
                }
            });
        });
    }

    closeModal(): void {
        this.onClose.next({ success: false });
        this.bsModalRef.hide();
    }
} 