<app-modal
  [title]="'Upload Image'"
  [firstButtonText]="'Update Banner'"
  [templateRef]="templateRef"
  (onFirstButtonClick)="imageCropped()"
  [loading]="false"
> 
</app-modal>

<ng-template #templateRef>
  <div class="row">
    <div class="mb-3">
      <input
        class="form-control"
        type="file"
        id="cropperImageUpload"
        (change)="handleFileInput($event)"
      />
    </div>
    <div>
      <angular-cropper
        #angularCropperComponent
        [cropperOptions]="config"
        [imageUrl]="imageUrl"
        (cropperReady)="imageCropped()"
      ></angular-cropper>
    </div>
  </div>
</ng-template>
