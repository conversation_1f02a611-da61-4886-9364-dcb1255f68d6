import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  Output,
  ViewChild,
  type OnInit,
} from '@angular/core';
import { SharedModule } from '../../shared.module';
import { AngularCropperjsModule } from 'angular-cropperjs';
import { ImagesService } from '../../services/images.service';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { UtilsService } from '../../services/utils.service';

@Component({
  selector: 'app-cropper',
  standalone: true,
  imports: [CommonModule, SharedModule, AngularCropperjsModule],
  templateUrl: './cropper.component.html',
  styleUrls: ['./cropper.component.scss'],
})
export class CropperComponent implements OnInit {
  @Input() onCrop!: Function;
  @ViewChild('angularCropperComponent') public angularCropper!: any;

  imageUrl: string = 'assets/images/placeholder.jpeg';
  resultImage: any;

  // Plugin configuration
  config = {
    zoomable: false,
    cropBoxResizable: false,
    viewMode: 1,
    cropmove: (event: any) => {
      const cropBoxData = this.angularCropper.cropper.getCropBoxData();
      if (cropBoxData.height > 300) {
        this.angularCropper.cropper.setCropBoxData({
          height: 300,
        });
      }
    },
    data: {
      height: 300,
    },
  };

  constructor(
    private imageService: ImagesService,
    private accountService: AccountService,
    private utils: UtilsService
  ) {}
  ngOnInit(): void {}

  handleFileInput(event: any) {
    if (event.target.files.length) {
      var fileTypes = ['jpg', 'jpeg', 'png']; //acceptable file types
      var extension = event.target.files[0].name.split('.').pop().toLowerCase(), //file extension from input file
        isSuccess = fileTypes.indexOf(extension) > -1; //is extension in acceptable types
      if (isSuccess) {
        //yes
        // start file reader
        const reader = new FileReader();
        const angularCropper = this.angularCropper;
        reader.onload = (event) => {
          if (event.target?.result) {
            angularCropper.imageUrl = event.target.result as any;
          }
        };
        reader.readAsDataURL(event.target.files[0]);
      } else {
        //no
        alert('Selected file is not an image. Please select an image file.');
      }
    }
  }

  imageCropped() {
    const croppedImage = this.angularCropper.cropper
      .getCroppedCanvas()
      .toDataURL('image/jpeg', 1);
    const image = this.base64ToBlob(croppedImage);
    const formData = new FormData();
    const userId = this.accountService.user$.value.userId;
    formData.append('UserId', userId);
    formData.append('photo', image, this.utils.generateRandomFileName());
    this.imageService.uploadBanner(formData).subscribe((response) => {
      this.onCrop(croppedImage);
    });
  }

  base64ToBlob(base64: string, contentType = 'image/png', sliceSize = 512) {
    const cleanBase64String = base64.split(',')[1];
    const byteCharacters = atob(cleanBase64String);
    const byteArrays = [];

    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      const slice = byteCharacters.slice(offset, offset + sliceSize);
      const byteNumbers = new Array(slice.length);

      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }

      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }

    return new Blob(byteArrays, { type: contentType });
  }
}
