<app-modal
  [title]="'Add Testimonial'"
  [firstButtonText]="testimonial ? 'Update' : 'Save'"
  [secondButtonText]="testimonial ? 'Delete' : null"
  [templateRef]="templateRef"
  (onFirstButtonClick)="onSubmit()"
  [loading]="false"
>
</app-modal>

<ng-template #templateRef>
  <form [formGroup]="testimonialForm" (ngSubmit)="onSubmit()">
    <div class="container">
      <!-- Name Field -->
      <div class="mb-3">
        <label for="name" class="form-label">Name:</label>
        <input
          id="name"
          type="text"
          class="form-control"
          formControlName="name"
        />
        <div
          *ngIf="
            testimonialForm.get('name')?.invalid &&
            testimonialForm.get('name')?.touched
          "
          class="text-danger"
        >
          Name is required.
        </div>
      </div>

      <!-- Company Name Field -->
      <div class="mb-3">
        <label for="comapnyName" class="form-label">Company Name:</label>
        <input
          id="comapnyName"
          type="text"
          class="form-control"
          formControlName="comapnyName"
        />
        <div
          *ngIf="
            testimonialForm.get('comapnyName')?.invalid &&
            testimonialForm.get('comapnyName')?.touched
          "
          class="text-danger"
        >
          Company name is required.
        </div>
      </div>

      <!-- Message Field -->
      <div class="mb-3">
        <label for="message" class="form-label">Message:</label>
        <textarea
          id="message"
          class="form-control"
          formControlName="message"
        ></textarea>
        <div
          *ngIf="
            testimonialForm.get('message')?.invalid &&
            testimonialForm.get('message')?.touched
          "
          class="text-danger"
        >
          Message is required.
        </div>
      </div>

      <!-- Company Logo File Upload -->
      <div class="mb-3">
        <label for="companyLogoFile" class="form-label"
          >Upload Company Logo File:</label
        >
        <input
          type="file"
          class="form-control"
          accept="image/*"
          (change)="handleFileInput($event, 'comapnyLogoFile')"
        />
      </div>

      <!-- User Image File Upload -->
      <div class="mb-3">
        <label for="userImageFile" class="form-label"
          >Upload User Image File:</label
        >
        <input
          type="file"
          class="form-control"
          accept="image/*"
          (change)="handleFileInput($event, 'userImageFile')"
        />
      </div>

      <div class="row">
        <div class="col-md-6">
          <label for="userImage">User Image</label>
          <img
            [src]="testimonial?.userImage || userImage"
            class=" img-thumbnail"
            alt="User Image"
            id="userImage"
            appImg
          />
        </div>
        <div class="col-md-6">
          <label for="companyImage">Company Image</label>
          <img
            [src]="testimonial?.comapnyLogo || companyImage"
            class="img-thumbnail"
            alt="Company Image"
            id="companyImage"
            appImg
          />
        </div>
      </div>
    </div>
  </form>
</ng-template>
