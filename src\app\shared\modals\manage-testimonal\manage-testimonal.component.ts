import { HttpClient } from '@angular/common/http';
import { Component, Input, type OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-manage-testimonal',
  templateUrl: './manage-testimonal.component.html',
  styleUrls: ['./manage-testimonal.component.scss'],
})
export class ManageTestimonalComponent implements OnInit {
  @Input() testimonial: any;
  @Input() onUpdate: any;
  @Input() onSave: any;
  testimonialForm!: FormGroup;
  editMode = false;
  userImage: any;
  companyImage: any;
  constructor(
    private formBuilder: FormBuilder,
    private httpClient: HttpClient
  ) {}
  ngOnInit(): void {
    this.initTestimonialForm();
    this.editMode = this.testimonial;
  }

  initTestimonialForm() {
    this.testimonialForm = this.formBuilder.group({
      id: [this.testimonial?.id || null], // Optional field, using the value from the testimonial if it exists
      name: [this.testimonial?.name || null, [Validators.required]], // Check for name or default to null
      userImageFile: [this.testimonial?.userImageFile || ''], // Check if userImageFile exists
      comapnyLogoFile: [this.testimonial?.comapnyLogoFile || ''], // Check if userImageFile exists
      comapnyLogo: [this.testimonial?.comapnyLogo || '', [Validators.required]], // Check for companyLogo or default to null comapnyLogo
      comapnyName: [
        this.testimonial?.comapnyName || null,
        [Validators.required],
      ], // Check for companyName or default to null
      message: [this.testimonial?.message || null, [Validators.required]], // Check for message or default to null
    });
  }

  onSubmit() {
    const payload = this.testimonialForm.value;
    const formData = new FormData();
    Object.keys(payload).forEach((key) => {
      formData.append(key, payload[key] || '');
    });
    this.httpClient
      .post('Testimonials/AddUpdateTestimonials', formData)
      .subscribe((response: any) => {
        if (this.onUpdate) {
          this.onUpdate(response.data);
        } else if (this.onSave) {
          this.onSave(response.data);
        }
      });
  }

  // For file upload handling (if needed)
  handleFileInput(event: any, fieldName: string) {
    const input = event.target as HTMLInputElement;
    const reader = new FileReader();
    reader.onload = (e) => {
      if (fieldName === 'comapnyLogoFile') {
        this.companyImage = e.target?.result;
      } else {
        this.userImage = e.target?.result;
      }
      let user: any = localStorage.getItem('user');
      user = JSON.parse(user);
      user.companyBanner = this.userImage;
      localStorage.setItem('user', JSON.stringify(user));
    };
    reader.readAsDataURL(event.target.files[0]);
    if (input?.files?.length) {
      this.testimonialForm.patchValue({ [fieldName]: input.files[0] });
    }
  }
}
