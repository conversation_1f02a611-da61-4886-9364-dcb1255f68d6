import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { ModalService } from '../../services/modal.service';

@Component({
  selector: 'app-profile-not-complete-alert',
  styleUrls: ['./profile-not-complete-alert.component.css'],
  templateUrl: `./profile-not-complete-alert.component.html`,
})
export class ProfileNotCompleteAlertComponent {
  constructor(private router: Router, private modalService: ModalService) {}
  navigateToCompanyDetails() {
    this.router.navigate(['/my-account/company-details']);
    this.modalService.closeModal();
  }
}
