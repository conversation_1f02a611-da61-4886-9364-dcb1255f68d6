<app-modal
  [title]="'Your connection request history'"
  [templateRef]="templateRef"
>
</app-modal>

<ng-template #templateRef>
  <div class="row" *ngIf="connectionRequests.length && !loading; else emptyState">
    <div class="col-md-12" *ngFor="let expert of connectionRequests">
      <div
        role="button"
        class="expert-connect-card-container border d-flex gap-4 p-2"
      >
        <div>
          <img
            style="height: 36px; width: 36px; border-radius: 50px"
            [src]="expert.profilePhoto || './assets/svgs/focile.svg'"
          />
        </div>
        <div>
          <span class="fs-14 fw-bold">
            {{ expert.userName | titlecase }}
          </span>
          <p class="m-0 fs-12 text-muted">
            <i
              [tooltip]="expert.userName + '\'s Role '"
              class="fas fa-user-tag text-primary"
            ></i>
            {{ expert.roleName }} at {{ expert.companyName }}
          </p>
          <p class="fs-12 text-muted">
            <i class="fab fa-buromobelexperte text-primary"></i>
            <span *ngFor="let item of expert.expertise; let i = index">
              <span *ngIf="i > 0"> || </span> {{ item.name }}
            </span>
          </p>
        </div>
        <div class="d-flex justify-content-end pe-3 pt-2" style="flex: 1">
          <button
            class="btn btn-outline-primary btn-sm p-3 rounded-5"
            role="button"
            (click)="withdrawRequest(expert)"
          >
            Withdraw Request
          </button>
        </div>
      </div>
    </div>
  </div>
</ng-template>
<ng-template #emptyState>
  <div class="alert alert-info">No history connection history available.</div>
</ng-template>
