import { HttpClient } from '@angular/common/http';
import { Component, type OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { finalize } from 'rxjs';

@Component({
  selector: 'app-view-follow-requests',
  templateUrl: './view-follow-requests.component.html',
})
export class ViewFollowRequestsComponent implements OnInit {
  connectionRequests: any = [];
  loading = false;
  constructor(
    private http: HttpClient,
    private readonly toaster: ToastrService
  ) {}
  ngOnInit(): void {
    this.loading = true;
    this.http
      .get(
        'Follower/GetUserRequestedList?userId=FFA294D6-4A89-41DB-4727-08DBBA061294'
      )
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((response: any) => {
        if (response.data) {
          this.connectionRequests = response.data;
        }
      });
  }

  withdrawRequest(expert: any) {
    this.http
      .get('Follower/RemoveRequest?followid=' + expert.followId)
      .subscribe((response: any) => {
        if (response.message && !response.data) {
          this.toaster.error(response.message);
        } else {
          this.connectionRequests = this.connectionRequests.filter(
            (x: any) => x.followId !== expert.followId
          );
          this.toaster.success(response.message);
        }
      });
  }
}
