<app-modal
  [title]="'User Details'"
  [firstButtonText]="!user.isUserApproved ? 'Approve' : null"
  [templateRef]="templateRef"
  (onFirstButtonClick)="approveUser()"
  secondButtonText="Update"
  (onSecondButtonClick)="handleUpdate()"
  [loading]="loading"
  [secondButtonLoading]="saving"
>
</app-modal>

<ng-template #templateRef>
  <div class="modal-body" [formGroup]="formGroup">
    <div class="row">
      <div class="col-md-6">
        <div class="mb-3">
          <label for="name" class="form-label"
            >First Name <span class="text-danger">*</span></label
          >
          <app-focile-input
            [type]="'text'"
            [id]="'id'"
            [name]="'name'"
            [disabled]="false"
            formControlName="firstName"
            [elementClass]="
              formGroup.get('firstName')?.touched &&
              (formGroup.get('firstName')?.errors?.required ||
                formGroup.get('firstName')?.errors?.pattern)
                ? 'is-invalid'
                : null
            "
          ></app-focile-input>
          <ng-container *ngIf="formGroup.get('firstName')?.touched">
            <span
              *ngIf="formGroup.get('firstName')?.errors?.required"
              class="text-danger"
            >
              First Name is required.
            </span>
            <span
              *ngIf="formGroup.get('firstName')?.errors?.pattern"
              class="text-danger"
            >
              First Name should contains only letters.
            </span>
          </ng-container>
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="lastName" class="form-label"
            >Last Name <span class="text-danger">*</span></label
          >
          <app-focile-input
            [type]="'text'"
            [id]="'lastName'"
            [name]="'lastName'"
            [disabled]="false"
            formControlName="lastName"
            [elementClass]="
              formGroup.get('lastName')?.touched &&
              (formGroup.get('lastName')?.errors?.required ||
                formGroup.get('lastName')?.errors?.pattern)
                ? 'is-invalid'
                : null
            "
          ></app-focile-input>
          <ng-container *ngIf="formGroup.get('lastName')?.touched">
            <span
              *ngIf="formGroup.get('lastName')?.errors?.required"
              class="text-danger"
            >
              Last Name is required.
            </span>
            <span
              *ngIf="formGroup.get('lastName')?.errors?.pattern"
              class="text-danger"
            >
              Last Name should contains only letters.
            </span>
          </ng-container>
        </div>
      </div>
      <div class="col-md-12">
        <div>
          <label for="email" class="form-label fw-bold"
            >Email <span class="text-danger">*</span></label
          >
          <app-focile-input
            [type]="'email'"
            [id]="'email'"
            [name]="'email'"
            [disabled]="false"
            [iconName]="'envelope'"
            formControlName="email"
            [elementClass]="
              (formGroup.get('email')?.touched &&
                formGroup.get('email')?.errors?.required) ||
              (formGroup.get('email')?.touched &&
                formGroup.get('email')?.errors?.email)
                ? 'is-invalid'
                : null
            "
          ></app-focile-input>
        </div>
        <span
          *ngIf="
            formGroup.get('email')?.touched &&
            formGroup.get('email')?.errors?.required
          "
          class="text-danger"
        >
          Email is required.
        </span>
        <span
          *ngIf="
            formGroup.get('email')?.touched &&
            formGroup.get('email')?.errors?.email
          "
          class="text-danger"
        >
          Email is not valid.
        </span>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="mobile" class="form-label fw-bold"
            >Mobile No. <span class="text-danger">*</span></label
          >
          <span class="d-flex">
            <span class="w-50">
              <focile-dropdown
                [items]="countryCodes"
                formControlName="dialCode"
                [bindValue]="'description'"
                [bindLabel]="'description'"
                [loading]="dataLoading"
                [clearable]="false"
              ></focile-dropdown>
            </span>
            <div class="w-100">
              <input
                type="text"
                class="form-control"
                id="orgname"
                formControlName="mobileNumber"
                placeholder="(xxx) xxx.xxxx"
                required
                [ngClass]="{
                  'is-invalid':
                    formGroup.get('mobileNumber')?.invalid &&
                    formGroup.get('mobileNumber')?.touched
                }"
                appPhoneNumber
              />
            </div>
          </span>
          <span
            *ngIf="
              formGroup.get('mobileNumber')?.touched &&
              formGroup.get('mobileNumber')?.errors?.required
            "
            class="text-danger"
          >
            Mobile No is required.
          </span>
          <span
            *ngIf="
              formGroup.get('mobileNumber')?.touched &&
              formGroup.get('mobileNumber')?.errors?.pattern
            "
            class="text-danger"
          >
            Mobile No is not valid.
          </span>
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="orgname" class="form-label"
            >Company Website <span class="required">*</span></label
          >
          <input
            type="text"
            name="companyWebsite"
            formControlName="companyWebsite"
            id="companyWebsite"
            class="form-control"
          />
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="orgname" class="form-label"
            >Company name <span class="required">*</span></label
          >
          <input
            type="text"
            class="form-control"
            id="orgname"
            placeholder="Type here"
            formControlName="companyName"
            required
            [ngClass]="{
              'is-invalid':
                formGroup.get('companyName')?.invalid &&
                formGroup.get('companyName')?.touched
            }"
          />
          <span class="invalid-feedback"> Company name is required. </span>
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="export" class="form-label"
            >Channel Partner <span class="required">*</span></label
          >
          <focile-dropdown
            [items]="editExpert.companyTypeList"
            formControlName="companyType"
            [bindValue]="'id'"
            [disabled]="formGroup.disabled"
            [loading]="dataLoading"
            [clearable]="false"
          ></focile-dropdown>
          <span
            class="text-danger"
            *ngIf="
              formGroup.get('CompanyType')?.touched &&
              formGroup.get('CompanyType')?.errors?.required
            "
          >
            Expert is required.
          </span>
        </div>
      </div>
      <div *ngIf="false" class="col-md-6">
        <div class="mb-3">
          <label for="typeOfInstallationIds" class="form-label fw-bold"
            >Type of installation <span class="required">*</span></label
          >
          <focile-dropdown
            [items]="editExpert.installationList"
            [placeholder]="'Select'"
            id="typeOfInstallationIds"
            formControlName="typeOfInstallationIds"
            [multiple]="true"
          ></focile-dropdown>
          <span
            class="text-danger"
            *ngIf="
              formGroup.get('typeOfExpert')?.touched &&
              formGroup.get('typeOfExpert')?.errors?.required
            "
          >
            Type of expert is required.
          </span>
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3">
          <label for="roles" class="form-label fw-bold"
            >Role <span class="required">*</span></label
          >
          <focile-dropdown
            [items]="editExpert.roleList"
            [placeholder]="'Select'"
            id="roles"
            formControlName="roleId"
            [clearable]="false"
            [disabled]="formGroup.disabled"
            [loading]="dataLoading"
          ></focile-dropdown>
          <span
            class="text-danger"
            *ngIf="
              formGroup.get('roles')?.touched &&
              formGroup.get('roles')?.errors?.required
            "
          >
            Role is required.
          </span>
        </div>
      </div>
    </div>
  </div>
</ng-template>
