import { HttpClient } from '@angular/common/http';
import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { finalize, map } from 'rxjs';
import { AccountService } from 'src/app/modules/account/services/account.service';

@Component({
  selector: 'app-view-user-detail',
  styleUrls: ['./view-user-detail.component.css'],
  templateUrl: './view-user-detail.component.html',
})
export class ViewUserDetailComponent implements OnInit {
  @Input() user: any;
  @Input() onApprove: any;
  @Input() onUpdate: any;
  experts: any;
  roleItems: any;
  editExpert: any;
  formGroup!: FormGroup;
  dataLoading = false;
  countryCodes = [];
  loading = false;
  saving = false;
  constructor(
    private account: AccountService,
    private formBuilder: FormBuilder,
    private http: HttpClient
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.editExpert = this.user;
    this.getUserDetail();
    this.getRegistraionData('2');
  }

  initForm() {
    this.formGroup = this.formBuilder.group({
      firstName: [null, Validators.required],
      phoneNumber: [null, Validators.required],
      email: [null, Validators.required],
      lastName: [null, Validators.required],
      dialCode: ['+1', Validators.required],
      workMobileNumber: [null, Validators.required],
      companyName: [null, Validators.required],
      companyWebsite: [null, [Validators.required]],
      mobileNumber: [null, Validators.required],
      roleId: [null, Validators.required],
      companyType: [null, Validators.required],
    });
  }

  getUserDetail() {
    this.loading = true;
    this.account.getUserDetails(this.user.id)
    .pipe(finalize(() => this.loading = false))
    .subscribe((response: any) => {
      this.editExpert = response.data;
      this.formGroup.patchValue({
        firstName: response.data.firstName,
        lastName: response.data.lastName,
        email: response.data.email,
        companyName: response.data?.expertDetail?.companyName,
        mobileNumber: response.data?.phoneNumber,
        expertId: response.data?.expertDetail?.expertId,
        companyType: response.data?.expertDetail?.companyTypeId,
        roleId: response.data?.expertDetail?.roleId,
        dialCode: response.data.countryCode,
        companyWebsite: response.data.expertDetail.companyWebsite,
      });
    });
  }

  getRegistraionData(userType: string) {
    this.account
      .getRegistrationData(userType)
      .pipe(map((response: any) => response.data))
      .subscribe((response: any) => {
        this.experts = response.expertList;
        this.roleItems = response.roleList;
        this.countryCodes = response?.countryList;
      });
  }

  approveUser() {
    this.onApprove();
  }

  handleUpdate() {
    const payload = {
      userId: this.user.id,
      companyId: this.user?.expertDetail?.companyId || this.user?.companyId,
      ...this.formGroup.value,
    };
    this.saving = true;
    this.http
      .put('RegisterUser/UpdateEarlyAdoptor', payload)
      .pipe(finalize(() => this.saving = false))
      .subscribe((resposne: any) => {
        if (resposne.data) {
          this.onUpdate();
        }
      });
  }
}
