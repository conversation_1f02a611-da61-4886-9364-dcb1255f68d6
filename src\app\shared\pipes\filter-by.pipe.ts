import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'filterBy',
})
export class FilterByPipe implements PipeTransform {
  transform(items: any[], keyword: string): any[] {
    if (!keyword) {
      return items;
    }
    const filteredList: any[] = [];
    items.forEach((e) => {
      if (
        `${e.userName}`.toLowerCase().includes(keyword) ||
        `${e.name}`.toLowerCase().includes(keyword) ||
        this.searchFromList(e.experts, keyword)
      ) {
        filteredList.push(e);
      }
    });

    return filteredList;
  }

  searchFromList(list: any[], keyword: string): boolean {
    let includeExpert = false;
    list?.forEach((e) => {
      if (`${e.name}`.toLowerCase().includes(keyword)) {
        includeExpert = true;
      }
    });
    return includeExpert;
  }
}
