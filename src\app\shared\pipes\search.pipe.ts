import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'search'
})
export class SearchPipe implements PipeTransform {

  transform(items: any[], searchText: string, ...searchFields: string[]): any[] {
    if (!items) return [];
    if (!searchText) return items;

    searchText = searchText.toLowerCase();

    return items.filter(item => {
      return searchFields.some(field => {
        const fieldValue = this.getFieldValue(item, field);
        return fieldValue?.toString().toLowerCase().includes(searchText);
      });
    });
  }

  private getFieldValue(item: any, field: string): any {
    return field.split('.').reduce((value, key) => (value ? value[key] : null), item);
  }
}
