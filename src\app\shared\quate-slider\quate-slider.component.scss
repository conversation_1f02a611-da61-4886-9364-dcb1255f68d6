.fc-only-header{
    width: 100%;
    display: flex;
    flex-direction: row;
    margin-bottom: 2.5rem;

    label{
        color: #014681;        
        min-width: 397px;
        height: 28px;
        font-style: normal;
        font-weight: 700;
        font-size: 23px;
        line-height: 120%;
        letter-spacing: 0.2em;
        text-transform: uppercase;
        color: #014681;
        flex: none;
        order: 0;
        flex-grow: 0;

    }

    h3{
        min-width: 825px;
        font-style: normal;
        font-weight: 700;
        font-size: 44px;
        line-height: 120%;
        color: #191825;
        flex: none;
        order: 1;
        flex-grow: 0;
        margin-bottom: 20px;
    }

    p{
        max-width: 825px;
        font-style: normal;
        font-weight: 300;
        font-size: 16px;
        line-height: 1.5;
        color: rgba(25, 24, 37, 0.5);
        flex: none;
        order: 1;
        flex-grow: 0;
    }
}

.fc-easy-card-row{
    display: flex;
    flex-direction: row;
    height: auto;
    gap: 2.5rem;        
}

.full-width{
    width: 100%;    
    z-index: 11;
    position: relative;

    ::ng-deep .owl-carousel.owl-drag .owl-item{
        display: flex;
        justify-content: start;
        align-items: center;
        opacity: 1;
        width: 100% !important;        
    
        &.active.center {
            transform: inherit;
            opacity: 1;
            z-index: 10;
          }
      }
      ::ng-deep .owl-carousel .owl-stage{
        display: flex;
      }
      ::ng-deep .owl-theme .owl-nav {
        max-width: 1180px;
        margin: 0;
        text-align: right;
        line-height: 60px;
        display: flex;
        justify-content: end;
        top: -171px;
        position: absolute;
        left: 906px;
        text-align: right;
        bottom: auto;
        display: none;
      }
      
      .owl-nav button {
        background-color: #333;
        color: #fff;
        border: none;
        padding: 10px;
        border-radius: 50%;
        cursor: pointer;
      }
      
      .owl-nav button:hover {
        background-color: #555;
      }
    
      ::ng-deep {
        .owl-theme .owl-dots{
        max-width: 1244px;
        margin: 0px auto;
        text-align: left;
        margin-top: 0px;
        top: 8rem;
        position: relative;
      }
    
      .owl-theme .owl-nav [class*=owl-] {
            width: 100px;
            height: 100px;
            left: 232px;
            top: 0px;
            background: white;
            border: 1px solid rgb(0 0 0 / 10%);
            border-radius: 100px;
            transform: matrix(-1, 0, 0, 1, 0, 0);
            color: black;
            text-align: center;
            justify-content: center;
            display: flex;
            transform: inherit;
            &:hover{
                background-color: #014681;
                color: white;
            }
    
            img{
                max-width: 20px;
            }
      }
      }
    
      ::ng-deep  .owl-theme .owl-dots .owl-dot span{
        width:15px;
        height:15px;
        background: #ffffff;
      }
    
      ::ng-deep .owl-theme .owl-dots .owl-dot.active span{
        background: #ffffff47;
        width: 40px;
      }
      ::ng-deep .owl-theme .owl-dots .owl-dot:hover span{
        background: #ffffff47;
      }
}

.hide-text{
  visibility: hidden;
}

.custom-nav-arrow {
  width: 24px;
  height: 24px;
  stroke: #000; /* Adjust color */
}

// ::ng-deep {
//   .owl-theme .owl-dots {
//     max-width: 1244px;
//     margin: 0px auto;
//     text-align: left;
//     margin-top: -66px;
//   }

//   .owl-theme .owl-nav [class*=owl-] {
//     width: 100px;
//     height: 100px;
//     left: 232px;
//     top: 0px;
//     background: #FFFFFF;
//     border-radius: 100px;
//     transform: matrix(-1, 0, 0, 1, 0, 0);
//     color: black;
//     text-align: center;
//     justify-content: center;
//     display: flex;
//     transform: inherit;

//     &:hover {
//       background-color: #014681;
//       color: white;
//     }

//     img {
//       max-width: 20px;
//     }
//   }
// }


.fc-recently-card{  
  box-sizing: border-box;
  width: 100%;
  min-height: 240px;
  border: 2px solid #DDDDDD;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  padding: 24px;
  gap: 20px;

  .fc-partner-name{
    display: flex;
    flex-direction: row;
    gap: 1rem;
  }

  .fc-card-avatar{
    display: flex;
    flex-direction: row;
    img{
      width: 50px;
      height: 50px;
      object-fit: cover;
    }
  }

  .fc-rating-start{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    flex:1
  }

  figure{
    margin-bottom: 0px;
    border-radius: 25px;
    overflow: hidden;
  }

  &:hover{
    border-color: #014681;
  }
}

.text-blue-100{
  color: #4F5665;
}

.text-black-100{
  color:#0B132A;
}


.fc-top-arrow{
  img{
    width: 20px;
  }
}
.fc-bottom-arrow{
  margin-left: auto;
  text-align: right;
  img{
    width:30px;
  }
}

.fc-easy-card{
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 100%;
  background: transparent;                
  // box-shadow: 0px 513px 205px rgba(0, 0, 0, 0.01), 0px 288px 173px rgba(0, 0, 0, 0.03), 0px 128px 128px rgba(0, 0, 0, 0.04), 0px 32px 71px rgba(0, 0, 0, 0.05), 0px 0px 0px rgba(0, 0, 0, 0.05);
  flex: none;
  order: 1;
  flex-grow: 0;
  border:transparent;
  overflow: hidden;
  padding-inline: 2rem;

  div{
      padding: 0rem;

      p{                
          width:100%;
          font-style: normal;
          font-weight: 400;
          font-size: 16px;
          line-height: 30px;
          color: white;
          flex: none;
          margin-bottom: 1.5rem;          
      }
      h4{
          color: white;
          font-size: 18px;
          margin-bottom: 1.5rem;          
      }

      h5{
        color: white;
        font-size: 18px;
        margin-bottom: 1rem; 
      }
  }
} 