import { Component, Input, SimpleChanges, ChangeDetectorRef } from '@angular/core';
import { OwlOptions } from 'ngx-owl-carousel-o';

@Component({
  selector: 'app-quate-slider',
  templateUrl: './quate-slider.component.html',
  styleUrls: ['./quate-slider.component.scss']
})
export class QuateSliderComponent {
  @Input() easyCards: any[] = [];

  carouselOptions: OwlOptions = {
    loop: true,
    margin: 10,
    nav: false,
    dots: true,
    responsive: {
      0: {
        items: 1
      },
      600: {
        items: 1
      },
      1000: {
        items: 1
      }
    }
  };

  constructor(private cdr: ChangeDetectorRef) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.easyCards) {
      console.log('easyCards:', this.easyCards);
      this.cdr.detectChanges(); // Trigger change detection
    }
  }
}