import { Injectable, NgZone } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { HubConnection } from '@microsoft/signalr';
import * as signalR from '@microsoft/signalr';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { BehaviorSubject, Subject } from 'rxjs';
import { finalize } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class ChatService {
  public hubConnection!: signalR.HubConnection;
  userState$: any = {};
  chatHub$ = new Subject();
  unReadMessagesConversationsCount = new BehaviorSubject(0);
  isConnected = false;
  activeUsers = new BehaviorSubject({});
  refreshUsers = new Subject();
  onNewMessage$ = new Subject();
  onNewNotification$ = new Subject();
  messageId$ = new Subject();
  updateMessageId$ = new Subject();
  constructor(
    public http: HttpClient,
    private account: AccountService,
    private ngZone: NgZone
  ) {
    this.connectToChatHub();
  }

  setHubConnection(connection: HubConnection) {
    this.hubConnection = connection;
  }

  createConnection() {
    this.account.user$.subscribe((userState) => {
      if (Object.keys(userState || {}).length) {
        this.userState$ = userState;
      }
    });
  }

  async connectToChatHub(): Promise<void> {
    // Create or reuse the existing HubConnection
    this.hubConnection = new signalR.HubConnectionBuilder()
      .withUrl(environment.host + '/chatsocket', {
        skipNegotiation: true,
        transport: signalR.HttpTransportType.WebSockets,
      })
      .configureLogging(signalR.LogLevel.Information)
      .build();

    this.hubConnection.start().then(() => {
      this.ngZone.run(() => {
        this.onMessageReceive();
        this.onNewNotification();
        this.account.user$.subscribe((response) => {
          if (response?.userId) {
            this.connectUser(response.userId);
            this.hubConnection.invoke('getactiveusers');
            this.refreshUsers.next(true);
          }
        });

        this.hubConnection.on('updateMessage', (updateMessage, messageId) => {
          this.updateMessageId$.next({ updateMessage, messageId });
        });

        this.hubConnection.on('MessageId', (messageId) => {
          this.messageId$.next(messageId);
        });
        this.refreshUsers.subscribe(() => {
          setTimeout(() => {
            this.hubConnection.invoke('getactiveusers');

            this.hubConnection.on('getactiveusers', (users) => {
              this.activeUsers.next(users);
            });
          }, 100);
        });
      });
    });
  }

  getOnlineUsers() {
    this.ngZone.run(() => {
      setTimeout(() => {
        this.hubConnection.on('onlineusers', (users) => {
          this.activeUsers.next(users);
        });
      }, 100);
    });
  }

  getUnreadChatMessagesCount(userId: any) {
    this.http
      .get(`Chat/GetTotalUnReadMessageCounts?userId=${userId}`)
      .subscribe((response: any) => {
        if (response.data) {
          this.unReadMessagesConversationsCount.next(response.data);
        }
      });
  }

  getChatList(userId: string,isBusienss:boolean = false) {
    return this.http.get(`Chat/GetUserConvertions?userId=${userId}&isBusiness=${isBusienss}`).pipe(
      finalize(() => {
        this.refreshUsers.next(true);
      })
    );
  }

  getConnectionId() {
    if (!Object.keys(this.userState$ || {}).length) return;
    this.hubConnection
      .invoke('Connect', this.userState$?.userId, this.userState$?.userName)
      .then((response) => {})
      .catch((e) => {
        console.error({ e });
      });
  }

  sendMessageAPI(
    reciveUser: any,
    message: any,
    conversationId: string = '',
    userId: string
  ) {
    this.hubConnection
      .invoke('SendMessage', reciveUser, message, conversationId, userId)
      .then((response) => {});
  }

  getTotalMessageCount() {}

  stopConneciton() {
    // this.hubConnection?.stop();
  }

  startConnection() {
    if (
      this.hubConnection &&
      this.hubConnection.state === signalR.HubConnectionState.Connected &&
      this.hubConnection.state !== signalR.HubConnectionState.Connected
    ) {
      this.hubConnection.start();
    }
  }

  connectUser(userId: string) {
    this.hubConnection
      .invoke('connect', userId)
      .then(() => {
        this.getOnlineUsers();
      })
      .catch((e) => {
        console.log(e);
      });
  }

  onMessageReceive() {
    this.hubConnection.on('ReceiveMessage', (message: any) => {
      this.onNewMessage$.next(message);
    });
  }

  onNewNotification() {
    this.hubConnection.on('notificationReceived', (notification) => {
      this.onNewNotification$.next(notification);
    });
  }

  updateMessage(reciverId: string, messageId: string, updateMessage: string) {
    return this.hubConnection.invoke(
      'UpdateMessaage',
      reciverId,
      messageId,
      updateMessage
    );
  }

  sendNotification(
    userId: any,
    message: any,
    senderId: any,
    conversationId: any
  ) {
    this.hubConnection.invoke(
      'Notification',
      userId,
      message,
      senderId,
      conversationId
    );
  }

  sendFollowNotification(userId: any, message: any, senderId: any) {
    this.hubConnection.invoke(
      'SendFollowNotification',
      userId,
      message,
      senderId
    );
  }

  disconnectUser(userId: any) {
    this.hubConnection
      .invoke('disconnect', userId)
      .then(() => {})
      .catch((e) => {
        console.log(e);
      });
  }
}
