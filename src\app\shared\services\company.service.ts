import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class CompanyService {
  constructor(private http: HttpClient) {}

  getCompanyExperts(companyId: any) {
    return this.http.get(`Company/GetComapnyExpertList/${companyId}`);
  }

  getExpertise(companyId: any) {
    return this.http.get(`Company/expertise?companyId=${companyId}`);
  }

  getCompanyRegistrationInfo(id: string) {
    return this.http.get(`Company/registrationinfo/${id}`);
  }
}
