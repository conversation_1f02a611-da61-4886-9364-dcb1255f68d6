import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, take } from 'rxjs';

@Injectable()
export class EcardFiltersService {
  private ecardFilterSubject = new BehaviorSubject<any[]>([]);
  ecardFilters$ = this.ecardFilterSubject.asObservable();

  constructor(private httpClient: HttpClient) {}

  getEcardFilters() {
    this.httpClient
      .get('ECard/GetFilterFormData')
      .pipe(take(1))
      .subscribe((response: any) => {
        if (response.data) {
          this.ecardFilterSubject.next(response);
        }
      });
  }
}
