import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { EMPTY_GUID } from '../constant';

@Injectable({
  providedIn: 'root',
})
export class EcardService {
  constructor(private httpClient: HttpClient) {}

  getEcards(_payload: any) {
    const payload: any = {
      loginUserId: EMPTY_GUID,
      isCertified: true,
      location: 0,
      ratting: 0,
      type: 0,
      role: EMPTY_GUID,
      productId: EMPTY_GUID,
      serviceId: EMPTY_GUID,
      solutionId: EMPTY_GUID,
      industryId: EMPTY_GUID,
      companySystemId: EMPTY_GUID,
      countryId: 0,
      stateId: 0,
      zipCode: '',
      cityId: 0,
      searchTerm: '',
      expertiseId: EMPTY_GUID,
      organizationTypeId: EMPTY_GUID,
      technologyId: EMPTY_GUID,
      typeOfInstallationId: EMPTY_GUID,
      comapnySystemId: EMPTY_GUID,
      ..._payload,
      pageSize: 6,
      isPagged: true
    };
    return this.httpClient.post('ECard/GetFilteredECardList', payload);
  }
}

type filterPayload = {
  loginUserId: string;
  isCertified: boolean;
  location: number;
  ratting: number; // Consider renaming to "rating" if it's a typo
  type: number;
  role: string;
  productId: string;
  serviceId: string;
  solutionId: string;
  industryId: string;
  companySystemId: string;
  countryId: number;
  stateId: number;
  zipCode: string;
  cityId: number;
  searchTerm: string;
  companyTypeId: number;
  expertiseId: string;
  organizationTypeId: string;
  technologyId: string;
  typeOfInstallationId: string;
  comapnySystemId: string; // Consider renaming to "companySystemId" if it's a typo
};
