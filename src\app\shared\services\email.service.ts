import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root',
})
export class EmailService {
  constructor(private http: HttpClient) {}

  sendMail(emailConfig: {
    id: string;
    email: string;
    subject: string;
    body: string;
    cc: string;
    bcc: string;
  }) {
    return this.http.post(`BulkInvite/SendBulkInviteMail`, emailConfig);
  }

  sendInvitation(payload: any) {
    return this.http.post(`BulkInvite/SendBulkInviteMail`, payload);
  }
}
