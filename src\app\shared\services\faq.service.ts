import { Injectable } from '@angular/core';
import { FAQ } from '../types/faq.type';

@Injectable({
  providedIn: 'root'
})
export class FaqService {
  faqs: FAQ[] = [];
  constructor() { }


  addFaq(faq: FAQ) {
    this.faqs.push(faq);
  }

  deleteFaq(id: string) {
    const index = this.faqs.findIndex(x => x.id == id)
    this.faqs.splice(index, 1);
  }

  editFaq(id: string, faq: FAQ) {
    const index = this.faqs.findIndex(x => x.id == id)
    this.faqs[index] = faq;
  }

}
