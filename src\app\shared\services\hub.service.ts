import { Injectable } from '@angular/core';
import * as signalR from '@microsoft/signalr';
import { HubConnection } from '@microsoft/signalr';
import { Subject } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class HubService {
  private hubConnection!: HubConnection;
  private followStatus$ = new Subject();
  constructor() {}

  async initHubConnection() {
    this.hubConnection = new signalR.HubConnectionBuilder()
      .withUrl(environment.host + '/chatsocket', {
        skipNegotiation: true,
        transport: signalR.HttpTransportType.WebSockets,
      })
      .build();
    const isConnected = this.getHubState();
    if (!isConnected) {
      this.hubConnection.start().then(() => {
        // this.connectUserToHub();
        this.hubConnection.on('ChangeFollowStatus', (status) => {
          this.followStatus$.next(status);
        });
      });
    }
  }

  changeFollowStatus(userId: any, status: any) {
    this.hubConnection
      .invoke('ChangeFollowStatus', userId, status)
      .then(() => {});
  }

  listenFollowStatus() {
    this.hubConnection.on('ChangeFollowStatus', (status) => {
      this.followStatus$.next(status);
    });
  }

  getFollowStatus() {
    return this.followStatus$.asObservable();
  }

  getHubState(): boolean {
    return this.hubConnection.state === signalR.HubConnectionState.Connected;
  }

  setHub(hub: any) {
    this.hubConnection = hub;
  }

  getHubConnection(): HubConnection {
    return this.hubConnection;
  }

  async connectUserToHub() {
    try {
      let user: any = localStorage.getItem('user');
      if (user) {
        user = JSON.parse(user);
        await this.hubConnection.invoke('Connect', user.userId, user.userName);
      }
    } catch (error) {
      console.error('connectUserToHub', error);
    }
  }

  disconnectHub() {
    // if (this.getHubState()) this.hubConnection.stop();
  }
}
