import { Injectable } from '@angular/core';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { ModalTypes } from '../modal.type';
import { AddEditCompanyExpertComponent } from '../modals/add-edit-company-expert/add-edit-company-expert.component';
import { ProfileNotCompleteAlertComponent } from '../modals/profile-not-complete-alert/profile-not-complete-alert.component';
import { ConnectionLimitExceedWarningComponent } from '../modals/connection-limit-exceed-warning/connection-limit-exceed-warning.component';
import { ViewExpertsComponent } from '../modals/view-experts/view-experts.component';
import { AddUserComponent } from '../modals/add-user/add-user.component';
import { AddPlanComponent } from '../modals/add-plan/add-plan.component';
import { ViewUserDetailComponent } from '../modals/view-user-detail/view-user-detail.component';
import { AddEditFaqComponent } from '../modals/add-edit-faq/add-edit-faq.component';
import { AddEditEndUserComponent } from '../modals/add-edit-end-user/add-edit-end-user.component';
import { ConfirmationComponent } from '../modals/confirmation/confirmation.component';
import { ManageTestimonalComponent } from '../modals/manage-testimonal/manage-testimonal.component';
import { CropperComponent } from '../modals/cropper/cropper.component';
import { ViewFollowRequestsComponent } from '../modals/view-follow-requests/view-follow-requests.component';
import { AddEditBlogCategoryComponent } from '../modals/add-edit-blog-category/add-edit-blog-category.component';
import { AddEditBlogComponent } from 'src/app/modules/admin/add-edit-blog/add-edit-blog.component';
import { BlogPreviewComponent } from 'src/app/modules/admin/blog-preview/blog-preview.component';
import { CreateEditPostComponent } from '../modals/create-edit-post/create-edit-post.component';


@Injectable({
  providedIn: 'root',
})
export class ModalService {
  modalRef!: BsModalRef;

  modalMapper = new Map<ModalTypes, any>([
    ['add-edit-company-expert', AddEditCompanyExpertComponent],
    ['profile-not-complete', ProfileNotCompleteAlertComponent],
    ['connection-limit-exceed', ConnectionLimitExceedWarningComponent],
    ['view-expert', ViewExpertsComponent],
    ['add-user', AddUserComponent],
    ['add-plan', AddPlanComponent],
    ['view-user-detail', ViewUserDetailComponent],
    ['add-edit-faq', AddEditFaqComponent],
    ['cropper', CropperComponent],
    ['confirmation', ConfirmationComponent],
    ['add-edit-testimonial', ManageTestimonalComponent],
    ['add-edit-end-user', AddEditEndUserComponent],
    ['add-edit-testimonial', ManageTestimonalComponent],
    ['view-follow-requests', ViewFollowRequestsComponent],
    ['add-edit-blog-category', AddEditBlogCategoryComponent],
    ['add-edit-blog', AddEditBlogComponent],
    ['blog-preview', BlogPreviewComponent],
    ['create-edit-post', CreateEditPostComponent],
  ]);

  constructor(private bsModalService: BsModalService) {
    console.log('ModalService initialized with components:', Array.from(this.modalMapper.keys()));
  }

  openModal(modalName: ModalTypes, intialState: ModalOptions = {}) {
    console.log('Opening modal:', modalName);
    let _component = this.modalMapper.get(modalName);

    if (!_component) {
      console.error(`Modal component not found for: ${modalName}`);
      console.log('Available modal types:', Array.from(this.modalMapper.keys()));
      return;
    }

    // Default modal configuration with centering and animations
    const defaultConfig: ModalOptions = {
      animated: true,
      keyboard: true,
      backdrop: true,
      ignoreBackdropClick: false,
      ...intialState
    };

    // Properly handle class combination for centering
    let modalClass = 'modal-dialog-centered';
    if (intialState.class) {
      modalClass = `${intialState.class} modal-dialog-centered`;
    }
    defaultConfig.class = modalClass;

    // Show the modal
    this.modalRef = this.bsModalService.show(_component, defaultConfig);

    // Apply custom styling and animations after modal is rendered
    setTimeout(() => {
      this.applyModalStyling();
    }, 50);

    return this.modalRef;
  }

  private applyModalStyling() {
    const modalElement = document.querySelector('.modal') as HTMLElement;
    const modalDialog = document.querySelector('.modal-dialog') as HTMLElement;

    if (modalElement && modalDialog) {
      // Add custom animation class
      modalElement.classList.add('custom-modal-animation');

      // Ensure proper centering
      modalElement.style.display = 'flex';
      modalElement.style.alignItems = 'center';
      modalElement.style.justifyContent = 'center';
      modalElement.style.padding = '1rem';

      // Add smooth animation to dialog
      modalDialog.classList.add('modal-animated');
    }
  }

  closeModal() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

  // Method to close modal with custom animation
  closeModalWithAnimation() {
    if (this.modalRef) {
      const modalElement = document.querySelector('.modal') as HTMLElement;
      if (modalElement) {
        modalElement.classList.add('fade-out');

        setTimeout(() => {
          this.modalRef.hide();
        }, 300);
      } else {
        this.modalRef.hide();
      }
    }
  }
}
