import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable()
export class SolutionService {
  private solutionsSubject = new BehaviorSubject<any[]>([]);
  solutions$ = this.solutionsSubject.asObservable();

  constructor(private http: HttpClient) {}

  getSolutions() {
    this.http
      .get(`Solution/GetSolutionLookUp`)
      .subscribe((solutionsResponse: any) => {
        if (solutionsResponse.data) {
          solutionsResponse.data.map((solution: any) => {
            solution.image = solution.description;
            solution.text = solution.name;
            return solution;
          });
          this.solutionsSubject.next(solutionsResponse);
        }
      });
  }
}
