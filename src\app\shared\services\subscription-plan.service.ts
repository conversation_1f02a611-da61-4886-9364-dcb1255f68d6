import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class SubscriptionPlanService {
  constructor(private http: HttpClient) {}

  getPlans(): Observable<PlansResponse> {
    return this.http.get<PlansResponse>(
      'SubscriptionPlan/GetSubscriptionPlanList'
    );
  }

  addPlan(plan: any): Observable<PlansResponse> {
    return this.http.post<PlansResponse>('SubscriptionPlan/AddUpdate', plan);
  }

  updatePlan(plan: PlanDto) {
    return this.http.post('SubscriptionPlan/AddUpdate', plan);
  }

  deletePlan(planId: any) {
    return this.http.delete('SubscriptionPlan/delete?id=' + planId);
  }

  addUserPlan(plan: any) {
    return this.http.post('SubscriptionPlan/AddUserPlan', plan);
  }

  getTransations(id: any) {
    return this.http.get(`SubscriptionPlan/Transactions/${id}`);
  }

  createPayment(planId: any,userId:any) {
    return this.http.post(`SubscriptionPlan/CreatePaymentIntent?planId=${planId}&UserId=${userId}`, {});
  }
}

export interface PlansResponse {
  message: string;
  data: Datum[];
  error: null;
  messageType: number;
}

export interface Datum {
  id: number;
  idLong: number;
  idGuid: string;
  name: string;
  image: null;
  description: null;
  otherData: number;
  selected: boolean;
}

export interface PlanDto {
  id: string;
  name: string;
  connectionLimit: number;
  description: string;
  imageUrl: string;
  duration: number;
  userId: string;
}
