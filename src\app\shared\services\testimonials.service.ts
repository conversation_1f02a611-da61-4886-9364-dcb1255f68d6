import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, take } from 'rxjs';

@Injectable()
export class TestimonialsService {
  private testimonialsSubject = new BehaviorSubject<Testimonials[]>([]);
  testimonials$ = this.testimonialsSubject.asObservable();

  constructor(private httpClient: HttpClient) {}

  getTestimonials() {
    this.httpClient
      .get('Testimonials/GetTestimonials')
      .pipe(take(1))
      .subscribe((response: any) => {
        if (response.data) {
          this.testimonialsSubject.next(response.data);
        }
      });
  }
}
export type Testimonials = {
  id:          string;
  name:        string;
  comapnyLogo: string;
  userImage:   string;
  comapnyName: string;
  message:     string;
  isActive:    boolean;
}
