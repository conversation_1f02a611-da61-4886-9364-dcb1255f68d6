import { Injectable } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class UtilsService {
  loading = true;
  location$ = new BehaviorSubject({
    latitude: null,
    longitude: null,
  });

  constructor(private domSanitizer: DomSanitizer) {}

  generateRandomFileName(length: number = 10) {
    const characters =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let randomString = '';

    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * characters.length);
      randomString += characters.charAt(randomIndex);
    }

    return randomString + '.jpeg';
  }

  getProfileImageSize() {
    return 2000 * 1024; // 2MB
  }

  getBannerImageSize() {
    return 5000 * 1024; // 5MB
  }

  getEmptyGuid() {
    return `00000000-0000-0000-0000-000000000000`;
  }

  hideNumber(numberStr: string) {
    return '*'.repeat(numberStr.length);
  }

  decodePhoneNumber(
    numberStr: string
  ): { show: boolean; number: any } | string {
    if (!numberStr.includes('_')) {
      return numberStr;
    }
    const numberParts = numberStr.split('_');
    const showNumber = JSON.parse(numberParts[1]);
    const number: string = numberParts[0];
    return {
      show: showNumber,
      number,
    };
  }

  setProfileImage(profileImage: string) {
    if (!profileImage || profileImage?.endsWith('UserProfilePhoto/')) {
      profileImage = './assets/svgs/focile.svg';
    }
    return profileImage;
  }

  getWorkConfig(workMobileNumber: string) {
    if (!workMobileNumber) return {};
    const workMobileParts = workMobileNumber.split('_');
    if (workMobileParts.length == 2) {
      workMobileParts.unshift('+1');
    }
    if (workMobileParts.length == 1) {
      workMobileParts.unshift('+1');
      workMobileParts.push('true');
    }
    const show = JSON.parse(workMobileParts[2]);
    return {
      countryCode: workMobileParts[0],
      workMobile: show
        ? workMobileParts[1]
        : this.hideNumber(workMobileParts[1]),
      show,
    };
  }

  getWorkMobileNumber(workMobileNumber: string) {
    const workMobileParts = workMobileNumber.split('_');
    if (workMobileParts.length == 2) {
      workMobileParts.unshift('+1');
    }
    if (workMobileParts.length == 1) {
      workMobileParts.unshift('+1');
      workMobileParts.push('true');
    }
    const show = JSON.parse(workMobileParts[2]);
    return {
      countryCode: workMobileParts[0],
      workMobile: workMobileParts[1],
      show,
    };
  }

  getPageSize = (array: any[] = [], pageSize = 1) =>
    Math.ceil(array.length / pageSize);

  getId(url: string) {
    if (!url) return null;
    var regExp =
      /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
    var match = url.match(regExp);

    if (match && match[2].length == 11) {
      return this.domSanitizer.bypassSecurityTrustResourceUrl(
        `//www.youtube.com/embed/` + match[2]
      );
    } else {
      return 'error';
    }
  }
}
