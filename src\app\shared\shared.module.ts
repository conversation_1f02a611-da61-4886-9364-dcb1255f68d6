import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ECardComponent } from './components/e-card/e-card.component';
import { SvgDirective } from './directive/svg.directive';
import { FDropdownComponent } from './components/f-dropdown/f-dropdown.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { FocileButtonComponent } from './components/focile-button/focile-button.component';
import { FocileInputComponent } from './components/focile-input/focile-input.component';
import { FocileFormElementComponent } from './components/focile-form-element/focile-form-element.component';
import { HelperTextComponent } from './components/helper-text/helper-text.component';
import { FocileChipComponent } from './components/focile-chip/focile-chip.component';
import { BsDropdownConfig, BsDropdownModule } from 'ngx-bootstrap/dropdown';
import { PopoverModule } from 'ngx-bootstrap/popover';
import { ModalModule } from 'ngx-bootstrap/modal';
import { SearchInputComponent } from './components/search-input/search-input.component';
import { SpinnerComponent } from './components/spinner/spinner.component';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { FromNowPipe } from './pipes/from-now.pipe';
import { FilterByPipe } from './pipes/filter-by.pipe';
import { ModalComponent } from './components/modal/modal.component';
import { AddUserComponent } from './modals/add-user/add-user.component';
import { PaginationComponent } from './components/pagination/pagination.component';
import { PaginationModule } from 'ngx-bootstrap/pagination';
import { ImgDirective } from './directives/img.directive';
import { ConfirmationComponent } from './modals/confirmation/confirmation.component';
import { ViewUserDetailComponent } from './modals/view-user-detail/view-user-detail.component';
import { AddPlanComponent } from './modals/add-plan/add-plan.component';
import { ConnectionLimitExceedWarningComponent } from './modals/connection-limit-exceed-warning/connection-limit-exceed-warning.component';
import { ViewFollowRequestsComponent } from './modals/view-follow-requests/view-follow-requests.component';
import { FocileBannersComponent } from '../modules/user/components/focile-banners/focile-banners.component';
import { PhoneFormatPipe } from '../modules/user/pipes/phone-number.pipe';
import { YtVideoComponent } from './components/yt-video/yt-video.component';
import { FootercardtitleDirective } from './directives/footercardtitle.directive';
import { TabsModule } from 'ngx-bootstrap/tabs';
import { ProfileNotCompleteAlertComponent } from './modals/profile-not-complete-alert/profile-not-complete-alert.component';
import { ViewExpertiseComponent } from './components/experties/view-expertise.component';
import { PickerModule } from '@ctrl/ngx-emoji-mart';
import { SocialMediaLinkFormComponent } from './forms/social-media-link-form/social-media-link-form.component';
import { ExpertFormComponent } from './forms/expert-form/expert-form.component';
import { AddEditFaqComponent } from './modals/add-edit-faq/add-edit-faq.component';
import { CropperComponent } from './modals/cropper/cropper.component';
import { FaqComponent } from './components/faq/faq.component';
import { BreadcrumbComponent } from './breadcrumb/breadcrumb.component';
import { BlogCardComponent } from './blog-card/blog-card.component';
import { ConnectionCardComponent } from './components/connection-card/connection-card.component';
import { JoinHelpCardComponent } from './components/join-help-card/join-help-card.component';
import { QuateSliderComponent } from './quate-slider/quate-slider.component';
import { CarouselModule } from 'ngx-owl-carousel-o';
import { ManageTestimonalComponent } from './modals/manage-testimonal/manage-testimonal.component';
import { AddEditCompanyExpertComponent } from './modals/add-edit-company-expert/add-edit-company-expert.component';
import { TinyMCEditorComponent } from './components/editor/editor.component';
import { ServiceActivitiesComponent } from './components/service-activities/service-activities.component';
import { CreateEditPostComponent } from './modals/create-edit-post/create-edit-post.component';

const IMPORT_EXPORTS_COMPONENTS = [
  FDropdownComponent,
  FocileButtonComponent,
  FocileInputComponent,
  FocileFormElementComponent,
  HelperTextComponent,
  FocileChipComponent,
  SearchInputComponent,
  SpinnerComponent,
  ECardComponent,
  FocileBannersComponent,
  FromNowPipe,
  FilterByPipe,
  ModalComponent,
  AddUserComponent,
  PaginationComponent,
  ImgDirective,
  ConfirmationComponent,
  ViewUserDetailComponent,
  AddPlanComponent,
  ConnectionLimitExceedWarningComponent,
  ViewFollowRequestsComponent,
  SvgDirective,
  PhoneFormatPipe,
  YtVideoComponent,
  ProfileNotCompleteAlertComponent,
  ViewExpertiseComponent,
  ExpertFormComponent,
  AddEditFaqComponent,
  FaqComponent,
  BreadcrumbComponent,
  ConnectionCardComponent,
  QuateSliderComponent,
  ManageTestimonalComponent,
  AddEditCompanyExpertComponent,
  ServiceActivitiesComponent,
  CreateEditPostComponent
];

const DIRECTIVES = [FootercardtitleDirective];

const IMPORT_EXPORTS_MODULES = [PopoverModule, TooltipModule, ReactiveFormsModule, ModalModule];
@NgModule({
  declarations: [...IMPORT_EXPORTS_COMPONENTS, ...DIRECTIVES],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NgSelectModule,
    BsDropdownModule,
    ModalModule.forRoot(),
    PopoverModule.forRoot(),
    TooltipModule.forRoot(),
    PaginationModule.forRoot(),
    TabsModule.forRoot(),
    PickerModule,
    SocialMediaLinkFormComponent,
    CarouselModule,
    TinyMCEditorComponent,
  ],
  exports: [
    ...IMPORT_EXPORTS_MODULES,
    ...IMPORT_EXPORTS_COMPONENTS,
    ...DIRECTIVES,
    TabsModule,
    NgSelectModule,
    PickerModule,
    CarouselModule,
    BsDropdownModule,
    ModalModule,
    TinyMCEditorComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class SharedModule { }
