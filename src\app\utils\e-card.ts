export interface Ecard {
 id: string;
  userId: string;
  address: string;
  companyId: string;
  companyName: string;
  email: string;
  companyWebsite: string;
  companyBanner: string;
  workMobileNumer: string;
  experts: Array<any>;
  companyImage: string;
  latLong: string;
  isCertified: boolean;
  isActive: boolean;
  companyType: string;
  profilePhoto: string;
  isFavorite: boolean;
  isFollowing: boolean;
  expertise: Expertise[];
  technology: Expertise[];
  elevatorPitchVideo: any;
  ecardBanner: string;
  organizationName: string;
  organizationTypeName: string;
  name:string;
  firstName:string;
}

export interface Expertise {
  id: number;
  idGuid: string;
  name: string;
  description: string;
  selected: boolean;
}
