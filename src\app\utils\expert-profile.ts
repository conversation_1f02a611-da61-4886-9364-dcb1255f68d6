// Generated by https://quicktype.io

export interface Expert {
  message: string;
  data: Data;
  messageType: number;
}

export interface Data {
  userId: string;
  about: string;
  address: string;
  email: string;
  city: string;
  companyId: string;
  companyName: string;
  companyWebsite: string;
  rating: number;
  workMobileNumer: string;
  companyImage: string;
  profilePhoto: string;
  isCertified: boolean;
  isActive: boolean;
  companyType: string;
  isFavorite: boolean;
  isFollowing: boolean;
  userType: number;
  expertise: any[];
  technology: Technology[];
  followers: number;
  following: number;
  serviceType: string[];
  products: string[];
  solutions: string[];
  industry: string[];
  experts: ExpertElement[];
  technologies: any[];
  admin: Admin;
  comments: Comment[];
  reviews: any;
  youtubeLink: string;
  twitterLink: string;
  instagramLink: string;
  facebookLink: string;
}

export interface Admin {
  firstName: string;
  lastName: string;
  isVerified: boolean;
  isCompleted: boolean;
  createdAt: string;
  profilePhoto: string;
  isActive: boolean;
  userType: number;
  userId: string;
  organizationTypeId: string;
  phoneNumber: string;
  comments: number;
  expertDetail: ExpertDetail;
  cityList: any[];
  stateList: any[];
  countryList: any[];
  organizationTypeList: any[];
  companyTypeList: any[];
  companySystemList: any[];
  solutionList: any[];
  roleList: any[];
  productList: any[];
  servicesList: any[];
  industryList: any[];
  companySizeList: any[];
  technologyList: any[];
  companyList: any[];
}

export interface ExpertDetail {
  userId: string;
  companyId: string;
  expertId: number;
  isCertified: boolean;
  companySize: number;
  roleId: string;
}

export interface Comment {
  companyId: string;
  id: string;
  likes: number;
  createdOn: string;
  comment: string;
  userName: string;
  userId: string;
  userImage: string;
  childComments: Comment[];
  parentCommentId?: string;
}

export interface ExpertElement {
  userId: string;
  address: string;
  email: string;
  city: string;
  companyId: string;
  companyName: string;
  companyWebsite: string;
  rating: number;
  workMobileNumer: string;
  companyImage: string;
  profilePhoto: string;
  isCertified: boolean;
  isActive: boolean;
  companyType: string;
  isFavorite: boolean;
  isFollowing: boolean;
  expertise: Technology[];
  technology: Technology[];
}

export interface Technology {
  id: number;
  idGuid: string;
  name: string;
  selected: boolean;
  description?: string;
}
