import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpClient,
  HttpErrorResponse,
} from '@angular/common/http';
import {
  catchError,
  EMPTY,
  Observable,
  switchMap,
  tap,
  throwError,
} from 'rxjs';
import { environment } from '../../environments/environment';
import { AccountService } from '../modules/account/services/account.service';
import { ChatService } from '../shared/services/chat.service';
import { Router } from '@angular/router';

@Injectable()
export class HttpInterceptorInterceptor implements HttpInterceptor {
  isRefreshing: any;
  constructor(
    private http: HttpClient,
    private account: AccountService,
    private chatService: ChatService,
    private router: Router
  ) { }
  intercept(
    request: HttpRequest<unknown>,
    next: HttpHandler
  ): Observable<HttpEvent<unknown>> {
    let user: any = localStorage.getItem('user');
    user = JSON.parse(user as any);
    const headers: any = {};
    if (user?.token) {
      headers.Authorization = `Bearer ${user?.token}`;
    }

    const apiRequest = request.clone({
      url: `${environment.apiUrl}${request.url}`,
      setHeaders: headers,
      withCredentials: true,
    });
    return next.handle(apiRequest).pipe(
      catchError((error: HttpErrorResponse) => {
        if (error.status === 401) {
          // Check if this is a blog-related API call
          const isBlogAPI = request.url.includes('Blog/All') ||
            request.url.includes('Blog/GetAllCategories') ||
            request.url.includes('blog/');

          // Only trigger logout for non-blog API calls
          if (!isBlogAPI) {
            this.handle401Error(request, next);
          }
        }
        return throwError(() => error);
      })
    );
  }

  private handle401Error(request: HttpRequest<any>, next: HttpHandler) {
    return this.sessionLogout();
  }

  private sessionLogout() {
    this.chatService.disconnectUser(this.account?.user$?.getValue()?.userId);
    this.account.user = {};
    this.account.isLoggedIn = false;
    this.account.isLoggedIn$.next(false);
    this.account.user$.next(null);
    localStorage.removeItem('user');
    this.router.navigate(['/']);
  }
}
