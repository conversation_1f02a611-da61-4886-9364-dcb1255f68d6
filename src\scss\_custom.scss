// MY THEME COLORS
$primary-dark: #014681;
$primary-light: #0052ff;
$primary-shade: #ccdcff;

$success: #81de4a;

$primary-100: #e6eeff;
$success-100: #f3fced;

$primary: $primary-dark;

$primary: #014681;
$primary-light: #ccdcff;

$danger-light: #ffddda;
$danger: #ff5347;

$success: #81de4a;
$success-light: #f3fced;

$warning: #fcdc00;
$warning-light: #fffce6;

$danger: #fd346e;
$danger-light: #ffebf1;

$primary-tint: #1de2cf;
$primary-tint-light: #e9fdfb;

$ligh-primary: linear-gradient(
    0deg,
    rgba(255, 255, 255, 0.8),
    rgba(255, 255, 255, 0.8)
  ),
  #0052ff;

///////////
/// ng-select colors
///  //////////
$ng-select-highlight: $primary;
// import bootstrap

@import "../../node_modules/bootstrap/scss/bootstrap.scss";

// ng-select
@import "~@ng-select/ng-select/themes/default.theme.css";

@import "./components/pill";
@import "./utilities/grid";
@import "./mixins/font-size";

.form-control:not([type="file"]) {
  @extend .form-control;
  min-height: 3rem;
}

.h-48 {
  min-height: 3rem;
}

.form-label {
  @extend .form-label;
  font-weight: 700;
  font-size: 12px;
  line-height: 16px;
}

.btn {
  @extend .btn;
  height: 52px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  line-height: 21px;
}

.ng-select-container {
  min-height: 3rem !important;
}

.ng-select .ng-arrow-wrapper .ng-arrow {
  border-color: none !important;
  border-style: none !important;
  border-width: 0 !important;
}
.ng-select .ng-arrow-wrapper {
  background-image: url("../assets/svgs/arrow-drown.svg");
  background-repeat: no-repeat;
  background-size: 14px 14px;
  margin-top: 8px;
}

ng-select.ng-invalid.ng-touched .ng-select-container {
  border-color: $danger;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 3px #fde6e8;
}

.container-fluid {
  padding-left: 7.25% !important;
  padding-right: 7.25% !important;
}
