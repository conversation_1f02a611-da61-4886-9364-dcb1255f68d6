// Breakpoints
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;

// Grid Container
.grid-container {
  display: grid;
}

// Grid Columns
.grid-cols-1 {
  grid-template-columns: repeat(1, 1fr);
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-cols-4 {
  grid-template-columns: repeat(4, 1fr);
}

.grid-cols-6 {
  grid-template-columns: repeat(6, 1fr);
}

.grid-cols-12 {
  grid-template-columns: repeat(12, 1fr);
}

// Responsive Grid Columns
@media (min-width: $breakpoint-sm) {
  .grid-cols-sm-1 {
    grid-template-columns: repeat(1, 1fr);
  }

  .grid-cols-sm-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  .grid-cols-sm-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  .grid-cols-sm-4 {
    grid-template-columns: repeat(4, 1fr);
  }

  .grid-cols-sm-6 {
    grid-template-columns: repeat(6, 1fr);
  }

  .grid-cols-sm-12 {
    grid-template-columns: repeat(12, 1fr);
  }
}

@media (min-width: $breakpoint-md) {
  .grid-cols-md-1 {
    grid-template-columns: repeat(1, 1fr);
  }

  .grid-cols-md-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  .grid-cols-md-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  .grid-cols-md-4 {
    grid-template-columns: repeat(4, 1fr);
  }

  .grid-cols-md-6 {
    grid-template-columns: repeat(6, 1fr);
  }

  .grid-cols-md-12 {
    grid-template-columns: repeat(12, 1fr);
  }
}

@media (min-width: $breakpoint-lg) {
  .grid-cols-lg-1 {
    grid-template-columns: repeat(1, 1fr);
  }

  .grid-cols-lg-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  .grid-cols-lg-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  .grid-cols-lg-4 {
    grid-template-columns: repeat(4, 1fr);
  }

  .grid-cols-lg-6 {
    grid-template-columns: repeat(6, 1fr);
  }

  .grid-cols-lg-12 {
    grid-template-columns: repeat(12, 1fr);
  }
}

@media (min-width: $breakpoint-xl) {
  .grid-cols-xl-1 {
    grid-template-columns: repeat(1, 1fr);
  }

  .grid-cols-xl-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  .grid-cols-xl-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  .grid-cols-xl-4 {
    grid-template-columns: repeat(4, 1fr);
  }

  .grid-cols-xl-6 {
    grid-template-columns: repeat(6, 1fr);
  }

  .grid-cols-xl-12 {
    grid-template-columns: repeat(12, 1fr);
  }
}

// Grid Rows
.grid-rows-1 {
  grid-template-rows: repeat(1, 1fr);
}

.grid-rows-2 {
  grid-template-rows: repeat(2, 1fr);
}

.grid-rows-3 {
  grid-template-rows: repeat(3, 1fr);
}

.grid-rows-4 {
  grid-template-rows: repeat(4, 1fr);
}

// Grid Gap
.grid-gap-1 {
  gap: 4px;
}

.grid-gap-2 {
  gap: 8px;
}

.grid-gap-3 {
  gap: 16px;
}

.grid-gap-4 {
  gap: 24px;
}

.grid-gap-5 {
  gap: 32px;
}
