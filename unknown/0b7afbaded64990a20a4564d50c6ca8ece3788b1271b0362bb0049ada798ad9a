import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeaderComponent } from './header/header.component';
import { FooterComponent } from './footer/footer.component';
import { RouterModule } from '@angular/router';
import { BsDropdownModule } from 'ngx-bootstrap/dropdown';
import { ModalModule } from 'ngx-bootstrap/modal';
import { BulkInviteComponent } from '../shared/components/bulk-invite/bulk-invite.component';
import { FormsModule } from '@angular/forms';
import { PopoverModule } from 'ngx-bootstrap/popover';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { SharedModule } from '../shared/shared.module';
@NgModule({
  declarations: [
    FooterComponent,
    HeaderComponent    
  ],
  exports: [FooterComponent, HeaderComponent],
  imports: [
    CommonModule,
    RouterModule,
    BsDropdownModule,
    FormsModule,
    ModalModule.forRoot(),
    PopoverModule.forRoot(),
    TooltipModule,
    SharedModule,    
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class LayoutModule {}
