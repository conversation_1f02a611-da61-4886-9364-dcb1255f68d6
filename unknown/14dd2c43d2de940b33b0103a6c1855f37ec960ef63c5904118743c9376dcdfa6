<div class="row mb-3">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card">
      <div class="card-body">
        <h4 class="card-title">Consultants</h4>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-12 grid-margin stretch-card">
    <div class="card">
      <div class="card-body">
        <h4 class="card-title">
          <div class="mb-3">
            <input
              type="search"
              class="form-control"
              id="search"
              [(ngModel)]="searchTerm"
              placeholder="Search..."
            />
          </div>
        </h4>
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th (click)="sortBy('userId')">#</th>
                <th (click)="sortBy('aspNetId')">Unique Identification</th>
                <th (click)="sortBy('firstName')">First Name</th>
                <th (click)="sortBy('lastName')">Last Name</th>
                <th (click)="sortBy('email')">Email</th>
                <th (click)="sortBy('expertDetail.companyName')">
                  Company Name
                </th>
                <th (click)="sortBy('address')">Address</th>
                <th (click)="sortBy('phoneNumber')">Contact</th>
              </tr>
            </thead>
            <tbody>
              <ng-container *ngIf="!dataLoading">
                <tr
                  *ngFor="
                    let item of experts
                      | search : searchTerm : 'firstName' : 'lastName' : 'email'
                  "
                >
                  <td>
                    {{ item.userId }}
                  </td>
                  <td>
                    {{ item.aspNetId }}
                  </td>
                  <td>
                    {{ item.firstName }}
                  </td>
                  <td>
                    {{ item.lastName }}
                  </td>
                  <td>
                    {{ item?.email }}
                  </td>
                  <td>
                    {{ item.expertDetail.companyName }}
                  </td>
                  <td>
                    {{ item.address || "Not available" }}
                  </td>
                  <td>{{ item.countryCode }} {{ item.phoneNumber }}</td>
                </tr>
              </ng-container>
              <tr *ngIf="dataLoading">
                <td colspan="7" class="text-center">data loading</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
