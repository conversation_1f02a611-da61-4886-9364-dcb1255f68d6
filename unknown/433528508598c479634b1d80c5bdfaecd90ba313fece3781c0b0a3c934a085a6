import { Component, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { AccountService } from '../services/account.service';
import {
  debounceTime,
  distinctUntilChanged,
  EMPTY,
  finalize,
  map,
  Subject,
  Subscription,
  switchMap,
  take,
  tap,
} from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { UserType } from 'src/app/utils/user-types';
import { UtilsService } from 'src/app/shared/services/utils.service';
import { companyTypeDisplay, Regex } from 'src/app/shared/constant';
import { ActivatedRoute, Router } from '@angular/router';
import { PopoverDirective } from 'ngx-bootstrap/popover';
import { NgSelectComponent } from '@ng-select/ng-select';
import { CompanyService } from 'src/app/shared/services/company.service';

@Component({
  selector: 'app-registration',
  templateUrl: './registration.component.html',
  styleUrls: ['./registration.component.scss'],
})
export class RegistrationComponent implements OnInit, OnDestroy {
  @ViewChild('companyWebsiteDDL') companyListddl!: NgSelectComponent;
  @ViewChild('pop') passwordPopover!: PopoverDirective;
  isFirstStepCompleted = false;
  isExpertStepOneCompleted = false;
  countries: any[] = [];
  expertise = [];
  registrationForm!: FormGroup;
  expertForm!: FormGroup;
  endUserForm!: FormGroup;
  expertDetailsForm!: FormGroup;
  organizationType = [];
  countriesLoading = true;

  states = [];
  statesLoading = false;

  cities = [];
  citiesLoading = false;
  lng: any;
  lat: any;
  isLocationEnabled = true;
  showPassword = false;
  showConfirmPassword = !1;
  solutionItems: any = [];
  roleItems: any = [];
  productItems: any = [];
  servicesItems: any = [];
  industryItems: any = [];
  technologyItems: any = [];
  companySize: Array<any> = [];
  experts: Array<any> = [];
  dataLoading = false;
  isCompanySystem = false;
  companySystems: Array<any> = [];
  subscriptionLevels: any;
  savingRecord = false;
  UserType = UserType;
  companyList: any = [];
  installationList = [];
  showInstallationType = false;
  Regx = Regex;
  showStrongPasswordPopup = false;
  isNextClicked = false;
  countryCodes: any;
  userType = UserType;
  passwordError = '';
  searchInput = new Subject<string>();
  companyTypeDisplay = companyTypeDisplay;
  _userType: any;
  subscription: Subscription[] = [];
  constructor(
    private formBuilder: FormBuilder,
    private account: AccountService,
    private toaster: ToastrService,
    private utils: UtilsService,
    private router: Router,
    private route: ActivatedRoute,
    private copmany: CompanyService
  ) {
    this.addCompany = this.addCompany.bind(this);
    this.addCompanyWebSite = this.addCompanyWebSite.bind(this);
  }
  ngOnDestroy(): void {
    this.subscription.map((x) => x?.unsubscribe());
  }

  ngOnInit(): void {
    this.initForm();
    this.route.queryParams.subscribe((params) => {
      const userType = params['userType'];
      this._userType = userType;
      if (userType) {
        this.registrationForm.get('userType')?.setValue(userType);
        this.handleUserTypeChange();
        this.getRegistraionData(userType, false);
      } else {
        this.getRegistraionData('2', false);
      }
    });

    this.getCountries();
    this.initUserDetailForm();
    this.initEndUserForm();
    this.initExpertDetailsForm();
    // this.registrationForm.get('password')?.valueChanges.subscribe((value) => {
    //   this.registrationForm
    //     .get('confirmPassword')
    //     ?.setValidators([Validators.pattern(value)]);
    // });

    this.getLocation();
    this.utils.location$.pipe(take(2)).subscribe((response: any) => {
      if (response?.length) {
        this.lat = response.split(',')[0];
        this.lng = response.split(',')[1];
        this.isLocationEnabled = true;
      }
    });

    this.searchInput
      .pipe(
        debounceTime(300), // wait 300ms after each keystroke before considering the term
        distinctUntilChanged(), // ignore new term if same as previous term
        switchMap((term: any) => {
          if (!term?.term?.trim()) {
            // If the term is empty, return an empty observable, canceling the request
            return EMPTY;
          }
          return this.handleSearchChange(term);
        }) // switch to new search observable each time the term changes
      )
      .subscribe((data: any) => {
        this.companyList = data.data;
      });
  }

  getCountries() {
    this.account.getCountries().subscribe((response: any) => {
      this.countryCodes = response.data;
      this.countryCodes = this.countryCodes.map((x: any) => {
        x.description = x.description;
        return x;
      });
    });
  }

  handlePasswordRuleChange(error: any) {
    if (error) {
      this.passwordPopover.show();
    } else {
      this.passwordPopover.hide();
    }
  }

  handleCompanyWebsiteChange(websiteChange: any) {
    if (!websiteChange?.idGuid) {
      this.registrationForm.patchValue({
        companyWebsite: websiteChange?.description,
      });
      this.registrationForm
        .get('companyWebsite')
        ?.setValidators([
          Validators.required,
          Validators.pattern(Regex.websiteUrl),
        ]);
      this.registrationForm.get('companyWebsite')?.updateValueAndValidity();
      this.registrationForm.get('companyName')?.enable();
      this.registrationForm.get('expert')?.enable();
      this.registrationForm.get('roleId')?.enable();
      this.registrationForm.get('userType')?.enable();
    } else if (websiteChange?.idGuid) {
      this.registrationForm
        .get('companyWebsite')
        ?.setValidators([Validators.required]);

      const subscription = this.copmany
        .getCompanyRegistrationInfo(websiteChange.idGuid)
        .subscribe((companyRegistraionInfo: any) => {
          if (!companyRegistraionInfo) return;
          this.registrationForm.patchValue({
            companyName: companyRegistraionInfo.name,
            expert: companyRegistraionInfo.companyTypeId,
            companyWebsite: companyRegistraionInfo.website,
          });
          this.registrationForm.get('companyName')?.disable();
          this.registrationForm.get('expert')?.disable();
          this.registrationForm.get('userType')?.disable();
        });

      this.subscription.push(subscription);

      // this.account
      //   .getCompanyDetails(websiteChange.idGuid)
      //   .subscribe((response: any) => {
      //     if (response.data && response.data.companyName) {
      //       this.registrationForm.patchValue({
      //         companyName: response.data.companyName,
      //         expert: response.data.comapnyTypeId,
      //         companyWebsite: response.data.companyId,
      //       });
      //       this.registrationForm.get('companyName')?.disable();
      //       this.registrationForm.get('expert')?.disable();
      //       this.registrationForm.get('userType')?.disable();
      //     }
      //   });
    }
  }

  handleOnClear() {
    this.registrationForm.patchValue({
      companyWebsite: null,
      companyName: null,
      expert: null,
      userType: '2',
    });
    this.registrationForm.get('companyName')?.enable;
    this.registrationForm.get('expert')?.enable();
    this.registrationForm.get('roleId')?.enable();
    this.registrationForm.get('userType')?.enable();
  }

  onAddTag(newCompanyWebsite: any) {
    // console.log({ newCompanyWebsite });
    // this.companyList.push({
    //   idGuid: newCompanyWebsite.description,
    //   description: newCompanyWebsite.description,
    // });
  }

  getRegistraionData(userType: string, fromNgOniti = true) {
    this.dataLoading = true;
    this.account
      .getRegistrationData(userType)
      .pipe(finalize(() => (this.dataLoading = false)))
      .subscribe((response: any) => {
        this.productItems = response.data?.productList || [];
        this.industryItems = response.data?.industryList || [];
        this.productItems = response.data?.productList || [];
        this.servicesItems = response.data?.servicesList || [];
        this.solutionItems = response.data?.solutionList || [];
        this.companySize = response.data?.companySizeList || [];
        this.technologyItems = response.data?.technologyList || [];
        this.roleItems = response.data?.roleList || [];
        this.countries = response.data?.countryList || [];
        this.expertise = response.data?.expertiseList || [];
        this.subscriptionLevels = response.data?.subsciptionLevelList || [];
        this.organizationType = response.data?.organizationTypeList || [];
        this.experts = response.data?.expertList || [];
        if (fromNgOniti) {
          this.isFirstStepCompleted = true;
        }
      });
  }

  handleExpertChange(companyType: any, disbleForm = false) {
    this.expertDetailsForm.get('companySystemIds')?.setValue([]);
    this.expertDetailsForm.get('companySystemIds')?.markAllAsTouched();
    this.isCompanySystem = true;
    this.account
      .getTypeOfExpert(companyType)
      .pipe(finalize(() => (this.isCompanySystem = false)))
      .subscribe((response: any) => {
        this.companySystems = response.data;
        if (disbleForm) {
          this.expertForm.disable();
          this.expertForm.get('roles')?.enable();
        }
      });
  }

  get controls() {
    return this.registrationForm.controls;
  }

  initForm() {
    this.registrationForm = this.formBuilder.group({
      firstname: [
        null,
        [Validators.required, Validators.pattern(this.Regx.personName)],
      ],
      lastname: [
        null,
        [Validators.required, Validators.pattern(this.Regx.personName)],
      ],
      mobileNumber: [
        null,
        [Validators.required, Validators.pattern(/^\d{6,13}$/)],
      ],
      email: [null, [Validators.required, Validators.email]],
      password: [
        null,
        [
          Validators.required,
          Validators.minLength(12),
          this.passwordStrengthValidator,
        ],
      ],
      confirmPassword: [null, Validators.required],
      userType: [`${this.userType.ExpertUser}`],
      termsAndConditions: [false, Validators.requiredTrue],
      emailUpdates: [false],
      countryCode: ['+1'],
      companyWebsite: [
        null,
        [Validators.required, Validators.pattern(Regex.websiteUrl)],
      ],
      companyName: [
        null,
        [Validators.required, Validators.pattern('^[a-zA-Z0-9 ]+$')],
      ],
      expert: [null, [Validators.required]],
      roleId: [null, [Validators.required]],
    });
  }

  passwordStrengthValidator(control: any) {
    const password = control.value;
    if (!password) {
      return null; // No error if the password is empty
    }

    // Check for strength (modify criteria as needed)
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumeric = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>$#]/.test(password);

    const regex =
      /(?=^.{8,}$)((?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).{12,}$/;
    const isValid = regex.test(password);

    return isValid ? null : { passwordStrength: true };
  }

  initEndUserForm() {
    this.endUserForm = this.formBuilder.group({
      organizationName: [null, Validators.required],
      organizationType: [null, Validators.required],
      termsAndConditions: [null, Validators.required],
      // address: [null, Validators.required],
      // country: [null, Validators.required],
      // state: [null, Validators.required],
      // city: [null, Validators.required],
      // zipcode: [null, Validators.required],
    });
  }

  initUserDetailForm() {
    this.expertForm = this.formBuilder.group({
      companyName: [null, Validators.required],
      companyId: [null],
      companyWebsite: [null, [Validators.required]],
      expert: [null, Validators.required],
      companySystemIds: [null, Validators.required],
      solution: [null, Validators.required],
      roles: [null, Validators.required],
      products: [null, Validators.required],
      services: [null, Validators.required],
      industries: [null, Validators.required],
      companySize: [null, Validators.required],
      technologies: [null, Validators.required],
      expertise: [null, Validators.required],
      subsciptionLevelId: [null, Validators.required],
      typeOfInstallationIds: [null],
    });
  }

  initExpertDetailsForm() {
    this.expertDetailsForm = this.formBuilder.group({
      workMobileNo: [null, Validators.required],
      address: [null, Validators.required],
      country: [null, Validators.required],
      state: [null, Validators.required],
      city: [null, Validators.required],
      zipcode: [null, Validators.required],
      workMobileNumberCountryCode: ['+93'],
      isShowWorkMobileNumber: true,
    });
  }

  gotoNextStep() {
    this.isNextClicked = true;
    const params = {
      email: this.registrationForm.value.email,
      mobileNumber: this.registrationForm.value.mobileNo,
      countryCode: this.registrationForm.value.countryCode,
    };
    this.dataLoading = true;
    this.account
      .checkEmailOrPhoneNumber(params)
      .pipe(finalize(() => (this.dataLoading = false)))
      .subscribe((response: any) => {
        if (response.data) {
          this.registrationForm.markAllAsTouched();
          if (this.registrationForm.valid) {
            this.getRegistraionData(this.registrationForm.value.userType, true);
          }
          return;
        } else {
          this.toaster.error('', response.message);
        }
      });
  }

  saveUser() {
    this.registrationForm.markAllAsTouched();
    if (this.registrationForm.invalid) return;
    const payload = this.registrationForm.getRawValue();
    if (payload.password != payload.confirmPassword)
      return this.toaster.error('Both password should be same');
    payload.DialCode = payload.countryCode;
    payload.companyType = payload.expert;
    this.dataLoading = true;
    this.registrationForm.disable();
    this.account
      .adminRegistration(payload)
      .pipe(
        finalize(
          () => ((this.dataLoading = false), this.registrationForm.enable())
        )
      )
      .subscribe((response: any) => {
        if (response.data) {
          this.companyListddl.clearModel();
          this.companyListddl.searchInput.nativeElement.value = '';

          this.toaster.success(
            'Check your inbox for a welcome email and an activation link soon.',
            'Registraion Completed!'
          );
          this.registrationForm.enable();
          this.registrationForm.reset();
          setTimeout(() => {
            this.registrationForm.patchValue({
              userType: '2',
              countryCode: '+1',
            });
            this.registrationForm.get('roleId')?.markAsUntouched();
            this.registrationForm.get('expert')?.markAsUntouched();
          }, 200);
        } else {
          this.toaster.error(response.message);
        }
      });
    return;
  }

  handleUserTypeChange() {
    const userTypeValue = this.registrationForm.get('userType')?.value;
    if (userTypeValue == 1) {
      this.registrationForm.get('companyWebsite')?.setValidators([]);
      this.registrationForm.get('companyName')?.setValidators([]);
      this.registrationForm.get('expert')?.setValidators([]);
      this.registrationForm.get('roleId')?.setValidators([]);
      this.registrationForm.get('companyWebsite')?.disable();
      this.registrationForm.get('companyName')?.disable();
      this.registrationForm.get('expert')?.disable();
      this.registrationForm.get('roleId')?.disable();
    } else if (userTypeValue == 2) {
      this.registrationForm.get('companyWebsite')?.enable();
      this.registrationForm.get('companyName')?.enable();
      this.registrationForm.get('expert')?.enable();
      this.registrationForm.get('expert')?.disabled;

      this.registrationForm.get('roleId')?.enable();
      this.registrationForm
        .get('companyWebsite')
        ?.setValidators([Validators.required]);
      this.registrationForm
        .get('companyName')
        ?.setValidators([Validators.required]);
      this.registrationForm.get('expert')?.setValidators([Validators.required]);
      this.registrationForm.get('roleId')?.setValidators([Validators.required]);
    }
  }

  setUserType(userType: number) {
    this.registrationForm.get('userType')?.setValue(`${userType}`);
    this.handleUserTypeChange();
  }

  handleChange(changedType?: string) {
    if (!changedType) return;
    this.account.getStates(changedType).subscribe((response: any) => {
      this.states = response.data;
      this.expertDetailsForm.get('state')?.setValue(null);
      this.expertDetailsForm.get('state')?.markAsUntouched();
      this.expertDetailsForm.get('city')?.setValue(null);
      this.expertDetailsForm.get('city')?.markAsTouched();
    });
  }
  handleStateChange($event: any) {
    this.account.getCities($event).subscribe((response: any) => {
      this.cities = response.data;
      this.expertDetailsForm.get('city')?.setValue(null);
      this.expertDetailsForm.get('city')?.markAsTouched();
    });
  }

  getLocation() {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position: any) => {
          if (position) {
            this.lat = position.coords.latitude;
            this.lng = position.coords.longitude;
            this.isLocationEnabled = true;
          }
        },
        (error: any) => {
          this.isLocationEnabled = false;
        }
      );
    } else {
      alert('Geolocation is not supported by this browser.');
    }
  }

  handlePasswordChange() {
    // this.registrationForm
    //   .get('confirmPassword')
    //   ?.setValidators([
    //     Validators.pattern(this.registrationForm.get('password')?.value),
    //   ]);
  }

  getStates() {
    const countryId = this.expertForm.value.country;
    if (!countryId) return;
    this.statesLoading = true;
    const self = this;
    this.account
      .getStates(this.expertForm.value.country)
      .pipe(map((response: any) => response.data))
      .subscribe({
        next(value) {
          self.states = value;
          self.statesLoading = false;
        },
        error(err) {
          self.statesLoading = false;
        },
      });
  }

  getCities() {
    this.citiesLoading = true;
    const self = this;
    this.account
      .getCities(this.expertForm.value.state)
      .pipe(map((response: any) => response.data))
      .subscribe({
        next(value) {
          self.cities = value;
          self.citiesLoading = false;
        },
        error(err) {
          self.citiesLoading = false;
        },
      });
  }

  handleOnClick() {}

  handleIconClick(type: string) {
    if (type === 'confirmPassword') {
      this.showConfirmPassword = !this.showConfirmPassword;
    } else {
      this.showPassword = !this.showPassword;
    }
  }

  endUserSignUp() {
    this.registerUser();
  }

  exportCanExitFirstStep() {
    this.expertForm.markAllAsTouched();
    this.expertForm.enable();
    this.isExpertStepOneCompleted = this.expertForm.valid;
  }

  registerUser() {
    this.expertForm.enable();
    const user: any = {
      firstName: this.registrationForm.value.firstname,
      phoneNumber: this.registrationForm.value.mobileNo,
      email: this.registrationForm.value.email,
      lastName: this.registrationForm.value.lastname,
      address:
        this.endUserForm.value.address ?? this.expertDetailsForm.value.address,
      country:
        this.endUserForm.value.country ?? this.expertDetailsForm.value.country,
      state: this.endUserForm.value.state ?? this.expertDetailsForm.value.state,
      city: this.endUserForm.value.city ?? this.expertDetailsForm.value.city,
      zipCode:
        this.endUserForm.value.zipcode ?? this.expertDetailsForm.value.zipcode,
      password: this.registrationForm.value.password,
      isVerified: true,
      isCompleted: true,
      isActive: true,
      userType: this.registrationForm.value.userType,
      latLong: `${this.lat || 0},${this.lng || 0}`,
      countryCode: this.registrationForm.value.countryCode,
      companyName: this.expertForm.value.companyName,
      companyId: this.expertForm.value.companyId || this.utils.getEmptyGuid(),
      companyWebsite: this.expertForm.value.companyWebsite,
      workMobileNumber: this.expertDetailsForm.value.workMobileNo,
      workMobileNumberCountryCode:
        this.registrationForm.value.workMobileNumberCountryCode,
      isShowWorkMobileNumber:
        this.registrationForm.value.IsShowWorkMobileNumber,
      expertId: this.expertForm.get('expert')?.value || 1,
      companySystemIds: this.expertForm
        .get('companySystemIds')
        ?.value?.toString(),
      solutionIds: this.expertForm.get('solution')?.value?.toString(),
      productIds: this.expertForm.get('products')?.value?.toString(),
      expertiseIds: this.expertForm.get('expertise')?.value?.toString(),
      serviceIds: this.expertForm.get('services')?.value?.toString(),
      industryIds: this.expertForm.get('industries')?.value?.toString(),
      companySize: this.expertForm.get('companySize')?.value || 1,
      technologyIds: this.expertForm.get('technologies')?.value?.toString(),
      roleId: this.expertForm.get('roles')?.value?.toString(),
      organizationName: this.endUserForm.value.organizationName,
      organizationType:
        this.endUserForm.value.organizationType || this.utils.getEmptyGuid(),
      subsciptionLevelId: this.expertForm.value?.subsciptionLevelId || 1,
      IsShowWorkMobileNumber: true,
    };

    this.savingRecord = true;
    this.account
      .registerUser(user)
      .pipe(finalize(() => (this.savingRecord = false)))
      .subscribe(
        (response: any) => {
          if (!response.data) {
            this.toaster.error(response.message);
            if (this.registrationForm.value.userType == 2) {
              this.isFirstStepCompleted != this.isFirstStepCompleted;
            }
            return;
          }
          this.toaster.success(
            `please check your email and click on the 'Verify Me' link that we've sent to your registered email address.`
          );
          this.registrationForm.reset();
          if (+user.userType === this.UserType.EndUser) {
            this.endUserForm.reset();
          } else if (+user.userType === this.UserType.ExpertUser) {
            this.expertForm.reset();
            this.expertDetailsForm.reset();
          }
          this.registrationForm.patchValue({
            userType: this.UserType.EndUser.toString(),
            countryCode: '+1',
          });
          this.isFirstStepCompleted = false;
          this.isExpertStepOneCompleted = false;
          this.router.navigate(['/account']);
        },
        (e) => {}
      );
  }

  addCompany(ngSelectAddValue: string) {
    this.companyList.push(ngSelectAddValue);
    this.expertForm.get('companyName')?.setValue(ngSelectAddValue);
    return {
      description: this.registrationForm.value.companyName || '',
      idGuid: ngSelectAddValue,
      name: ngSelectAddValue,
    };
  }

  addCompanyWebSite(ngSelectAddValue: string) {
    this.companyList.push(ngSelectAddValue);
    this.expertForm.get('companyWebsite')?.setValue(ngSelectAddValue);
    return {
      description: ngSelectAddValue,
      id: ngSelectAddValue,
      idGuid: ngSelectAddValue,
      name: ngSelectAddValue,
      selected: false,
    };
  }

  getCompanyDetails($event: string, disableExpertForm: boolean) {
    if (!$event) return;

    const isUserDefiend = this.companyList.find((x: any) => x === $event);
    const isExistingCompanySelected = this.companyList.find(
      (x: any) => x.idGuid === $event
    )?.idGuid;
    if (isExistingCompanySelected) {
      this.expertForm.get('companyId')?.setValue(isExistingCompanySelected);
    }
    if (isUserDefiend) return;

    this.dataLoading = true;
    this.account
      .getCompanyDetails($event)
      .pipe(
        tap((response: any) => {
          if (response?.data?.comapnyTypeId) {
            this.handleExpertChange(
              response?.data?.comapnyTypeId,
              disableExpertForm
            );
          }
        }),
        finalize(() => (this.dataLoading = false))
      )
      .subscribe((response: any) => {
        if (response.data) {
          const companyDetails = response.data;
          this.expertForm.patchValue({
            companyName: companyDetails.companyName,
            companySize: companyDetails.companySize,
            expert: companyDetails.comapnyTypeId,
            expertise: companyDetails.expertiseIds?.split(','),
            industries: companyDetails.industryIds?.split(','),
            products: companyDetails.productIds?.split(','),
            services: companyDetails.serviceIds?.split(','),
            solution: companyDetails.solutionIds?.split(','),
            technologies: companyDetails.technologyIds?.split(','),
            subsciptionLevelId: companyDetails.subsciptionLevelId,
            companySystemIds: companyDetails.companySystemIds?.split(','),
          });
          if (companyDetails?.expertId) {
            this.handleExpertChange(companyDetails.expertId, disableExpertForm);
          }
        } else {
        }
      });
  }

  handleSearchChange(searchInputValue: any) {
    return this.account.searchCompanyWebsite(searchInputValue?.term);
  }

  onSearch(term: any): void {
    this.searchInput.next(term);
  }

  urlValidator(): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } | null => {
      const urlPattern = /^(http?|ftp):\/\/[^\s/$.?#].[^\s]*$/i;
      const valid = urlPattern.test(control.value);
      return valid ? null : { invalidUrl: { value: control.value } };
    };
  }

  handleOptionSelection(searchInputValue: any) {
    this.expertForm
      .get('companyWebsite')
      ?.setValue(searchInputValue.description);

    this.getCompanyDetails(searchInputValue.idGuid, true);
    this.companyList = [];
  }

  handleBackEvent() {
    this.isFirstStepCompleted = !this.isFirstStepCompleted;
  }

  handleSearchInputTouched($event: any) {}

  goBack() {
    if (this.isNextClicked) {
      const userType = this.registrationForm.get('userType')?.value;
      if (+userType === 1) {
        // end user
        this.isFirstStepCompleted = false;
      } else {
        // expert
        if (!this.isExpertStepOneCompleted) {
          this.isFirstStepCompleted = false;
        }
        this.isExpertStepOneCompleted = false;
      }
      this.isNextClicked = false;
    } else {
      this.router.navigate(['/home']);
    }
  }
}
