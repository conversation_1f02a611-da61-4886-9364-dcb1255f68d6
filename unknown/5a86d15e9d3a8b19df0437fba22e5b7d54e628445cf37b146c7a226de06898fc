<div>
  <h4>Manage Plans</h4>
  <p>You may add, modify, or deactivate the active plans at your discretion.</p>
</div>
<div class="alert alert-light" role="alert">
  <div class="text-center">
    <p  *ngIf="!plains$?.length && !loading">No Plans added yet click here to add plan.</p>
    <button class="btn btn-primary" (click)="addPlan()">Add Plan</button>
  </div>
</div>

<div class="card border-0 shadow mb-5">
  <div class="card-body">
    <div class="table-responsive">
      <table class="table table-centered table-nowrap mb-0 rounded">
        <thead class="thead-light">
          <tr>
            <th class="border-0 rounded-start">#</th>
            <th class="border-0">Plan Id</th>
            <th class="border-0">Name</th>
            <th class="border-0">Image</th>
            <th class="border-0">Description</th>
            <th class="border-0 rounded-end">Action</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let expert of plains$; let i = index">
            <td class="border-0">
              {{ i + 1 }}
            </td>
            <td class="border-0 fw-bold">
              {{ expert.idGuid }}
            </td>
            <td class="border-0 text-danger">
              <div class="d-flex align-items-center">
                <span class="fw-bold"> {{ expert?.name }}</span>
              </div>
            </td>
            <td class="border-0 fw-bold">
              <div>
                <img
                  [src]="expert.image || './assets/svgs/focile.svg'"
                  alt="Focile Plain Image"
                  class="img img-thumbnail"
                  style="height: 100px; width: 100px"
                />
              </div>
            </td>
            <td class="border-0">
              {{ expert.description }}
            </td>
            <td class="border-0 text-success">
              <div
                class="btn-group"
                dropdown
                container="body"
                placement="bottom left"
              >
                <button
                  dropdownToggle
                  type="button"
                  class="btn btn-primary dropdown-toggle"
                >
                  <i class="fa fa-ellipsis-h"></i>
                </button>
                <ul
                  id="dropdown-animated"
                  *dropdownMenu
                  class="dropdown-menu dropdown-menu-right"
                  role="menu"
                  aria-labelledby="button-animated"
                >
                  <li role="menuitem" role="button" (click)="onEdit(expert)">
                    <a class="dropdown-item">
                      <i class="fa fa-pen"></i>
                      <span class="ms-3">Edit</span></a
                    >
                  </li>
                  <li role="menuitem" role="button">
                    <a class="dropdown-item text-danger" (click)="deletePlan(expert.id)">
                      <i class="fa fa-trash"></i>
                      <span class="ms-3">Deactivate Plain</span></a
                    >
                  </li>
                </ul>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
