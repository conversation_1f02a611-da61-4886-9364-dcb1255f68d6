<app-account-shell>
  <ng-container header>
    <div>
      <!-- <img
        role="button"
        [routerLink]="'/home'"
        src="./assets/svgs/focile.svg"
        alt=""
        srcset=""
      /> -->
    </div>
    <!-- <div routerLink="/account" class="mt-3 d-flex align-items-center mb-4">
      <span role="button" class="text-primary fw-bold"> < Home</span>
    </div> -->

    <div class="d-flex align-items-center mt-3 mt-sm-2 w-100 justify-content-between justify-content-sm-start z-index-1">
      <div class="m-focile-logo d-flex d-sm-none">
        <a class="navbar-brand" routerLink="/home">
          <div>
            <img role="button" [routerLink]="'/home'" src="./assets/svgs/focile.svg" alt="focile logo" height="50" />
          </div>
        </a>
      </div>
      <span class="fc-back-arrow d-flex flex-row align-items-center gap-2 cursor-pointer" routerLink="/account/choose-option" role="button">
        <span class="d-flex h-auto">
          <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M7.41 1.41L2.83 6L7.41 10.59L6 12L0 6L6 0L7.41 1.41Z" fill="rgba(0, 0, 0, 0.5)"/>
              </svg>                    
         </span>
         Back
      </span>
    </div>
    <div class="d-flex flex-wrap flex-column">
      <span class="text-fw-800 fs-3 mt-5 d-flex flex-wrap flex-row dynamic-text">
      <span class="text-primary ms-0 ms-sm-0"><b class="text-black">Password Reset</b></span>
      </span> 
      <p class="text-muted mt-1 mb-4 mb-sm-5">
      Enter your login email address to reset your password and follow the instructions
      </p>
    </div>
  </ng-container>
  <ng-container body>
    <ng-container *ngIf="!isEmailSended">
      <ng-container *ngTemplateOutlet="recorvery"></ng-container>
    </ng-container>
    <ng-container *ngIf="isEmailSended">
      <ng-container *ngTemplateOutlet="passwordRecovery"></ng-container>
    </ng-container>
  </ng-container>
  <ng-container footer>
    <div class="text-left mt-3">
      <span>Already have an account?</span> &nbsp;
      <span [routerLink]="'/account'" role="button" class="text-primary fw-bold"
        >Sign in</span
      >
    </div>
  </ng-container>
</app-account-shell>

<ng-template #recorvery>
  <form>
    <div *ngIf="recoveryForm" [formGroup]="recoveryForm" class="row">
      <div class="w-100 col-md-12 mb-3">
        <label for="email" class="form-label"
          >Email <span class="text-danger">*</span></label
        >
        <app-focile-input
          [type]="'email'"
          [id]="'email'"
          [name]="'email'"
          [disabled]="false"
          formControlName="email"
          placeholder="Enter your login email"
          [elementClass]="
            (recoveryForm.get('email')?.touched &&
              recoveryForm.get('email')?.errors?.required) ||
            (recoveryForm.get('email')?.touched &&
              recoveryForm.get('email')?.errors?.email)
              ? 'is-invalid'
              : null
          "
        ></app-focile-input>
        <span
          *ngIf="
            recoveryForm.get('email')?.touched &&
            recoveryForm.get('email')?.errors?.required
          "
          class="text-danger"
        >
          Email is required.
        </span>
        <span
          *ngIf="
            recoveryForm.get('email')?.touched &&
            recoveryForm.get('email')?.errors?.email
          "
          class="text-danger"
        >
          Email is not valid.
        </span>
      </div>
    </div>
    <div>
      <focile-button
        btnClass="sign-up-btn"
        type="submit"
        [loading]="loading"
        [disabled]="recoveryForm.invalid"
        (onClick)="handleRecoveryClick($event)"
      >
        Start recovery
      </focile-button>
    </div>
  </form>
</ng-template>

<ng-template #passwordRecovery>
  <div [formGroup]="resetPasswordForm" class="row my-4">
    <div class="col-md-12 mb-3">
      <div>
        <label for="password" class="form-label"
          >Password <span class="text-danger">*</span>
          <i
            [popover]="popTemplate"
            (click)="showStrongPasswordPopup = !showStrongPasswordPopup"
            class="fa fa-info-circle ms-2 text-primary"
            placement="top"
            role="button"
            [outsideClick]="true"
          ></i>
        </label>
        <app-focile-input
          [type]="showPassword ? 'text' : 'password'"
          [id]="'password'"
          [name]="'password'"
          [disabled]="false"
          [iconName]="showPassword ? 'eye' : 'eye-slash'"
          (onIconClick)="handleIconClick('password')"
          formControlName="password"
          [elementClass]="
            resetPasswordForm.get('password')?.touched &&
            (resetPasswordForm.get('password')?.errors?.required ||
              resetPasswordForm.get('password')?.errors?.pattern)
              ? 'is-invalid'
              : null
          "
        ></app-focile-input>
        <ng-template #popTemplate let-message="message">
          <app-password-rules></app-password-rules>
        </ng-template>
      </div>
      <span
        *ngIf="
          resetPasswordForm.get('password')?.touched &&
          resetPasswordForm.get('password')?.errors?.required
        "
        class="text-danger"
      >
        Password is required.
      </span>
      <span
        *ngIf="
          resetPasswordForm.get('password')?.touched &&
          resetPasswordForm.get('password')?.errors?.pattern
        "
        class="text-danger"
      >
        Password is not strong.
      </span>
    </div>
    <div class="col-md-12">
      <div>
        <label for="confirmPassword" class="form-label"
          >Confirm Password <span class="text-danger">*</span></label
        >
        <app-focile-input
          [type]="showConfirmPassword ? 'text' : 'password'"
          [id]="'password'"
          [name]="'password'"
          [disabled]="false"
          [iconName]="showConfirmPassword ? 'eye' : 'eye-slash'"
          (onIconClick)="handleIconClick('confirmPassword')"
          formControlName="confirmPassword"
          [elementClass]="
            resetPasswordForm.get('confirmPassword')?.touched &&
            resetPasswordForm.get('confirmPassword')?.errors
              ? 'is-invalid'
              : null
          "
        ></app-focile-input>
      </div>
      <span
        *ngIf="
          (resetPasswordForm.get('confirmPassword')?.touched ||
            resetPasswordForm.get('confirmPassword')?.dirty) &&
          resetPasswordForm.get('confirmPassword')?.errors?.required
        "
        class="text-danger"
      >
        Confirm Password is required.
      </span>
      <span
        *ngIf="
          resetPasswordForm.get('confirmPassword')?.touched &&
          resetPasswordForm.get('confirmPassword')?.dirty &&
          !resetPasswordForm.get('password')?.errors?.pattern &&
          resetPasswordForm.get('confirmPassword')?.value !=
            resetPasswordForm.get('password')?.value
        "
        class="text-danger"
      >
        Both password should be same.
      </span>
    </div>
  </div>
  <focile-button
    (onClick)="recoverPassword()"
    [btnClass]="'w-100'"
    [loading]="loading"
    [disabled]="resetPasswordForm.invalid"
  >
    Reset Password
  </focile-button>
</ng-template>
<app-footer></app-footer>