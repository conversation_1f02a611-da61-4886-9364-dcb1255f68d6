import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { UtilsService } from 'src/app/shared/services/utils.service';

@Component({
  selector: 'app-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss'],
})
export class FooterComponent implements OnInit {
  isHomeRoute: boolean = false;
  constructor(public utls: UtilsService, private router: Router, public account: AccountService,) {
    {
      account.isLoggedIn$.subscribe((response) => {
        this.isLoggedIn = response;
      });
    }    
  }
  @Input() isLoggedIn: boolean = false;
  navigate(url: string) {
    window.location.href = '#' + url;
  }

  ngOnInit(): void {
  this.router.events.subscribe(() => {
    this.isHomeRoute = this.router.url === '/home';
  });
}
}
