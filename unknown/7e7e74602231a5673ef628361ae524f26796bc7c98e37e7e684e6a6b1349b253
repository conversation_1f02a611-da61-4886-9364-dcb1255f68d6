import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AccountService } from '../services/account.service';
import { catchError, finalize, tap } from 'rxjs';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
})
export class LoginComponent implements OnInit {
  loginForm!: FormGroup;
  loading = false;
  showPassword = false;
  rememberMe = false; // Add this property
  constructor(
    private router: Router,
    private formBuilder: FormBuilder,
    private account: AccountService,
    private toastr: ToastrService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadRememberedEmail(); // Load remembered email on init
  }

  initForm() {
    this.loginForm = this.formBuilder.group({
      email: [null, Validators.required],
      password: [null, Validators.required],
      rememberMe: [false], // Add rememberMe to the form
    });
  }

  goto() {
    this.router.navigate(['/account/choose-option']);
  }

  gotoRecoverPassword() {
    this.router.navigate(['/account/recovery']);
  }
  loadRememberedEmail() {
    const rememberedEmail = localStorage.getItem('rememberedEmail');
    if (rememberedEmail) {
      this.loginForm.patchValue({
        email: rememberedEmail,
        rememberMe: true,
      });
      this.rememberMe = true;
    } else {
      this.loginForm.patchValue({ rememberMe: false });
      this.rememberMe = false;
    }
  }

  handleLogin() {
    if (this.rememberMe) {
      localStorage.setItem('rememberedEmail', this.loginForm.value.email);
    } else {
      localStorage.removeItem('rememberedEmail');
    }

    this.loading = !this.loading;
    this.account
      .login(this.loginForm.value)
      .pipe(
        tap((response: any) => {
          let data = response.data;
          localStorage.setItem('user', JSON.stringify(data));
        }),
        finalize(() => (this.loading = !this.loading)),
        catchError((error) => {
          return error;
        })
      )
      .subscribe((response: any) => {
        if (response && response?.error) {
          return console.error({ response });
        }
        this.account.profileImage$.next(response.data.profilePhoto);
        if (response.messageType === 1) {
          this.toastr.error(response.message);
          return;
        }
        let data = response.data;
        this.account.user$.next(data);
        this.account.isLoggedIn = true;
        this.account.isLoggedIn$.next(true);
        this.router.navigate(['/']);
      });
  }

  handleIconClick() {
    this.showPassword = !this.showPassword;
  }
}
