import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { finalize } from 'rxjs';
import { AddPlanComponent } from 'src/app/shared/modals/add-plan/add-plan.component';
import { ModalService } from 'src/app/shared/services/modal.service';
import { SubscriptionPlanService } from 'src/app/shared/services/subscription-plan.service';

@Component({
  selector: 'app-manage-plans',
  styleUrls: ['./manage-plans.component.css'],
  templateUrl: './manage-plans.component.html',
})
export class ManagePlansComponent implements OnInit {
  loading = false;
  plains$: any = [];
  constructor(
    private modalSerive: ModalService,
    private subscriptionPlanService: SubscriptionPlanService,
    private readonly toaster: ToastrService
  ) {}

  ngOnInit(): void {
    this.getPlans();
  }

  addPlan() {
    this.modalSerive.openModal('add-plan', {
      initialState: {
        success: () => {
          this.getPlans();
        },
      },
      class: 'modal-xl',
    });
  }

  getPlans() {
    this.subscriptionPlanService
      .getPlans()
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((response: any) => {
        this.plains$ = response.data;
      });
  }

  onEdit($event: any) {
    this.modalSerive.openModal('add-plan', {
      initialState: {
        plan: $event,
        success: () => {
          this.toaster.success('Record has been updated.');
          this.getPlans();
        },
      },
      class: 'modal-xl',
    });
  }

  deletePlan(id: any) {
    this.subscriptionPlanService.deletePlan(id).subscribe((response) => {
      this.toaster.success('Plain deactivated.');
      this.getPlans();
    });
  }
}
