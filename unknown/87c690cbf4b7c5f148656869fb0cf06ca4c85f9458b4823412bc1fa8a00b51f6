<app-modal [title]="'Blog Category'" [firstButtonText]="'Save'" [templateRef]="templateRef"
    [firstButtonDisabled]="false" [firstButtonLoading]="loading" (onFirstButtonClick)="handleOnSubmit()" (onSecondButtonClick)="handleCancel()">
</app-modal>

<ng-template #templateRef>
    <form id="blogCategoryForm" [formGroup]="blogForm" class="needs-validation" novalidate>
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="name" class="form-label">Title</label>
                    <input type="text" class="form-control" id="name" formControlName="title" required />
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="subtitle" class="form-label">Subtitle</label>
                    <input type="text" class="form-control" id="subtitle" formControlName="subTitle" required />
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="category" class="form-label">Select Category</label>
                    <select class="form-select form-control" id="category" formControlName="categoryId" required>
                        <option value="" disabled selected>Select a category</option>
                        <option *ngFor="let category of categories" [value]="category.id">{{ category.name }}</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="slug" class="form-label">Slug ( URL Part )</label>
                    <input type="text" class="form-control" id="slug" formControlName="slug" required />
                </div>
            </div>
            <div class="col-md-12">
                <div class="mb-3">
                    <label for="tags" class="form-label">Tags</label>
                    <input type="text" class="form-control" id="tags" formControlName="tags"
                        placeholder="Enter tags separated by commas" />
                </div>
            </div>
            <div class="col-md-12">
                <div class="mb-3">
                    <label for="description" class="form-label">Description</label>
                    <app-editor [content]="blogForm.controls['content'].value"
                        (onChange)="handleEditorChange($event)"></app-editor>
                </div>
            </div>
            <div class="col-md-12">
                <div class="mb-3">
                    <label for="image" class="form-label">Image</label>
                    <input type="file" class="form-control" id="image" accept="image/*"
                        (change)="handleFileInput($event)" />
                </div>
            </div>
            <div class="col-md-12" *ngIf="uploadProgress">
                <div class="progress">
                    <div class="progress-bar" role="progressbar" [style.width.%]="uploadProgress" aria-valuemin="0"
                        aria-valuemax="100">{{uploadProgress}}%</div>
                </div>
            </div>
        </div>
    </form>
</ng-template>