import { Component } from '@angular/core';
import { IndustryService } from '../services/industry.service';
import { finalize } from 'rxjs';
import { BsModalService } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
@Component({
  selector: 'app-industries',
  templateUrl: './industries.component.html',
  styleUrls: ['./industries.component.scss'],
})
export class IndustriesComponent {
  isLoading = true;
  products: Array<any> = [];
  industryName = null;
  loading = false;
  editObj: any;
  deleteId = '';
  modalRef: any;
  constructor(
    private industry: IndustryService,
    private modalService: BsModalService,
    private readonly toaster: ToastrService
  ) {}

  ngOnInit(): void {
    this.getProducts();
  }

  getProducts() {
    this.isLoading = true;
    this.industry
      .getIndustries()
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe((response: any) => {
        this.products = response.data;
      });
  }

  generateIndustry() {
    const industry: any = {
      name: this.industryName,
      isActive: true,
    };
    if (this.editObj) {
      industry.id = this.editObj.id;
    }
    this.loading = true;
    this.industry
      .createRecord(industry)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((response) => {
        if (response) {
          if (this.editObj) {
            this.products = this.products.map((product) => {
              if (product.id === this.editObj.id) {
                product.name = this.industryName;
              }
              return product;
            });
          } else {
            this.products.push({
              id: this.products.length + 1,
              ...industry,
            });
          }
          this.industryName = null;
          this.editObj = null;
          this.toaster.success(response.message);
        }
      });
  }

  edit(item: any) {
    this.editObj = item;
    this.industryName = item.name;
  }

  deleteConfirmation(template: any, item: any) {
    this.deleteId = item.id;
    this.modalRef = this.modalService.show(template, {
      class: 'modal-lg',
    });
  }

  deleteRole() {
    this.industry.deleteItem(this.deleteId).subscribe((response: any) => {
      if (response.messageType) {
        return this.toaster.error(response.message);
      }
      this.modalRef.hide();
      this.deleteId = '';
      return this.toaster.success(response.message);
    });
  }
}
