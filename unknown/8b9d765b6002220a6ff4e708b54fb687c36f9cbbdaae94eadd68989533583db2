<app-modal [title]="blog.title" [templateRef]="templateRef" [firstButtonDisabled]="false" [firstButtonText]="'Edit'"
    (onFirstButtonClick)="handleOnEdit()" (onSecondButtonClick)="handleCancel()">
</app-modal>
<ng-template #templateRef let-modalRef="modalRef">
    <div class="modal-body">
        <div class="d-flex flex-column mb-3">
            <ul class="list-group">
                <li class="list-group-item">Title: {{ blog.title }}</li>
                <li class="list-group-item">Subtitle: {{ blog.subTitle }}</li>
                <li class="list-group-item">Created At: {{ blog.createdAt | date: 'mediumDate' }}</li>
                <li class="list-group-item">Tags: <span *ngFor="let tag of blog.tags" class="badge bg-secondary ms-2">{{
                        tag }}</span></li>
            </ul>
        </div>
        <span [innerHTML]="blog.content"></span>
    </div>
</ng-template>