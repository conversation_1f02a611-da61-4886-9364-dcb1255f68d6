<div>
  <nav aria-label="breadcrumb" class="d-none d-md-inline-block">
    <ol class="breadcrumb breadcrumb-dark breadcrumb-transparent">
      <li class="breadcrumb-item">
        <a href="#"
          ><svg
            class="icon icon-xxs"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
            ></path></svg
        ></a>
      </li>
    </ol>
  </nav>
  <div class="d-flex flex-column justify-content-between w-100 flex-wrap">
    <div class="mb-3 mb-lg-0">
      <h1 class="h4">Pending Elevator pitch approvals</h1>
      <p class="mb-0">
        <!-- pending description -->
      </p>
    </div>
    <div class="card border-0 shadow-sm mb-5">
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-centered table-nowrap mb-0 rounded">
            <thead class="thead-light">
              <tr>
                <th class="border-0 rounded-start">#</th>
                <th class="border-0 col-2">Person Name</th>
                <th class="border-0 col-2">Video Type</th>
                <th class="border-0">Video Link</th>
                <th class="border-0">Date</th>
                <th class="border-0">Status</th>
                <th class="border-0 rounded-end text-center">Action</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let expert of data; let i = index">
                <td class="border-0">
                  {{ i + 1 }}
                </td>
                <td class="border-0 fw-bold col-2">
                  {{ expert.userName }}
                </td>
                <td class="border-0 fw-bold col-2">
                  {{ expert.approvePostTypeDescription }}
                </td>
                <td class="border-0 text-danger col-2">
                  <div class="d-flex align-items-center">
                    <span class="fw-bold">
                      <a target="_blank">
                        {{ expert.url }}
                      </a>
                    </span>
                  </div>
                </td>
                <td class="border-0 fw-bold col-2">
                  {{ expert.uploadedAt | date }}
                </td>
                <td class="border-0 col-2">
                  <label
                    *ngIf="expert.status == ApprovePostStatusEnum.Pendding"
                    class="badge text-bg-warning"
                  >
                    Pending
                  </label>
                  <label
                    *ngIf="expert.status == ApprovePostStatusEnum.Approved"
                    class="badge text-bg-primary"
                  >
                    Approved
                  </label>
                  <label
                    *ngIf="expert.status == ApprovePostStatusEnum.Reject"
                    class="badge text-bg-danger"
                  >
                    Rejected
                  </label>
                </td>
                <td class="border-0 text-success text-center col-2">
                  <ng-container *ngIf="expert.status == 1">
                    <button
                      (click)="
                        approvePitch(expert, ApprovePostStatusEnum.Approved)
                      "
                      class="btn btn-sm btn-outline-primary h-auto"
                      tooltip="approve"
                    >
                      <i class="fas fa-check"></i>
                    </button>
                    <button
                      (click)="
                        approvePitch(expert, ApprovePostStatusEnum.Reject)
                      "
                      class="btn btn-sm btn-outline-danger ms-3 h-auto"
                      tooltip="reject"
                    >
                      <i class="far fa-times-circle"></i>
                    </button>
                  </ng-container>
                  <button
                    class="btn btn-sm btn-outline-success ms-3 h-auto"
                    tooltip="view"
                  >
                    <i class="fas fa-eye"></i>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
          <app-pagination
            [data]="dataCopy"
            (onPageChange)="data = $event.data"
          ></app-pagination>
        </div>
      </div>
    </div>
  </div>
</div>
