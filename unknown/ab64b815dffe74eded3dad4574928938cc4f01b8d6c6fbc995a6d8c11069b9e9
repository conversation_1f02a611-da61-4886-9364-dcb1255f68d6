import { HttpClient } from '@angular/common/http';
import { Component, type OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-elevator-approvals',
  templateUrl: './elevator-approvals.component.html',
  styleUrls: ['./elevator-approvals.component.css'],
})
export class ElevatorApprovalsComponent implements OnInit {
  data: any = [];
  dataCopy: any = [];
  ApprovePostStatusEnum = ApprovePostStatusEnum;
  constructor(
    private httpClient: HttpClient,
    private readonly toaster: ToastrService
  ) {}

  ngOnInit(): void {
    this.getElevatorPitchs();
  }

  getElevatorPitchs() {
    this.httpClient
      .get(`ApprovePost/GetUserApprovePenddingPost`)
      .subscribe((response: any) => {
        this.data = response.data;
      });
  }

  approvePitch(expert: any, status: number) {
    this.httpClient
      .post(`ApprovePost/ApproveRejectPost?id=${expert.id}&status=${status}`, {})
      .subscribe((response: any) => {
        if (response.message) {
          this.toaster.error(
            'Something went wrong please contact focile team.'
          );
        } else {
          expert.status = status;
          this.toaster.success('Elevator pitch approved successfully');
        }
      });
  }
}

enum ApprovePostStatusEnum {
  Pendding = 1,
  Approved = 2,
  Reject = 3,
}
