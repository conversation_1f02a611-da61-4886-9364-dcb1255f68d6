.fc-footer-wrapper{
    background:#12141D;
    position: relative;
    min-height: 461px;
    overflow: hidden;

    &::after {
        position: absolute;
        width: 269px;
        height: 269px;
        right:1rem;
        top: 14px;
        background: #014581;
        filter: blur(262px);
        content: '';
    }

    &::before {
        position: absolute;
        width: 455px;
        height: 455px;
        left: -151px;
        top: 214px;
        background: #024581;
        filter: blur(262px);
        content: '';
    }    
}

.fc-ft-top-bar{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-top: 48px;
    margin-bottom: 38px;
}

.fc-footer-logo{
    width: 211px;
    img{
        max-width: 110px;
    }
    p{
        font-size: 14px;
        line-height: 1.5;
        color: white;
        margin-top: 38px;
        margin-bottom: 48px;

        b{
            color: #FFA500;
        }
    }

    .fc-ft-social{
        display: flex;
        flex-direction: row;
        gap: 1rem;

        ul{
            margin-bottom: 0px;
        }
    }
}

.fc-ft-menu{
    display: flex;
    flex-direction: row;
    gap: 70px;

    ul{
        list-style: none;
        padding-left: 0px;
        display: flex;
        flex-direction: column;
        gap: 1rem;
        
        h4{
            color: white;
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 18px;
        }

        li{
            a{
                color: white;
                font-size: 14px;
                text-decoration: none;
            }
        }
    }
}

.fc-hq-address{
    width: 251px;
    display: flex;
    flex-direction: column;

            
    h4{
        color: white;
        font-size: 18px;
        font-weight: 700;
        margin-bottom: 38px;
    }

    .address{
        font-size:14px;
        font-weight: 400;
        color: white;
        margin-bottom: 28px;
    }

    .genenal-address{
        font-size: 14px;
        display: flex;
        flex-direction: column;
        label{
            color: rgb(255 255 255 / 80%);
            margin-bottom:8px;
        }
        span{
            color: white;
        }

        a{
            text-decoration: none;
            color: white;
        }
    }

    hr{
        border-color: #3E4550;
    }
}

.fc-ft-bottom{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding-block:30px;
    border-top: 1px solid rgb(62 69 80 / 25%);
    z-index: 1;
    position: relative;

    .copyright{
        font-size: 14px;
        color: white;
    }

    ul{
        list-style: none;
        display: flex;
        flex-direction: row;
        margin-bottom: 0px;
        padding-left: 0px;

        li{        
            display: flex;
            align-items: center;
            line-height: 1;
            a{
                font-size: 14px;
                color: white;
                text-decoration: none;
                padding: 0px 10px;
                border-right: 1px solid white;                
            }
            &:last-child a{
                border-right: 0px;
            }
        }
    }
}

@media(max-width:768px) {
    .fc-ft-top-bar {
        flex-direction: column;
        margin-top: 3rem;
        margin-bottom:1rem;
    }

    .fc-ft-menu {
        width: 100%;
        flex-direction: column;
        margin-top:2rem;
        gap: 2rem;

        ul{
            margin-bottom: 0px;
            h4{
                margin-bottom: 0;
                font-size: 1rem;
            }
        }
    }
    .fc-footer-logo{
        width: 100%;

        img{
            height: 50px;
            max-width:max-content;
        }

        p{
            margin-top: 2rem;
            margin-bottom: 2rem;
        }
        .fc-ft-social{
            ul{
            justify-content: center !important;
            gap: 2rem;
            }
        }
    }
    .fc-footer-wrapper{
        .fc-container{
            z-index: 0;
            position: relative;
        }
    }
    .fc-hq-address{
        margin-top: 2rem;
        width: 100%;
        h4{
            margin-bottom: 1rem;
        }
        .address{
            margin-bottom: 1rem;

            br{
                display: none;
            }
        }

        .genenal-address{
            display: flex;
            gap: 1rem;
            label{
                margin-bottom: 0px;
            }
        }
    }
    .fc-ft-bottom{
        justify-content: center;
        align-items: center;
        flex-direction: column;
        ul{
            text-align: center;
            justify-content: center;
            margin-top: 1rem;
            flex-wrap: wrap;
            gap: 10px;

            li a{
                font-size: 12px;
            }
        }
        .copyright{
            font-size: 12px;
        }
    }
}