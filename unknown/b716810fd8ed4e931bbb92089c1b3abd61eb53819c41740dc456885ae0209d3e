import { HttpClient, HttpEvent, HttpEventType } from '@angular/common/http';
import { Component, Input, type OnInit } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { EMPTY, finalize, switchMap, tap } from 'rxjs';
import { ModalService } from 'src/app/shared/services/modal.service';

@Component({
  selector: 'app-add-edit-blog',
  templateUrl: './add-edit-blog.component.html',
  styleUrls: ['./add-edit-blog.component.scss'],
})
export class AddEditBlogComponent implements OnInit {
  @Input() blog: any;
  @Input() onEditSuccess: any;
  blogForm!: FormGroup;
  categories: any[] = [];
  editMode = false;
  content: string = '';
  loading = false;
  // File input for image upload
  imageFile: File | null = null;

  uploadProgress: any;
  constructor(
    private fb: FormBuilder,
    private http: HttpClient,
    private toaster: ToastrService,
    private modalService: ModalService
  ) {}
  ngOnInit(): void {
    this.blogForm = this.fb.group({
      title: [this.blog?.title || null, Validators.required],
      subTitle: [this.blog?.subTitle || null, Validators.required],
      categoryId: [this.blog?.categoryId || null, Validators.required],
      content: [this.blog?.content || null, Validators.required],
      slug: [this.blog?.slug || null, Validators.required],
      tags: [this.blog?.tags || null, Validators.required],
    });

    if (this.blog) {
      this.editMode = true;
      this.content = this.blog.content;
      this.blogForm.addControl('id', new FormControl(this.blog.id));
    }

    this.blogForm.get('title')?.valueChanges.subscribe((value: string) => {
      if (!value) return;
      const slug = value
        .toLowerCase()
        .replace(/\s+/g, '-')
        .replace(/[^a-z0-9\-]/g, '');
      this.blogForm.get('slug')?.setValue(slug, { emitEvent: false });
    });

    this.getAllCategorise();
  }

  getAllCategorise() {
    this.http.get('blog/getallcategories').subscribe((response: any) => {
      this.categories = response;
    });
  }

  handleOnSubmit() {
    if (this.blogForm.valid) {
      const payload = this.blogForm.value;
      if (this.editMode) {
        this.http.post(`blog/UpdateBlog`, payload).subscribe(() => {
          this.toaster.success('Blog updated successfully!');
          this.modalService.closeModal();
          this.onEditSuccess(payload);
        });
      } else {
        this.loading = true;
        this.http
          .post('blog/addblog', payload)
          .pipe(
            switchMap((blogResponse: any) => {
              if (this.imageFile) {
                const formData = new FormData();
                formData.append('file', this.imageFile);
                return this.http
                  .post(`blog/UploadBlogBanner/${blogResponse.id}`, formData, {
                    reportProgress: true,
                    observe: 'events',
                  })
                  .pipe(
                    tap((event: HttpEvent<any>) => {
                      switch (event.type) {
                        case HttpEventType.Sent:
                          console.log('Upload started...');
                          break;

                        case HttpEventType.UploadProgress:
                          if (event.total) {
                            const progress = Math.round(
                              (event.loaded / event.total) * 100
                            );
                            this.uploadProgress = progress; // You can bind this to a progress bar
                          }
                          break;

                        case HttpEventType.Response:
                          console.log('Upload complete', event.body);
                          this.uploadProgress = null; // Reset progress after completion
                          break;
                      }
                    })
                  );
              }
              return EMPTY;
            })
          )
          .pipe(
            finalize(() => {
              this.loading = false;
            })
          )
          .subscribe((response: HttpEvent<any>) => {
            if (response.type === HttpEventType.Response) {
              console.log({ response });
              this.toaster.success('Blog saved successfully!');
              this.blogForm.reset();
              this.modalService.closeModal();
            }
          });
      }
    } else {
      this.blogForm.markAllAsTouched();
    }
  }

  handleCancel() {}

  handleEditorChange($event: string) {
    this.blogForm.patchValue({ content: $event });
  }

  handleOnCategoryChange($event: any) {
    const selectedCategory = this.categories.find(
      (category) => category.id === $event
    );
    if (selectedCategory) {
      this.blogForm.patchValue({
        slug: selectedCategory.slug,
      });
    }
  }

  handleFileInput($event: any) {
    const file = $event.target.files[0];
    console.log(file);
    if (file) {
      this.imageFile = file;
      // this.blogForm.patchValue({ image: file });
    }
  }
}
