<div class="sign-in-wrapper">
  <div class="sign-box">
    <div>
      <img role="button" [routerLink]="'/home'" src="./assets/images/focile-logo.svg" alt="focile logo" />
    </div>
    <!-- <div class="mt-3 d-flex align-items-center">
      <i routerLink="/home" tooltip="Go Back" role="button" triggers=""></i>
      < <span routerLink="/home" role="button" class="text-primary fw-bold">
        &nbsp; Home </span>
    </div> -->
    <span class="fw-semibold fs-4 mt-4 mb-3"> Sign in </span>
    <p class="text-muted">
      To improve your experience and connect with the right experts on our platform, please activate your location in
      the settings.
    </p>
    <form [formGroup]="loginForm" autocomplete="off" class="w-100">
      <div class="mb-3 form-txt">
        <label for="email" class="form-label">Email address</label>
        <app-focile-input [type]="'email'" [id]="'email'" [name]="'email'" [placeholder]="'Enter your email'"
          [disabled]="false" [iconName]="'envelope'" formControlName="email"></app-focile-input>
      </div>

      <div class="mb-3 form-txt">
        <label for="password" class="form-label">Password</label>
        <app-focile-input [type]="showPassword ? 'text' : 'password'" [id]="'password'" [name]="'password'"
          [placeholder]="'Enter your password'" [disabled]="false" (onIconClick)="handleIconClick()"
          formControlName="password" [autocomplete]="'new-password'"></app-focile-input>

        <div class="hide-show-tag">

          <span (click)="handleIconClick()" class="position-absolute" style="top: 2px; right:6px">
            {{ showPassword ? 'Hide' : 'Show' }} <i [ngClass]="showPassword ? 'fa fa-eye' : 'fa fa-eye-slash'"></i>
          </span>
        </div>
      </div>
      <div class="d-flex flex-row justify-content-between mb-3">
        <span class="password-hint">Use your 12 or more characters for the login</span>        
        <a routerLink="/recovery" class="text-primary forgot-text">Forgot password?</a>
      </div>
      <div class="form-check mt-4 cursor-pointer">
        <input
          type="checkbox"
          class="form-check-input"
          id="rememberMe"
          formControlName="rememberMe"
        />
        <label class="form-check-label cursor-pointer" for="rememberMe">Remember me</label>
      </div>   
      <div class="fc-dont-account-link">
        <div class="">
          <span>Don't have a focile account?</span>
          <span tooltip="Create new account as focile expert or focile member" (click)="goto()" role="button"
            class="create-new">Create now</span>
        </div>
        <focile-button [btnType]="'primary'" [btnClass]="'w-auto submit-btn'" [loading]="loading"
          [disabled]="loginForm.invalid" (onClick)="handleLogin()" [type]="'submit'">
          Sign in</focile-button>
      </div>
    </form>
  </div>
</div>
<app-footer></app-footer>