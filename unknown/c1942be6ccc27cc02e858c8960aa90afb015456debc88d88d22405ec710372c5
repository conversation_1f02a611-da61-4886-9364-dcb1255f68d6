import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AccountService } from '../services/account.service';
import { ToastrService } from 'ngx-toastr';
import { finalize } from 'rxjs';
import { ProfileStatusEnum } from 'src/app/shared/constant';

@Component({
  selector: 'app-actiate-account',
  templateUrl: './actiate-account.component.html',
  styleUrls: ['./actiate-account.component.scss'],
})
export class ActiateAccountComponent implements OnInit {
  email: any;
  token: any;
  loading = false;
  constructor(
    private readonly activate: ActivatedRoute,
    private readonly accoount: AccountService,
    private readonly ngxToaster: ToastrService,
    private readonly router: Router
  ) {}

  ngOnInit(): void {
    this.activate.queryParams.subscribe((response: any) => {
      if (response.email && response.token) {
        this.email = response.email;
        this.token = decodeURIComponent(response.token.replace(' ', '+'));
      }
    });
  }

  verifyMe() {
    this.loading = true;
    this.accoount
      .activateAccount({
        email: this.email,
        token: this.token,
      })
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((response: any) => {
        if (response.data) {
          let message = '';
          if (response.message == ProfileStatusEnum.AlreadyApproved) {
            message =
              'Your account is already verified. You can now log in with your credentials';
            this.ngxToaster.info(message);
          } else {
            message =
              'Congratulations your account has been succssfully activated';
            this.ngxToaster.success(message);
          }
          this.router.navigate(['/account']);
        } else {
          this.ngxToaster.error(
            '',
            'Oops something went wrong please try again latter.'
          );
        }
      });
  }
}
