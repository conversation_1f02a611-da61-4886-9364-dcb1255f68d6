import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { ConfirmationComponent } from 'src/app/shared/modals/confirmation/confirmation.component';
import { ViewUserDetailComponent } from 'src/app/shared/modals/view-user-detail/view-user-detail.component';
import { ModalService } from 'src/app/shared/services/modal.service';
import { ExpertService } from '../services/expert.service';
import { UserProfileStatusEnum } from 'src/app/shared/constant';
import { finalize } from 'rxjs';
import { HttpClient } from '@angular/common/http';
@Component({
  selector: 'app-pending-approvals',
  styleUrls: ['./pending-approvals.component.css'],
  templateUrl: './pending-approvals.component.html',
})
export class PendingApprovalsComponent implements OnInit {
  data: any = [];
  dataCopy: any[] = [];
  PAGE_PER_RECORDS = 10;
  pendingApprovals = [];
  loading = false;
  adminData: any[] = [];
  adminDataCopy: any[] = [];
  isAdopters = false;
  constructor(
    private expertService: ExpertService,
    private toaster: ToastrService,
    private modalService: ModalService,
    private http: HttpClient
  ) {}
  ngOnInit(): void {
    this.getPendingApprovals();
  }

  setProfileState(row: any) {
    this.expertService
      .setProfileApproval(row.id, UserProfileStatusEnum.Approved)
      .subscribe((response: any) => {
        if (response.data) {
          // const userCopy = this.dataCopy.find((x) => x.userId === row.id);
          // const user = this.data.find((x: any) => x.userId === row.id);
          // if (user) {
          row.isUserApproved = UserProfileStatusEnum.Approved;
          //   userCopy.isUserApproved = UserProfileStatusEnum.Approved;
          // }
          this.toaster.success('User Approved');
        }
      });
  }

  deleteRow(row: any) {
    this.expertService
      .setProfileApproval(row.id, UserProfileStatusEnum.Rejected)
      .subscribe((response: any) => {
        if (response.data) {
          row.isUserApproved = UserProfileStatusEnum.Rejected;
        }
      });
  }

  getEarlyAdopters() {
    this.loading = true;
    this.adminData = [];
    this.adminDataCopy = [];
    this.isAdopters = true;
    this.expertService
      .getEarlyAdopters()
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((response: any) => {
        if (response?.data?.length) {
          this.adminDataCopy = response?.data;
          this.adminDataCopy.sort((a, b) =>
            new Date(a.createdAt) > new Date(b.createdAt) ? -1 : 1
          );
          this.adminData = this.adminDataCopy.slice(0, this.PAGE_PER_RECORDS);
        }
      });
  }

  getPendingApprovals() {
    this.loading = true;
    this.data = [];
    this.dataCopy = [];
    this.isAdopters = false;
    this.expertService
      .getPendingApprovals()
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((response: any) => {
        if (response.data.length) {
          this.dataCopy = response.data.map((x: any, index: number) => {
            x.id = x.userId;
            x.personName = x.firstName + ' ' + x.lastName;
            x.companyName_role =
              x.userType === 1
                ? `${x.organizationName}`
                : `${x.expertDetail.companyName} - ${x.expertDetail.companyType}`;
            x.address = `${x.address}, ${x.cityName} - ${x.zipCode} , ${x.stateName}, ${x.countryName}`;
            x.email_phoneNumber = `${x.email} - ${x.phoneNumber}`;
            x.website = x.expertDetail.companyWebsite;
            x.isUserApproved = x.isUserApproved;
            return x;
          });
          this.dataCopy.sort((a, b) =>
            new Date(a.createdAt) > new Date(b.createdAt) ? -1 : 1
          );
          this.data = this.dataCopy.slice(0, this.PAGE_PER_RECORDS);
        }
      });
  }

  redirectToExternalURL(website: string) {
    window.open(website, '_blank');
  }

  viewProfile(expert: any) {
    this.modalService.openModal('view-user-detail', {
      initialState: {
        user: expert,
        onApprove: () => {
          this.setProfileState(expert);
          this.modalService.closeModal();
        },
        onUpdate: () => {
          this.modalService.closeModal();
          if (this.isAdopters) {
            this.getEarlyAdopters();
          } else {
            this.getPendingApprovals();
          }
        },
      },
      class: 'modal-lg',
    });
  }

  confirmationDeactivateUser(expert: any) {
    this.modalService.openModal('confirmation', {
      initialState: {
        firstButtonText: 'Deactivate',
        message: `Are you sure you want to deactivate this user ?`,
        onConfirm: () => {
          this.deactivateUser(expert);
        },
      },
    });
  }

  deactivateUser(expert: any) {
    this.http
      .delete(`user/deactivateuser?UserId=${expert.user.id}`)
      .subscribe((response: any) => {
        if (response.data) {
          this.toaster.success(`User deactivated successfully.`);
          expert.isActive = false;
        } else {
          this.toaster.error('Opps please try again later.');
        }
      });
  }
}
