<app-account-shell [ngClass]="{'light-blue': registrationForm.value.userType == 1, 'dark-blue': registrationForm.value.userType == 2}">
  <app-helper-text
    *ngIf="!isLocationEnabled"
    [message]="'In order to sign up please enable allow location.'"
  >
  </app-helper-text>
  <ng-container header>
    <div class="d-flex align-items-center mt-3 mt-sm-2 w-100 justify-content-between justify-content-sm-start z-index-1">
      <div class="m-focile-logo d-flex d-sm-none">
        <a class="navbar-brand" routerLink="/home">
          <div>
            <img role="button" [routerLink]="'/home'" src="./assets/svgs/focile.svg" alt="focile logo" height="50" />
          </div>
        </a>
      </div>
      <span class="fc-back-arrow d-flex flex-row align-items-center gap-2 cursor-pointer" routerLink="/account/choose-option" role="button">
        <span>
          <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M7.41 1.41L2.83 6L7.41 10.59L6 12L0 6L6 0L7.41 1.41Z" fill="rgba(0, 0, 0, 0.5)"/>
              </svg>                    
         </span>
         Back
      </span>
    </div>
    <span class="text-fw-800 fs-3 mt-5 d-flex flex-wrap flex-row dynamic-text">      
      <span class="text-primary ms-0 ms-sm-0"><b class="text-black">Sign up</b> - {{ registrationForm.value.userType == 1 ? "Individuals and Business Members" : "Channel Partner, Expert or Consultant" }}
      </span>
    </span>
    <p class="text-muted mt-1 mb-4 mb-sm-5">
      Before we start, please check your setting to allow current location for
      better service support
    </p>
  </ng-container>
  <ng-container body>
    <ng-container *ngIf="!isFirstStepCompleted">
      <form [formGroup]="registrationForm" autocomplete="off">
        <ng-container *ngIf="!isFirstStepCompleted">
          <div class="row pt-0">
            <div class="col-md-6">
              <div class="mb-3 mb-sm-4">
                <label for="name" class="form-label"
                  >First Name <span class="text-danger">*</span></label
                >
                <app-focile-input
                  [type]="'text'"
                  [id]="'id'"
                  [name]="'name'"
                  [disabled]="false"
                  formControlName="firstname"
                  [elementClass]="
                    registrationForm.get('firstname')?.touched &&
                    (registrationForm.get('firstname')?.errors?.required ||
                      registrationForm.get('firstname')?.errors?.pattern)
                      ? 'is-invalid'
                      : null
                  "
                ></app-focile-input>
                <ng-container
                  *ngIf="registrationForm.get('firstname')?.touched"
                >
                  <span
                    *ngIf="registrationForm.get('firstname')?.errors?.required"
                    class="text-danger"
                  >
                    First Name is required.
                  </span>
                  <span
                    *ngIf="registrationForm.get('firstname')?.errors?.pattern"
                    class="text-danger"
                  >
                    First Name should contains only letters.
                  </span>
                </ng-container>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3 mb-sm-4">
                <label for="lastname" class="form-label"
                  >Last Name <span class="text-danger">*</span></label
                >
                <app-focile-input
                  [type]="'text'"
                  [id]="'lastname'"
                  [name]="'lastname'"
                  [disabled]="false"
                  formControlName="lastname"
                  [elementClass]="
                    registrationForm.get('lastname')?.touched &&
                    (registrationForm.get('lastname')?.errors?.required ||
                      registrationForm.get('lastname')?.errors?.pattern)
                      ? 'is-invalid'
                      : null
                  "
                ></app-focile-input>
                <ng-container *ngIf="registrationForm.get('lastname')?.touched">
                  <span
                    *ngIf="registrationForm.get('lastname')?.errors?.required"
                    class="text-danger"
                  >
                    Last Name is required.
                  </span>
                  <span
                    *ngIf="registrationForm.get('lastname')?.errors?.pattern"
                    class="text-danger"
                  >
                    Last Name should contains only letters.
                  </span>
                </ng-container>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3 mb-sm-4">
                <label for="mobile" class="form-label"
                  >Mobile No. <span class="text-danger">*</span></label
                >
                <span class="d-flex gap-3">
                  <span class="w-50">
                    <focile-dropdown
                      [items]="countryCodes"
                      formControlName="countryCode"
                      [bindValue]="'description'"
                      [bindLabel]="'description'"
                      [loading]="dataLoading"
                      [clearable]="false"
                    ></focile-dropdown>
                  </span>
                  <div class="w-100">
                    <input
                      type="text"
                      class="form-control"
                      id="orgname"
                      formControlName="mobileNumber"
                      placeholder="(xxx) xxx.xxxx"
                      required
                      [ngClass]="{
                        'is-invalid':
                          expertDetailsForm.get('mobileNumber')?.invalid &&
                          expertDetailsForm.get('mobileNumber')?.touched
                      }"
                      appPhoneNumber
                    />
                  </div>
                </span>
                <span
                  *ngIf="
                    registrationForm.get('mobileNumber')?.touched &&
                    registrationForm.get('mobileNumber')?.errors?.required
                  "
                  class="text-danger"
                >
                  Mobile No is required.
                </span>
                <span
                  *ngIf="
                    registrationForm.get('mobileNumber')?.touched &&
                    registrationForm.get('mobileNumber')?.errors?.pattern
                  "
                  class="text-danger"
                >
                  Mobile No is not valid.
                </span>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3 mb-sm-4">
                <label for="email" class="form-label"
                  >Email <span class="text-danger">*</span></label
                >
                <app-focile-input
                  [type]="'email'"
                  [id]="'email'"
                  [name]="'email'"
                  [disabled]="false"
                  [iconName]="'envelope'"
                  formControlName="email"
                  [elementClass]="
                    (registrationForm.get('email')?.touched &&
                      registrationForm.get('email')?.errors?.required) ||
                    (registrationForm.get('email')?.touched &&
                      registrationForm.get('email')?.errors?.email)
                      ? 'is-invalid'
                      : null
                  "
                ></app-focile-input>
              </div>
              <span
                *ngIf="
                  registrationForm.get('email')?.touched &&
                  registrationForm.get('email')?.errors?.required
                "
                class="text-danger"
              >
                Email is required.
              </span>
              <span
                *ngIf="
                  registrationForm.get('email')?.touched &&
                  registrationForm.get('email')?.errors?.email
                "
                class="text-danger"
              >
                Email is not valid.
              </span>
            </div>
            <div class="col-md-6 mb-4">
              <div class="position-relative">
                <label for="password" class="form-label"
                  >Password <span class="text-danger">*</span>
                  <i
                    [popover]="popTemplate"
                    (click)="showStrongPasswordPopup = !showStrongPasswordPopup"
                    class="fa fa-info-circle ms-2 text-primary"
                    placement="top"
                    role="button"
                    [outsideClick]="true"
                  ></i>
                </label>

                <input
                  [type]="showPassword ? 'text' : 'password'"
                  [class]="'form-control'"
                  [id]="'password'"
                  [name]="'password'"
                  formControlName="password"
                  [popover]="popTemplate"
                  #pop="bs-popover"
                  [placement]="'right'"
                  placeholder="Type here"
                  triggers="focus blur"
                  autocomplete="new-password"
                  [ngClass]="{
                    'is-invalid':
                      (registrationForm.get('password')?.touched &&
                        registrationForm.get('password')?.invalid) ||
                      passwordError.length
                  }"
                />
                <span
                  class="position-absolute"
                  (click)="handleIconClick('password')"
                  style="top: 43px; right: 22px"
                >
                  <i
                    class="fa"
                    [ngClass]="{
                      'fa-eye': !showPassword,
                      'fa-eye-slash': showPassword
                    }"
                  ></i>
                </span>
                <ng-template #popTemplate>
                  <app-password-rules
                    [password]="registrationForm.get('password')?.value"
                    (isValid)="handlePasswordRuleChange($event)"
                  ></app-password-rules>
                </ng-template>
              </div>
              <div
                *ngIf="
                  registrationForm.get('password')?.touched &&
                  registrationForm.get('password')?.invalid &&
                  !registrationForm.get('password')?.value
                "
                class="text-danger"
              >
                <div *ngIf="registrationForm.get('password')?.invalid">
                  Password is required.
                </div>
              </div>
            </div>
            <div class="col-md-6 mb-4">
              <div>
                <label for="confirmPassword" class="form-label"
                  >Confirm Password <span class="text-danger">*</span></label
                >
                <span class="position-relative">
                  <input
                    [type]="showConfirmPassword ? 'text' : 'password'"
                    [class]="'form-control'"
                    [id]="'confirmPassword'"
                    [name]="'confirmPassword'"
                    formControlName="confirmPassword"
                    placeholder="Type here"
                    [ngClass]="{
                      'is-invalid':
                        registrationForm.get('confirmPassword')?.touched &&
                        registrationForm.get('confirmPassword')?.errors
                    }"
                  />
                  <span
                    class="position-absolute"
                    (click)="handleIconClick('confirmPassword')"
                    style="top: 43px; right: 22px"
                    role="button"
                  >
                    <i
                      class="fa"
                      [ngClass]="{
                        'fa-eye': !showConfirmPassword,
                        'fa-eye-slash': showConfirmPassword
                      }"
                    ></i>
                  </span>
                </span>
              </div>
              <div
                *ngIf="
                  registrationForm.get('confirmPassword')?.touched &&
                  registrationForm.get('confirmPassword')?.errors?.required
                "
                class="text-danger"
              >
                Confirm Password is required.
              </div>
              <span
                *ngIf="
                  registrationForm.get('confirmPassword')?.touched &&
                  registrationForm.get('confirmPassword')?.value !=
                    registrationForm.get('password')?.value &&
                  !registrationForm.get('confirmPassword')?.errors?.required
                "
                class="text-danger"
              >
                Both password should be same.
              </span>
            </div>
            <ng-container *ngIf="_userType == 2">
              <div class="col-md-6">
                <div class="mb-3 mb-sm-4">
                  <label for="companyWebsite" class="form-label"
                    >Company Website
                    <span
                      *ngIf="
                        registrationForm.value.userType == userType.ExpertUser
                      "
                      class="required"
                      >*</span
                    ></label
                  >

                  <ng-select
                    [items]="companyList"
                    bindLabel="description"
                    bindValue="idGuid"
                    placeholder="Type here"
                    [addTag]="true"
                    (clear)="handleOnClear()"
                    (add)="onAddTag($event)"
                    [typeahead]="searchInput"
                    (search)="onSearch($event)"
                    [readonly]="
                      registrationForm.get('companyWebsite')?.disabled
                        ? true
                        : false
                    "
                    #companyWebsiteDDL
                    (blur)="
                      registrationForm.get('companyWebsite')?.markAsTouched()
                    "
                    (change)="handleCompanyWebsiteChange($event)"
                  >
                  </ng-select>
                  <div
                    class="text-danger"
                    *ngIf="
                      registrationForm.get('companyWebsite')?.invalid &&
                      registrationForm.get('companyWebsite')?.touched
                    "
                  >
                    Website should be a valid url
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3 mb-sm-4">
                  <label for="orgname" class="form-label"
                    >Company name
                    <span
                      *ngIf="
                        registrationForm.value.userType == userType.ExpertUser
                      "
                      class="required"
                      >*</span
                    ></label
                  >
                  <input
                    type="text"
                    class="form-control"
                    id="orgname"
                    placeholder="Type here"
                    formControlName="companyName"
                    required
                    [ngClass]="{
                      'is-invalid':
                        registrationForm.get('companyName')?.invalid &&
                        registrationForm.get('companyName')?.touched
                    }"
                  />
                  <div
                    class="text-danger"
                    *ngIf="
                      registrationForm.get('companyName')?.hasError('pattern')
                    "
                  >
                    Company Name can only contain letters and numbers.
                  </div>
                  <div
                    *ngIf="
                      registrationForm.get('companyName')?.errors?.required &&
                      registrationForm.get('companyName')?.touched
                    "
                    class="text-danger"
                  >
                    Company name is required.
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3 mb-sm-4">
                  <label for="export" class="form-label">Channel Group
                    <span *ngIf="
                                    registrationForm.value.userType == userType.ExpertUser
                                  " class="required">*</span></label>
                  <ng-select [items]="experts" bindLabel="name" bindValue="id" formControlName="expert" placeholder="Select an item"
                    [disabled]="registrationForm.disabled" [loading]="dataLoading" [clearable]="false">
                  </ng-select>
                  <span class="text-danger" *ngIf="
                                  registrationForm.get('expert')?.touched &&
                                  registrationForm.get('expert')?.errors?.required
                                ">
                    Expert is required.
                  </span>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3 mb-sm-4">
                  <label for="roles" class="form-label"
                    >Role
                    <span
                      *ngIf="
                        registrationForm.value.userType == userType.ExpertUser
                      "
                      class="required"
                      >*</span
                    ></label
                  >
                  <ng-select
                    [items]="roleItems"
                    bindLabel="name"
                    bindValue="idGuid"
                    formControlName="roleId"
                    [placeholder]="'Select'"
                    [disabled]="registrationForm.disabled"
                    [loading]="dataLoading"
                    [clearable]="false"
                  >
                  </ng-select>
                  <span
                    class="text-danger"
                    *ngIf="
                      registrationForm.get('roleId')?.touched &&
                      registrationForm.get('roleId')?.errors?.required
                    "
                  >
                    Role is required.
                  </span>
                </div>
              </div>
              <div class="row d-none">
                <div class="col-md-6 mt-3">
                  <div
                    role="button"
                    (click)="setUserType(userType.ExpertUser)"
                    class="mb-4"
                    style="min-height: 181px"
                  >
                    <label for="expert" class="form-label text-bold"
                      >Channel Partner, Expert and Consultants</label
                    >

                    <p
                      class="bg-default p-3 d-flex flex-column rounded shadow"
                      [ngClass]="{
                        'bg-primary text-bg-primary shadow-lg':
                          registrationForm.getRawValue().userType == 2
                      }"
                    >
                      <input
                        type="radio"
                        class="mb-4"
                        formControlName="userType"
                        name="userType"
                        value="2"
                        (change)="handleUserTypeChange()"
                      />

                      <span>
                        Are you an SMB channel partner or expert working at a
                        reseller, vendor, or a distributor looking to connect
                        with counterparts, provide services, or connect with
                        end-users?
                      </span>
                    </p>
                  </div>
                </div>
                <div class="col-md-6 mt-3">
                  <div
                    role="button"
                    (click)="setUserType(userType.EndUser)"
                    class="mb-4"
                    style="min-height: 181px"
                  >
                    <label
                      for="exampleFormControlInput1"
                      class="form-label text-bold"
                      >End User</label
                    >
                    <p
                      class="p-3 d-flex flex-column rounded shadow-sm"
                      [ngClass]="{
                        'bg-primary text-bg-primary shadow-lg':
                          registrationForm.getRawValue().userType == 1
                      }"
                      style="line-height: 40px"
                    >
                      <input
                        type="radio"
                        class="mb-4"
                        formControlName="userType"
                        name="userType"
                        value="1"
                        (change)="handleUserTypeChange()"
                      />
                      <span>
                        Are you an End-User looking to connect with Experts to
                        implement a solution at your organization?
                      </span>
                    </p>
                  </div>
                </div>
              </div>
            </ng-container>
            <div class="col-md-12" *ngIf="_userType == 2">
              <div class="form-check">
                <input
                  class="form-check-input"
                  type="checkbox"
                  id="emailUpdates"
                  name="emailUpdates"
                  formControlName="emailUpdates"
                />
                <label
                  class="form-check-label text-black cursor-pointer"
                  for="emailUpdates"
                >
                  I agree to receive email updates
                </label>
              </div>
            </div>
            <div class="col-md-12" *ngIf="_userType == 2">
              <div class="form-check">
                <input
                  class="form-check-input"
                  type="checkbox"
                  id="termsAndConditions"
                  name="termsAndConditions"
                  formControlName="termsAndConditions"
                />
                <label
                  class="form-check-label text-black cursor-pointer"
                  for="termsAndConditions"
                >
                  I have read and agreed to our
                  <a routerLink="/terms-and-condtions" class="text-black">Terms</a>,
                  <a routerLink="/privacy-policy" class="text-black">Privacy</a>
                  <span>, and the </span>
                  <a
                    *ngIf="registrationForm.value.userType == '2'"
                    [routerLink]="['/agreements']"
                    [queryParams]="{ q: 'partner' }"
                    class="text-black"
                    >Partner Agreement</a
                  >
                  <a
                    *ngIf="registrationForm.value.userType != '2'"
                    [routerLink]="['/agreements']"
                    [queryParams]="{ q: 'user' }"
                    class="text-black"
                    >User Agreement</a
                  >
                </label>
              </div>
            </div>
            <div class="col-md-12 mt-0 mt-sm-5 mb-4" *ngIf="_userType == 2">
              <div class="row">
                <div class="col-sm-6">
                  <div routerLink="/account" role="button" class="text-left mt-4">
                    <span>Already have an account?</span>
                    <a class="text-black
                                  " routerLink="/login"> Sign in</a>
                  </div>
                </div>
                <div class="col-sm-6 d-flex align-items-center justify-content-end mt-4 mt-sm-0">
                  <focile-button [btnType]="'primary'" btnClass="sign-up-btn" [loading]="dataLoading" (onClick)="saveUser()" [disabled]="registrationForm.invalid"
                    *ngIf="registrationForm.getRawValue().userType == '2'">
                    Submit</focile-button>
                </div>
              </div>
            </div>
          </div>
        </ng-container>
        <ng-container *ngIf="_userType == 1">
          <ng-container *ngTemplateOutlet="endUserType"> </ng-container>
        </ng-container>
      </form>
    </ng-container>

    <ng-container *ngIf="isFirstStepCompleted">
      <ng-container *ngIf="registrationForm.get('userType')?.value == '1'">
        <ng-container *ngTemplateOutlet="endUserType"> </ng-container>
      </ng-container>
    </ng-container>
  </ng-container>
  <!-- <ng-container footer>
    <div routerLink="/account" role="button" class="text-center mt-3">
      <span>Already have an account?</span>
      <span class="text-primary fw-bold" routerLink="/login"> Sign in</span>
    </div>
  </ng-container> -->
</app-account-shell>
<ng-template #endUserType>
  <form [formGroup]="endUserForm" autocomplete="off">
    <div class="row pt-0">
      <div class="col-md-6">
        <div class="mb-3 mb-sm-4">
          <label for="name" class="form-label"
            >Organization Name <span class="text-danger">*</span></label
          >
          <app-focile-input
            [type]="'text'"
            [id]="'id'"
            [name]="'name'"
            [disabled]="false"
            formControlName="organizationName"
            [elementClass]="
              endUserForm.get('organizationName')?.errors?.required &&
              endUserForm.get('organizationName')?.touched
                ? 'is-invalid'
                : null
            "
          ></app-focile-input>
          <span
            *ngIf="
              endUserForm.get('organizationName')?.touched &&
              endUserForm.get('organizationName')?.errors?.required
            "
            class="text-danger"
          >
            Organization name is required.
          </span>
        </div>
      </div>
      <div class="col-md-6">
        <div class="mb-3 mb-sm-4">
          <label for="name" class="form-label"
            >Vertical <span class="text-danger">*</span></label
          >
          <focile-dropdown
            [placeholder]="'Select an option'"
            formControlName="organizationType"
            [items]="organizationType"
            (change)="handleChange('organizationType')"
          ></focile-dropdown>
          <span
            *ngIf="
              endUserForm.get('organizationType')?.touched &&
              endUserForm.get('organizationType')?.errors?.required
            "
            class="text-danger"
          >
            Vertical is required.
          </span>
        </div>
      </div>
      <div class="col-md-12">
        <div class="form-check">
          <input
            class="form-check-input"
            type="checkbox"
            id="emailUpdates"
            name="emailUpdates"
            formControlName="emailUpdates"
          />
          <label
            class="form-check-label text-black cursor-pointer"
            for="emailUpdates"
          >
            I agree to receive email updates
          </label>
        </div>
      </div>
      <div class="col-md-12">
        <div class="form-check">
          <input
            class="form-check-input"
            type="checkbox"
            id="termsAndConditions"
            name="termsAndConditions"
            formControlName="termsAndConditions"
          />
          <label
            class="form-check-label text-black cursor-pointer"
            for="termsAndConditions"
          >
            I have read and agreed to our
            <a routerLink="/terms-and-condtions" class="text-black">Terms</a>,
            <a routerLink="/privacy-policy" class="text-black">Privacy</a>
            <span>, and the </span>
            <a
              *ngIf="registrationForm.value.userType == '2'"
              [routerLink]="['/agreements']"
              [queryParams]="{ q: 'partner' }"
              class="text-black"
              >Partner Agreement</a
            >
            <a
              *ngIf="registrationForm.value.userType != '2'"
              [routerLink]="['/agreements']"
              [queryParams]="{ q: 'user' }"
              class="text-black"
              >User Agreement</a
            >
          </label>
        </div>
      </div>
      <div class="col-md-12 mt-0 mt-sm-5 mb-4">
        <div class="row">
          <div class="col-sm-6">
            <div routerLink="/account" role="button" class="text-left mt-4">
              <span>Already have an account?</span>
              <a class="text-black
              " routerLink="/login"> Sign in</a>
            </div>
          </div>
          <div class="col-sm-6 d-flex align-items-center justify-content-end mt-4 mt-sm-0">
<focile-button [btnType]="'primary'" [btnClass]="'sign-up-btn'" [loading]="savingRecord"
  [disabled]="endUserForm.invalid || !endUserForm.get('termsAndConditions')?.value"
  (onClick)="endUserSignUp()">Create an account</focile-button>
          </div>
        </div>
      </div>
    </div>
  </form>
</ng-template>

<app-footer></app-footer>