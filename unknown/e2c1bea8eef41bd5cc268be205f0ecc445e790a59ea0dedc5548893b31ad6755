import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LoginComponent } from './login/login.component';
import { RouterModule } from '@angular/router';
import { RegistrationComponent } from './registration/registration.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { ShellComponent } from './shell/shell.component';
import { ForgotPasswordComponent } from './forgot-password/forgot-password.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { PopoverModule } from 'ngx-bootstrap/popover';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { ActiateAccountComponent } from './actiate-account/actiate-account.component';
import { CopyrightsPolicyComponent } from './copyrights-policy/copyrights-policy.component';
import { PasswordRulesComponent } from './components/password-rules/password-rules.component';
import { UnsubscribeEmailsComponent } from '../user-details/unsubscribe-emails/unsubscribe-emails.component';
import { ChooseOptionComponent } from './choose-option/choose-option.component';
import { FooterComponent } from 'src/app/layout/footer/footer.component';
import { LayoutModule } from 'src/app/layout/layout.module';

@NgModule({
  declarations: [
    LoginComponent,
    RegistrationComponent,
    ShellComponent,
    ForgotPasswordComponent,
    ActiateAccountComponent,
    CopyrightsPolicyComponent,
    PasswordRulesComponent,
    ChooseOptionComponent,       
  ],
  imports: [
    CommonModule,
    SharedModule,
    ReactiveFormsModule,
    FormsModule,
    LayoutModule,    
    PopoverModule.forRoot(),
    TooltipModule.forRoot(),
    RouterModule.forChild([
      {
        path: '',
        component: LoginComponent,
      },
      {
        path: 'login',
        component: LoginComponent,
      },
      {
        path: 'register',
        component: RegistrationComponent,
      },
      {
        path: 'recovery',
        component: ForgotPasswordComponent,
      },
      {
        path: 'activate-account',
        component: ActiateAccountComponent,
      },
      {
        path: 'unsubscribe-emails',
        component: UnsubscribeEmailsComponent,
      },
      {
        path: 'choose-option',
        component: ChooseOptionComponent,
      },
      // {
      //   path: '**',
      //   redirectTo: 'login'
      // },
    ]),    
  ],
  exports: [SharedModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class AccountModule {}